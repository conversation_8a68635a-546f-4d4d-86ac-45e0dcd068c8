<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>RGB LED Control</title>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#0a0a0a">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="RGB Control">
    <link rel="apple-touch-icon" href="/icon-192.png">
    <link rel="apple-touch-startup-image" href="/icon-512.png">
    <style>
        :root {
            --bg-color: #0a0a0a;
            --text-color: #b19cd9;
            --neon-glow: #9370DB;
            --panel-bg: rgba(20, 20, 20, 0.8);
            --slider-bg: #2a2a2a;
            --slider-active: #9370DB;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: fixed;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            overflow-y: auto;  /* Allow scrolling within container if needed */
            -webkit-overflow-scrolling: touch;
            height: 100%;
            padding-bottom: 20px;
        }

        h1 {
            color: var(--neon-glow);
            text-align: center;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 
                0 0 5px var(--neon-glow),
                0 0 10px var(--neon-glow),
                0 0 20px var(--neon-glow);
            animation: pulse 2s infinite;
        }

        .panel {
            background: var(--panel-bg);
            border: 2px solid var(--neon-glow);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 0 15px rgba(0, 255, 153, 0.2);
        }

        .info-panel {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 10px;
            font-size: 0.9em;
        }

        .info-label {
            color: #888;
        }

        .info-value {
            color: var(--neon-glow);
            text-align: right;
        }

        .color-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 20px auto;
            border: 3px solid var(--neon-glow);
            box-shadow: 0 0 20px rgba(0, 255, 153, 0.3);
            transition: all 0.3s ease;
        }

        .slider-container {
            margin: 20px 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #888;
        }

        input[type="range"] {
            -webkit-appearance: none;
            appearance: none; /* Add for compatibility */
            width: 100%;
            height: 15px;  /* Было 10px */
            background: var(--slider-bg);
            border-radius: 7px;  /* Немного увеличим для соответствия */
            outline: none;
            margin: 15px 0;  /* Увеличим отступы */
            touch-action: none;  /* Prevent scrolling on touch devices */
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 50px;  /* Было 20px */
            height: 50px; /* Было 20px */
            background: var(--slider-active);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 10px var(--neon-glow);
        }

        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }

        .value-display {
            float: right;
            color: var(--neon-glow);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RGB LED Control</h1>
        
        <div class="panel info-panel">
            <div class="info-label">IP Address:</div>
            <div class="info-value" id="ip-address">Loading...</div>
            <div class="info-label">Memory:</div>
            <div class="info-value" id="free-memory">Loading...</div>
        </div>

        <div class="panel">
            <div class="color-preview" id="colorPreview"></div>
            
            <div class="slider-container">
                <label for="redSlider">Red <span class="value-display" id="redValue">0</span></label>
                <input type="range" id="redSlider" min="0" max="255" value="0">
            </div>
            
            <div class="slider-container">
                <label for="greenSlider">Green <span class="value-display" id="greenValue">0</span></label>
                <input type="range" id="greenSlider" min="0" max="255" value="0">
            </div>
            
            <div class="slider-container">
                <label for="blueSlider">Blue <span class="value-display" id="blueValue">0</span></label>
                <input type="range" id="blueSlider" min="0" max="255" value="0">
            </div>
        </div>
    </div>

    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(err => {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        const redSlider = document.getElementById('redSlider');
        const greenSlider = document.getElementById('greenSlider');
        const blueSlider = document.getElementById('blueSlider');
        const colorPreview = document.getElementById('colorPreview');
        const redValue = document.getElementById('redValue');
        const greenValue = document.getElementById('greenValue');
        const blueValue = document.getElementById('blueValue');
        
        let updateTimeout = null;
        
        function updateColor() {
            const r = redSlider.value;
            const g = greenSlider.value;
            const b = blueSlider.value;
            
            redValue.textContent = r;
            greenValue.textContent = g;
            blueValue.textContent = b;
            
            colorPreview.style.backgroundColor = `rgb(${r},${g},${b})`;
            colorPreview.style.boxShadow = `0 0 20px rgb(${r},${g},${b})`;
            
            clearTimeout(updateTimeout);
            updateTimeout = setTimeout(() => {
                fetch(`/update-led?r=${r}&g=${g}&b=${b}`)
                    .catch(error => console.error('Error updating LED:', error));
            }, 50);
        }
        
        redSlider.addEventListener('input', updateColor);
        greenSlider.addEventListener('input', updateColor);
        blueSlider.addEventListener('input', updateColor);
        
        async function updateSystemInfo() {
            try {
                const response = await fetch('/system-info');
                const data = await response.json();
                document.getElementById('ip-address').textContent = data.ip;
                document.getElementById('free-memory').textContent = data.memory + ' bytes';
            } catch (error) {
                console.error('Error fetching system info:', error);
            }
        }
        
        updateSystemInfo();
        setInterval(updateSystemInfo, 5000);
        updateColor();
    </script>
</body>
</html>
