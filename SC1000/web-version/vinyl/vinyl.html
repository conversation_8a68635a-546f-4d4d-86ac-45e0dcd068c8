<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>RGB LED Control</title>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#0a0a0a">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="RGB Control">
    <link rel="icon" type="image/png" sizes="192x192" href="/icon-192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/icon-512.png">
    <link rel="apple-touch-icon" sizes="192x192" href="/icon-192.png">
    <link rel="apple-touch-icon" sizes="512x512" href="/icon-512.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            overflow: hidden;
            overscroll-behavior: none;
            touch-action: manipulation;
            position: fixed;
            width: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #0a0a0a;
            padding: 0 20px;
        }
        
        .color-circles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
            will-change: transform;
        }

        .circle {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 120vmax;
            height: 120vmax;
            border-radius: 50%;
            mix-blend-mode: screen;
            will-change: transform;
        }

        #redCircle {
            background: conic-gradient(
                from 0deg,
                rgba(255, 130, 150, 0.8) 0deg,
                rgba(255, 130, 150, 0.4) 180deg,
                rgba(255, 130, 150, 0) 360deg
            );
            transform: translate(-50%, -50%);
            box-shadow:
                inset -30px -30px 60px rgba(0, 0, 0, 0.3),
                0 0 40px rgba(255, 130, 150, 0.3),
                0 15px 30px rgba(0, 0, 0, 0.2);
        }

        #greenCircle {
            background: conic-gradient(
                from 0deg,
                rgba(130, 255, 150, 0.8) 0deg,
                rgba(130, 255, 150, 0.4) 180deg,
                rgba(130, 255, 150, 0) 360deg
            );
            transform: translate(-50%, -50%);
            box-shadow:
                inset -30px -30px 60px rgba(0, 0, 0, 0.3),
                0 0 40px rgba(130, 255, 150, 0.3),
                0 15px 30px rgba(0, 0, 0, 0.2);
        }

        #blueCircle {
            background: conic-gradient(
                from 0deg,
                rgba(130, 150, 255, 0.8) 0deg,
                rgba(130, 150, 255, 0.4) 180deg,
                rgba(130, 150, 255, 0) 360deg
            );
            transform: translate(-50%, -50%);
            box-shadow:
                inset -30px -30px 60px rgba(0, 0, 0, 0.3),
                0 0 40px rgba(130, 150, 255, 0.3),
                0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .container {
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            position: relative; 
            z-index: 1; 
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 25px 20px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .bottom-panel {
            padding: 10px;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .info-panel {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 10px 15px;
            font-size: 0.9em;
        }

        .info-label { 
            color: rgba(255, 255, 255, 0.6);
        }

        .info-value {
            text-align: right;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .slider-container { 
            margin: 15px 0;
        }

        .slider-container:first-child {
            margin-top: 0;
        }

        .slider-container:last-child {
            margin-bottom: 0;
        }

        input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            outline: none;
            touch-action: manipulation;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        #redSlider::-webkit-slider-thumb {
            background: rgba(255, 80, 80, 0.3);
        }

        #greenSlider::-webkit-slider-thumb {
            background: rgba(80, 255, 80, 0.3);
        }

        #blueSlider::-webkit-slider-thumb {
            background: rgba(80, 80, 255, 0.3);
        }

        .vinyl-record {
            position: absolute;
            width: 300px; /* Размер пластинки */
            height: 300px;
            background-color: #080808; /* Очень темный, почти черный */
            border-radius: 50%;
            box-shadow: 
                0 20px 50px rgba(0, 0, 0, 0.7), /* Более выраженная тень */
                inset 0 0 40px rgba(0, 0, 0, 0.9); /* Глубокая внутренняя тень */
            z-index: 0; 
            top: 500px; 
            left: 50%;
            transform: translateX(-50%);
            overflow: hidden;
            animation: rotate 10s linear infinite; 
            border: none; /* Убираем внешнюю рамку */
            /* Добавим легкий радиальный градиент для объема */
            background-image: radial-gradient(circle at center, #1a1a1a 0%, #080808 70%);
        }

        @keyframes rotate {
            from {
                transform: translateX(-50%) rotate(0deg);
            }
            to {
                transform: translateX(-50%) rotate(360deg);
            }
        }

        .vinyl-grooves {
            position: absolute;
            inset: 5%; 
            border-radius: 50%;
            background-image: repeating-radial-gradient(circle, 
                #101010, #101010 0.5px, #181818 0.5px, #181818 1.5px); 
            background-size: 100% 100%;
            box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.7); 
            z-index: 0; /* Убедимся, что канавки под рамкой и этикеткой */
        }

        /* НОВЫЕ СТИЛИ ДЛЯ ПРОЗРАЧНОЙ РАМКИ */
        .vinyl-transparent-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            /* Размер рамки: диаметр этикетки (100px) + 2 * толщина рамки (6px) = 112px */
            width: 112px; 
            height: 112px;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 0.5; /* Находится между канавками (0) и этикеткой (1) */
            
            /* Применяем стили прозрачного стекла */
            background: rgba(255, 255, 255, 0.05); /* Очень легкий фон */
            backdrop-filter: blur(8px); /* Размытие фона под рамкой */
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1); /* Тонкая граница рамки */
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Легкая тень */
        }


        .vinyl-label {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100px; /* Размер этикетки */
            height: 100px;
            border-radius: 50%;
            background: #d32f2f; /* Более темный красный, как на фото */
            transform: translate(-50%, -50%);
            border: none; /* УДАЛЯЕМ рамку с самой этикетки */
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px; /* Размер шрифта для текста */
            color: #fbc02d; /* Золотистый цвет текста */
            font-weight: normal; 
            text-transform: none; 
            font-family: serif; 
            letter-spacing: 0.5px; 
            overflow: hidden;
            transition: background-color 0.2s, color 0.2s; 
            z-index: 1; /* Убедимся, что этикетка над рамкой */
        }

        .vinyl-label::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px; 
            height: 20px;
            border-radius: 50%;
            background-color: #080808; 
            transform: translate(-50%, -50%);
            z-index: 1; 
            border: 2px solid #333; 
        }

        .vinyl-label span {
            position: relative; 
            z-index: 0;
            transform: none; 
        }

        /* Стили для блика */
        .vinyl-glare {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            overflow: hidden;
            z-index: 2; /* Блик поверх всего */
            background: none; /* Убираем старый фон блика */
            animation: none; /* Убираем старую анимацию блика */
        }

        .vinyl-glare::before {
             content: '';
            position: absolute;
            width: 150%; 
            height: 150%;
            top: -25%; 
            left: -25%;
            border-radius: 50%;
            background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.25) 0%, transparent 40%);
            transform-origin: 50% 50%; 
            animation: none; /* Убираем анимацию вращения блика */
        }


        /* Медиа-запрос для адаптации размера пластинки на маленьких экранах */
        @media (max-width: 480px) {
            .vinyl-record {
                width: 250px;
                height: 250px;
                top: 430px;
            }
            .vinyl-transparent-ring {
                /* Размер рамки для мобильных: диаметр этикетки (80px) + 2 * толщина рамки (6px) = 92px */
                width: 92px; 
                height: 92px;
            }
            .vinyl-label {
                width: 80px;
                height: 80px;
                font-size: 12px; 
            }
            .vinyl_label::after {
                width: 18px;
                height: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="color-circles">
        <div class="circle" id="redCircle"></div>
        <div class="circle" id="greenCircle"></div>
        <div class="circle" id="blueCircle"></div>
    </div>
    <div class="container">
        <div class="panel info-panel">
            <div class="info-label">Status:</div>
            <div class="info-value" id="connection-status">Connected</div>
            <div class="info-label">IP Address:</div>
            <div class="info-value" id="ip-address">Loading...</div>
            <div class="info-label">Memory:</div>
            <div class="info-value" id="free-memory">Loading...</div>
        </div>

        <div class="panel">
            <div class="slider-container">
                <input type="range" id="redSlider" min="0" max="255" value="0">
            </div>
            
            <div class="slider-container">
                <input type="range" id="greenSlider" min="0" max="255" value="0">
            </div>
            
            <div class="slider-container">
                <input type="range" id="blueSlider" min="0" max="255" value="0">
            </div>
        </div>

        <div class="panel bottom-panel">
        </div>

    </div>

    <div class="vinyl-record">
        <div class="vinyl-grooves"></div>
        <div class="vinyl-transparent-ring"></div> <!-- НОВЫЙ ЭЛЕМЕНТ ДЛЯ ПРОЗРАЧНОЙ РАМКИ -->
        <div class="vinyl-label">
            <span>#000000</span>
        </div>
        <div class="vinyl-glare"></div> 
    </div>

    <script>
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js').catch(console.error);
        }

        const redSlider = document.getElementById('redSlider');
        const greenSlider = document.getElementById('greenSlider');
        const blueSlider = document.getElementById('blueSlider');
        const redCircle = document.getElementById('redCircle');
        const greenCircle = document.getElementById('greenCircle');
        const blueCircle = document.getElementById('blueCircle');
        // НОВОЕ: Получаем доступ к элементам этикетки
        const vinylLabel = document.querySelector('.vinyl-label');
        const vinylLabelText = document.querySelector('.vinyl-label span');
        
        let updateTimeout = null;
        let lastGradientUpdate = 0;
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const gradientUpdateDelay = isMobile ? 100 : 16; 
        
        // НОВОЕ: Функция для определения, светлый ли цвет (для выбора цвета текста)
        function isColorLight(r, g, b) {
            // Формула для вычисления воспринимаемой яркости
            const brightness = Math.round(((parseInt(r) * 299) +
                                       (parseInt(g) * 587) +
                                       (parseInt(b) * 114)) / 1000);
            return (brightness > 150); // Возвращаем true, если цвет светлый
        }

        function updateGradient() {
            const now = Date.now();
            if (now - lastGradientUpdate < gradientUpdateDelay) return;
            
            const r = parseInt(redSlider.value);
            const g = parseInt(greenSlider.value);
            const b = parseInt(blueSlider.value);
            
            const redAngle = (r / 255) * 360;
            const greenAngle = (g / 255) * 360;
            const blueAngle = (b / 255) * 360;
            
            redCircle.style.transform = `translate(-50%, -50%) rotate(${redAngle}deg)`;
            greenCircle.style.transform = `translate(-50%, -50%) rotate(${greenAngle}deg)`;
            blueCircle.style.transform = `translate(-50%, -50%) rotate(${blueAngle}deg)`;
            
            // --- НОВЫЙ БЛОК: Обновление этикетки на пластинке ---
            // Формируем HEX-код цвета
            const toHex = c => ('0' + c.toString(16)).slice(-2);
            const hexColor = `#${toHex(r)}${toHex(g)}${toHex(b)}`;

            // Устанавливаем фон этикетки
            vinylLabel.style.backgroundColor = `rgb(${r}, ${g}, ${b})`;
            // Обновляем текст на этикетке
            vinylLabelText.textContent = hexColor.toUpperCase();
            // Меняем цвет текста на черный или белый для лучшей читаемости
            vinylLabel.style.color = isColorLight(r, g, b) ? '#000000' : '#FFFFFF';
            // --- КОНЕЦ НОВОГО БЛОКА ---

            lastGradientUpdate = now;
        }

        function updateColor() {
            updateGradient();
            
            clearTimeout(updateTimeout);
            updateTimeout = setTimeout(() => {
                fetch(`/update-led?r=${redSlider.value}&g=${greenSlider.value}&b=${blueSlider.value}`)
                    .catch(console.error);
            }, 50);
        }
        
        redSlider.addEventListener('input', updateColor);
        greenSlider.addEventListener('input', updateColor);
        blueSlider.addEventListener('input', updateColor);
        
        async function updateSystemInfo() {
            try {
                const response = await fetch('/system-info');
                const data = await response.json();
                document.getElementById('ip-address').textContent = data.ip;
                document.getElementById('free-memory').textContent = data.memory + ' bytes';
            } catch (error) {
                console.error('Error fetching system info:', error);
            }
        }
        
        async function syncTime() {
            const now = new Date();
            const timeData = {
                year: now.getFullYear(), month: now.getMonth() + 1, day: now.getDate(),
                hour: now.getHours(), minute: now.getMinutes(), second: now.getSeconds()
            };

            try {
                await fetch('/set-time', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(timeData)
                });
                console.log('Time synchronized');
            } catch (error) {
                console.error('Error syncing time:', error);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            syncTime();
            updateSystemInfo();
            setInterval(updateSystemInfo, 5000);
            updateColor();
        });
    </script>
</body>
</html>