# SC1000 Web Emulator

Веб-версия портативного цифрового скретч-инструмента SC1000, работающая в браузере.

## Возможности

### ✅ Реализованные функции:
- **Два деки**: Beats (биты) и Samples (сэмплы)
- **Загрузка файлов**: Поддержка MP3, WAV и других аудиоформатов
- **Джог-колесо**: Интерактивное управление мышью/тачем с реалистичным скретчингом
- **Продвинутый скретчинг**:
  - Изменение скорости воспроизведения в реальном времени
  - Точное позиционирование при медленном движении
  - Визуальная обратная связь с цветовыми эффектами
  - Индикатор направления и интенсивности скретчинга
- **Кроссфейдер**: Микширование между деками
- **Управление воспроизведением**: Play/Pause/Stop для каждого дека
- **Навигация по трекам**: Previous/Next для переключения файлов
- **Контроль громкости**: Независимые регуляторы для каждого дека
- **Cue точки**: Установка и возврат к cue точкам
- **Loop режим**: Зацикливание сэмплов
- **Визуальная обратная связь**: Прогресс-бары, информация о треках, эффекты скретчинга

### 🔄 Упрощения по сравнению с оригиналом:
- Использует Web Audio API вместо ALSA
- Джог-колесо эмулируется мышью/тачем
- Нет физических кнопок и фейдеров
- Упрощенная обработка скретчинга
- Нет real-time аудио обработки низкого уровня

## Использование

1. **Откройте `index.html` в современном браузере**
2. **Загрузите аудиофайлы**:
   - Нажмите "Load Beat Files" для загрузки битов
   - Нажмите "Load Sample Files" для загрузки сэмплов
3. **Управление**:
   - Используйте кнопки Play/Stop для воспроизведения
   - **Скретчинг с джог-колесом**:
     - Нажмите и удерживайте джог-колесо
     - Медленно двигайте для точного позиционирования
     - Быстро двигайте для эффекта скретчинга
     - Отпустите для возврата к нормальному воспроизведению
     - Наблюдайте за цветовыми эффектами и индикатором
   - Перемещайте кроссфейдер для микширования
   - Регулируйте громкость каждого дека

## Технические детали

### Архитектура:
- **HTML5**: Структура интерфейса
- **CSS3**: Стилизация в стиле ретро-терминала
- **JavaScript ES6**: Логика эмулятора
- **Web Audio API**: Аудио обработка

### Основные классы:
- `SC1000Emulator`: Главный класс эмулятора
- Управление деками (beatDeck, sampleDeck)
- Обработка джог-колеса и кроссфейдера
- Файловая система через File API

## Портирование на микроконтроллеры

### ESP32 (Рекомендуется):
```cpp
// Основные компоненты для ESP32 версии:
- ESP32-A2DP для Bluetooth аудио
- I2S для цифрового аудио выхода  
- VS1053 или аналогичный MP3 декодер
- AS5600 магнитный энкодер для джог-колеса
- SD карта для хранения файлов
- OLED дисплей для интерфейса
```

**Преимущества ESP32:**
- Встроенный WiFi/Bluetooth
- Достаточно RAM (520KB)
- I2S поддержка
- Arduino IDE совместимость
- Низкая стоимость

**Ограничения:**
- Ограниченная производительность для real-time аудио
- Нужен внешний MP3 декодер
- Меньше GPIO по сравнению с оригиналом

### STM32 (Более сложно):
```cpp
// Компоненты для STM32:
- STM32F4/F7 с DSP возможностями
- External SRAM для буферизации
- I2S DAC для аудио выхода
- FatFS для работы с SD картой
- RTOS для многозадачности
```

**Преимущества STM32:**
- Высокая производительность
- Больше периферии
- Real-time возможности
- Профессиональное качество

**Ограничения:**
- Сложность разработки
- Нужны внешние компоненты
- Высокая стоимость разработки

## Упрощенная версия для микроконтроллеров

### Минимальная функциональность:
1. **Один дек** вместо двух
2. **Простое воспроизведение** без скретчинга
3. **Базовые кнопки**: Play/Stop/Next/Prev
4. **LED индикация** вместо дисплея
5. **Фиксированная громкость**

### Рекомендуемая архитектура:
```
ESP32 + VS1053 + SD Card + Rotary Encoder + Buttons + I2S DAC
```

## Файловая структура

```
web-version/
├── index.html          # Главная страница
├── sc1000.js          # JavaScript логика
├── README.md          # Документация
└── examples/          # Примеры аудиофайлов (опционально)
```

## Совместимость браузеров

- ✅ Chrome 66+
- ✅ Firefox 60+
- ✅ Safari 11.1+
- ✅ Edge 79+
- ⚠️ Требует HTTPS для некоторых функций

## Разработка

### Для добавления новых функций:
1. Модифицируйте `SC1000Emulator` класс в `sc1000.js`
2. Добавьте соответствующие HTML элементы
3. Обновите CSS стили при необходимости

### Для оптимизации:
- Используйте Web Workers для аудио обработки
- Добавьте AudioWorklet для низкой задержки
- Реализуйте более сложные эффекты скретчинга

## Лицензия

Основано на оригинальном проекте SC1000 (GPL v2)
Веб-версия также распространяется под GPL v2
