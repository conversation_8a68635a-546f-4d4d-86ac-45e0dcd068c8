class SC1000Emulator {
    constructor() {
        this.audioContext = null;
        this.beatDeck = {
            files: [],
            currentIndex: 0,
            audio: null,
            isPlaying: false,
            volume: 0.5
        };
        this.sampleDeck = {
            files: [],
            currentIndex: 0,
            audio: null,
            isPlaying: false,
            volume: 0.5,
            isLooping: false,
            cuePoint: 0
        };
        this.crossfaderValue = 0.5;
        this.jogWheelAngle = 0;
        this.isJogTouched = false;
        this.originalPlaybackRate = 1.0;
        this.scratchResetTimer = null;
        this.lastJogMovement = 0;
        this.jogVelocity = 0;

        this.init();
    }

    async init() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.error('Web Audio API not supported:', e);
        }
        
        this.setupEventListeners();
        this.updateStatus('SC1000 Web Emulator Ready');
    }

    setupEventListeners() {
        // File inputs
        document.getElementById('beat-files').addEventListener('change', (e) => {
            this.loadFiles(e.target.files, 'beat');
        });
        
        document.getElementById('sample-files').addEventListener('change', (e) => {
            this.loadFiles(e.target.files, 'sample');
        });

        // Beat deck controls
        document.getElementById('beat-play').addEventListener('click', () => {
            this.togglePlay('beat');
        });
        
        document.getElementById('beat-stop').addEventListener('click', () => {
            this.stop('beat');
        });
        
        document.getElementById('beat-prev').addEventListener('click', () => {
            this.previousTrack('beat');
        });
        
        document.getElementById('beat-next').addEventListener('click', () => {
            this.nextTrack('beat');
        });

        // Sample deck controls
        document.getElementById('sample-prev').addEventListener('click', () => {
            this.previousTrack('sample');
        });
        
        document.getElementById('sample-next').addEventListener('click', () => {
            this.nextTrack('sample');
        });
        
        document.getElementById('sample-cue').addEventListener('click', () => {
            this.setCue();
        });
        
        document.getElementById('sample-loop').addEventListener('click', () => {
            this.toggleLoop();
        });

        // Volume controls
        document.getElementById('beat-volume').addEventListener('input', (e) => {
            this.setVolume('beat', e.target.value / 100);
        });
        
        document.getElementById('sample-volume').addEventListener('input', (e) => {
            this.setVolume('sample', e.target.value / 100);
        });

        // Jog wheel
        this.setupJogWheel();
        
        // Crossfader
        this.setupCrossfader();
        
        // Update displays
        setInterval(() => this.updateDisplays(), 100);
    }

    setupJogWheel() {
        const jogWheel = document.getElementById('jog-wheel');
        let isDragging = false;
        let lastAngle = 0;

        const getAngle = (e, element) => {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const x = e.clientX - centerX;
            const y = e.clientY - centerY;
            return Math.atan2(y, x);
        };

        jogWheel.addEventListener('mousedown', (e) => {
            isDragging = true;
            this.isJogTouched = true;
            lastAngle = getAngle(e, jogWheel);
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const currentAngle = getAngle(e, jogWheel);
            const deltaAngle = currentAngle - lastAngle;

            // Handle angle wrapping
            let normalizedDelta = deltaAngle;
            if (normalizedDelta > Math.PI) normalizedDelta -= 2 * Math.PI;
            if (normalizedDelta < -Math.PI) normalizedDelta += 2 * Math.PI;

            // Увеличиваем чувствительность для лучшего скретчинга
            this.jogWheelAngle += normalizedDelta;

            // Передаем более точное значение для скретчинга
            this.handleJogMovement(normalizedDelta);

            // Визуальное вращение джог-колеса
            jogWheel.style.transform = `rotate(${this.jogWheelAngle}rad)`;

            // Добавляем эффект инерции при быстром движении
            if (Math.abs(normalizedDelta) > 0.3) {
                jogWheel.style.transition = 'none';
            } else {
                jogWheel.style.transition = 'transform 0.1s ease-out';
            }

            lastAngle = currentAngle;
            e.preventDefault(); // Предотвращаем выделение текста
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                this.isJogTouched = false;

                // Возвращаем нормальную скорость воспроизведения
                if (this.sampleDeck.audio && this.sampleDeck.audio.playbackRate !== undefined) {
                    this.sampleDeck.audio.playbackRate = this.originalPlaybackRate;
                }

                // Очищаем визуальные эффекты
                this.clearScratchFeedback();

                // Возвращаем плавные переходы
                const jogWheel = document.getElementById('jog-wheel');
                jogWheel.style.transition = 'transform 0.2s ease-out';
            }
        });

        // Touch events for mobile
        jogWheel.addEventListener('touchstart', (e) => {
            isDragging = true;
            this.isJogTouched = true;
            lastAngle = getAngle(e.touches[0], jogWheel);
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;

            const currentAngle = getAngle(e.touches[0], jogWheel);
            const deltaAngle = currentAngle - lastAngle;

            let normalizedDelta = deltaAngle;
            if (normalizedDelta > Math.PI) normalizedDelta -= 2 * Math.PI;
            if (normalizedDelta < -Math.PI) normalizedDelta += 2 * Math.PI;

            this.jogWheelAngle += normalizedDelta;
            this.handleJogMovement(normalizedDelta);

            // Визуальное вращение с эффектами
            jogWheel.style.transform = `rotate(${this.jogWheelAngle}rad)`;

            if (Math.abs(normalizedDelta) > 0.3) {
                jogWheel.style.transition = 'none';
            } else {
                jogWheel.style.transition = 'transform 0.1s ease-out';
            }

            lastAngle = currentAngle;
            e.preventDefault();
        });

        document.addEventListener('touchend', () => {
            if (isDragging) {
                isDragging = false;
                this.isJogTouched = false;

                // Возвращаем нормальную скорость воспроизведения
                if (this.sampleDeck.audio && this.sampleDeck.audio.playbackRate !== undefined) {
                    this.sampleDeck.audio.playbackRate = this.originalPlaybackRate;
                }

                // Очищаем визуальные эффекты
                this.clearScratchFeedback();

                // Возвращаем плавные переходы
                const jogWheel = document.getElementById('jog-wheel');
                jogWheel.style.transition = 'transform 0.2s ease-out';
            }
        });
    }

    setupCrossfader() {
        const crossfader = document.getElementById('crossfader');
        const fader = crossfader.parentElement;
        let isDragging = false;

        const updateFaderPosition = (e) => {
            const rect = fader.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
            
            crossfader.style.left = percentage + '%';
            this.crossfaderValue = percentage / 100;
            this.updateCrossfaderMix();
        };

        crossfader.addEventListener('mousedown', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            updateFaderPosition(e);
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events
        crossfader.addEventListener('touchstart', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            updateFaderPosition(e.touches[0]);
            e.preventDefault();
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });
    }

    async loadFiles(files, deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        deck.files = Array.from(files);
        deck.currentIndex = 0;
        
        if (deck.files.length > 0) {
            await this.loadTrack(deckType, 0);
            this.updateStatus(`Loaded ${deck.files.length} files to ${deckType} deck`);
        }
    }

    async loadTrack(deckType, index) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        
        if (index < 0 || index >= deck.files.length) return;
        
        // Stop current audio
        if (deck.audio) {
            deck.audio.pause();
            deck.audio = null;
        }
        
        deck.currentIndex = index;
        const file = deck.files[index];
        
        try {
            const url = URL.createObjectURL(file);
            deck.audio = new Audio(url);
            deck.audio.volume = deck.volume;
            deck.audio.loop = deckType === 'sample' ? deck.isLooping : false;
            
            // Wait for metadata to load
            await new Promise((resolve) => {
                deck.audio.addEventListener('loadedmetadata', resolve);
            });
            
            this.updateTrackDisplay(deckType);
            
        } catch (error) {
            console.error('Error loading track:', error);
            this.updateStatus('Error loading track: ' + file.name);
        }
    }

    togglePlay(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        
        if (!deck.audio) return;
        
        if (deck.isPlaying) {
            deck.audio.pause();
            deck.isPlaying = false;
            document.getElementById(`${deckType}-play`).textContent = '▶ PLAY';
        } else {
            // Resume Web Audio Context if needed
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
            
            deck.audio.play();
            deck.isPlaying = true;
            document.getElementById(`${deckType}-play`).textContent = '⏸ PAUSE';
        }
    }

    stop(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        
        if (!deck.audio) return;
        
        deck.audio.pause();
        deck.audio.currentTime = 0;
        deck.isPlaying = false;
        document.getElementById(`${deckType}-play`).textContent = '▶ PLAY';
    }

    previousTrack(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        const newIndex = deck.currentIndex - 1;
        
        if (newIndex >= 0) {
            this.loadTrack(deckType, newIndex);
        }
    }

    nextTrack(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        const newIndex = deck.currentIndex + 1;
        
        if (newIndex < deck.files.length) {
            this.loadTrack(deckType, newIndex);
        }
    }

    setCue() {
        if (this.sampleDeck.audio) {
            this.sampleDeck.cuePoint = this.sampleDeck.audio.currentTime;
            this.updateStatus(`Cue point set at ${this.formatTime(this.sampleDeck.cuePoint)}`);
        }
    }

    toggleLoop() {
        this.sampleDeck.isLooping = !this.sampleDeck.isLooping;
        if (this.sampleDeck.audio) {
            this.sampleDeck.audio.loop = this.sampleDeck.isLooping;
        }
        
        const button = document.getElementById('sample-loop');
        button.style.background = this.sampleDeck.isLooping ? '#00ff00' : '#444';
        button.style.color = this.sampleDeck.isLooping ? '#000' : '#fff';
        
        this.updateStatus(`Loop ${this.sampleDeck.isLooping ? 'enabled' : 'disabled'}`);
    }

    setVolume(deckType, volume) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        deck.volume = volume;
        
        if (deck.audio) {
            deck.audio.volume = volume * (deckType === 'beat' ? 
                (1 - this.crossfaderValue) : this.crossfaderValue);
        }
        
        document.getElementById(`${deckType}-volume-display`).textContent = 
            Math.round(volume * 100) + '%';
    }

    updateCrossfaderMix() {
        if (this.beatDeck.audio) {
            this.beatDeck.audio.volume = this.beatDeck.volume * (1 - this.crossfaderValue);
        }
        
        if (this.sampleDeck.audio) {
            this.sampleDeck.audio.volume = this.sampleDeck.volume * this.crossfaderValue;
        }
    }

    handleJogMovement(deltaAngle) {
        if (!this.sampleDeck.audio || !this.isJogTouched) return;

        // Более реалистичный скретчинг
        const scratchSensitivity = 20; // Увеличенная чувствительность
        const scratchSpeed = deltaAngle * scratchSensitivity;

        // Сохраняем оригинальную скорость воспроизведения
        if (!this.originalPlaybackRate) {
            this.originalPlaybackRate = this.sampleDeck.audio.playbackRate || 1.0;
        }

        // Сохраняем скорость движения для инерции
        this.jogVelocity = scratchSpeed;
        this.lastJogMovement = Date.now();

        if (Math.abs(scratchSpeed) > 0.02) {
            // Активный скретчинг

            // Для очень медленного движения - точное позиционирование
            if (Math.abs(scratchSpeed) < 0.1) {
                const timeShift = scratchSpeed * 0.1; // Сдвиг по времени
                const newTime = this.sampleDeck.audio.currentTime + timeShift;

                if (newTime >= 0 && newTime <= this.sampleDeck.audio.duration) {
                    this.sampleDeck.audio.currentTime = newTime;
                }

                // Останавливаем воспроизведение для точного контроля
                if (this.sampleDeck.isPlaying) {
                    this.sampleDeck.audio.pause();
                }
            } else {
                // Для быстрого движения - изменение скорости
                const newRate = 1.0 + scratchSpeed;
                const clampedRate = Math.max(-4, Math.min(4, newRate));

                if (this.sampleDeck.audio.playbackRate !== undefined) {
                    this.sampleDeck.audio.playbackRate = clampedRate;
                }

                // Возобновляем воспроизведение если было остановлено
                if (this.sampleDeck.isPlaying && this.sampleDeck.audio.paused) {
                    this.sampleDeck.audio.play();
                }
            }

            // Визуальная обратная связь
            this.updateScratchFeedback(scratchSpeed);

            // Сбрасываем таймер возврата к нормальной скорости
            clearTimeout(this.scratchResetTimer);
            this.scratchResetTimer = setTimeout(() => {
                if (this.sampleDeck.audio && !this.isJogTouched) {
                    this.sampleDeck.audio.playbackRate = this.originalPlaybackRate;
                    if (this.sampleDeck.isPlaying && this.sampleDeck.audio.paused) {
                        this.sampleDeck.audio.play();
                    }
                    this.clearScratchFeedback();
                }
            }, 150);
        } else {
            // Очень медленное движение или остановка
            if (this.sampleDeck.audio.playbackRate !== undefined) {
                this.sampleDeck.audio.playbackRate = this.originalPlaybackRate;
            }
            this.clearScratchFeedback();
        }
    }

    updateScratchFeedback(scratchSpeed) {
        const jogWheel = document.getElementById('jog-wheel');
        const indicator = document.getElementById('scratch-indicator');
        const intensity = Math.min(1, Math.abs(scratchSpeed) / 2);

        // Изменяем цвет границы в зависимости от интенсивности скретчинга
        if (scratchSpeed > 0) {
            jogWheel.style.borderColor = `rgb(${255 * intensity}, ${255 * (1-intensity)}, 0)`; // Желтый-красный
            indicator.textContent = `Scratching Forward (${(scratchSpeed * 100).toFixed(0)}%)`;
            indicator.style.color = `rgb(${255 * intensity}, ${255 * (1-intensity)}, 0)`;
        } else {
            jogWheel.style.borderColor = `rgb(0, ${255 * (1-intensity)}, ${255 * intensity})`; // Зеленый-синий
            indicator.textContent = `Scratching Backward (${(Math.abs(scratchSpeed) * 100).toFixed(0)}%)`;
            indicator.style.color = `rgb(0, ${255 * (1-intensity)}, ${255 * intensity})`;
        }

        // Добавляем эффект свечения
        const glowColor = jogWheel.style.borderColor;
        jogWheel.style.boxShadow = `inset 0 0 25px rgba(0,0,0,0.7), 0 0 ${30 * intensity}px ${glowColor}`;
    }

    clearScratchFeedback() {
        const jogWheel = document.getElementById('jog-wheel');
        const indicator = document.getElementById('scratch-indicator');

        jogWheel.style.borderColor = '#00ff00';
        jogWheel.style.boxShadow = 'inset 0 0 25px rgba(0,0,0,0.7), 0 0 15px rgba(0,255,0,0.3)';

        indicator.textContent = 'Touch & Drag to Scratch';
        indicator.style.color = '#888';
    }

    updateDisplays() {
        this.updateTrackDisplay('beat');
        this.updateTrackDisplay('sample');
    }

    updateTrackDisplay(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        
        if (!deck.audio) return;
        
        const trackName = deck.files[deck.currentIndex]?.name || 'No track';
        const currentTime = deck.audio.currentTime || 0;
        const duration = deck.audio.duration || 0;
        const progress = duration > 0 ? (currentTime / duration) * 100 : 0;
        
        document.getElementById(`${deckType}-track-name`).textContent = trackName;
        document.getElementById(`${deckType}-track-time`).textContent = 
            `${this.formatTime(currentTime)} / ${this.formatTime(duration)}`;
        document.getElementById(`${deckType}-progress`).style.width = progress + '%';
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '00:00';
        
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    updateStatus(message) {
        document.getElementById('status').textContent = message;
        console.log('SC1000:', message);
    }
}

// Initialize the emulator when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SC1000Emulator();
});
