class SC1000Emulator {
    constructor() {
        this.beatDeck = {
            files: [],
            currentIndex: 0,
            audio: null,
            isPlaying: false,
            volume: 0.5
        };
        this.sampleDeck = {
            files: [],
            currentIndex: 0,
            audio: null,
            isPlaying: false,
            volume: 0.5,
            isLooping: false,
            cuePoint: 0,
            originalRate: 1.0,
            baseTime: 0
        };
        this.crossfaderValue = 0.5;
        this.scratchPosition = 0.5;
        this.isScratchTouched = false;
        this.lastScratchPosition = 0.5;
        this.scratchVelocity = 0;
        this.scratchUpdateInterval = null;
        this.lastTimeChange = 0;
        this.lastMouseMove = 0;
        this.ironMode = true; // Режим "как железо" - по умолчанию включен!
        this.vinylSyncInterval = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateStatus('🎛️ SC1000 Web Emulator Ready! Load audio files to start DJing!');
    }

    setupEventListeners() {
        // File inputs
        document.getElementById('beat-files').addEventListener('change', (e) => {
            this.loadFiles(e.target.files, 'beat');
        });
        
        document.getElementById('sample-files').addEventListener('change', (e) => {
            this.loadFiles(e.target.files, 'sample');
        });

        // Beat deck controls
        document.getElementById('beat-play').addEventListener('click', () => {
            this.togglePlay('beat');
        });
        
        document.getElementById('beat-stop').addEventListener('click', () => {
            this.stop('beat');
        });
        
        document.getElementById('beat-prev').addEventListener('click', () => {
            this.previousTrack('beat');
        });
        
        document.getElementById('beat-next').addEventListener('click', () => {
            this.nextTrack('beat');
        });

        // Sample deck controls
        document.getElementById('sample-play').addEventListener('click', () => {
            this.togglePlay('sample');
        });

        document.getElementById('sample-stop').addEventListener('click', () => {
            this.stop('sample');
        });

        document.getElementById('sample-prev').addEventListener('click', () => {
            this.previousTrack('sample');
        });

        document.getElementById('sample-next').addEventListener('click', () => {
            this.nextTrack('sample');
        });

        document.getElementById('sample-cue').addEventListener('click', () => {
            this.setCue();
        });

        document.getElementById('sample-loop').addEventListener('click', () => {
            this.toggleLoop();
        });

        // Volume controls
        document.getElementById('beat-volume').addEventListener('input', (e) => {
            this.setVolume('beat', e.target.value / 100);
        });
        
        document.getElementById('sample-volume').addEventListener('input', (e) => {
            this.setVolume('sample', e.target.value / 100);
        });

        // Vinyl turntable control
        this.setupVinylTurntable();
        
        // Crossfader
        this.setupCrossfader();
        
        // Iron Mode toggle
        document.getElementById('iron-mode-toggle').addEventListener('click', () => {
            this.toggleIronMode();
        });

        // Update displays
        setInterval(() => this.updateDisplays(), 100);
    }

    setupVinylTurntable() {
        const vinylRecord = document.getElementById('vinyl-record');
        const playhead = document.getElementById('playhead');
        let isDragging = false;
        let lastAngle = 0;
        let vinylRotation = 0; // Текущий поворот пластинки

        const getAngleFromCenter = (clientX, clientY) => {
            const rect = vinylRecord.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            const deltaX = clientX - centerX;
            const deltaY = clientY - centerY;

            return Math.atan2(deltaY, deltaX) * (180 / Math.PI);
        };

        const updateVinylPosition = (clientX, clientY) => {
            if (!isDragging || !this.sampleDeck.audio) return;

            const currentAngle = getAngleFromCenter(clientX, clientY);
            let angleDiff = currentAngle - lastAngle;

            // Handle wrap-around
            if (angleDiff > 180) angleDiff -= 360;
            if (angleDiff < -180) angleDiff += 360;

            // Накапливаем поворот пластинки с ПОНИЖЕННОЙ чувствительностью
            vinylRotation += angleDiff * 0.1; // Уменьшили чувствительность в 10 раз

            // КЛЮЧЕВОЕ: Поворот пластинки = позиция в треке
            const duration = this.sampleDeck.audio.duration || 1;
            const rotationsPerTrack = 3; // 3 полных оборота на весь трек
            const trackPosition = (vinylRotation % (360 * rotationsPerTrack)) / (360 * rotationsPerTrack);
            const newTime = Math.max(0, Math.min(duration, trackPosition * duration));

            // Устанавливаем позицию в треке
            try {
                this.sampleDeck.audio.currentTime = newTime;

                // ИСПРАВЛЕНИЕ: Убираем отрицательные playbackRate (не поддерживается)
                // Вместо этого делаем простое управление скоростью
                const rotationSpeed = Math.abs(angleDiff) / 50; // Увеличили чувствительность скорости
                let playbackRate;

                if (angleDiff < 0) {
                    // Против часовой = замедление (имитация реверса)
                    playbackRate = Math.max(0.1, Math.min(1.0, 1.0 - rotationSpeed));
                } else {
                    // По часовой = ускорение
                    playbackRate = Math.max(0.1, Math.min(4.0, 1.0 + rotationSpeed));
                }

                this.sampleDeck.audio.playbackRate = playbackRate;

                // Отключаем preservesPitch для винилового эффекта
                this.sampleDeck.audio.preservesPitch = false;

                // Логируем направление
                const direction = angleDiff < 0 ? '🔄 CCW' : '⏩ CW';
                if (Math.abs(angleDiff) > 5) {
                    console.log(`${direction} rate=${playbackRate.toFixed(2)}`);
                }

            } catch (e) {
                console.warn('Vinyl control error:', e);
            }

            // Обновляем визуальное положение пластинки и playhead
            vinylRecord.style.transform = `rotate(${vinylRotation}deg)`;
            playhead.style.transform = `translate(-50%, -50%) rotate(${trackPosition * 360}deg)`;

            lastAngle = currentAngle;
        };

        // Mouse events
        vinylRecord.addEventListener('mousedown', (e) => {
            if (!this.sampleDeck.audio) return;

            isDragging = true;
            this.isScratchTouched = true;
            lastAngle = getAngleFromCenter(e.clientX, e.clientY);

            // Останавливаем автоматическое вращение
            vinylRecord.style.animation = 'none';
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            updateVinylPosition(e.clientX, e.clientY);
            e.preventDefault();
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                this.isScratchTouched = false;

                // Возвращаем нормальную скорость
                if (this.sampleDeck.audio) {
                    this.sampleDeck.audio.playbackRate = 1.0;
                }

                // Запускаем синхронизацию с треком
                this.startVinylSync();
            }
        });

        // Touch events
        vinylRecord.addEventListener('touchstart', (e) => {
            if (!this.sampleDeck.audio) return;

            isDragging = true;
            this.isScratchTouched = true;
            const touch = e.touches[0];
            lastAngle = getAngleFromCenter(touch.clientX, touch.clientY);

            vinylRecord.style.animation = 'none';
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            const touch = e.touches[0];
            updateVinylPosition(touch.clientX, touch.clientY);
            e.preventDefault();
        });

        document.addEventListener('touchend', () => {
            if (isDragging) {
                isDragging = false;
                this.isScratchTouched = false;

                if (this.sampleDeck.audio) {
                    this.sampleDeck.audio.playbackRate = 1.0;
                }

                this.startVinylSync();
            }
        });

        // Запускаем синхронизацию
        this.startVinylSync();
    }

    startVinylSync() {
        if (this.vinylSyncInterval) {
            clearInterval(this.vinylSyncInterval);
        }

        const vinylRecord = document.getElementById('vinyl-record');
        const playhead = document.getElementById('playhead');

        this.vinylSyncInterval = setInterval(() => {
            if (!this.isScratchTouched && this.sampleDeck.audio && this.sampleDeck.isPlaying) {
                // Синхронизируем пластинку с треком
                const currentTime = this.sampleDeck.audio.currentTime;
                const duration = this.sampleDeck.audio.duration || 1;
                const progress = currentTime / duration;

                // 3 оборота за трек
                const rotation = progress * 360 * 3;
                const playheadRotation = progress * 360;

                vinylRecord.style.transform = `rotate(${rotation}deg)`;
                playhead.style.transform = `translate(-50%, -50%) rotate(${playheadRotation}deg)`;

            } else if (!this.isScratchTouched && !this.sampleDeck.isPlaying) {
                // Медленное декоративное вращение
                vinylRecord.style.animation = 'vinyl-rotate 10s linear infinite';
            }
        }, 50);
    }

    setupCrossfader() {
        const crossfader = document.getElementById('crossfader');
        const fader = crossfader.parentElement;
        let isDragging = false;

        const updateFaderPosition = (e) => {
            const rect = fader.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
            
            crossfader.style.left = percentage + '%';
            this.crossfaderValue = percentage / 100;
            this.updateCrossfaderMix();
        };

        crossfader.addEventListener('mousedown', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            updateFaderPosition(e);
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events
        crossfader.addEventListener('touchstart', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            updateFaderPosition(e.touches[0]);
            e.preventDefault();
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });
    }

    async loadFiles(files, deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        deck.files = Array.from(files);
        deck.currentIndex = 0;

        if (deck.files.length > 0) {
            await this.loadTrack(deckType, 0);
            this.updateStatus(`✅ Loaded ${deck.files.length} files to ${deckType.toUpperCase()} deck - Ready to play!`);

            // Автоматически показываем подсказку для сэмплов
            if (deckType === 'sample') {
                setTimeout(() => {
                    this.updateStatus(`🎵 SAMPLES loaded! Press PLAY then use the jog wheel to scratch!`);
                }, 2000);
            }
        } else {
            this.updateStatus(`❌ No files loaded to ${deckType} deck`);
        }
    }

    async loadTrack(deckType, index) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;

        if (index < 0 || index >= deck.files.length) return;

        // Stop current audio
        if (deck.audio) {
            deck.audio.pause();
            deck.audio = null;
        }

        deck.currentIndex = index;
        const file = deck.files[index];

        try {
            const url = URL.createObjectURL(file);
            deck.audio = new Audio(url);
            deck.audio.volume = deck.volume;
            deck.audio.loop = deckType === 'sample' ? deck.isLooping : false;
            deck.audio.preload = 'auto';

            // Важно: отключаем стандартные контролы браузера
            deck.audio.controls = false;

            // ДИАГНОСТИКА: Проверяем поддержку preservesPitch
            console.log('🔧 Testing preservesPitch support:');
            console.log('preservesPitch:', 'preservesPitch' in deck.audio);
            console.log('mozPreservesPitch:', 'mozPreservesPitch' in deck.audio);
            console.log('webkitPreservesPitch:', 'webkitPreservesPitch' in deck.audio);

            // Пробуем отключить сохранение тона
            try {
                deck.audio.preservesPitch = false;
                console.log('✅ Set preservesPitch = false');
            } catch (e) {
                console.log('❌ preservesPitch failed:', e);
            }

            try {
                deck.audio.mozPreservesPitch = false;
                console.log('✅ Set mozPreservesPitch = false');
            } catch (e) {
                console.log('❌ mozPreservesPitch failed:', e);
            }

            try {
                deck.audio.webkitPreservesPitch = false;
                console.log('✅ Set webkitPreservesPitch = false');
            } catch (e) {
                console.log('❌ webkitPreservesPitch failed:', e);
            }

            // АГРЕССИВНАЯ оптимизация как в железе SC1000
            deck.audio.crossOrigin = 'anonymous';

            // Отключаем все что может тормозить
            deck.audio.muted = false;
            deck.audio.defaultMuted = false;

            // Принудительная загрузка в память
            deck.audio.load();

            // Устанавливаем минимальную задержку
            if (deck.audio.mozAudioChannelType) {
                deck.audio.mozAudioChannelType = 'content';
            }

            // Wait for metadata to load
            await new Promise((resolve, reject) => {
                deck.audio.addEventListener('loadedmetadata', () => {
                    // ПОВТОРНО устанавливаем preservesPitch после загрузки метаданных
                    try {
                        deck.audio.preservesPitch = false;
                        deck.audio.mozPreservesPitch = false;
                        deck.audio.webkitPreservesPitch = false;
                        console.log('🔧 Re-set preservesPitch after metadata load');
                    } catch (e) {
                        console.warn('Could not re-set preservesPitch:', e);
                    }
                    resolve();
                });
                deck.audio.addEventListener('error', reject);
                setTimeout(reject, 5000); // Timeout after 5 seconds
            });

            this.updateTrackDisplay(deckType);
            this.updateStatus(`✅ Loaded: ${file.name}`);

        } catch (error) {
            console.error('Error loading track:', error);
            this.updateStatus('❌ Error loading track: ' + file.name);
        }
    }

    togglePlay(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;

        if (!deck.audio) {
            this.updateStatus(`❌ No track loaded in ${deckType.toUpperCase()} deck! Load files first.`);
            return;
        }

        if (deck.isPlaying) {
            deck.audio.pause();
            deck.isPlaying = false;
            document.getElementById(`${deckType}-play`).textContent = '▶ PLAY';
            this.updateStatus(`⏸ ${deckType.toUpperCase()} paused`);
        } else {
            deck.audio.play().then(() => {
                deck.isPlaying = true;
                document.getElementById(`${deckType}-play`).textContent = '⏸ PAUSE';

                if (deckType === 'sample') {
                    this.updateStatus(`🎵 SAMPLES playing! Now drag the slider to scratch!`);
                    // Сохраняем базовое время для скретчинга
                    deck.baseTime = deck.audio.currentTime;
                } else {
                    this.updateStatus(`🎵 BEATS playing!`);
                }
            }).catch((error) => {
                console.error('Playback failed:', error);
                this.updateStatus(`❌ Playback failed: ${error.message}`);
            });
        }
    }

    stop(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;

        if (!deck.audio) return;

        deck.audio.pause();
        deck.audio.currentTime = 0;
        deck.isPlaying = false;
        document.getElementById(`${deckType}-play`).textContent = '▶ PLAY';
        this.updateStatus(`⏹ ${deckType.toUpperCase()} stopped`);
    }

    previousTrack(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        const newIndex = deck.currentIndex - 1;
        
        if (newIndex >= 0) {
            this.loadTrack(deckType, newIndex);
        }
    }

    nextTrack(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        const newIndex = deck.currentIndex + 1;
        
        if (newIndex < deck.files.length) {
            this.loadTrack(deckType, newIndex);
        }
    }

    setCue() {
        if (this.sampleDeck.audio) {
            this.sampleDeck.cuePoint = this.sampleDeck.audio.currentTime;
            this.updateStatus(`Cue point set at ${this.formatTime(this.sampleDeck.cuePoint)}`);
        }
    }

    toggleLoop() {
        this.sampleDeck.isLooping = !this.sampleDeck.isLooping;
        if (this.sampleDeck.audio) {
            this.sampleDeck.audio.loop = this.sampleDeck.isLooping;
        }
        
        const button = document.getElementById('sample-loop');
        button.style.background = this.sampleDeck.isLooping ? '#00ff00' : '#444';
        button.style.color = this.sampleDeck.isLooping ? '#000' : '#fff';
        
        this.updateStatus(`Loop ${this.sampleDeck.isLooping ? 'enabled' : 'disabled'}`);
    }

    setVolume(deckType, volume) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        deck.volume = volume;
        
        if (deck.audio) {
            deck.audio.volume = volume * (deckType === 'beat' ? 
                (1 - this.crossfaderValue) : this.crossfaderValue);
        }
        
        document.getElementById(`${deckType}-volume-display`).textContent = 
            Math.round(volume * 100) + '%';
    }

    updateCrossfaderMix() {
        if (this.beatDeck.audio) {
            this.beatDeck.audio.volume = this.beatDeck.volume * (1 - this.crossfaderValue);
        }
        
        if (this.sampleDeck.audio) {
            this.sampleDeck.audio.volume = this.sampleDeck.volume * this.crossfaderValue;
        }
    }

    // Функция удалена - теперь используется прямое управление пластинкой

    releaseScratch() {
        // Возвращаем нормальную скорость воспроизведения
        if (this.sampleDeck.audio) {
            try {
                this.sampleDeck.audio.playbackRate = 1.0;

                // Возобновляем воспроизведение если было включено
                if (this.sampleDeck.isPlaying && this.sampleDeck.audio.paused) {
                    this.sampleDeck.audio.play().catch(() => {});
                }
            } catch (e) {
                console.warn('Error resuming normal playback:', e);
            }
        }

        this.clearScratchFeedback();
    }

    toggleIronMode() {
        this.ironMode = !this.ironMode;
        const button = document.getElementById('iron-mode-toggle');

        if (this.ironMode) {
            button.textContent = '🔥 IRON MODE: ON (Vinyl-style pitch changes + Olimex performance)';
            button.style.background = '#ff4400';
            button.style.borderColor = '#ff6600';
            button.style.color = '#fff';

            this.updateStatus('🔥 IRON MODE ENABLED! Vinyl-style pitch changes + maximum performance');

            // Агрессивные оптимизации
            this.optimizeForIronMode();

        } else {
            button.textContent = '🔥 IRON MODE: OFF (Enable for vinyl-style scratching)';
            button.style.background = '#444';
            button.style.borderColor = '#666';
            button.style.color = '#fff';

            this.updateStatus('🎛️ Iron Mode disabled - normal browser mode (pitch preserved)');
        }
    }

    optimizeForIronMode() {
        // Оптимизации для максимальной производительности

        // Отключаем сборщик мусора на время скретчинга (если возможно)
        if (window.gc) {
            window.gc();
        }

        // ВИНИЛОВОГО ЭФФЕКТА: Отключаем сохранение тона для всех деков
        [this.beatDeck, this.sampleDeck].forEach(deck => {
            if (deck.audio) {
                deck.audio.preservesPitch = false;
                deck.audio.mozPreservesPitch = false;
                deck.audio.webkitPreservesPitch = false;

                // Минимальная буферизация
                if (deck.audio.mozSetup) {
                    deck.audio.mozSetup(2, 44100);
                }
            }
        });

        // Предупреждаем пользователя
        setTimeout(() => {
            this.updateStatus('🔥 IRON MODE: Vinyl-style pitch changes enabled! Close other tabs for best performance!');
        }, 2000);
    }



    updateScratchFeedback(scratchIntensity) {
        const scratchContainer = document.querySelector('.scratch-slider-container');
        const scratchHandle = document.getElementById('scratch-handle');
        const indicator = document.getElementById('scratch-indicator');
        const intensity = Math.min(1, Math.abs(scratchIntensity));

        // Изменяем цвет контейнера и ручки в зависимости от направления
        if (scratchIntensity > 0) {
            // Движение вправо (вперед)
            const red = Math.floor(255 * intensity);
            const green = Math.floor(255 * (1 - intensity));
            const color = `rgb(${red}, ${green}, 0)`;

            scratchContainer.style.borderColor = color;
            scratchHandle.style.background = `linear-gradient(180deg, ${color}, #aa6600)`;
            indicator.textContent = `⏩ FORWARD SCRATCH (${(intensity * 100).toFixed(0)}%)`;
            indicator.style.color = color;
        } else {
            // Движение влево (реверс)
            const blue = Math.floor(255 * intensity);
            const green = Math.floor(255 * (1 - intensity));
            const color = `rgb(0, ${green}, ${blue})`;

            scratchContainer.style.borderColor = color;
            scratchHandle.style.background = `linear-gradient(180deg, ${color}, #0066aa)`;
            indicator.textContent = `🔄 REVERSE SCRATCH (${(intensity * 100).toFixed(0)}%)`;
            indicator.style.color = color;
        }

        // Добавляем эффект свечения
        const glowIntensity = 15 + (intensity * 25);
        scratchContainer.style.boxShadow = `inset 0 0 20px rgba(0,0,0,0.7), 0 0 ${glowIntensity}px ${scratchContainer.style.borderColor}`;
    }

    clearScratchFeedback() {
        const scratchContainer = document.querySelector('.scratch-slider-container');
        const scratchHandle = document.getElementById('scratch-handle');
        const indicator = document.getElementById('scratch-indicator');

        // Возвращаем стандартные цвета
        scratchContainer.style.borderColor = '#00ff00';
        scratchContainer.style.boxShadow = 'inset 0 0 20px rgba(0,0,0,0.7), 0 0 15px rgba(0,255,0,0.3)';
        scratchHandle.style.background = 'linear-gradient(180deg, #00ff00, #00aa00)';

        // Показываем разные сообщения в зависимости от состояния
        if (this.sampleDeck.isPlaying) {
            indicator.textContent = 'Drag slider left/right to scratch';
            indicator.style.color = '#00ff00';
        } else {
            indicator.textContent = 'Press PLAY first, then drag slider left/right to scratch';
            indicator.style.color = '#888';
        }
    }

    updateDisplays() {
        this.updateTrackDisplay('beat');
        this.updateTrackDisplay('sample');
    }

    updateTrackDisplay(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;

        if (!deck.audio) return;

        const trackName = deck.files[deck.currentIndex]?.name || 'No track';
        const currentTime = deck.audio.currentTime || 0;
        const duration = deck.audio.duration || 0;
        const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

        document.getElementById(`${deckType}-track-name`).textContent = trackName;
        document.getElementById(`${deckType}-track-time`).textContent =
            `${this.formatTime(currentTime)} / ${this.formatTime(duration)}`;
        document.getElementById(`${deckType}-progress`).style.width = progress + '%';
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '00:00';
        
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    updateStatus(message) {
        document.getElementById('status').textContent = message;
        console.log('SC1000:', message);
    }
}

// Initialize the emulator when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SC1000Emulator();
});
