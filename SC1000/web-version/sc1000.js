class SC1000Emulator {
    constructor() {
        this.audioContext = null;
        this.beatDeck = {
            files: [],
            currentIndex: 0,
            audio: null,
            audioBuffer: null,
            source: null,
            gainNode: null,
            isPlaying: false,
            volume: 0.5
        };
        this.sampleDeck = {
            files: [],
            currentIndex: 0,
            audio: null,
            audioBuffer: null,
            source: null,
            gainNode: null,
            isPlaying: false,
            volume: 0.5,
            isLooping: false,
            cuePoint: 0,
            currentTime: 0,
            startTime: 0,
            pauseTime: 0
        };
        this.crossfaderValue = 0.5;
        this.scratchPosition = 0.5;
        this.isScratchTouched = false;
        this.scratchStartPosition = 0;
        this.scratchStartTime = 0;
        this.animationFrame = null;

        this.init();
    }

    async init() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.error('Web Audio API not supported:', e);
        }
        
        this.setupEventListeners();
        this.updateStatus('🎛️ SC1000 Web Emulator Ready! Load audio files to start DJing!');
    }

    setupEventListeners() {
        // File inputs
        document.getElementById('beat-files').addEventListener('change', (e) => {
            this.loadFiles(e.target.files, 'beat');
        });
        
        document.getElementById('sample-files').addEventListener('change', (e) => {
            this.loadFiles(e.target.files, 'sample');
        });

        // Beat deck controls
        document.getElementById('beat-play').addEventListener('click', () => {
            this.togglePlay('beat');
        });
        
        document.getElementById('beat-stop').addEventListener('click', () => {
            this.stop('beat');
        });
        
        document.getElementById('beat-prev').addEventListener('click', () => {
            this.previousTrack('beat');
        });
        
        document.getElementById('beat-next').addEventListener('click', () => {
            this.nextTrack('beat');
        });

        // Sample deck controls
        document.getElementById('sample-play').addEventListener('click', () => {
            this.togglePlay('sample');
        });

        document.getElementById('sample-stop').addEventListener('click', () => {
            this.stop('sample');
        });

        document.getElementById('sample-prev').addEventListener('click', () => {
            this.previousTrack('sample');
        });

        document.getElementById('sample-next').addEventListener('click', () => {
            this.nextTrack('sample');
        });

        document.getElementById('sample-cue').addEventListener('click', () => {
            this.setCue();
        });

        document.getElementById('sample-loop').addEventListener('click', () => {
            this.toggleLoop();
        });

        // Volume controls
        document.getElementById('beat-volume').addEventListener('input', (e) => {
            this.setVolume('beat', e.target.value / 100);
        });
        
        document.getElementById('sample-volume').addEventListener('input', (e) => {
            this.setVolume('sample', e.target.value / 100);
        });

        // Scratch slider
        this.setupScratchSlider();
        
        // Crossfader
        this.setupCrossfader();
        
        // Update displays
        setInterval(() => this.updateDisplays(), 100);
    }

    setupScratchSlider() {
        const scratchSlider = document.getElementById('scratch-slider');
        const scratchHandle = document.getElementById('scratch-handle');
        let isDragging = false;
        let lastX = 0;

        const updateSliderPosition = (clientX) => {
            const rect = scratchSlider.getBoundingClientRect();
            const x = clientX - rect.left;
            const percentage = Math.max(0, Math.min(1, x / rect.width));

            this.scratchPosition = percentage;

            // Обновляем визуальную позицию ручки
            scratchHandle.style.left = (percentage * 100) + '%';

            // Вычисляем скорость движения
            const currentTime = Date.now();
            const deltaX = clientX - lastX;
            const deltaTime = currentTime - this.lastScratchTime;

            if (deltaTime > 0) {
                this.scratchVelocity = deltaX / deltaTime;
            }

            this.handleScratchMovement(percentage);

            lastX = clientX;
            this.lastScratchTime = currentTime;
        };

        // Mouse events
        scratchSlider.addEventListener('mousedown', (e) => {
            isDragging = true;
            this.isScratchTouched = true;
            lastX = e.clientX;
            this.lastScratchTime = Date.now();
            updateSliderPosition(e.clientX);
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            updateSliderPosition(e.clientX);
            e.preventDefault();
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                this.isScratchTouched = false;
                this.releaseScratch();
            }
        });

        // Touch events for mobile
        scratchSlider.addEventListener('touchstart', (e) => {
            isDragging = true;
            this.isScratchTouched = true;
            lastX = e.touches[0].clientX;
            this.lastScratchTime = Date.now();
            updateSliderPosition(e.touches[0].clientX);
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            updateSliderPosition(e.touches[0].clientX);
            e.preventDefault();
        });

        document.addEventListener('touchend', () => {
            if (isDragging) {
                isDragging = false;
                this.isScratchTouched = false;
                this.releaseScratch();
            }
        });
    }

    setupCrossfader() {
        const crossfader = document.getElementById('crossfader');
        const fader = crossfader.parentElement;
        let isDragging = false;

        const updateFaderPosition = (e) => {
            const rect = fader.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
            
            crossfader.style.left = percentage + '%';
            this.crossfaderValue = percentage / 100;
            this.updateCrossfaderMix();
        };

        crossfader.addEventListener('mousedown', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            updateFaderPosition(e);
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events
        crossfader.addEventListener('touchstart', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            updateFaderPosition(e.touches[0]);
            e.preventDefault();
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });
    }

    async loadFiles(files, deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        deck.files = Array.from(files);
        deck.currentIndex = 0;

        if (deck.files.length > 0) {
            await this.loadTrack(deckType, 0);
            this.updateStatus(`✅ Loaded ${deck.files.length} files to ${deckType.toUpperCase()} deck - Ready to play!`);

            // Автоматически показываем подсказку для сэмплов
            if (deckType === 'sample') {
                setTimeout(() => {
                    this.updateStatus(`🎵 SAMPLES loaded! Press PLAY then use the jog wheel to scratch!`);
                }, 2000);
            }
        } else {
            this.updateStatus(`❌ No files loaded to ${deckType} deck`);
        }
    }

    async loadTrack(deckType, index) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;

        if (index < 0 || index >= deck.files.length) return;

        // Stop current audio
        this.stopDeck(deck);

        deck.currentIndex = index;
        const file = deck.files[index];

        try {
            // Create audio context if needed
            if (!this.audioContext) {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }

            // Resume context if suspended
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            // Load file as ArrayBuffer
            const arrayBuffer = await file.arrayBuffer();

            // Decode audio data
            deck.audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

            // Create gain node for volume control
            deck.gainNode = this.audioContext.createGain();
            deck.gainNode.connect(this.audioContext.destination);
            deck.gainNode.gain.value = deck.volume;

            // Reset timing
            deck.currentTime = 0;
            deck.startTime = 0;
            deck.pauseTime = 0;

            this.updateTrackDisplay(deckType);
            this.updateStatus(`✅ Loaded: ${file.name}`);

        } catch (error) {
            console.error('Error loading track:', error);
            this.updateStatus('❌ Error loading track: ' + file.name);
        }
    }

    stopDeck(deck) {
        if (deck.source) {
            try {
                deck.source.stop();
            } catch (e) {
                // Source might already be stopped
            }
            deck.source.disconnect();
            deck.source = null;
        }
        deck.isPlaying = false;
        deck.currentTime = 0;
        deck.startTime = 0;
        deck.pauseTime = 0;
    }

    async togglePlay(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;

        if (!deck.audioBuffer) {
            this.updateStatus(`❌ No track loaded in ${deckType.toUpperCase()} deck! Load files first.`);
            return;
        }

        if (deck.isPlaying) {
            // Pause
            this.pauseDeck(deck);
            document.getElementById(`${deckType}-play`).textContent = '▶ PLAY';
            this.updateStatus(`⏸ ${deckType.toUpperCase()} paused`);
        } else {
            // Play
            try {
                await this.playDeck(deck);
                document.getElementById(`${deckType}-play`).textContent = '⏸ PAUSE';

                if (deckType === 'sample') {
                    this.updateStatus(`🎵 SAMPLES playing! Now drag the slider to scratch!`);
                } else {
                    this.updateStatus(`🎵 BEATS playing!`);
                }

                // Start position tracking
                this.startPositionTracking();

            } catch (error) {
                console.error('Playback failed:', error);
                this.updateStatus(`❌ Playback failed: ${error.message}`);
            }
        }
    }

    async playDeck(deck) {
        // Resume context if needed
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }

        // Create new source
        deck.source = this.audioContext.createBufferSource();
        deck.source.buffer = deck.audioBuffer;
        deck.source.connect(deck.gainNode);

        // Set loop if needed
        if (deck.isLooping) {
            deck.source.loop = true;
        }

        // Calculate start position
        const offset = deck.pauseTime || 0;
        deck.startTime = this.audioContext.currentTime - offset;

        // Start playback
        deck.source.start(0, offset);
        deck.isPlaying = true;

        // Handle end of playback
        deck.source.onended = () => {
            if (deck.isPlaying) {
                deck.isPlaying = false;
                deck.currentTime = 0;
                deck.pauseTime = 0;
                deck.startTime = 0;
            }
        };
    }

    pauseDeck(deck) {
        if (deck.source && deck.isPlaying) {
            // Calculate current position
            deck.pauseTime = this.audioContext.currentTime - deck.startTime;

            // Stop source
            try {
                deck.source.stop();
            } catch (e) {
                // Already stopped
            }
            deck.source.disconnect();
            deck.source = null;
            deck.isPlaying = false;
        }
    }

    stop(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;

        if (!deck.audioBuffer) return;

        this.stopDeck(deck);
        document.getElementById(`${deckType}-play`).textContent = '▶ PLAY';
        this.updateStatus(`⏹ ${deckType.toUpperCase()} stopped`);
    }

    previousTrack(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        const newIndex = deck.currentIndex - 1;
        
        if (newIndex >= 0) {
            this.loadTrack(deckType, newIndex);
        }
    }

    nextTrack(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        const newIndex = deck.currentIndex + 1;
        
        if (newIndex < deck.files.length) {
            this.loadTrack(deckType, newIndex);
        }
    }

    setCue() {
        if (this.sampleDeck.audio) {
            this.sampleDeck.cuePoint = this.sampleDeck.audio.currentTime;
            this.updateStatus(`Cue point set at ${this.formatTime(this.sampleDeck.cuePoint)}`);
        }
    }

    toggleLoop() {
        this.sampleDeck.isLooping = !this.sampleDeck.isLooping;
        if (this.sampleDeck.audio) {
            this.sampleDeck.audio.loop = this.sampleDeck.isLooping;
        }
        
        const button = document.getElementById('sample-loop');
        button.style.background = this.sampleDeck.isLooping ? '#00ff00' : '#444';
        button.style.color = this.sampleDeck.isLooping ? '#000' : '#fff';
        
        this.updateStatus(`Loop ${this.sampleDeck.isLooping ? 'enabled' : 'disabled'}`);
    }

    setVolume(deckType, volume) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;
        deck.volume = volume;
        
        if (deck.audio) {
            deck.audio.volume = volume * (deckType === 'beat' ? 
                (1 - this.crossfaderValue) : this.crossfaderValue);
        }
        
        document.getElementById(`${deckType}-volume-display`).textContent = 
            Math.round(volume * 100) + '%';
    }

    updateCrossfaderMix() {
        if (this.beatDeck.audio) {
            this.beatDeck.audio.volume = this.beatDeck.volume * (1 - this.crossfaderValue);
        }
        
        if (this.sampleDeck.audio) {
            this.sampleDeck.audio.volume = this.sampleDeck.volume * this.crossfaderValue;
        }
    }

    handleScratchMovement(position) {
        if (!this.isScratchTouched || !this.sampleDeck.audioBuffer) {
            return;
        }

        // Проверяем, что сэмплы загружены
        if (this.sampleDeck.files.length === 0) {
            this.updateStatus(`❌ Load SAMPLE files first to use scratching!`);
            return;
        }

        // Вычисляем отклонение от центра (0.5)
        const deviation = position - 0.5; // -0.5 до +0.5
        const scratchIntensity = deviation * 2; // -1 до +1

        // Если только начали скретчинг, запоминаем начальную позицию
        if (!this.scratchStartTime) {
            this.scratchStartTime = this.audioContext.currentTime;
            this.scratchStartPosition = this.getCurrentSamplePosition();
        }

        // Вычисляем новую позицию на основе движения слайдера
        const scratchDelta = (position - 0.5) * 10; // Чувствительность скретчинга
        const newPosition = this.scratchStartPosition + scratchDelta;

        // Ограничиваем позицию в пределах трека
        const clampedPosition = Math.max(0, Math.min(this.sampleDeck.audioBuffer.duration, newPosition));

        // Если сэмпл играет, останавливаем его и создаем новый источник для скретчинга
        if (this.sampleDeck.isPlaying) {
            this.pauseDeck(this.sampleDeck);
        }

        // Создаем новый источник для скретчинга
        this.playScratchPosition(clampedPosition, scratchIntensity);

        // Обновляем визуальную обратную связь
        this.updateScratchFeedback(scratchIntensity);
    }

    playScratchPosition(position, intensity) {
        // Останавливаем текущий источник если есть
        if (this.sampleDeck.source) {
            try {
                this.sampleDeck.source.stop();
                this.sampleDeck.source.disconnect();
            } catch (e) {
                // Уже остановлен
            }
        }

        // Создаем новый источник
        this.sampleDeck.source = this.audioContext.createBufferSource();
        this.sampleDeck.source.buffer = this.sampleDeck.audioBuffer;
        this.sampleDeck.source.connect(this.sampleDeck.gainNode);

        // Устанавливаем скорость воспроизведения на основе интенсивности
        const playbackRate = 1.0 + (intensity * 2); // От -1x до +3x
        this.sampleDeck.source.playbackRate.value = Math.max(0.1, Math.abs(playbackRate));

        // Воспроизводим короткий фрагмент
        const duration = Math.abs(intensity) > 0.1 ? 0.1 : 0.05; // Короткие фрагменты для скретчинга

        try {
            this.sampleDeck.source.start(0, position, duration);
        } catch (e) {
            console.warn('Error starting scratch playback:', e);
        }
    }

    getCurrentSamplePosition() {
        if (!this.sampleDeck.isPlaying || !this.sampleDeck.startTime) {
            return this.sampleDeck.pauseTime || 0;
        }

        const elapsed = this.audioContext.currentTime - this.sampleDeck.startTime;
        return (this.sampleDeck.pauseTime || 0) + elapsed;
    }

    async releaseScratch() {
        // Возвращаем слайдер в центр
        const scratchHandle = document.getElementById('scratch-handle');
        scratchHandle.style.left = '50%';
        this.scratchPosition = 0.5;

        // Сбрасываем время начала скретчинга
        this.scratchStartTime = 0;
        this.scratchStartPosition = 0;

        // Останавливаем текущий источник скретчинга
        if (this.sampleDeck.source) {
            try {
                this.sampleDeck.source.stop();
                this.sampleDeck.source.disconnect();
            } catch (e) {
                // Уже остановлен
            }
            this.sampleDeck.source = null;
        }

        // Возобновляем нормальное воспроизведение если было включено
        if (this.sampleDeck.isPlaying) {
            try {
                await this.playDeck(this.sampleDeck);
            } catch (e) {
                console.warn('Error resuming playback:', e);
            }
        }

        this.clearScratchFeedback();
    }

    startPositionTracking() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }

        const updatePosition = () => {
            // Обновляем позицию для всех деков
            this.updateDeckPosition(this.beatDeck, 'beat');
            this.updateDeckPosition(this.sampleDeck, 'sample');

            this.animationFrame = requestAnimationFrame(updatePosition);
        };

        updatePosition();
    }

    updateDeckPosition(deck, deckType) {
        if (!deck.audioBuffer) return;

        let currentTime = 0;

        if (deck.isPlaying && deck.startTime) {
            currentTime = (this.audioContext.currentTime - deck.startTime) + (deck.pauseTime || 0);
        } else {
            currentTime = deck.pauseTime || 0;
        }

        // Ограничиваем время в пределах трека
        currentTime = Math.max(0, Math.min(deck.audioBuffer.duration, currentTime));
        deck.currentTime = currentTime;

        // Обновляем отображение
        this.updateTrackDisplay(deckType);
    }

    updateScratchFeedback(scratchIntensity) {
        const scratchContainer = document.querySelector('.scratch-slider-container');
        const scratchHandle = document.getElementById('scratch-handle');
        const indicator = document.getElementById('scratch-indicator');
        const intensity = Math.min(1, Math.abs(scratchIntensity));

        // Изменяем цвет контейнера и ручки в зависимости от направления
        if (scratchIntensity > 0) {
            // Движение вправо (вперед)
            const red = Math.floor(255 * intensity);
            const green = Math.floor(255 * (1 - intensity));
            const color = `rgb(${red}, ${green}, 0)`;

            scratchContainer.style.borderColor = color;
            scratchHandle.style.background = `linear-gradient(180deg, ${color}, #aa6600)`;
            indicator.textContent = `Scratching FORWARD (${(intensity * 100).toFixed(0)}%)`;
            indicator.style.color = color;
        } else {
            // Движение влево (назад)
            const blue = Math.floor(255 * intensity);
            const green = Math.floor(255 * (1 - intensity));
            const color = `rgb(0, ${green}, ${blue})`;

            scratchContainer.style.borderColor = color;
            scratchHandle.style.background = `linear-gradient(180deg, ${color}, #0066aa)`;
            indicator.textContent = `Scratching BACKWARD (${(intensity * 100).toFixed(0)}%)`;
            indicator.style.color = color;
        }

        // Добавляем эффект свечения
        const glowIntensity = 15 + (intensity * 25);
        scratchContainer.style.boxShadow = `inset 0 0 20px rgba(0,0,0,0.7), 0 0 ${glowIntensity}px ${scratchContainer.style.borderColor}`;
    }

    clearScratchFeedback() {
        const scratchContainer = document.querySelector('.scratch-slider-container');
        const scratchHandle = document.getElementById('scratch-handle');
        const indicator = document.getElementById('scratch-indicator');

        // Возвращаем стандартные цвета
        scratchContainer.style.borderColor = '#00ff00';
        scratchContainer.style.boxShadow = 'inset 0 0 20px rgba(0,0,0,0.7), 0 0 15px rgba(0,255,0,0.3)';
        scratchHandle.style.background = 'linear-gradient(180deg, #00ff00, #00aa00)';

        // Показываем разные сообщения в зависимости от состояния
        if (this.sampleDeck.isPlaying) {
            indicator.textContent = 'Drag slider left/right to scratch';
            indicator.style.color = '#00ff00';
        } else {
            indicator.textContent = 'Press PLAY first, then drag slider left/right to scratch';
            indicator.style.color = '#888';
        }
    }

    updateDisplays() {
        this.updateTrackDisplay('beat');
        this.updateTrackDisplay('sample');
    }

    updateTrackDisplay(deckType) {
        const deck = deckType === 'beat' ? this.beatDeck : this.sampleDeck;

        if (!deck.audioBuffer) return;

        const trackName = deck.files[deck.currentIndex]?.name || 'No track';
        const currentTime = deck.currentTime || 0;
        const duration = deck.audioBuffer.duration || 0;
        const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

        document.getElementById(`${deckType}-track-name`).textContent = trackName;
        document.getElementById(`${deckType}-track-time`).textContent =
            `${this.formatTime(currentTime)} / ${this.formatTime(duration)}`;
        document.getElementById(`${deckType}-progress`).style.width = progress + '%';
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '00:00';
        
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    updateStatus(message) {
        document.getElementById('status').textContent = message;
        console.log('SC1000:', message);
    }
}

// Initialize the emulator when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SC1000Emulator();
});
