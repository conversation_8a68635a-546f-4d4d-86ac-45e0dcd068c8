<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SC1000 Web Emulator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            font-family: 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .sc1000-container {
            background: #2a2a2a;
            border: 3px solid #444;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(0,255,0,0.3);
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
            margin: 0;
            font-size: 2.5em;
        }

        .deck {
            background: #333;
            border: 2px solid #555;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .deck-title {
            color: #00ff00;
            font-size: 1.2em;
            margin-bottom: 15px;
            text-align: center;
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .jog-wheel {
            width: 140px;
            height: 140px;
            border: 5px solid #00ff00;
            border-radius: 50%;
            background: radial-gradient(circle, #555 20%, #333 50%, #111 80%);
            margin: 0 auto 20px;
            position: relative;
            cursor: grab;
            box-shadow: inset 0 0 25px rgba(0,0,0,0.7), 0 0 15px rgba(0,255,0,0.3);
            transition: transform 0.1s ease-out, box-shadow 0.2s ease;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
        }

        .jog-wheel:active {
            cursor: grabbing;
            transform: scale(1.02);
        }

        .jog-wheel:hover {
            box-shadow: inset 0 0 25px rgba(0,0,0,0.7), 0 0 20px rgba(0,255,0,0.5);
        }

        .jog-wheel::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 50%;
            width: 6px;
            height: 25px;
            background: linear-gradient(180deg, #00ff00, #00aa00);
            transform: translateX(-50%);
            border-radius: 3px;
            box-shadow: 0 0 8px rgba(0,255,0,0.6);
        }

        .jog-wheel::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #00ff00, #008800);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 10px rgba(0,255,0,0.4);
        }

        .fader {
            width: 100%;
            height: 40px;
            background: #222;
            border: 2px solid #555;
            border-radius: 20px;
            position: relative;
            margin: 10px 0;
        }

        .fader-handle {
            width: 30px;
            height: 36px;
            background: #00ff00;
            border-radius: 15px;
            position: absolute;
            top: 2px;
            cursor: pointer;
            transition: left 0.1s;
            box-shadow: 0 0 10px rgba(0,255,0,0.5);
        }

        .button {
            background: #444;
            border: 2px solid #666;
            color: #fff;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }

        .button:hover {
            background: #555;
            border-color: #00ff00;
        }

        .button:active {
            background: #00ff00;
            color: #000;
        }

        .track-info {
            background: #1a1a1a;
            border: 1px solid #555;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
        }

        .track-name {
            color: #00ff00;
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .track-time {
            color: #888;
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #333;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #ffff00);
            width: 0%;
            transition: width 0.1s;
        }

        .file-input {
            margin: 20px 0;
            text-align: center;
        }

        .file-input input[type="file"] {
            display: none;
        }

        .file-input label {
            background: #444;
            border: 2px solid #666;
            color: #fff;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .file-input label:hover {
            background: #555;
            border-color: #00ff00;
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }

        .volume-slider {
            flex: 1;
            height: 6px;
            background: #333;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: #00ff00;
            border-radius: 50%;
            cursor: pointer;
        }

        .status {
            text-align: center;
            margin-top: 20px;
            padding: 10px;
            background: #1a1a1a;
            border-radius: 5px;
            border: 1px solid #555;
        }
    </style>
</head>
<body>
    <div class="sc1000-container">
        <div class="header">
            <h1>SC1000</h1>
            <div>Web Emulator</div>
        </div>

        <!-- Beat Deck -->
        <div class="deck">
            <div class="deck-title">BEATS</div>
            
            <div class="file-input">
                <label for="beat-files">Load Beat Files</label>
                <input type="file" id="beat-files" multiple accept="audio/*">
            </div>

            <div class="track-info">
                <div class="track-name" id="beat-track-name">No track loaded</div>
                <div class="track-time" id="beat-track-time">00:00 / 00:00</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="beat-progress"></div>
                </div>
            </div>

            <div class="volume-control">
                <span>VOL</span>
                <input type="range" class="volume-slider" id="beat-volume" min="0" max="100" value="50">
                <span id="beat-volume-display">50%</span>
            </div>

            <div class="controls">
                <button class="button" id="beat-prev">◀ PREV</button>
                <button class="button" id="beat-next">NEXT ▶</button>
                <button class="button" id="beat-play">▶ PLAY</button>
                <button class="button" id="beat-stop">⏹ STOP</button>
            </div>
        </div>

        <!-- Sample Deck -->
        <div class="deck">
            <div class="deck-title">SAMPLES</div>
            
            <div class="jog-wheel" id="jog-wheel"></div>
            <div style="text-align: center; margin-top: 10px;">
                <div id="scratch-indicator" style="color: #888; font-size: 0.9em;">Touch & Drag to Scratch</div>
            </div>
            
            <div class="file-input">
                <label for="sample-files">Load Sample Files</label>
                <input type="file" id="sample-files" multiple accept="audio/*">
            </div>

            <div class="track-info">
                <div class="track-name" id="sample-track-name">No track loaded</div>
                <div class="track-time" id="sample-track-time">00:00 / 00:00</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="sample-progress"></div>
                </div>
            </div>

            <div class="volume-control">
                <span>VOL</span>
                <input type="range" class="volume-slider" id="sample-volume" min="0" max="100" value="50">
                <span id="sample-volume-display">50%</span>
            </div>

            <div class="controls">
                <button class="button" id="sample-prev">◀ PREV</button>
                <button class="button" id="sample-next">NEXT ▶</button>
                <button class="button" id="sample-cue">CUE</button>
                <button class="button" id="sample-loop">LOOP</button>
            </div>
        </div>

        <!-- Crossfader -->
        <div class="fader">
            <div class="fader-handle" id="crossfader" style="left: 50%;"></div>
        </div>
        <div style="text-align: center; margin-top: 10px;">CROSSFADER</div>

        <div class="status" id="status">
            Ready - Load some audio files to start
        </div>
    </div>

    <script src="sc1000.js"></script>
</body>
</html>
