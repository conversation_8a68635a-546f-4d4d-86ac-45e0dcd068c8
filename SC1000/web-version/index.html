<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SC1000 Web Emulator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            font-family: 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .sc1000-container {
            background: #2a2a2a;
            border: 3px solid #444;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(0,255,0,0.3);
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
            margin: 0;
            font-size: 2.5em;
        }

        .deck {
            background: #333;
            border: 2px solid #555;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .deck-title {
            color: #00ff00;
            font-size: 1.2em;
            margin-bottom: 15px;
            text-align: center;
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .scratch-slider-container {
            width: 300px;
            height: 60px;
            margin: 0 auto 20px;
            position: relative;
            background: linear-gradient(90deg, #222 0%, #444 50%, #222 100%);
            border: 3px solid #00ff00;
            border-radius: 30px;
            box-shadow: inset 0 0 20px rgba(0,0,0,0.7), 0 0 15px rgba(0,255,0,0.3);
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
        }

        .scratch-slider {
            width: 100%;
            height: 100%;
            position: relative;
            cursor: grab;
            border-radius: 27px;
            overflow: hidden;
        }

        .scratch-slider:active {
            cursor: grabbing;
        }

        .scratch-handle {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40px;
            height: 50px;
            background: linear-gradient(180deg, #00ff00, #00aa00);
            border: 2px solid #fff;
            border-radius: 20px;
            transform: translate(-50%, -50%);
            cursor: grab;
            box-shadow: 0 0 15px rgba(0,255,0,0.6), inset 0 0 10px rgba(255,255,255,0.2);
            transition: all 0.1s ease;
        }

        .scratch-handle:active {
            cursor: grabbing;
            transform: translate(-50%, -50%) scale(1.1);
        }

        .scratch-handle::before {
            content: '⟷';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #000;
            font-size: 16px;
            font-weight: bold;
        }

        .scratch-marks {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .scratch-mark {
            position: absolute;
            top: 20%;
            width: 2px;
            height: 60%;
            background: rgba(0,255,0,0.3);
        }

        .scratch-mark.center {
            left: 50%;
            background: rgba(0,255,0,0.8);
            width: 3px;
        }

        .scratch-mark.quarter {
            background: rgba(0,255,0,0.5);
        }

        /* Vinyl Turntable Styles - Improved Animation */
        .turntable-container {
            width: 300px;
            height: 300px;
            margin: 0 auto 20px;
            position: relative;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
        }

        .vinyl-record {
            width: 300px;
            height: 300px;
            background-color: #080808;
            border-radius: 50%;
            position: relative;
            cursor: grab;
            box-shadow:
                0 20px 50px rgba(0, 0, 0, 0.7),
                inset 0 0 40px rgba(0, 0, 0, 0.9);
            overflow: hidden;
            animation: vinyl-rotate 10s linear infinite;
            border: none;
            background-image: radial-gradient(circle at center, #1a1a1a 0%, #080808 70%);
            will-change: transform;
            transition: transform 0.1s ease;
        }

        .vinyl-record:active {
            cursor: grabbing;
        }

        .vinyl-record.spinning {
            animation: vinyl-spin 1.5s linear infinite;
        }

        @keyframes vinyl-rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes vinyl-spin {
            from {
                transform: rotate(0deg) scale(1.02);
            }
            to {
                transform: rotate(360deg) scale(1.02);
            }
        }

        .vinyl-grooves {
            position: absolute;
            inset: 5%;
            border-radius: 50%;
            background-image: repeating-radial-gradient(circle,
                #101010, #101010 0.5px, #181818 0.5px, #181818 1.5px);
            opacity: 0.8;
            box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.7);
            z-index: 0;
            pointer-events: none;
        }

        .vinyl-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 112px;
            height: 112px;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 0.5;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            pointer-events: none;
        }

        .center-hole {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #080808;
            transform: translate(-50%, -50%);
            z-index: 1;
            border: 2px solid #333;
        }

        .vinyl-label {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: #d32f2f;
            transform: translate(-50%, -50%);
            border: none;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            color: #fbc02d;
            font-weight: normal;
            text-transform: none;
            font-family: serif;
            letter-spacing: 0.5px;
            overflow: hidden;
            transition: background-color 0.2s, color 0.2s;
            z-index: 1;
            pointer-events: none;
        }

        .vinyl-label::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #080808;
            transform: translate(-50%, -50%);
            z-index: 1;
            border: 2px solid #333;
        }

        .label-text {
            position: relative;
            z-index: 0;
            transform: none;
            color: #fbc02d;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 0 0 5px rgba(251, 192, 45, 0.8);
        }

        .label-subtext {
            color: #888;
            font-size: 8px;
            margin-top: 2px;
        }

        .tonearm {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 4px;
            height: 120px;
            background: linear-gradient(to bottom, #666, #333);
            border-radius: 2px;
            transform-origin: top center;
            transform: rotate(25deg);
            box-shadow: 2px 2px 10px rgba(0,0,0,0.5);
            pointer-events: none;
            z-index: 2;
        }

        .tonearm::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: -3px;
            width: 10px;
            height: 10px;
            background: #ff4400;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255,68,0,0.8);
        }

        .scratch-indicator-dot {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 6px;
            background: #00ff00;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 10px rgba(0,255,0,0.8);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: 3;
        }

        .scratch-indicator-dot.active {
            opacity: 1;
        }

        .vinyl-glare {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            overflow: hidden;
            z-index: 2;
            background: none;
            animation: none;
            pointer-events: none;
        }

        .vinyl-glare::before {
            content: '';
            position: absolute;
            width: 150%;
            height: 150%;
            top: -25%;
            left: -25%;
            border-radius: 50%;
            background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.25) 0%, transparent 40%);
            transform-origin: 50% 50%;
            animation: none;
        }

        .fader {
            width: 100%;
            height: 40px;
            background: #222;
            border: 2px solid #555;
            border-radius: 20px;
            position: relative;
            margin: 10px 0;
        }

        .fader-handle {
            width: 30px;
            height: 36px;
            background: #00ff00;
            border-radius: 15px;
            position: absolute;
            top: 2px;
            cursor: pointer;
            transition: left 0.1s;
            box-shadow: 0 0 10px rgba(0,255,0,0.5);
        }

        .button {
            background: #444;
            border: 2px solid #666;
            color: #fff;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }

        .button:hover {
            background: #555;
            border-color: #00ff00;
        }

        .button:active {
            background: #00ff00;
            color: #000;
        }

        .track-info {
            background: #1a1a1a;
            border: 1px solid #555;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
        }

        .track-name {
            color: #00ff00;
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .track-time {
            color: #888;
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #333;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #ffff00);
            width: 0%;
            transition: width 0.1s;
        }

        .file-input {
            margin: 20px 0;
            text-align: center;
        }

        .file-input input[type="file"] {
            display: none;
        }

        .file-input label {
            background: #444;
            border: 2px solid #666;
            color: #fff;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .file-input label:hover {
            background: #555;
            border-color: #00ff00;
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }

        .volume-slider {
            flex: 1;
            height: 6px;
            background: #333;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: #00ff00;
            border-radius: 50%;
            cursor: pointer;
        }

        .status {
            text-align: center;
            margin-top: 20px;
            padding: 10px;
            background: #1a1a1a;
            border-radius: 5px;
            border: 1px solid #555;
        }
    </style>
</head>
<body>
    <div class="sc1000-container">
        <div class="header">
            <h1>SC1000</h1>
            <div>Web Emulator</div>
        </div>

        <!-- Beat Deck -->
        <div class="deck">
            <div class="deck-title">BEATS (Background Rhythm)</div>
            
            <div class="file-input">
                <label for="beat-files">📁 Load Beat Files (Background Music)</label>
                <input type="file" id="beat-files" multiple accept="audio/*">
                <div style="font-size: 0.8em; color: #888; margin-top: 5px;">
                    Load MP3/WAV files for background rhythm
                </div>
            </div>

            <div class="track-info">
                <div class="track-name" id="beat-track-name">No track loaded</div>
                <div class="track-time" id="beat-track-time">00:00 / 00:00</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="beat-progress"></div>
                </div>
            </div>

            <div class="volume-control">
                <span>VOL</span>
                <input type="range" class="volume-slider" id="beat-volume" min="0" max="100" value="50">
                <span id="beat-volume-display">50%</span>
            </div>

            <div class="controls">
                <button class="button" id="beat-prev">◀ PREV</button>
                <button class="button" id="beat-next">NEXT ▶</button>
                <button class="button" id="beat-play">▶ PLAY</button>
                <button class="button" id="beat-stop">⏹ STOP</button>
            </div>
        </div>

        <!-- Sample Deck -->
        <div class="deck">
            <div class="deck-title">SAMPLES (Scratch Material)</div>
            
            <div class="scratch-slider-container">
                <div class="scratch-slider" id="scratch-slider">
                    <div class="scratch-marks">
                        <div class="scratch-mark" style="left: 10%;"></div>
                        <div class="scratch-mark quarter" style="left: 25%;"></div>
                        <div class="scratch-mark center"></div>
                        <div class="scratch-mark quarter" style="left: 75%;"></div>
                        <div class="scratch-mark" style="left: 90%;"></div>
                    </div>
                    <div class="scratch-handle" id="scratch-handle"></div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 10px;">
                <div id="scratch-indicator" style="color: #888; font-size: 0.9em;">Press PLAY first, then drag slider left/right to scratch</div>
                <div style="font-size: 0.8em; color: #666; margin-top: 5px;">← BACKWARD | CENTER | FORWARD →</div>
            </div>
            
            <div class="file-input">
                <label for="sample-files">🎵 Load Sample Files (Scratch Material)</label>
                <input type="file" id="sample-files" multiple accept="audio/*">
                <div style="font-size: 0.8em; color: #888; margin-top: 5px;">
                    Load MP3/WAV files for scratching with the vinyl turntable
                </div>
            </div>

            <div class="track-info">
                <div class="track-name" id="sample-track-name">No track loaded</div>
                <div class="track-time" id="sample-track-time">00:00 / 00:00</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="sample-progress"></div>
                </div>
            </div>

            <div class="volume-control">
                <span>VOL</span>
                <input type="range" class="volume-slider" id="sample-volume" min="0" max="100" value="50">
                <span id="sample-volume-display">50%</span>
            </div>

            <div class="controls">
                <button class="button" id="sample-prev">◀ PREV</button>
                <button class="button" id="sample-next">NEXT ▶</button>
                <button class="button" id="sample-play">▶ PLAY</button>
                <button class="button" id="sample-stop">⏹ STOP</button>
            </div>

            <div class="controls" style="margin-top: 10px;">
                <button class="button" id="sample-cue">CUE</button>
                <button class="button" id="sample-loop">LOOP</button>
            </div>
        </div>

        <!-- Crossfader -->
        <div class="fader">
            <div class="fader-handle" id="crossfader" style="left: 50%;"></div>
        </div>
        <div style="text-align: center; margin-top: 10px;">CROSSFADER</div>

        <div class="status" id="status">
            🎛️ SC1000 Ready! Load BEATS for background music, SAMPLES for scratching
        </div>

        <!-- Iron Mode Toggle -->
        <div style="text-align: center; margin-top: 15px;">
            <button id="iron-mode-toggle" class="button" style="background: #ff4400; border-color: #ff6600; color: #fff;">
                🔥 IRON MODE: ON (Olimex-like performance)
            </button>
        </div>

        <!-- Instructions -->
        <div style="margin-top: 20px; padding: 15px; background: #1a1a1a; border: 1px solid #555; border-radius: 5px;">
            <h3 style="color: #00ff00; margin-top: 0;">📖 Quick Start Guide:</h3>
            <ol style="color: #ccc; line-height: 1.6;">
                <li><strong>Load BEATS:</strong> Click "Load Beat Files" and select background music</li>
                <li><strong>Load SAMPLES:</strong> Click "Load Sample Files" and select scratch material</li>
                <li><strong>Play BEATS:</strong> Press ▶ PLAY in the BEATS section for background</li>
                <li><strong>Play SAMPLES:</strong> Press ▶ PLAY in the SAMPLES section (required for scratching!)</li>
                <li><strong>SCRATCH:</strong> Click and drag the vinyl record while samples are playing!</li>
                <li><strong>Mix:</strong> Use the crossfader to blend between beats and samples</li>
            </ol>
            <div style="color: #888; font-size: 0.9em; margin-top: 10px;">
                💡 <strong>Tip:</strong> Any audio files work - no special naming required!
            </div>
        </div>
    </div>

    <script src="sc1000.js"></script>
</body>
</html>
