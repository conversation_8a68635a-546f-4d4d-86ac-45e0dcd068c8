<!DOCTYPE html>
<html>
<head>
    <title>Pitch Test</title>
</head>
<body>
    <h1>🎵 Pitch Test</h1>
    
    <input type="file" id="fileInput" accept="audio/*">
    <br><br>
    
    <button id="playBtn">Play Normal</button>
    <button id="playFastBtn">Play Fast (preservesPitch=true)</button>
    <button id="playFastNoPitchBtn">Play Fast (preservesPitch=false)</button>
    <button id="stopBtn">Stop</button>
    
    <br><br>
    
    <div id="info"></div>
    
    <script>
        let audio = null;
        
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const url = URL.createObjectURL(file);
                audio = new Audio(url);
                
                audio.addEventListener('loadedmetadata', () => {
                    const info = document.getElementById('info');
                    info.innerHTML = `
                        <h3>Audio Properties:</h3>
                        <p>preservesPitch supported: ${'preservesPitch' in audio}</p>
                        <p>mozPreservesPitch supported: ${'mozPreservesPitch' in audio}</p>
                        <p>webkitPreservesPitch supported: ${'webkitPreservesPitch' in audio}</p>
                        <p>Current preservesPitch: ${audio.preservesPitch}</p>
                        <p>Duration: ${audio.duration.toFixed(2)}s</p>
                    `;
                });
            }
        });
        
        document.getElementById('playBtn').addEventListener('click', () => {
            if (audio) {
                audio.currentTime = 0;
                audio.playbackRate = 1.0;
                audio.preservesPitch = true;
                audio.play();
                console.log('Playing normal: rate=1.0, preservesPitch=true');
            }
        });
        
        document.getElementById('playFastBtn').addEventListener('click', () => {
            if (audio) {
                audio.currentTime = 0;
                audio.playbackRate = 2.0;
                audio.preservesPitch = true;
                audio.play();
                console.log('Playing fast with pitch preservation: rate=2.0, preservesPitch=true');
            }
        });
        
        document.getElementById('playFastNoPitchBtn').addEventListener('click', () => {
            if (audio) {
                audio.currentTime = 0;
                audio.playbackRate = 2.0;
                audio.preservesPitch = false;
                audio.mozPreservesPitch = false;
                audio.webkitPreservesPitch = false;
                audio.play();
                console.log('Playing fast WITHOUT pitch preservation: rate=2.0, preservesPitch=false');
                console.log('Actual preservesPitch:', audio.preservesPitch);
            }
        });
        
        document.getElementById('stopBtn').addEventListener('click', () => {
            if (audio) {
                audio.pause();
                audio.currentTime = 0;
            }
        });
    </script>
</body>
</html>
