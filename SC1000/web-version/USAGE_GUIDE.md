# 🎛️ SC1000 Web Emulator - Полное руководство пользователя

## 🤔 Почему два дека? BEATS vs SAMPLES

### 📻 **BEATS (Биты/Ритмы)**
- **Назначение**: Фоновая музыка, основной ритм
- **Что загружать**: Полные треки, инструментальные биты, лупы
- **Как работает**: Играет постоянно как основа для микса
- **Управление**: Простое воспроизведение, регулировка громкости
- **Примеры файлов**: 
  - `beat_01.mp3` (любое название)
  - `instrumental_loop.wav`
  - `background_music.mp3`

### 🎵 **SAMPLES (Сэмплы)**
- **Назначение**: Материал для скретчинга и эффектов
- **Что загружать**: Короткие сэмплы, вокальные фразы, звуковые эффекты
- **Как работает**: Контролируется джог-колесом для скретчинга
- **Управление**: Джог-колесо, cue точки, loops
- **Примеры файлов**:
  - `vocal_sample.mp3` (любое название)
  - `scratch_sound.wav`
  - `effect_01.mp3`

## 🚀 Пошаговая инструкция

### Шаг 1: Подготовка файлов
```
❌ НЕ НУЖНО специальных названий!
✅ Любые MP3/WAV файлы работают
✅ Можно использовать одни и те же файлы в обоих деках
```

### Шаг 2: Загрузка файлов
1. **BEATS**: Нажмите "📁 Load Beat Files" → выберите фоновую музыку
2. **SAMPLES**: Нажмите "🎵 Load Sample Files" → выберите материал для скретчинга

### Шаг 3: Воспроизведение
1. **Запустите BEATS**: Нажмите ▶ PLAY в секции BEATS
2. **Запустите SAMPLES**: Нажмите ▶ PLAY в секции SAMPLES

### Шаг 4: Скретчинг! 🎧
1. **Убедитесь что SAMPLES играют** (кнопка показывает ⏸ PAUSE)
2. **Нажмите и перетаскивайте горизонтальный слайдер**
3. **Центр слайдера** → нормальное воспроизведение
4. **Влево** → скретчинг назад (синий цвет)
5. **Вправо** → скретчинг вперед (красный/желтый цвет)
6. **Отпустите** → автоматический возврат в центр
7. **Наблюдайте за процентами** интенсивности скретчинга

## 🎚️ Элементы управления

### Кнопки BEATS:
- **▶ PLAY/⏸ PAUSE**: Воспроизведение/пауза
- **⏹ STOP**: Остановка
- **◀ PREV / NEXT ▶**: Переключение треков
- **VOL**: Громкость beats

### Кнопки SAMPLES:
- **▶ PLAY/⏸ PAUSE**: Воспроизведение/пауза (нужно для скретчинга!)
- **◀ PREV / NEXT ▶**: Переключение сэмплов
- **CUE**: Установка cue точки
- **LOOP**: Зацикливание сэмпла
- **VOL**: Громкость samples

### Скретч-слайдер:
- **Клик + перетаскивание влево/вправо**: Скретчинг
- **Центральная позиция**: Нормальное воспроизведение
- **Небольшое отклонение**: Точное позиционирование
- **Большое отклонение**: Скретч эффекты с изменением скорости
- **Отпускание**: Автоматический возврат в центр

### Кроссфейдер:
- **Левая позиция**: Только BEATS
- **Центр**: Микс BEATS + SAMPLES
- **Правая позиция**: Только SAMPLES

## 🔧 Решение проблем

### ❌ "Сэмплы не воспроизводятся"
**Решение:**
1. Убедитесь что файлы загружены (видно название трека)
2. Нажмите ▶ PLAY в секции SAMPLES
3. Проверьте громкость SAMPLES и кроссфейдер

### ❌ "Скретчинг не работает"
**Решение:**
1. **ОБЯЗАТЕЛЬНО**: Сначала нажмите ▶ PLAY для SAMPLES
2. Убедитесь что трек воспроизводится (показывает ⏸ PAUSE)
3. Нажмите и перетаскивайте горизонтальный слайдер
4. Двигайте влево/вправо от центра

### ❌ "Нет звука"
**Решение:**
1. Проверьте громкость браузера
2. Проверьте регуляторы VOL в интерфейсе
3. Проверьте позицию кроссфейдера
4. Нажмите в любое место страницы (активация Web Audio)

### ❌ "Файлы не загружаются"
**Решение:**
1. Используйте MP3 или WAV файлы
2. Проверьте размер файлов (не более 100MB)
3. Попробуйте другие файлы

## 💡 Советы для лучшего скретчинга

### Техника скретчинга:
1. **Baby Scratch**: Медленные движения вперед-назад
2. **Forward Scratch**: Быстрое движение вперед, медленно назад
3. **Transform**: Быстрые короткие движения
4. **Chirp**: Резкие движения с остановками

### Лучшие сэмплы для скретчинга:
- Короткие вокальные фразы (1-5 секунд)
- Звуки "Ahh", "Fresh", "Yeah"
- Музыкальные фразы с четким началом
- Звуковые эффекты

### Настройка микса:
1. Начните с BEATS на 70% громкости
2. SAMPLES на 80% громкости
3. Кроссфейдер в центре
4. Регулируйте по вкусу

## 🎯 Примеры использования

### Сценарий 1: Простой микс
1. Загрузите инструментальный бит в BEATS
2. Загрузите вокальный сэмпл в SAMPLES
3. Запустите оба дека
4. Используйте кроссфейдер для микширования

### Сценарий 2: Скретч сессия
1. Загрузите любую музыку в BEATS (фон)
2. Загрузите короткие сэмплы в SAMPLES
3. Запустите BEATS для фона
4. Скретчите SAMPLES под ритм

### Сценарий 3: Эксперименты
1. Загрузите одинаковые файлы в оба дека
2. Играйте один нормально, второй скретчите
3. Создавайте интересные ритмические паттерны

## 🌐 Совместимость

### Поддерживаемые браузеры:
- ✅ Chrome 66+
- ✅ Firefox 60+
- ✅ Safari 11.1+
- ✅ Edge 79+

### Поддерживаемые форматы:
- ✅ MP3
- ✅ WAV
- ✅ OGG (в некоторых браузерах)
- ✅ M4A (в некоторых браузерах)

### Устройства:
- ✅ Компьютер (мышь)
- ✅ Планшет (touch)
- ✅ Смартфон (touch)

---

**🎉 Готово! Теперь вы знаете как использовать SC1000 Web Emulator!**

*Помните: главное - экспериментировать и получать удовольствие от процесса! 🎧*
