ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2018-12-14T15:21:58',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 6.5','Open CASCADE 6.5'
  ,'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('PCB','PCB','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15),#19);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#13 = DIRECTION('',(0.E+000,0.E+000,1.));
#14 = DIRECTION('',(1.,0.E+000,-0.E+000));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.E+000,0.E+000,-1.64592));
#17 = DIRECTION('',(0.E+000,0.E+000,1.));
#18 = DIRECTION('',(1.,0.E+000,-0.E+000));
#19 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#23)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#20,#21,#22)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#20 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#21 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#22 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#23 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#20,
  'distance_accuracy_value','confusion accuracy');
#24 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#25 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#26),#224);
#26 = MANIFOLD_SOLID_BREP('',#27);
#27 = CLOSED_SHELL('',(#28,#124,#210,#217));
#28 = ADVANCED_FACE('',(#29),#43,.T.);
#29 = FACE_BOUND('',#30,.F.);
#30 = EDGE_LOOP('',(#31,#61,#92,#93));
#31 = ORIENTED_EDGE('',*,*,#32,.T.);
#32 = EDGE_CURVE('',#33,#35,#37,.T.);
#33 = VERTEX_POINT('',#34);
#34 = CARTESIAN_POINT('',(119.99999876,59.99999938,0.E+000));
#35 = VERTEX_POINT('',#36);
#36 = CARTESIAN_POINT('',(119.99999876,59.99999938,1.64592));
#37 = SEAM_CURVE('',#38,(#42,#54),.PCURVE_S1.);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(119.99999876,59.99999938,0.E+000));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.E+000,0.E+000,1.));
#42 = PCURVE('',#43,#48);
#43 = CYLINDRICAL_SURFACE('',#44,59.99999938);
#44 = AXIS2_PLACEMENT_3D('',#45,#46,#47);
#45 = CARTESIAN_POINT('',(59.99999938,59.99999938,0.E+000));
#46 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#47 = DIRECTION('',(1.,0.E+000,-0.E+000));
#48 = DEFINITIONAL_REPRESENTATION('',(#49),#53);
#49 = LINE('',#50,#51);
#50 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#51 = VECTOR('',#52,1.);
#52 = DIRECTION('',(-0.E+000,-1.));
#53 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#54 = PCURVE('',#43,#55);
#55 = DEFINITIONAL_REPRESENTATION('',(#56),#60);
#56 = LINE('',#57,#58);
#57 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#58 = VECTOR('',#59,1.);
#59 = DIRECTION('',(-0.E+000,-1.));
#60 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#61 = ORIENTED_EDGE('',*,*,#62,.T.);
#62 = EDGE_CURVE('',#35,#35,#63,.T.);
#63 = SURFACE_CURVE('',#64,(#69,#76),.PCURVE_S1.);
#64 = CIRCLE('',#65,59.99999938);
#65 = AXIS2_PLACEMENT_3D('',#66,#67,#68);
#66 = CARTESIAN_POINT('',(59.99999938,59.99999938,1.64592));
#67 = DIRECTION('',(0.E+000,0.E+000,1.));
#68 = DIRECTION('',(1.,0.E+000,-0.E+000));
#69 = PCURVE('',#43,#70);
#70 = DEFINITIONAL_REPRESENTATION('',(#71),#75);
#71 = LINE('',#72,#73);
#72 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#73 = VECTOR('',#74,1.);
#74 = DIRECTION('',(-1.,0.E+000));
#75 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#76 = PCURVE('',#77,#82);
#77 = PLANE('',#78);
#78 = AXIS2_PLACEMENT_3D('',#79,#80,#81);
#79 = CARTESIAN_POINT('',(119.99999876,59.99999938,1.64592));
#80 = DIRECTION('',(-0.E+000,0.E+000,-1.));
#81 = DIRECTION('',(-1.,0.E+000,0.E+000));
#82 = DEFINITIONAL_REPRESENTATION('',(#83),#91);
#83 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#84,#85,#86,#87,#88,#89,#90),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#84 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#85 = CARTESIAN_POINT('',(0.E+000,103.92304738026));
#86 = CARTESIAN_POINT('',(89.99999907,51.961523690131));
#87 = CARTESIAN_POINT('',(179.99999814,1.469576143791E-014));
#88 = CARTESIAN_POINT('',(89.99999907,-51.96152369013));
#89 = CARTESIAN_POINT('',(9.237055564881E-014,-103.9230473802));
#90 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#91 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#92 = ORIENTED_EDGE('',*,*,#32,.F.);
#93 = ORIENTED_EDGE('',*,*,#94,.F.);
#94 = EDGE_CURVE('',#33,#33,#95,.T.);
#95 = SURFACE_CURVE('',#96,(#101,#108),.PCURVE_S1.);
#96 = CIRCLE('',#97,59.99999938);
#97 = AXIS2_PLACEMENT_3D('',#98,#99,#100);
#98 = CARTESIAN_POINT('',(59.99999938,59.99999938,0.E+000));
#99 = DIRECTION('',(0.E+000,0.E+000,1.));
#100 = DIRECTION('',(1.,0.E+000,-0.E+000));
#101 = PCURVE('',#43,#102);
#102 = DEFINITIONAL_REPRESENTATION('',(#103),#107);
#103 = LINE('',#104,#105);
#104 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#105 = VECTOR('',#106,1.);
#106 = DIRECTION('',(-1.,0.E+000));
#107 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#108 = PCURVE('',#109,#114);
#109 = PLANE('',#110);
#110 = AXIS2_PLACEMENT_3D('',#111,#112,#113);
#111 = CARTESIAN_POINT('',(119.99999876,59.99999938,0.E+000));
#112 = DIRECTION('',(-0.E+000,0.E+000,-1.));
#113 = DIRECTION('',(-1.,0.E+000,0.E+000));
#114 = DEFINITIONAL_REPRESENTATION('',(#115),#123);
#115 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#116,#117,#118,#119,#120,#121
,#122),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#116 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#117 = CARTESIAN_POINT('',(0.E+000,103.92304738026));
#118 = CARTESIAN_POINT('',(89.99999907,51.961523690131));
#119 = CARTESIAN_POINT('',(179.99999814,1.469576143791E-014));
#120 = CARTESIAN_POINT('',(89.99999907,-51.96152369013));
#121 = CARTESIAN_POINT('',(9.237055564881E-014,-103.9230473802));
#122 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#123 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#124 = ADVANCED_FACE('',(#125),#139,.F.);
#125 = FACE_BOUND('',#126,.T.);
#126 = EDGE_LOOP('',(#127,#157,#183,#184));
#127 = ORIENTED_EDGE('',*,*,#128,.T.);
#128 = EDGE_CURVE('',#129,#131,#133,.T.);
#129 = VERTEX_POINT('',#130);
#130 = CARTESIAN_POINT('',(63.999999,59.99999938,0.E+000));
#131 = VERTEX_POINT('',#132);
#132 = CARTESIAN_POINT('',(63.999999,59.99999938,1.64592));
#133 = SEAM_CURVE('',#134,(#138,#150),.PCURVE_S1.);
#134 = LINE('',#135,#136);
#135 = CARTESIAN_POINT('',(63.999999,59.99999938,0.E+000));
#136 = VECTOR('',#137,1.);
#137 = DIRECTION('',(0.E+000,0.E+000,1.));
#138 = PCURVE('',#139,#144);
#139 = CYLINDRICAL_SURFACE('',#140,3.99999962);
#140 = AXIS2_PLACEMENT_3D('',#141,#142,#143);
#141 = CARTESIAN_POINT('',(59.99999938,59.99999938,0.E+000));
#142 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#143 = DIRECTION('',(1.,0.E+000,-0.E+000));
#144 = DEFINITIONAL_REPRESENTATION('',(#145),#149);
#145 = LINE('',#146,#147);
#146 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#147 = VECTOR('',#148,1.);
#148 = DIRECTION('',(-0.E+000,-1.));
#149 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#150 = PCURVE('',#139,#151);
#151 = DEFINITIONAL_REPRESENTATION('',(#152),#156);
#152 = LINE('',#153,#154);
#153 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#154 = VECTOR('',#155,1.);
#155 = DIRECTION('',(-0.E+000,-1.));
#156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#157 = ORIENTED_EDGE('',*,*,#158,.T.);
#158 = EDGE_CURVE('',#131,#131,#159,.T.);
#159 = SURFACE_CURVE('',#160,(#165,#172),.PCURVE_S1.);
#160 = CIRCLE('',#161,3.99999962);
#161 = AXIS2_PLACEMENT_3D('',#162,#163,#164);
#162 = CARTESIAN_POINT('',(59.99999938,59.99999938,1.64592));
#163 = DIRECTION('',(0.E+000,0.E+000,1.));
#164 = DIRECTION('',(1.,0.E+000,-0.E+000));
#165 = PCURVE('',#139,#166);
#166 = DEFINITIONAL_REPRESENTATION('',(#167),#171);
#167 = LINE('',#168,#169);
#168 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#169 = VECTOR('',#170,1.);
#170 = DIRECTION('',(-1.,0.E+000));
#171 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#172 = PCURVE('',#77,#173);
#173 = DEFINITIONAL_REPRESENTATION('',(#174),#182);
#174 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#175,#176,#177,#178,#179,#180
,#181),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#175 = CARTESIAN_POINT('',(55.99999976,7.105427357601E-015));
#176 = CARTESIAN_POINT('',(55.99999976,6.928202572096));
#177 = CARTESIAN_POINT('',(61.99999919,3.464101286048));
#178 = CARTESIAN_POINT('',(67.99999862,8.085144703846E-015));
#179 = CARTESIAN_POINT('',(61.99999919,-3.464101286048));
#180 = CARTESIAN_POINT('',(55.99999976,-6.928202572096));
#181 = CARTESIAN_POINT('',(55.99999976,7.105427357601E-015));
#182 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#183 = ORIENTED_EDGE('',*,*,#128,.F.);
#184 = ORIENTED_EDGE('',*,*,#185,.F.);
#185 = EDGE_CURVE('',#129,#129,#186,.T.);
#186 = SURFACE_CURVE('',#187,(#192,#199),.PCURVE_S1.);
#187 = CIRCLE('',#188,3.99999962);
#188 = AXIS2_PLACEMENT_3D('',#189,#190,#191);
#189 = CARTESIAN_POINT('',(59.99999938,59.99999938,0.E+000));
#190 = DIRECTION('',(0.E+000,0.E+000,1.));
#191 = DIRECTION('',(1.,0.E+000,-0.E+000));
#192 = PCURVE('',#139,#193);
#193 = DEFINITIONAL_REPRESENTATION('',(#194),#198);
#194 = LINE('',#195,#196);
#195 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#196 = VECTOR('',#197,1.);
#197 = DIRECTION('',(-1.,0.E+000));
#198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#199 = PCURVE('',#109,#200);
#200 = DEFINITIONAL_REPRESENTATION('',(#201),#209);
#201 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#202,#203,#204,#205,#206,#207
,#208),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#202 = CARTESIAN_POINT('',(55.99999976,7.105427357601E-015));
#203 = CARTESIAN_POINT('',(55.99999976,6.928202572096));
#204 = CARTESIAN_POINT('',(61.99999919,3.464101286048));
#205 = CARTESIAN_POINT('',(67.99999862,8.085144703846E-015));
#206 = CARTESIAN_POINT('',(61.99999919,-3.464101286048));
#207 = CARTESIAN_POINT('',(55.99999976,-6.928202572096));
#208 = CARTESIAN_POINT('',(55.99999976,7.105427357601E-015));
#209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#210 = ADVANCED_FACE('',(#211,#214),#109,.T.);
#211 = FACE_BOUND('',#212,.F.);
#212 = EDGE_LOOP('',(#213));
#213 = ORIENTED_EDGE('',*,*,#94,.T.);
#214 = FACE_BOUND('',#215,.T.);
#215 = EDGE_LOOP('',(#216));
#216 = ORIENTED_EDGE('',*,*,#185,.T.);
#217 = ADVANCED_FACE('',(#218,#221),#77,.F.);
#218 = FACE_BOUND('',#219,.T.);
#219 = EDGE_LOOP('',(#220));
#220 = ORIENTED_EDGE('',*,*,#62,.T.);
#221 = FACE_BOUND('',#222,.F.);
#222 = EDGE_LOOP('',(#223));
#223 = ORIENTED_EDGE('',*,*,#158,.T.);
#224 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#228)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#225,#226,#227)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#225 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#226 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#227 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#228 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#225,
  'distance_accuracy_value','confusion accuracy');
#229 = SHAPE_DEFINITION_REPRESENTATION(#230,#25);
#230 = PRODUCT_DEFINITION_SHAPE('','',#231);
#231 = PRODUCT_DEFINITION('design','',#232,#235);
#232 = PRODUCT_DEFINITION_FORMATION('','',#233);
#233 = PRODUCT('Board','Board','',(#234));
#234 = PRODUCT_CONTEXT('',#2,'mechanical');
#235 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#236 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#237,#239);
#237 = ( REPRESENTATION_RELATIONSHIP('','',#25,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#238) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#238 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#239 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#240
  );
#240 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('145','=>[0:1:1:2]','',#5,#231,$);
#241 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#233));
#242 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#243)
  ,#224);
#243 = STYLED_ITEM('color',(#244),#26);
#244 = PRESENTATION_STYLE_ASSIGNMENT((#245,#251));
#245 = SURFACE_STYLE_USAGE(.BOTH.,#246);
#246 = SURFACE_SIDE_STYLE('',(#247));
#247 = SURFACE_STYLE_FILL_AREA(#248);
#248 = FILL_AREA_STYLE('',(#249));
#249 = FILL_AREA_STYLE_COLOUR('',#250);
#250 = COLOUR_RGB('',0.E+000,0.501960813999,0.E+000);
#251 = CURVE_STYLE('',#252,POSITIVE_LENGTH_MEASURE(0.1),#250);
#252 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
