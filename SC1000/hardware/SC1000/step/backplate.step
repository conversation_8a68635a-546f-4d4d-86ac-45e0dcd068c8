ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2018-12-14T15:17:29',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 6.5','Open CASCADE 6.5'
  ,'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('PCB','PCB','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15),#19);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#13 = DIRECTION('',(0.E+000,0.E+000,1.));
#14 = DIRECTION('',(1.,0.E+000,-0.E+000));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.E+000,0.E+000,-1.12192054));
#17 = DIRECTION('',(0.E+000,0.E+000,1.));
#18 = DIRECTION('',(1.,0.E+000,-0.E+000));
#19 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#23)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#20,#21,#22)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#20 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#21 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#22 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#23 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#20,
  'distance_accuracy_value','confusion accuracy');
#24 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#25 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#26),#6988);
#26 = MANIFOLD_SOLID_BREP('',#27);
#27 = CLOSED_SHELL('',(#28,#148,#224,#295,#342,#420,#530,#606,#682,#758,
    #834,#910,#986,#1062,#1138,#1214,#1290,#1366,#1442,#1518,#1594,#1670
    ,#1746,#1822,#1898,#1974,#2050,#2126,#2202,#2278,#2354,#2430,#2506,
    #2582,#2658,#2734,#2810,#2886,#2962,#3038,#3109,#3156,#3266,#3342,
    #3418,#3494,#3570,#3646,#3722,#3798,#3874,#3950,#4026,#4102,#4178,
    #4254,#4330,#4406,#4482,#4558,#4634,#4710,#4786,#4862,#4938,#5014,
    #5090,#5166,#5242,#5318,#5394,#5470,#5546,#5622,#5698,#5774,#5845,
    #5892,#5970,#6048,#6126,#6204,#6282,#6360,#6438,#6516,#6594,#6672,
    #6750,#6869));
#28 = ADVANCED_FACE('',(#29),#43,.T.);
#29 = FACE_BOUND('',#30,.T.);
#30 = EDGE_LOOP('',(#31,#66,#94,#122));
#31 = ORIENTED_EDGE('',*,*,#32,.T.);
#32 = EDGE_CURVE('',#33,#35,#37,.T.);
#33 = VERTEX_POINT('',#34);
#34 = CARTESIAN_POINT('',(0.E+000,-11.59999966,0.E+000));
#35 = VERTEX_POINT('',#36);
#36 = CARTESIAN_POINT('',(0.E+000,-11.59999966,1.12192054));
#37 = SURFACE_CURVE('',#38,(#42,#54),.PCURVE_S1.);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(0.E+000,-11.59999966,0.E+000));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.E+000,0.E+000,1.));
#42 = PCURVE('',#43,#48);
#43 = PLANE('',#44);
#44 = AXIS2_PLACEMENT_3D('',#45,#46,#47);
#45 = CARTESIAN_POINT('',(0.E+000,-11.59999966,0.E+000));
#46 = DIRECTION('',(-1.,0.E+000,0.E+000));
#47 = DIRECTION('',(0.E+000,1.,0.E+000));
#48 = DEFINITIONAL_REPRESENTATION('',(#49),#53);
#49 = LINE('',#50,#51);
#50 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#51 = VECTOR('',#52,1.);
#52 = DIRECTION('',(0.E+000,-1.));
#53 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#54 = PCURVE('',#55,#60);
#55 = PLANE('',#56);
#56 = AXIS2_PLACEMENT_3D('',#57,#58,#59);
#57 = CARTESIAN_POINT('',(129.99999908,-11.59999966,0.E+000));
#58 = DIRECTION('',(0.E+000,-1.,0.E+000));
#59 = DIRECTION('',(-1.,0.E+000,0.E+000));
#60 = DEFINITIONAL_REPRESENTATION('',(#61),#65);
#61 = LINE('',#62,#63);
#62 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#63 = VECTOR('',#64,1.);
#64 = DIRECTION('',(0.E+000,-1.));
#65 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#66 = ORIENTED_EDGE('',*,*,#67,.T.);
#67 = EDGE_CURVE('',#35,#68,#70,.T.);
#68 = VERTEX_POINT('',#69);
#69 = CARTESIAN_POINT('',(0.E+000,11.59999966,1.12192054));
#70 = SURFACE_CURVE('',#71,(#75,#82),.PCURVE_S1.);
#71 = LINE('',#72,#73);
#72 = CARTESIAN_POINT('',(0.E+000,-11.59999966,1.12192054));
#73 = VECTOR('',#74,1.);
#74 = DIRECTION('',(0.E+000,1.,0.E+000));
#75 = PCURVE('',#43,#76);
#76 = DEFINITIONAL_REPRESENTATION('',(#77),#81);
#77 = LINE('',#78,#79);
#78 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#79 = VECTOR('',#80,1.);
#80 = DIRECTION('',(1.,0.E+000));
#81 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#82 = PCURVE('',#83,#88);
#83 = PLANE('',#84);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(0.E+000,-11.59999966,1.12192054));
#86 = DIRECTION('',(0.E+000,0.E+000,1.));
#87 = DIRECTION('',(1.,0.E+000,-0.E+000));
#88 = DEFINITIONAL_REPRESENTATION('',(#89),#93);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(0.E+000,1.));
#93 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#94 = ORIENTED_EDGE('',*,*,#95,.F.);
#95 = EDGE_CURVE('',#96,#68,#98,.T.);
#96 = VERTEX_POINT('',#97);
#97 = CARTESIAN_POINT('',(0.E+000,11.59999966,0.E+000));
#98 = SURFACE_CURVE('',#99,(#103,#110),.PCURVE_S1.);
#99 = LINE('',#100,#101);
#100 = CARTESIAN_POINT('',(0.E+000,11.59999966,0.E+000));
#101 = VECTOR('',#102,1.);
#102 = DIRECTION('',(0.E+000,0.E+000,1.));
#103 = PCURVE('',#43,#104);
#104 = DEFINITIONAL_REPRESENTATION('',(#105),#109);
#105 = LINE('',#106,#107);
#106 = CARTESIAN_POINT('',(23.19999932,0.E+000));
#107 = VECTOR('',#108,1.);
#108 = DIRECTION('',(0.E+000,-1.));
#109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#110 = PCURVE('',#111,#116);
#111 = PLANE('',#112);
#112 = AXIS2_PLACEMENT_3D('',#113,#114,#115);
#113 = CARTESIAN_POINT('',(0.E+000,11.59999966,0.E+000));
#114 = DIRECTION('',(0.E+000,1.,0.E+000));
#115 = DIRECTION('',(1.,0.E+000,0.E+000));
#116 = DEFINITIONAL_REPRESENTATION('',(#117),#121);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(0.E+000,-1.));
#121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#122 = ORIENTED_EDGE('',*,*,#123,.F.);
#123 = EDGE_CURVE('',#33,#96,#124,.T.);
#124 = SURFACE_CURVE('',#125,(#129,#136),.PCURVE_S1.);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(0.E+000,-11.59999966,0.E+000));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(0.E+000,1.,0.E+000));
#129 = PCURVE('',#43,#130);
#130 = DEFINITIONAL_REPRESENTATION('',(#131),#135);
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(1.,0.E+000));
#135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#136 = PCURVE('',#137,#142);
#137 = PLANE('',#138);
#138 = AXIS2_PLACEMENT_3D('',#139,#140,#141);
#139 = CARTESIAN_POINT('',(0.E+000,-11.59999966,0.E+000));
#140 = DIRECTION('',(0.E+000,0.E+000,1.));
#141 = DIRECTION('',(1.,0.E+000,-0.E+000));
#142 = DEFINITIONAL_REPRESENTATION('',(#143),#147);
#143 = LINE('',#144,#145);
#144 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#145 = VECTOR('',#146,1.);
#146 = DIRECTION('',(0.E+000,1.));
#147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#148 = ADVANCED_FACE('',(#149),#111,.T.);
#149 = FACE_BOUND('',#150,.T.);
#150 = EDGE_LOOP('',(#151,#152,#175,#203));
#151 = ORIENTED_EDGE('',*,*,#95,.T.);
#152 = ORIENTED_EDGE('',*,*,#153,.T.);
#153 = EDGE_CURVE('',#68,#154,#156,.T.);
#154 = VERTEX_POINT('',#155);
#155 = CARTESIAN_POINT('',(129.99999908,11.59999966,1.12192054));
#156 = SURFACE_CURVE('',#157,(#161,#168),.PCURVE_S1.);
#157 = LINE('',#158,#159);
#158 = CARTESIAN_POINT('',(0.E+000,11.59999966,1.12192054));
#159 = VECTOR('',#160,1.);
#160 = DIRECTION('',(1.,0.E+000,0.E+000));
#161 = PCURVE('',#111,#162);
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(1.,0.E+000));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = PCURVE('',#83,#169);
#169 = DEFINITIONAL_REPRESENTATION('',(#170),#174);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(0.E+000,23.19999932));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(1.,0.E+000));
#174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#175 = ORIENTED_EDGE('',*,*,#176,.F.);
#176 = EDGE_CURVE('',#177,#154,#179,.T.);
#177 = VERTEX_POINT('',#178);
#178 = CARTESIAN_POINT('',(129.99999908,11.59999966,0.E+000));
#179 = SURFACE_CURVE('',#180,(#184,#191),.PCURVE_S1.);
#180 = LINE('',#181,#182);
#181 = CARTESIAN_POINT('',(129.99999908,11.59999966,0.E+000));
#182 = VECTOR('',#183,1.);
#183 = DIRECTION('',(0.E+000,0.E+000,1.));
#184 = PCURVE('',#111,#185);
#185 = DEFINITIONAL_REPRESENTATION('',(#186),#190);
#186 = LINE('',#187,#188);
#187 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#188 = VECTOR('',#189,1.);
#189 = DIRECTION('',(0.E+000,-1.));
#190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#191 = PCURVE('',#192,#197);
#192 = PLANE('',#193);
#193 = AXIS2_PLACEMENT_3D('',#194,#195,#196);
#194 = CARTESIAN_POINT('',(129.99999908,11.59999966,0.E+000));
#195 = DIRECTION('',(1.,0.E+000,-0.E+000));
#196 = DIRECTION('',(0.E+000,-1.,0.E+000));
#197 = DEFINITIONAL_REPRESENTATION('',(#198),#202);
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(0.E+000,-1.));
#202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#203 = ORIENTED_EDGE('',*,*,#204,.F.);
#204 = EDGE_CURVE('',#96,#177,#205,.T.);
#205 = SURFACE_CURVE('',#206,(#210,#217),.PCURVE_S1.);
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(0.E+000,11.59999966,0.E+000));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(1.,0.E+000,0.E+000));
#210 = PCURVE('',#111,#211);
#211 = DEFINITIONAL_REPRESENTATION('',(#212),#216);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(1.,0.E+000));
#216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#217 = PCURVE('',#137,#218);
#218 = DEFINITIONAL_REPRESENTATION('',(#219),#223);
#219 = LINE('',#220,#221);
#220 = CARTESIAN_POINT('',(0.E+000,23.19999932));
#221 = VECTOR('',#222,1.);
#222 = DIRECTION('',(1.,0.E+000));
#223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#224 = ADVANCED_FACE('',(#225),#192,.T.);
#225 = FACE_BOUND('',#226,.T.);
#226 = EDGE_LOOP('',(#227,#228,#251,#274));
#227 = ORIENTED_EDGE('',*,*,#176,.T.);
#228 = ORIENTED_EDGE('',*,*,#229,.T.);
#229 = EDGE_CURVE('',#154,#230,#232,.T.);
#230 = VERTEX_POINT('',#231);
#231 = CARTESIAN_POINT('',(129.99999908,-11.59999966,1.12192054));
#232 = SURFACE_CURVE('',#233,(#237,#244),.PCURVE_S1.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(129.99999908,11.59999966,1.12192054));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(0.E+000,-1.,0.E+000));
#237 = PCURVE('',#192,#238);
#238 = DEFINITIONAL_REPRESENTATION('',(#239),#243);
#239 = LINE('',#240,#241);
#240 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#241 = VECTOR('',#242,1.);
#242 = DIRECTION('',(1.,0.E+000));
#243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#244 = PCURVE('',#83,#245);
#245 = DEFINITIONAL_REPRESENTATION('',(#246),#250);
#246 = LINE('',#247,#248);
#247 = CARTESIAN_POINT('',(129.99999908,23.19999932));
#248 = VECTOR('',#249,1.);
#249 = DIRECTION('',(0.E+000,-1.));
#250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#251 = ORIENTED_EDGE('',*,*,#252,.F.);
#252 = EDGE_CURVE('',#253,#230,#255,.T.);
#253 = VERTEX_POINT('',#254);
#254 = CARTESIAN_POINT('',(129.99999908,-11.59999966,0.E+000));
#255 = SURFACE_CURVE('',#256,(#260,#267),.PCURVE_S1.);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(129.99999908,-11.59999966,0.E+000));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(0.E+000,0.E+000,1.));
#260 = PCURVE('',#192,#261);
#261 = DEFINITIONAL_REPRESENTATION('',(#262),#266);
#262 = LINE('',#263,#264);
#263 = CARTESIAN_POINT('',(23.19999932,0.E+000));
#264 = VECTOR('',#265,1.);
#265 = DIRECTION('',(0.E+000,-1.));
#266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#267 = PCURVE('',#55,#268);
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = LINE('',#270,#271);
#270 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#271 = VECTOR('',#272,1.);
#272 = DIRECTION('',(0.E+000,-1.));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = ORIENTED_EDGE('',*,*,#275,.F.);
#275 = EDGE_CURVE('',#177,#253,#276,.T.);
#276 = SURFACE_CURVE('',#277,(#281,#288),.PCURVE_S1.);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(129.99999908,11.59999966,0.E+000));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(0.E+000,-1.,0.E+000));
#281 = PCURVE('',#192,#282);
#282 = DEFINITIONAL_REPRESENTATION('',(#283),#287);
#283 = LINE('',#284,#285);
#284 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#285 = VECTOR('',#286,1.);
#286 = DIRECTION('',(1.,0.E+000));
#287 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#288 = PCURVE('',#137,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#294);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(129.99999908,23.19999932));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(0.E+000,-1.));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = ADVANCED_FACE('',(#296),#55,.T.);
#296 = FACE_BOUND('',#297,.T.);
#297 = EDGE_LOOP('',(#298,#299,#320,#321));
#298 = ORIENTED_EDGE('',*,*,#252,.T.);
#299 = ORIENTED_EDGE('',*,*,#300,.T.);
#300 = EDGE_CURVE('',#230,#35,#301,.T.);
#301 = SURFACE_CURVE('',#302,(#306,#313),.PCURVE_S1.);
#302 = LINE('',#303,#304);
#303 = CARTESIAN_POINT('',(129.99999908,-11.59999966,1.12192054));
#304 = VECTOR('',#305,1.);
#305 = DIRECTION('',(-1.,0.E+000,0.E+000));
#306 = PCURVE('',#55,#307);
#307 = DEFINITIONAL_REPRESENTATION('',(#308),#312);
#308 = LINE('',#309,#310);
#309 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#310 = VECTOR('',#311,1.);
#311 = DIRECTION('',(1.,0.E+000));
#312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#313 = PCURVE('',#83,#314);
#314 = DEFINITIONAL_REPRESENTATION('',(#315),#319);
#315 = LINE('',#316,#317);
#316 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#317 = VECTOR('',#318,1.);
#318 = DIRECTION('',(-1.,0.E+000));
#319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#320 = ORIENTED_EDGE('',*,*,#32,.F.);
#321 = ORIENTED_EDGE('',*,*,#322,.F.);
#322 = EDGE_CURVE('',#253,#33,#323,.T.);
#323 = SURFACE_CURVE('',#324,(#328,#335),.PCURVE_S1.);
#324 = LINE('',#325,#326);
#325 = CARTESIAN_POINT('',(129.99999908,-11.59999966,0.E+000));
#326 = VECTOR('',#327,1.);
#327 = DIRECTION('',(-1.,0.E+000,0.E+000));
#328 = PCURVE('',#55,#329);
#329 = DEFINITIONAL_REPRESENTATION('',(#330),#334);
#330 = LINE('',#331,#332);
#331 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#332 = VECTOR('',#333,1.);
#333 = DIRECTION('',(1.,0.E+000));
#334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#335 = PCURVE('',#137,#336);
#336 = DEFINITIONAL_REPRESENTATION('',(#337),#341);
#337 = LINE('',#338,#339);
#338 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#339 = VECTOR('',#340,1.);
#340 = DIRECTION('',(-1.,0.E+000));
#341 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#342 = ADVANCED_FACE('',(#343),#357,.T.);
#343 = FACE_BOUND('',#344,.F.);
#344 = EDGE_LOOP('',(#345,#375,#397,#398));
#345 = ORIENTED_EDGE('',*,*,#346,.T.);
#346 = EDGE_CURVE('',#347,#349,#351,.T.);
#347 = VERTEX_POINT('',#348);
#348 = CARTESIAN_POINT('',(8.19998614,-6.6000122,0.E+000));
#349 = VERTEX_POINT('',#350);
#350 = CARTESIAN_POINT('',(8.19998614,-6.6000122,1.12192054));
#351 = SEAM_CURVE('',#352,(#356,#368),.PCURVE_S1.);
#352 = LINE('',#353,#354);
#353 = CARTESIAN_POINT('',(8.19998614,-6.6000122,0.E+000));
#354 = VECTOR('',#355,1.);
#355 = DIRECTION('',(0.E+000,0.E+000,1.));
#356 = PCURVE('',#357,#362);
#357 = CYLINDRICAL_SURFACE('',#358,1.59999934);
#358 = AXIS2_PLACEMENT_3D('',#359,#360,#361);
#359 = CARTESIAN_POINT('',(6.5999868,-6.6000122,0.E+000));
#360 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#361 = DIRECTION('',(1.,0.E+000,-0.E+000));
#362 = DEFINITIONAL_REPRESENTATION('',(#363),#367);
#363 = LINE('',#364,#365);
#364 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#365 = VECTOR('',#366,1.);
#366 = DIRECTION('',(-0.E+000,-1.));
#367 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#368 = PCURVE('',#357,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(-0.E+000,-1.));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = ORIENTED_EDGE('',*,*,#376,.T.);
#376 = EDGE_CURVE('',#349,#349,#377,.T.);
#377 = SURFACE_CURVE('',#378,(#383,#390),.PCURVE_S1.);
#378 = CIRCLE('',#379,1.59999934);
#379 = AXIS2_PLACEMENT_3D('',#380,#381,#382);
#380 = CARTESIAN_POINT('',(6.5999868,-6.6000122,1.12192054));
#381 = DIRECTION('',(0.E+000,0.E+000,1.));
#382 = DIRECTION('',(1.,0.E+000,-0.E+000));
#383 = PCURVE('',#357,#384);
#384 = DEFINITIONAL_REPRESENTATION('',(#385),#389);
#385 = LINE('',#386,#387);
#386 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#387 = VECTOR('',#388,1.);
#388 = DIRECTION('',(-1.,0.E+000));
#389 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#390 = PCURVE('',#83,#391);
#391 = DEFINITIONAL_REPRESENTATION('',(#392),#396);
#392 = CIRCLE('',#393,1.59999934);
#393 = AXIS2_PLACEMENT_2D('',#394,#395);
#394 = CARTESIAN_POINT('',(6.5999868,4.99998746));
#395 = DIRECTION('',(1.,0.E+000));
#396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#397 = ORIENTED_EDGE('',*,*,#346,.F.);
#398 = ORIENTED_EDGE('',*,*,#399,.F.);
#399 = EDGE_CURVE('',#347,#347,#400,.T.);
#400 = SURFACE_CURVE('',#401,(#406,#413),.PCURVE_S1.);
#401 = CIRCLE('',#402,1.59999934);
#402 = AXIS2_PLACEMENT_3D('',#403,#404,#405);
#403 = CARTESIAN_POINT('',(6.5999868,-6.6000122,0.E+000));
#404 = DIRECTION('',(0.E+000,0.E+000,1.));
#405 = DIRECTION('',(1.,0.E+000,-0.E+000));
#406 = PCURVE('',#357,#407);
#407 = DEFINITIONAL_REPRESENTATION('',(#408),#412);
#408 = LINE('',#409,#410);
#409 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#410 = VECTOR('',#411,1.);
#411 = DIRECTION('',(-1.,0.E+000));
#412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#413 = PCURVE('',#137,#414);
#414 = DEFINITIONAL_REPRESENTATION('',(#415),#419);
#415 = CIRCLE('',#416,1.59999934);
#416 = AXIS2_PLACEMENT_2D('',#417,#418);
#417 = CARTESIAN_POINT('',(6.5999868,4.99998746));
#418 = DIRECTION('',(1.,0.E+000));
#419 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#420 = ADVANCED_FACE('',(#421),#435,.F.);
#421 = FACE_BOUND('',#422,.F.);
#422 = EDGE_LOOP('',(#423,#458,#481,#509));
#423 = ORIENTED_EDGE('',*,*,#424,.T.);
#424 = EDGE_CURVE('',#425,#427,#429,.T.);
#425 = VERTEX_POINT('',#426);
#426 = CARTESIAN_POINT('',(18.9999874,-2.59033518,0.E+000));
#427 = VERTEX_POINT('',#428);
#428 = CARTESIAN_POINT('',(18.9999874,-2.59033518,1.12192054));
#429 = SURFACE_CURVE('',#430,(#434,#446),.PCURVE_S1.);
#430 = LINE('',#431,#432);
#431 = CARTESIAN_POINT('',(18.9999874,-2.59033518,0.E+000));
#432 = VECTOR('',#433,1.);
#433 = DIRECTION('',(0.E+000,0.E+000,1.));
#434 = PCURVE('',#435,#440);
#435 = PLANE('',#436);
#436 = AXIS2_PLACEMENT_3D('',#437,#438,#439);
#437 = CARTESIAN_POINT('',(18.9999874,-2.59033518,0.E+000));
#438 = DIRECTION('',(9.801754873774E-002,-0.995184686447,0.E+000));
#439 = DIRECTION('',(-0.995184686447,-9.801754873774E-002,0.E+000));
#440 = DEFINITIONAL_REPRESENTATION('',(#441),#445);
#441 = LINE('',#442,#443);
#442 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#443 = VECTOR('',#444,1.);
#444 = DIRECTION('',(0.E+000,-1.));
#445 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#446 = PCURVE('',#447,#452);
#447 = PLANE('',#448);
#448 = AXIS2_PLACEMENT_3D('',#449,#450,#451);
#449 = CARTESIAN_POINT('',(24.00000026,-2.60001258,0.E+000));
#450 = DIRECTION('',(-1.935471396762E-003,-0.999998126973,0.E+000));
#451 = DIRECTION('',(-0.999998126973,1.935471396762E-003,0.E+000));
#452 = DEFINITIONAL_REPRESENTATION('',(#453),#457);
#453 = LINE('',#454,#455);
#454 = CARTESIAN_POINT('',(5.000022225174,0.E+000));
#455 = VECTOR('',#456,1.);
#456 = DIRECTION('',(0.E+000,-1.));
#457 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#458 = ORIENTED_EDGE('',*,*,#459,.T.);
#459 = EDGE_CURVE('',#427,#460,#462,.T.);
#460 = VERTEX_POINT('',#461);
#461 = CARTESIAN_POINT('',(18.60791808,-2.6289508,1.12192054));
#462 = SURFACE_CURVE('',#463,(#467,#474),.PCURVE_S1.);
#463 = LINE('',#464,#465);
#464 = CARTESIAN_POINT('',(18.9999874,-2.59033518,1.12192054));
#465 = VECTOR('',#466,1.);
#466 = DIRECTION('',(-0.995184686447,-9.801754873774E-002,0.E+000));
#467 = PCURVE('',#435,#468);
#468 = DEFINITIONAL_REPRESENTATION('',(#469),#473);
#469 = LINE('',#470,#471);
#470 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#471 = VECTOR('',#472,1.);
#472 = DIRECTION('',(1.,0.E+000));
#473 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#474 = PCURVE('',#83,#475);
#475 = DEFINITIONAL_REPRESENTATION('',(#476),#480);
#476 = LINE('',#477,#478);
#477 = CARTESIAN_POINT('',(18.9999874,9.00966448));
#478 = VECTOR('',#479,1.);
#479 = DIRECTION('',(-0.995184686447,-9.801754873774E-002));
#480 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#481 = ORIENTED_EDGE('',*,*,#482,.F.);
#482 = EDGE_CURVE('',#483,#460,#485,.T.);
#483 = VERTEX_POINT('',#484);
#484 = CARTESIAN_POINT('',(18.60791808,-2.6289508,0.E+000));
#485 = SURFACE_CURVE('',#486,(#490,#497),.PCURVE_S1.);
#486 = LINE('',#487,#488);
#487 = CARTESIAN_POINT('',(18.60791808,-2.6289508,0.E+000));
#488 = VECTOR('',#489,1.);
#489 = DIRECTION('',(0.E+000,0.E+000,1.));
#490 = PCURVE('',#435,#491);
#491 = DEFINITIONAL_REPRESENTATION('',(#492),#496);
#492 = LINE('',#493,#494);
#493 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#494 = VECTOR('',#495,1.);
#495 = DIRECTION('',(0.E+000,-1.));
#496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#497 = PCURVE('',#498,#503);
#498 = PLANE('',#499);
#499 = AXIS2_PLACEMENT_3D('',#500,#501,#502);
#500 = CARTESIAN_POINT('',(18.60791808,-2.6289508,0.E+000));
#501 = DIRECTION('',(0.290281519442,-0.956941293638,0.E+000));
#502 = DIRECTION('',(-0.956941293638,-0.290281519442,0.E+000));
#503 = DEFINITIONAL_REPRESENTATION('',(#504),#508);
#504 = LINE('',#505,#506);
#505 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#506 = VECTOR('',#507,1.);
#507 = DIRECTION('',(0.E+000,-1.));
#508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#509 = ORIENTED_EDGE('',*,*,#510,.F.);
#510 = EDGE_CURVE('',#425,#483,#511,.T.);
#511 = SURFACE_CURVE('',#512,(#516,#523),.PCURVE_S1.);
#512 = LINE('',#513,#514);
#513 = CARTESIAN_POINT('',(18.9999874,-2.59033518,0.E+000));
#514 = VECTOR('',#515,1.);
#515 = DIRECTION('',(-0.995184686447,-9.801754873774E-002,0.E+000));
#516 = PCURVE('',#435,#517);
#517 = DEFINITIONAL_REPRESENTATION('',(#518),#522);
#518 = LINE('',#519,#520);
#519 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#520 = VECTOR('',#521,1.);
#521 = DIRECTION('',(1.,0.E+000));
#522 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#523 = PCURVE('',#137,#524);
#524 = DEFINITIONAL_REPRESENTATION('',(#525),#529);
#525 = LINE('',#526,#527);
#526 = CARTESIAN_POINT('',(18.9999874,9.00966448));
#527 = VECTOR('',#528,1.);
#528 = DIRECTION('',(-0.995184686447,-9.801754873774E-002));
#529 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#530 = ADVANCED_FACE('',(#531),#498,.F.);
#531 = FACE_BOUND('',#532,.F.);
#532 = EDGE_LOOP('',(#533,#534,#557,#585));
#533 = ORIENTED_EDGE('',*,*,#482,.T.);
#534 = ORIENTED_EDGE('',*,*,#535,.T.);
#535 = EDGE_CURVE('',#460,#536,#538,.T.);
#536 = VERTEX_POINT('',#537);
#537 = CARTESIAN_POINT('',(18.23091604,-2.74331176,1.12192054));
#538 = SURFACE_CURVE('',#539,(#543,#550),.PCURVE_S1.);
#539 = LINE('',#540,#541);
#540 = CARTESIAN_POINT('',(18.60791808,-2.6289508,1.12192054));
#541 = VECTOR('',#542,1.);
#542 = DIRECTION('',(-0.956941293638,-0.290281519442,0.E+000));
#543 = PCURVE('',#498,#544);
#544 = DEFINITIONAL_REPRESENTATION('',(#545),#549);
#545 = LINE('',#546,#547);
#546 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#547 = VECTOR('',#548,1.);
#548 = DIRECTION('',(1.,0.E+000));
#549 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#550 = PCURVE('',#83,#551);
#551 = DEFINITIONAL_REPRESENTATION('',(#552),#556);
#552 = LINE('',#553,#554);
#553 = CARTESIAN_POINT('',(18.60791808,8.97104886));
#554 = VECTOR('',#555,1.);
#555 = DIRECTION('',(-0.956941293638,-0.290281519442));
#556 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#557 = ORIENTED_EDGE('',*,*,#558,.F.);
#558 = EDGE_CURVE('',#559,#536,#561,.T.);
#559 = VERTEX_POINT('',#560);
#560 = CARTESIAN_POINT('',(18.23091604,-2.74331176,0.E+000));
#561 = SURFACE_CURVE('',#562,(#566,#573),.PCURVE_S1.);
#562 = LINE('',#563,#564);
#563 = CARTESIAN_POINT('',(18.23091604,-2.74331176,0.E+000));
#564 = VECTOR('',#565,1.);
#565 = DIRECTION('',(0.E+000,0.E+000,1.));
#566 = PCURVE('',#498,#567);
#567 = DEFINITIONAL_REPRESENTATION('',(#568),#572);
#568 = LINE('',#569,#570);
#569 = CARTESIAN_POINT('',(0.393965693096,0.E+000));
#570 = VECTOR('',#571,1.);
#571 = DIRECTION('',(0.E+000,-1.));
#572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#573 = PCURVE('',#574,#579);
#574 = PLANE('',#575);
#575 = AXIS2_PLACEMENT_3D('',#576,#577,#578);
#576 = CARTESIAN_POINT('',(18.23091604,-2.74331176,0.E+000));
#577 = DIRECTION('',(0.471397848625,-0.881920670078,0.E+000));
#578 = DIRECTION('',(-0.881920670078,-0.471397848625,0.E+000));
#579 = DEFINITIONAL_REPRESENTATION('',(#580),#584);
#580 = LINE('',#581,#582);
#581 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#582 = VECTOR('',#583,1.);
#583 = DIRECTION('',(0.E+000,-1.));
#584 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#585 = ORIENTED_EDGE('',*,*,#586,.F.);
#586 = EDGE_CURVE('',#483,#559,#587,.T.);
#587 = SURFACE_CURVE('',#588,(#592,#599),.PCURVE_S1.);
#588 = LINE('',#589,#590);
#589 = CARTESIAN_POINT('',(18.60791808,-2.6289508,0.E+000));
#590 = VECTOR('',#591,1.);
#591 = DIRECTION('',(-0.956941293638,-0.290281519442,0.E+000));
#592 = PCURVE('',#498,#593);
#593 = DEFINITIONAL_REPRESENTATION('',(#594),#598);
#594 = LINE('',#595,#596);
#595 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#596 = VECTOR('',#597,1.);
#597 = DIRECTION('',(1.,0.E+000));
#598 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#599 = PCURVE('',#137,#600);
#600 = DEFINITIONAL_REPRESENTATION('',(#601),#605);
#601 = LINE('',#602,#603);
#602 = CARTESIAN_POINT('',(18.60791808,8.97104886));
#603 = VECTOR('',#604,1.);
#604 = DIRECTION('',(-0.956941293638,-0.290281519442));
#605 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#606 = ADVANCED_FACE('',(#607),#574,.F.);
#607 = FACE_BOUND('',#608,.F.);
#608 = EDGE_LOOP('',(#609,#610,#633,#661));
#609 = ORIENTED_EDGE('',*,*,#558,.T.);
#610 = ORIENTED_EDGE('',*,*,#611,.T.);
#611 = EDGE_CURVE('',#536,#612,#614,.T.);
#612 = VERTEX_POINT('',#613);
#613 = CARTESIAN_POINT('',(17.88346944,-2.9290264,1.12192054));
#614 = SURFACE_CURVE('',#615,(#619,#626),.PCURVE_S1.);
#615 = LINE('',#616,#617);
#616 = CARTESIAN_POINT('',(18.23091604,-2.74331176,1.12192054));
#617 = VECTOR('',#618,1.);
#618 = DIRECTION('',(-0.881920670078,-0.471397848625,0.E+000));
#619 = PCURVE('',#574,#620);
#620 = DEFINITIONAL_REPRESENTATION('',(#621),#625);
#621 = LINE('',#622,#623);
#622 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#623 = VECTOR('',#624,1.);
#624 = DIRECTION('',(1.,0.E+000));
#625 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#626 = PCURVE('',#83,#627);
#627 = DEFINITIONAL_REPRESENTATION('',(#628),#632);
#628 = LINE('',#629,#630);
#629 = CARTESIAN_POINT('',(18.23091604,8.8566879));
#630 = VECTOR('',#631,1.);
#631 = DIRECTION('',(-0.881920670078,-0.471397848625));
#632 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#633 = ORIENTED_EDGE('',*,*,#634,.F.);
#634 = EDGE_CURVE('',#635,#612,#637,.T.);
#635 = VERTEX_POINT('',#636);
#636 = CARTESIAN_POINT('',(17.88346944,-2.9290264,0.E+000));
#637 = SURFACE_CURVE('',#638,(#642,#649),.PCURVE_S1.);
#638 = LINE('',#639,#640);
#639 = CARTESIAN_POINT('',(17.88346944,-2.9290264,0.E+000));
#640 = VECTOR('',#641,1.);
#641 = DIRECTION('',(0.E+000,0.E+000,1.));
#642 = PCURVE('',#574,#643);
#643 = DEFINITIONAL_REPRESENTATION('',(#644),#648);
#644 = LINE('',#645,#646);
#645 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#646 = VECTOR('',#647,1.);
#647 = DIRECTION('',(0.E+000,-1.));
#648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#649 = PCURVE('',#650,#655);
#650 = PLANE('',#651);
#651 = AXIS2_PLACEMENT_3D('',#652,#653,#654);
#652 = CARTESIAN_POINT('',(17.88346944,-2.9290264,0.E+000));
#653 = DIRECTION('',(0.634397425685,-0.773007054486,0.E+000));
#654 = DIRECTION('',(-0.773007054486,-0.634397425685,0.E+000));
#655 = DEFINITIONAL_REPRESENTATION('',(#656),#660);
#656 = LINE('',#657,#658);
#657 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#658 = VECTOR('',#659,1.);
#659 = DIRECTION('',(0.E+000,-1.));
#660 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#661 = ORIENTED_EDGE('',*,*,#662,.F.);
#662 = EDGE_CURVE('',#559,#635,#663,.T.);
#663 = SURFACE_CURVE('',#664,(#668,#675),.PCURVE_S1.);
#664 = LINE('',#665,#666);
#665 = CARTESIAN_POINT('',(18.23091604,-2.74331176,0.E+000));
#666 = VECTOR('',#667,1.);
#667 = DIRECTION('',(-0.881920670078,-0.471397848625,0.E+000));
#668 = PCURVE('',#574,#669);
#669 = DEFINITIONAL_REPRESENTATION('',(#670),#674);
#670 = LINE('',#671,#672);
#671 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#672 = VECTOR('',#673,1.);
#673 = DIRECTION('',(1.,0.E+000));
#674 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#675 = PCURVE('',#137,#676);
#676 = DEFINITIONAL_REPRESENTATION('',(#677),#681);
#677 = LINE('',#678,#679);
#678 = CARTESIAN_POINT('',(18.23091604,8.8566879));
#679 = VECTOR('',#680,1.);
#680 = DIRECTION('',(-0.881920670078,-0.471397848625));
#681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#682 = ADVANCED_FACE('',(#683),#650,.F.);
#683 = FACE_BOUND('',#684,.F.);
#684 = EDGE_LOOP('',(#685,#686,#709,#737));
#685 = ORIENTED_EDGE('',*,*,#634,.T.);
#686 = ORIENTED_EDGE('',*,*,#687,.T.);
#687 = EDGE_CURVE('',#612,#688,#690,.T.);
#688 = VERTEX_POINT('',#689);
#689 = CARTESIAN_POINT('',(17.57893106,-3.17895732,1.12192054));
#690 = SURFACE_CURVE('',#691,(#695,#702),.PCURVE_S1.);
#691 = LINE('',#692,#693);
#692 = CARTESIAN_POINT('',(17.88346944,-2.9290264,1.12192054));
#693 = VECTOR('',#694,1.);
#694 = DIRECTION('',(-0.773007054486,-0.634397425685,0.E+000));
#695 = PCURVE('',#650,#696);
#696 = DEFINITIONAL_REPRESENTATION('',(#697),#701);
#697 = LINE('',#698,#699);
#698 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#699 = VECTOR('',#700,1.);
#700 = DIRECTION('',(1.,0.E+000));
#701 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#702 = PCURVE('',#83,#703);
#703 = DEFINITIONAL_REPRESENTATION('',(#704),#708);
#704 = LINE('',#705,#706);
#705 = CARTESIAN_POINT('',(17.88346944,8.67097326));
#706 = VECTOR('',#707,1.);
#707 = DIRECTION('',(-0.773007054486,-0.634397425685));
#708 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#709 = ORIENTED_EDGE('',*,*,#710,.F.);
#710 = EDGE_CURVE('',#711,#688,#713,.T.);
#711 = VERTEX_POINT('',#712);
#712 = CARTESIAN_POINT('',(17.57893106,-3.17895732,0.E+000));
#713 = SURFACE_CURVE('',#714,(#718,#725),.PCURVE_S1.);
#714 = LINE('',#715,#716);
#715 = CARTESIAN_POINT('',(17.57893106,-3.17895732,0.E+000));
#716 = VECTOR('',#717,1.);
#717 = DIRECTION('',(0.E+000,0.E+000,1.));
#718 = PCURVE('',#650,#719);
#719 = DEFINITIONAL_REPRESENTATION('',(#720),#724);
#720 = LINE('',#721,#722);
#721 = CARTESIAN_POINT('',(0.393965848349,0.E+000));
#722 = VECTOR('',#723,1.);
#723 = DIRECTION('',(0.E+000,-1.));
#724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#725 = PCURVE('',#726,#731);
#726 = PLANE('',#727);
#727 = AXIS2_PLACEMENT_3D('',#728,#729,#730);
#728 = CARTESIAN_POINT('',(17.57893106,-3.17895732,0.E+000));
#729 = DIRECTION('',(0.773007054486,-0.634397425685,0.E+000));
#730 = DIRECTION('',(-0.634397425685,-0.773007054486,0.E+000));
#731 = DEFINITIONAL_REPRESENTATION('',(#732),#736);
#732 = LINE('',#733,#734);
#733 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#734 = VECTOR('',#735,1.);
#735 = DIRECTION('',(0.E+000,-1.));
#736 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#737 = ORIENTED_EDGE('',*,*,#738,.F.);
#738 = EDGE_CURVE('',#635,#711,#739,.T.);
#739 = SURFACE_CURVE('',#740,(#744,#751),.PCURVE_S1.);
#740 = LINE('',#741,#742);
#741 = CARTESIAN_POINT('',(17.88346944,-2.9290264,0.E+000));
#742 = VECTOR('',#743,1.);
#743 = DIRECTION('',(-0.773007054486,-0.634397425685,0.E+000));
#744 = PCURVE('',#650,#745);
#745 = DEFINITIONAL_REPRESENTATION('',(#746),#750);
#746 = LINE('',#747,#748);
#747 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#748 = VECTOR('',#749,1.);
#749 = DIRECTION('',(1.,0.E+000));
#750 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#751 = PCURVE('',#137,#752);
#752 = DEFINITIONAL_REPRESENTATION('',(#753),#757);
#753 = LINE('',#754,#755);
#754 = CARTESIAN_POINT('',(17.88346944,8.67097326));
#755 = VECTOR('',#756,1.);
#756 = DIRECTION('',(-0.773007054486,-0.634397425685));
#757 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#758 = ADVANCED_FACE('',(#759),#726,.F.);
#759 = FACE_BOUND('',#760,.F.);
#760 = EDGE_LOOP('',(#761,#762,#785,#813));
#761 = ORIENTED_EDGE('',*,*,#710,.T.);
#762 = ORIENTED_EDGE('',*,*,#763,.T.);
#763 = EDGE_CURVE('',#688,#764,#766,.T.);
#764 = VERTEX_POINT('',#765);
#765 = CARTESIAN_POINT('',(17.32900014,-3.4834957,1.12192054));
#766 = SURFACE_CURVE('',#767,(#771,#778),.PCURVE_S1.);
#767 = LINE('',#768,#769);
#768 = CARTESIAN_POINT('',(17.57893106,-3.17895732,1.12192054));
#769 = VECTOR('',#770,1.);
#770 = DIRECTION('',(-0.634397425685,-0.773007054486,0.E+000));
#771 = PCURVE('',#726,#772);
#772 = DEFINITIONAL_REPRESENTATION('',(#773),#777);
#773 = LINE('',#774,#775);
#774 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#775 = VECTOR('',#776,1.);
#776 = DIRECTION('',(1.,0.E+000));
#777 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#778 = PCURVE('',#83,#779);
#779 = DEFINITIONAL_REPRESENTATION('',(#780),#784);
#780 = LINE('',#781,#782);
#781 = CARTESIAN_POINT('',(17.57893106,8.42104234));
#782 = VECTOR('',#783,1.);
#783 = DIRECTION('',(-0.634397425685,-0.773007054486));
#784 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#785 = ORIENTED_EDGE('',*,*,#786,.F.);
#786 = EDGE_CURVE('',#787,#764,#789,.T.);
#787 = VERTEX_POINT('',#788);
#788 = CARTESIAN_POINT('',(17.32900014,-3.4834957,0.E+000));
#789 = SURFACE_CURVE('',#790,(#794,#801),.PCURVE_S1.);
#790 = LINE('',#791,#792);
#791 = CARTESIAN_POINT('',(17.32900014,-3.4834957,0.E+000));
#792 = VECTOR('',#793,1.);
#793 = DIRECTION('',(0.E+000,0.E+000,1.));
#794 = PCURVE('',#726,#795);
#795 = DEFINITIONAL_REPRESENTATION('',(#796),#800);
#796 = LINE('',#797,#798);
#797 = CARTESIAN_POINT('',(0.393965848349,0.E+000));
#798 = VECTOR('',#799,1.);
#799 = DIRECTION('',(0.E+000,-1.));
#800 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#801 = PCURVE('',#802,#807);
#802 = PLANE('',#803);
#803 = AXIS2_PLACEMENT_3D('',#804,#805,#806);
#804 = CARTESIAN_POINT('',(17.32900014,-3.4834957,0.E+000));
#805 = DIRECTION('',(0.881920670078,-0.471397848625,0.E+000));
#806 = DIRECTION('',(-0.471397848625,-0.881920670078,0.E+000));
#807 = DEFINITIONAL_REPRESENTATION('',(#808),#812);
#808 = LINE('',#809,#810);
#809 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#810 = VECTOR('',#811,1.);
#811 = DIRECTION('',(0.E+000,-1.));
#812 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#813 = ORIENTED_EDGE('',*,*,#814,.F.);
#814 = EDGE_CURVE('',#711,#787,#815,.T.);
#815 = SURFACE_CURVE('',#816,(#820,#827),.PCURVE_S1.);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(17.57893106,-3.17895732,0.E+000));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(-0.634397425685,-0.773007054486,0.E+000));
#820 = PCURVE('',#726,#821);
#821 = DEFINITIONAL_REPRESENTATION('',(#822),#826);
#822 = LINE('',#823,#824);
#823 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#824 = VECTOR('',#825,1.);
#825 = DIRECTION('',(1.,0.E+000));
#826 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#827 = PCURVE('',#137,#828);
#828 = DEFINITIONAL_REPRESENTATION('',(#829),#833);
#829 = LINE('',#830,#831);
#830 = CARTESIAN_POINT('',(17.57893106,8.42104234));
#831 = VECTOR('',#832,1.);
#832 = DIRECTION('',(-0.634397425685,-0.773007054486));
#833 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#834 = ADVANCED_FACE('',(#835),#802,.F.);
#835 = FACE_BOUND('',#836,.F.);
#836 = EDGE_LOOP('',(#837,#838,#861,#889));
#837 = ORIENTED_EDGE('',*,*,#786,.T.);
#838 = ORIENTED_EDGE('',*,*,#839,.T.);
#839 = EDGE_CURVE('',#764,#840,#842,.T.);
#840 = VERTEX_POINT('',#841);
#841 = CARTESIAN_POINT('',(17.1432855,-3.8309423,1.12192054));
#842 = SURFACE_CURVE('',#843,(#847,#854),.PCURVE_S1.);
#843 = LINE('',#844,#845);
#844 = CARTESIAN_POINT('',(17.32900014,-3.4834957,1.12192054));
#845 = VECTOR('',#846,1.);
#846 = DIRECTION('',(-0.471397848625,-0.881920670078,0.E+000));
#847 = PCURVE('',#802,#848);
#848 = DEFINITIONAL_REPRESENTATION('',(#849),#853);
#849 = LINE('',#850,#851);
#850 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#851 = VECTOR('',#852,1.);
#852 = DIRECTION('',(1.,0.E+000));
#853 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#854 = PCURVE('',#83,#855);
#855 = DEFINITIONAL_REPRESENTATION('',(#856),#860);
#856 = LINE('',#857,#858);
#857 = CARTESIAN_POINT('',(17.32900014,8.11650396));
#858 = VECTOR('',#859,1.);
#859 = DIRECTION('',(-0.471397848625,-0.881920670078));
#860 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#861 = ORIENTED_EDGE('',*,*,#862,.F.);
#862 = EDGE_CURVE('',#863,#840,#865,.T.);
#863 = VERTEX_POINT('',#864);
#864 = CARTESIAN_POINT('',(17.1432855,-3.8309423,0.E+000));
#865 = SURFACE_CURVE('',#866,(#870,#877),.PCURVE_S1.);
#866 = LINE('',#867,#868);
#867 = CARTESIAN_POINT('',(17.1432855,-3.8309423,0.E+000));
#868 = VECTOR('',#869,1.);
#869 = DIRECTION('',(0.E+000,0.E+000,1.));
#870 = PCURVE('',#802,#871);
#871 = DEFINITIONAL_REPRESENTATION('',(#872),#876);
#872 = LINE('',#873,#874);
#873 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#874 = VECTOR('',#875,1.);
#875 = DIRECTION('',(0.E+000,-1.));
#876 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#877 = PCURVE('',#878,#883);
#878 = PLANE('',#879);
#879 = AXIS2_PLACEMENT_3D('',#880,#881,#882);
#880 = CARTESIAN_POINT('',(17.1432855,-3.8309423,0.E+000));
#881 = DIRECTION('',(0.956941293638,-0.290281519442,0.E+000));
#882 = DIRECTION('',(-0.290281519442,-0.956941293638,0.E+000));
#883 = DEFINITIONAL_REPRESENTATION('',(#884),#888);
#884 = LINE('',#885,#886);
#885 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#886 = VECTOR('',#887,1.);
#887 = DIRECTION('',(0.E+000,-1.));
#888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#889 = ORIENTED_EDGE('',*,*,#890,.F.);
#890 = EDGE_CURVE('',#787,#863,#891,.T.);
#891 = SURFACE_CURVE('',#892,(#896,#903),.PCURVE_S1.);
#892 = LINE('',#893,#894);
#893 = CARTESIAN_POINT('',(17.32900014,-3.4834957,0.E+000));
#894 = VECTOR('',#895,1.);
#895 = DIRECTION('',(-0.471397848625,-0.881920670078,0.E+000));
#896 = PCURVE('',#802,#897);
#897 = DEFINITIONAL_REPRESENTATION('',(#898),#902);
#898 = LINE('',#899,#900);
#899 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#900 = VECTOR('',#901,1.);
#901 = DIRECTION('',(1.,0.E+000));
#902 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#903 = PCURVE('',#137,#904);
#904 = DEFINITIONAL_REPRESENTATION('',(#905),#909);
#905 = LINE('',#906,#907);
#906 = CARTESIAN_POINT('',(17.32900014,8.11650396));
#907 = VECTOR('',#908,1.);
#908 = DIRECTION('',(-0.471397848625,-0.881920670078));
#909 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#910 = ADVANCED_FACE('',(#911),#878,.F.);
#911 = FACE_BOUND('',#912,.F.);
#912 = EDGE_LOOP('',(#913,#914,#937,#965));
#913 = ORIENTED_EDGE('',*,*,#862,.T.);
#914 = ORIENTED_EDGE('',*,*,#915,.T.);
#915 = EDGE_CURVE('',#840,#916,#918,.T.);
#916 = VERTEX_POINT('',#917);
#917 = CARTESIAN_POINT('',(17.02892454,-4.20794434,1.12192054));
#918 = SURFACE_CURVE('',#919,(#923,#930),.PCURVE_S1.);
#919 = LINE('',#920,#921);
#920 = CARTESIAN_POINT('',(17.1432855,-3.8309423,1.12192054));
#921 = VECTOR('',#922,1.);
#922 = DIRECTION('',(-0.290281519442,-0.956941293638,0.E+000));
#923 = PCURVE('',#878,#924);
#924 = DEFINITIONAL_REPRESENTATION('',(#925),#929);
#925 = LINE('',#926,#927);
#926 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#927 = VECTOR('',#928,1.);
#928 = DIRECTION('',(1.,0.E+000));
#929 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#930 = PCURVE('',#83,#931);
#931 = DEFINITIONAL_REPRESENTATION('',(#932),#936);
#932 = LINE('',#933,#934);
#933 = CARTESIAN_POINT('',(17.1432855,7.76905736));
#934 = VECTOR('',#935,1.);
#935 = DIRECTION('',(-0.290281519442,-0.956941293638));
#936 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#937 = ORIENTED_EDGE('',*,*,#938,.F.);
#938 = EDGE_CURVE('',#939,#916,#941,.T.);
#939 = VERTEX_POINT('',#940);
#940 = CARTESIAN_POINT('',(17.02892454,-4.20794434,0.E+000));
#941 = SURFACE_CURVE('',#942,(#946,#953),.PCURVE_S1.);
#942 = LINE('',#943,#944);
#943 = CARTESIAN_POINT('',(17.02892454,-4.20794434,0.E+000));
#944 = VECTOR('',#945,1.);
#945 = DIRECTION('',(0.E+000,0.E+000,1.));
#946 = PCURVE('',#878,#947);
#947 = DEFINITIONAL_REPRESENTATION('',(#948),#952);
#948 = LINE('',#949,#950);
#949 = CARTESIAN_POINT('',(0.393965693096,0.E+000));
#950 = VECTOR('',#951,1.);
#951 = DIRECTION('',(0.E+000,-1.));
#952 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#953 = PCURVE('',#954,#959);
#954 = PLANE('',#955);
#955 = AXIS2_PLACEMENT_3D('',#956,#957,#958);
#956 = CARTESIAN_POINT('',(17.02892454,-4.20794434,0.E+000));
#957 = DIRECTION('',(0.995184686447,-9.801754873774E-002,0.E+000));
#958 = DIRECTION('',(-9.801754873774E-002,-0.995184686447,0.E+000));
#959 = DEFINITIONAL_REPRESENTATION('',(#960),#964);
#960 = LINE('',#961,#962);
#961 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#962 = VECTOR('',#963,1.);
#963 = DIRECTION('',(0.E+000,-1.));
#964 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#965 = ORIENTED_EDGE('',*,*,#966,.F.);
#966 = EDGE_CURVE('',#863,#939,#967,.T.);
#967 = SURFACE_CURVE('',#968,(#972,#979),.PCURVE_S1.);
#968 = LINE('',#969,#970);
#969 = CARTESIAN_POINT('',(17.1432855,-3.8309423,0.E+000));
#970 = VECTOR('',#971,1.);
#971 = DIRECTION('',(-0.290281519442,-0.956941293638,0.E+000));
#972 = PCURVE('',#878,#973);
#973 = DEFINITIONAL_REPRESENTATION('',(#974),#978);
#974 = LINE('',#975,#976);
#975 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#976 = VECTOR('',#977,1.);
#977 = DIRECTION('',(1.,0.E+000));
#978 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#979 = PCURVE('',#137,#980);
#980 = DEFINITIONAL_REPRESENTATION('',(#981),#985);
#981 = LINE('',#982,#983);
#982 = CARTESIAN_POINT('',(17.1432855,7.76905736));
#983 = VECTOR('',#984,1.);
#984 = DIRECTION('',(-0.290281519442,-0.956941293638));
#985 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#986 = ADVANCED_FACE('',(#987),#954,.F.);
#987 = FACE_BOUND('',#988,.F.);
#988 = EDGE_LOOP('',(#989,#990,#1013,#1041));
#989 = ORIENTED_EDGE('',*,*,#938,.T.);
#990 = ORIENTED_EDGE('',*,*,#991,.T.);
#991 = EDGE_CURVE('',#916,#992,#994,.T.);
#992 = VERTEX_POINT('',#993);
#993 = CARTESIAN_POINT('',(16.99030892,-4.60001366,1.12192054));
#994 = SURFACE_CURVE('',#995,(#999,#1006),.PCURVE_S1.);
#995 = LINE('',#996,#997);
#996 = CARTESIAN_POINT('',(17.02892454,-4.20794434,1.12192054));
#997 = VECTOR('',#998,1.);
#998 = DIRECTION('',(-9.801754873774E-002,-0.995184686447,0.E+000));
#999 = PCURVE('',#954,#1000);
#1000 = DEFINITIONAL_REPRESENTATION('',(#1001),#1005);
#1001 = LINE('',#1002,#1003);
#1002 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1003 = VECTOR('',#1004,1.);
#1004 = DIRECTION('',(1.,0.E+000));
#1005 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1006 = PCURVE('',#83,#1007);
#1007 = DEFINITIONAL_REPRESENTATION('',(#1008),#1012);
#1008 = LINE('',#1009,#1010);
#1009 = CARTESIAN_POINT('',(17.02892454,7.39205532));
#1010 = VECTOR('',#1011,1.);
#1011 = DIRECTION('',(-9.801754873774E-002,-0.995184686447));
#1012 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1013 = ORIENTED_EDGE('',*,*,#1014,.F.);
#1014 = EDGE_CURVE('',#1015,#992,#1017,.T.);
#1015 = VERTEX_POINT('',#1016);
#1016 = CARTESIAN_POINT('',(16.99030892,-4.60001366,0.E+000));
#1017 = SURFACE_CURVE('',#1018,(#1022,#1029),.PCURVE_S1.);
#1018 = LINE('',#1019,#1020);
#1019 = CARTESIAN_POINT('',(16.99030892,-4.60001366,0.E+000));
#1020 = VECTOR('',#1021,1.);
#1021 = DIRECTION('',(0.E+000,0.E+000,1.));
#1022 = PCURVE('',#954,#1023);
#1023 = DEFINITIONAL_REPRESENTATION('',(#1024),#1028);
#1024 = LINE('',#1025,#1026);
#1025 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#1026 = VECTOR('',#1027,1.);
#1027 = DIRECTION('',(0.E+000,-1.));
#1028 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1029 = PCURVE('',#1030,#1035);
#1030 = PLANE('',#1031);
#1031 = AXIS2_PLACEMENT_3D('',#1032,#1033,#1034);
#1032 = CARTESIAN_POINT('',(16.99030892,-4.60001366,0.E+000));
#1033 = DIRECTION('',(0.995184686447,9.801754873774E-002,-0.E+000));
#1034 = DIRECTION('',(9.801754873774E-002,-0.995184686447,0.E+000));
#1035 = DEFINITIONAL_REPRESENTATION('',(#1036),#1040);
#1036 = LINE('',#1037,#1038);
#1037 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1038 = VECTOR('',#1039,1.);
#1039 = DIRECTION('',(0.E+000,-1.));
#1040 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1041 = ORIENTED_EDGE('',*,*,#1042,.F.);
#1042 = EDGE_CURVE('',#939,#1015,#1043,.T.);
#1043 = SURFACE_CURVE('',#1044,(#1048,#1055),.PCURVE_S1.);
#1044 = LINE('',#1045,#1046);
#1045 = CARTESIAN_POINT('',(17.02892454,-4.20794434,0.E+000));
#1046 = VECTOR('',#1047,1.);
#1047 = DIRECTION('',(-9.801754873774E-002,-0.995184686447,0.E+000));
#1048 = PCURVE('',#954,#1049);
#1049 = DEFINITIONAL_REPRESENTATION('',(#1050),#1054);
#1050 = LINE('',#1051,#1052);
#1051 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1052 = VECTOR('',#1053,1.);
#1053 = DIRECTION('',(1.,0.E+000));
#1054 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1055 = PCURVE('',#137,#1056);
#1056 = DEFINITIONAL_REPRESENTATION('',(#1057),#1061);
#1057 = LINE('',#1058,#1059);
#1058 = CARTESIAN_POINT('',(17.02892454,7.39205532));
#1059 = VECTOR('',#1060,1.);
#1060 = DIRECTION('',(-9.801754873774E-002,-0.995184686447));
#1061 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1062 = ADVANCED_FACE('',(#1063),#1030,.F.);
#1063 = FACE_BOUND('',#1064,.F.);
#1064 = EDGE_LOOP('',(#1065,#1066,#1089,#1117));
#1065 = ORIENTED_EDGE('',*,*,#1014,.T.);
#1066 = ORIENTED_EDGE('',*,*,#1067,.T.);
#1067 = EDGE_CURVE('',#992,#1068,#1070,.T.);
#1068 = VERTEX_POINT('',#1069);
#1069 = CARTESIAN_POINT('',(17.02892454,-4.99208298,1.12192054));
#1070 = SURFACE_CURVE('',#1071,(#1075,#1082),.PCURVE_S1.);
#1071 = LINE('',#1072,#1073);
#1072 = CARTESIAN_POINT('',(16.99030892,-4.60001366,1.12192054));
#1073 = VECTOR('',#1074,1.);
#1074 = DIRECTION('',(9.801754873774E-002,-0.995184686447,0.E+000));
#1075 = PCURVE('',#1030,#1076);
#1076 = DEFINITIONAL_REPRESENTATION('',(#1077),#1081);
#1077 = LINE('',#1078,#1079);
#1078 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1079 = VECTOR('',#1080,1.);
#1080 = DIRECTION('',(1.,0.E+000));
#1081 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1082 = PCURVE('',#83,#1083);
#1083 = DEFINITIONAL_REPRESENTATION('',(#1084),#1088);
#1084 = LINE('',#1085,#1086);
#1085 = CARTESIAN_POINT('',(16.99030892,6.999986));
#1086 = VECTOR('',#1087,1.);
#1087 = DIRECTION('',(9.801754873774E-002,-0.995184686447));
#1088 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1089 = ORIENTED_EDGE('',*,*,#1090,.F.);
#1090 = EDGE_CURVE('',#1091,#1068,#1093,.T.);
#1091 = VERTEX_POINT('',#1092);
#1092 = CARTESIAN_POINT('',(17.02892454,-4.99208298,0.E+000));
#1093 = SURFACE_CURVE('',#1094,(#1098,#1105),.PCURVE_S1.);
#1094 = LINE('',#1095,#1096);
#1095 = CARTESIAN_POINT('',(17.02892454,-4.99208298,0.E+000));
#1096 = VECTOR('',#1097,1.);
#1097 = DIRECTION('',(0.E+000,0.E+000,1.));
#1098 = PCURVE('',#1030,#1099);
#1099 = DEFINITIONAL_REPRESENTATION('',(#1100),#1104);
#1100 = LINE('',#1101,#1102);
#1101 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#1102 = VECTOR('',#1103,1.);
#1103 = DIRECTION('',(0.E+000,-1.));
#1104 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1105 = PCURVE('',#1106,#1111);
#1106 = PLANE('',#1107);
#1107 = AXIS2_PLACEMENT_3D('',#1108,#1109,#1110);
#1108 = CARTESIAN_POINT('',(17.02892454,-4.99208298,0.E+000));
#1109 = DIRECTION('',(0.956941293638,0.290281519442,-0.E+000));
#1110 = DIRECTION('',(0.290281519442,-0.956941293638,0.E+000));
#1111 = DEFINITIONAL_REPRESENTATION('',(#1112),#1116);
#1112 = LINE('',#1113,#1114);
#1113 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1114 = VECTOR('',#1115,1.);
#1115 = DIRECTION('',(0.E+000,-1.));
#1116 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1117 = ORIENTED_EDGE('',*,*,#1118,.F.);
#1118 = EDGE_CURVE('',#1015,#1091,#1119,.T.);
#1119 = SURFACE_CURVE('',#1120,(#1124,#1131),.PCURVE_S1.);
#1120 = LINE('',#1121,#1122);
#1121 = CARTESIAN_POINT('',(16.99030892,-4.60001366,0.E+000));
#1122 = VECTOR('',#1123,1.);
#1123 = DIRECTION('',(9.801754873774E-002,-0.995184686447,0.E+000));
#1124 = PCURVE('',#1030,#1125);
#1125 = DEFINITIONAL_REPRESENTATION('',(#1126),#1130);
#1126 = LINE('',#1127,#1128);
#1127 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1128 = VECTOR('',#1129,1.);
#1129 = DIRECTION('',(1.,0.E+000));
#1130 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1131 = PCURVE('',#137,#1132);
#1132 = DEFINITIONAL_REPRESENTATION('',(#1133),#1137);
#1133 = LINE('',#1134,#1135);
#1134 = CARTESIAN_POINT('',(16.99030892,6.999986));
#1135 = VECTOR('',#1136,1.);
#1136 = DIRECTION('',(9.801754873774E-002,-0.995184686447));
#1137 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1138 = ADVANCED_FACE('',(#1139),#1106,.F.);
#1139 = FACE_BOUND('',#1140,.F.);
#1140 = EDGE_LOOP('',(#1141,#1142,#1165,#1193));
#1141 = ORIENTED_EDGE('',*,*,#1090,.T.);
#1142 = ORIENTED_EDGE('',*,*,#1143,.T.);
#1143 = EDGE_CURVE('',#1068,#1144,#1146,.T.);
#1144 = VERTEX_POINT('',#1145);
#1145 = CARTESIAN_POINT('',(17.1432855,-5.36908502,1.12192054));
#1146 = SURFACE_CURVE('',#1147,(#1151,#1158),.PCURVE_S1.);
#1147 = LINE('',#1148,#1149);
#1148 = CARTESIAN_POINT('',(17.02892454,-4.99208298,1.12192054));
#1149 = VECTOR('',#1150,1.);
#1150 = DIRECTION('',(0.290281519442,-0.956941293638,0.E+000));
#1151 = PCURVE('',#1106,#1152);
#1152 = DEFINITIONAL_REPRESENTATION('',(#1153),#1157);
#1153 = LINE('',#1154,#1155);
#1154 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1155 = VECTOR('',#1156,1.);
#1156 = DIRECTION('',(1.,0.E+000));
#1157 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1158 = PCURVE('',#83,#1159);
#1159 = DEFINITIONAL_REPRESENTATION('',(#1160),#1164);
#1160 = LINE('',#1161,#1162);
#1161 = CARTESIAN_POINT('',(17.02892454,6.60791668));
#1162 = VECTOR('',#1163,1.);
#1163 = DIRECTION('',(0.290281519442,-0.956941293638));
#1164 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1165 = ORIENTED_EDGE('',*,*,#1166,.F.);
#1166 = EDGE_CURVE('',#1167,#1144,#1169,.T.);
#1167 = VERTEX_POINT('',#1168);
#1168 = CARTESIAN_POINT('',(17.1432855,-5.36908502,0.E+000));
#1169 = SURFACE_CURVE('',#1170,(#1174,#1181),.PCURVE_S1.);
#1170 = LINE('',#1171,#1172);
#1171 = CARTESIAN_POINT('',(17.1432855,-5.36908502,0.E+000));
#1172 = VECTOR('',#1173,1.);
#1173 = DIRECTION('',(0.E+000,0.E+000,1.));
#1174 = PCURVE('',#1106,#1175);
#1175 = DEFINITIONAL_REPRESENTATION('',(#1176),#1180);
#1176 = LINE('',#1177,#1178);
#1177 = CARTESIAN_POINT('',(0.393965693096,0.E+000));
#1178 = VECTOR('',#1179,1.);
#1179 = DIRECTION('',(0.E+000,-1.));
#1180 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1181 = PCURVE('',#1182,#1187);
#1182 = PLANE('',#1183);
#1183 = AXIS2_PLACEMENT_3D('',#1184,#1185,#1186);
#1184 = CARTESIAN_POINT('',(17.1432855,-5.36908502,0.E+000));
#1185 = DIRECTION('',(0.881920670078,0.471397848625,-0.E+000));
#1186 = DIRECTION('',(0.471397848625,-0.881920670078,0.E+000));
#1187 = DEFINITIONAL_REPRESENTATION('',(#1188),#1192);
#1188 = LINE('',#1189,#1190);
#1189 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1190 = VECTOR('',#1191,1.);
#1191 = DIRECTION('',(0.E+000,-1.));
#1192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1193 = ORIENTED_EDGE('',*,*,#1194,.F.);
#1194 = EDGE_CURVE('',#1091,#1167,#1195,.T.);
#1195 = SURFACE_CURVE('',#1196,(#1200,#1207),.PCURVE_S1.);
#1196 = LINE('',#1197,#1198);
#1197 = CARTESIAN_POINT('',(17.02892454,-4.99208298,0.E+000));
#1198 = VECTOR('',#1199,1.);
#1199 = DIRECTION('',(0.290281519442,-0.956941293638,0.E+000));
#1200 = PCURVE('',#1106,#1201);
#1201 = DEFINITIONAL_REPRESENTATION('',(#1202),#1206);
#1202 = LINE('',#1203,#1204);
#1203 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1204 = VECTOR('',#1205,1.);
#1205 = DIRECTION('',(1.,0.E+000));
#1206 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1207 = PCURVE('',#137,#1208);
#1208 = DEFINITIONAL_REPRESENTATION('',(#1209),#1213);
#1209 = LINE('',#1210,#1211);
#1210 = CARTESIAN_POINT('',(17.02892454,6.60791668));
#1211 = VECTOR('',#1212,1.);
#1212 = DIRECTION('',(0.290281519442,-0.956941293638));
#1213 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1214 = ADVANCED_FACE('',(#1215),#1182,.F.);
#1215 = FACE_BOUND('',#1216,.F.);
#1216 = EDGE_LOOP('',(#1217,#1218,#1241,#1269));
#1217 = ORIENTED_EDGE('',*,*,#1166,.T.);
#1218 = ORIENTED_EDGE('',*,*,#1219,.T.);
#1219 = EDGE_CURVE('',#1144,#1220,#1222,.T.);
#1220 = VERTEX_POINT('',#1221);
#1221 = CARTESIAN_POINT('',(17.32900014,-5.71653162,1.12192054));
#1222 = SURFACE_CURVE('',#1223,(#1227,#1234),.PCURVE_S1.);
#1223 = LINE('',#1224,#1225);
#1224 = CARTESIAN_POINT('',(17.1432855,-5.36908502,1.12192054));
#1225 = VECTOR('',#1226,1.);
#1226 = DIRECTION('',(0.471397848625,-0.881920670078,0.E+000));
#1227 = PCURVE('',#1182,#1228);
#1228 = DEFINITIONAL_REPRESENTATION('',(#1229),#1233);
#1229 = LINE('',#1230,#1231);
#1230 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1231 = VECTOR('',#1232,1.);
#1232 = DIRECTION('',(1.,0.E+000));
#1233 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1234 = PCURVE('',#83,#1235);
#1235 = DEFINITIONAL_REPRESENTATION('',(#1236),#1240);
#1236 = LINE('',#1237,#1238);
#1237 = CARTESIAN_POINT('',(17.1432855,6.23091464));
#1238 = VECTOR('',#1239,1.);
#1239 = DIRECTION('',(0.471397848625,-0.881920670078));
#1240 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1241 = ORIENTED_EDGE('',*,*,#1242,.F.);
#1242 = EDGE_CURVE('',#1243,#1220,#1245,.T.);
#1243 = VERTEX_POINT('',#1244);
#1244 = CARTESIAN_POINT('',(17.32900014,-5.71653162,0.E+000));
#1245 = SURFACE_CURVE('',#1246,(#1250,#1257),.PCURVE_S1.);
#1246 = LINE('',#1247,#1248);
#1247 = CARTESIAN_POINT('',(17.32900014,-5.71653162,0.E+000));
#1248 = VECTOR('',#1249,1.);
#1249 = DIRECTION('',(0.E+000,0.E+000,1.));
#1250 = PCURVE('',#1182,#1251);
#1251 = DEFINITIONAL_REPRESENTATION('',(#1252),#1256);
#1252 = LINE('',#1253,#1254);
#1253 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#1254 = VECTOR('',#1255,1.);
#1255 = DIRECTION('',(0.E+000,-1.));
#1256 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1257 = PCURVE('',#1258,#1263);
#1258 = PLANE('',#1259);
#1259 = AXIS2_PLACEMENT_3D('',#1260,#1261,#1262);
#1260 = CARTESIAN_POINT('',(17.32900014,-5.71653162,0.E+000));
#1261 = DIRECTION('',(0.773007054486,0.634397425685,-0.E+000));
#1262 = DIRECTION('',(0.634397425685,-0.773007054486,0.E+000));
#1263 = DEFINITIONAL_REPRESENTATION('',(#1264),#1268);
#1264 = LINE('',#1265,#1266);
#1265 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1266 = VECTOR('',#1267,1.);
#1267 = DIRECTION('',(0.E+000,-1.));
#1268 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1269 = ORIENTED_EDGE('',*,*,#1270,.F.);
#1270 = EDGE_CURVE('',#1167,#1243,#1271,.T.);
#1271 = SURFACE_CURVE('',#1272,(#1276,#1283),.PCURVE_S1.);
#1272 = LINE('',#1273,#1274);
#1273 = CARTESIAN_POINT('',(17.1432855,-5.36908502,0.E+000));
#1274 = VECTOR('',#1275,1.);
#1275 = DIRECTION('',(0.471397848625,-0.881920670078,0.E+000));
#1276 = PCURVE('',#1182,#1277);
#1277 = DEFINITIONAL_REPRESENTATION('',(#1278),#1282);
#1278 = LINE('',#1279,#1280);
#1279 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1280 = VECTOR('',#1281,1.);
#1281 = DIRECTION('',(1.,0.E+000));
#1282 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1283 = PCURVE('',#137,#1284);
#1284 = DEFINITIONAL_REPRESENTATION('',(#1285),#1289);
#1285 = LINE('',#1286,#1287);
#1286 = CARTESIAN_POINT('',(17.1432855,6.23091464));
#1287 = VECTOR('',#1288,1.);
#1288 = DIRECTION('',(0.471397848625,-0.881920670078));
#1289 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1290 = ADVANCED_FACE('',(#1291),#1258,.F.);
#1291 = FACE_BOUND('',#1292,.F.);
#1292 = EDGE_LOOP('',(#1293,#1294,#1317,#1345));
#1293 = ORIENTED_EDGE('',*,*,#1242,.T.);
#1294 = ORIENTED_EDGE('',*,*,#1295,.T.);
#1295 = EDGE_CURVE('',#1220,#1296,#1298,.T.);
#1296 = VERTEX_POINT('',#1297);
#1297 = CARTESIAN_POINT('',(17.57893106,-6.02107,1.12192054));
#1298 = SURFACE_CURVE('',#1299,(#1303,#1310),.PCURVE_S1.);
#1299 = LINE('',#1300,#1301);
#1300 = CARTESIAN_POINT('',(17.32900014,-5.71653162,1.12192054));
#1301 = VECTOR('',#1302,1.);
#1302 = DIRECTION('',(0.634397425685,-0.773007054486,0.E+000));
#1303 = PCURVE('',#1258,#1304);
#1304 = DEFINITIONAL_REPRESENTATION('',(#1305),#1309);
#1305 = LINE('',#1306,#1307);
#1306 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1307 = VECTOR('',#1308,1.);
#1308 = DIRECTION('',(1.,0.E+000));
#1309 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1310 = PCURVE('',#83,#1311);
#1311 = DEFINITIONAL_REPRESENTATION('',(#1312),#1316);
#1312 = LINE('',#1313,#1314);
#1313 = CARTESIAN_POINT('',(17.32900014,5.88346804));
#1314 = VECTOR('',#1315,1.);
#1315 = DIRECTION('',(0.634397425685,-0.773007054486));
#1316 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1317 = ORIENTED_EDGE('',*,*,#1318,.F.);
#1318 = EDGE_CURVE('',#1319,#1296,#1321,.T.);
#1319 = VERTEX_POINT('',#1320);
#1320 = CARTESIAN_POINT('',(17.57893106,-6.02107,0.E+000));
#1321 = SURFACE_CURVE('',#1322,(#1326,#1333),.PCURVE_S1.);
#1322 = LINE('',#1323,#1324);
#1323 = CARTESIAN_POINT('',(17.57893106,-6.02107,0.E+000));
#1324 = VECTOR('',#1325,1.);
#1325 = DIRECTION('',(0.E+000,0.E+000,1.));
#1326 = PCURVE('',#1258,#1327);
#1327 = DEFINITIONAL_REPRESENTATION('',(#1328),#1332);
#1328 = LINE('',#1329,#1330);
#1329 = CARTESIAN_POINT('',(0.393965848349,0.E+000));
#1330 = VECTOR('',#1331,1.);
#1331 = DIRECTION('',(0.E+000,-1.));
#1332 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1333 = PCURVE('',#1334,#1339);
#1334 = PLANE('',#1335);
#1335 = AXIS2_PLACEMENT_3D('',#1336,#1337,#1338);
#1336 = CARTESIAN_POINT('',(17.57893106,-6.02107,0.E+000));
#1337 = DIRECTION('',(0.634397425685,0.773007054486,-0.E+000));
#1338 = DIRECTION('',(0.773007054486,-0.634397425685,0.E+000));
#1339 = DEFINITIONAL_REPRESENTATION('',(#1340),#1344);
#1340 = LINE('',#1341,#1342);
#1341 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1342 = VECTOR('',#1343,1.);
#1343 = DIRECTION('',(0.E+000,-1.));
#1344 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1345 = ORIENTED_EDGE('',*,*,#1346,.F.);
#1346 = EDGE_CURVE('',#1243,#1319,#1347,.T.);
#1347 = SURFACE_CURVE('',#1348,(#1352,#1359),.PCURVE_S1.);
#1348 = LINE('',#1349,#1350);
#1349 = CARTESIAN_POINT('',(17.32900014,-5.71653162,0.E+000));
#1350 = VECTOR('',#1351,1.);
#1351 = DIRECTION('',(0.634397425685,-0.773007054486,0.E+000));
#1352 = PCURVE('',#1258,#1353);
#1353 = DEFINITIONAL_REPRESENTATION('',(#1354),#1358);
#1354 = LINE('',#1355,#1356);
#1355 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1356 = VECTOR('',#1357,1.);
#1357 = DIRECTION('',(1.,0.E+000));
#1358 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1359 = PCURVE('',#137,#1360);
#1360 = DEFINITIONAL_REPRESENTATION('',(#1361),#1365);
#1361 = LINE('',#1362,#1363);
#1362 = CARTESIAN_POINT('',(17.32900014,5.88346804));
#1363 = VECTOR('',#1364,1.);
#1364 = DIRECTION('',(0.634397425685,-0.773007054486));
#1365 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1366 = ADVANCED_FACE('',(#1367),#1334,.F.);
#1367 = FACE_BOUND('',#1368,.F.);
#1368 = EDGE_LOOP('',(#1369,#1370,#1393,#1421));
#1369 = ORIENTED_EDGE('',*,*,#1318,.T.);
#1370 = ORIENTED_EDGE('',*,*,#1371,.T.);
#1371 = EDGE_CURVE('',#1296,#1372,#1374,.T.);
#1372 = VERTEX_POINT('',#1373);
#1373 = CARTESIAN_POINT('',(17.88346944,-6.27100092,1.12192054));
#1374 = SURFACE_CURVE('',#1375,(#1379,#1386),.PCURVE_S1.);
#1375 = LINE('',#1376,#1377);
#1376 = CARTESIAN_POINT('',(17.57893106,-6.02107,1.12192054));
#1377 = VECTOR('',#1378,1.);
#1378 = DIRECTION('',(0.773007054486,-0.634397425685,0.E+000));
#1379 = PCURVE('',#1334,#1380);
#1380 = DEFINITIONAL_REPRESENTATION('',(#1381),#1385);
#1381 = LINE('',#1382,#1383);
#1382 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1383 = VECTOR('',#1384,1.);
#1384 = DIRECTION('',(1.,0.E+000));
#1385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1386 = PCURVE('',#83,#1387);
#1387 = DEFINITIONAL_REPRESENTATION('',(#1388),#1392);
#1388 = LINE('',#1389,#1390);
#1389 = CARTESIAN_POINT('',(17.57893106,5.57892966));
#1390 = VECTOR('',#1391,1.);
#1391 = DIRECTION('',(0.773007054486,-0.634397425685));
#1392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1393 = ORIENTED_EDGE('',*,*,#1394,.F.);
#1394 = EDGE_CURVE('',#1395,#1372,#1397,.T.);
#1395 = VERTEX_POINT('',#1396);
#1396 = CARTESIAN_POINT('',(17.88346944,-6.27100092,0.E+000));
#1397 = SURFACE_CURVE('',#1398,(#1402,#1409),.PCURVE_S1.);
#1398 = LINE('',#1399,#1400);
#1399 = CARTESIAN_POINT('',(17.88346944,-6.27100092,0.E+000));
#1400 = VECTOR('',#1401,1.);
#1401 = DIRECTION('',(0.E+000,0.E+000,1.));
#1402 = PCURVE('',#1334,#1403);
#1403 = DEFINITIONAL_REPRESENTATION('',(#1404),#1408);
#1404 = LINE('',#1405,#1406);
#1405 = CARTESIAN_POINT('',(0.393965848349,0.E+000));
#1406 = VECTOR('',#1407,1.);
#1407 = DIRECTION('',(0.E+000,-1.));
#1408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1409 = PCURVE('',#1410,#1415);
#1410 = PLANE('',#1411);
#1411 = AXIS2_PLACEMENT_3D('',#1412,#1413,#1414);
#1412 = CARTESIAN_POINT('',(17.88346944,-6.27100092,0.E+000));
#1413 = DIRECTION('',(0.471397848625,0.881920670078,-0.E+000));
#1414 = DIRECTION('',(0.881920670078,-0.471397848625,0.E+000));
#1415 = DEFINITIONAL_REPRESENTATION('',(#1416),#1420);
#1416 = LINE('',#1417,#1418);
#1417 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1418 = VECTOR('',#1419,1.);
#1419 = DIRECTION('',(0.E+000,-1.));
#1420 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1421 = ORIENTED_EDGE('',*,*,#1422,.F.);
#1422 = EDGE_CURVE('',#1319,#1395,#1423,.T.);
#1423 = SURFACE_CURVE('',#1424,(#1428,#1435),.PCURVE_S1.);
#1424 = LINE('',#1425,#1426);
#1425 = CARTESIAN_POINT('',(17.57893106,-6.02107,0.E+000));
#1426 = VECTOR('',#1427,1.);
#1427 = DIRECTION('',(0.773007054486,-0.634397425685,0.E+000));
#1428 = PCURVE('',#1334,#1429);
#1429 = DEFINITIONAL_REPRESENTATION('',(#1430),#1434);
#1430 = LINE('',#1431,#1432);
#1431 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1432 = VECTOR('',#1433,1.);
#1433 = DIRECTION('',(1.,0.E+000));
#1434 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1435 = PCURVE('',#137,#1436);
#1436 = DEFINITIONAL_REPRESENTATION('',(#1437),#1441);
#1437 = LINE('',#1438,#1439);
#1438 = CARTESIAN_POINT('',(17.57893106,5.57892966));
#1439 = VECTOR('',#1440,1.);
#1440 = DIRECTION('',(0.773007054486,-0.634397425685));
#1441 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1442 = ADVANCED_FACE('',(#1443),#1410,.F.);
#1443 = FACE_BOUND('',#1444,.F.);
#1444 = EDGE_LOOP('',(#1445,#1446,#1469,#1497));
#1445 = ORIENTED_EDGE('',*,*,#1394,.T.);
#1446 = ORIENTED_EDGE('',*,*,#1447,.T.);
#1447 = EDGE_CURVE('',#1372,#1448,#1450,.T.);
#1448 = VERTEX_POINT('',#1449);
#1449 = CARTESIAN_POINT('',(18.23091604,-6.45671556,1.12192054));
#1450 = SURFACE_CURVE('',#1451,(#1455,#1462),.PCURVE_S1.);
#1451 = LINE('',#1452,#1453);
#1452 = CARTESIAN_POINT('',(17.88346944,-6.27100092,1.12192054));
#1453 = VECTOR('',#1454,1.);
#1454 = DIRECTION('',(0.881920670078,-0.471397848625,0.E+000));
#1455 = PCURVE('',#1410,#1456);
#1456 = DEFINITIONAL_REPRESENTATION('',(#1457),#1461);
#1457 = LINE('',#1458,#1459);
#1458 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1459 = VECTOR('',#1460,1.);
#1460 = DIRECTION('',(1.,0.E+000));
#1461 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1462 = PCURVE('',#83,#1463);
#1463 = DEFINITIONAL_REPRESENTATION('',(#1464),#1468);
#1464 = LINE('',#1465,#1466);
#1465 = CARTESIAN_POINT('',(17.88346944,5.32899874));
#1466 = VECTOR('',#1467,1.);
#1467 = DIRECTION('',(0.881920670078,-0.471397848625));
#1468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1469 = ORIENTED_EDGE('',*,*,#1470,.F.);
#1470 = EDGE_CURVE('',#1471,#1448,#1473,.T.);
#1471 = VERTEX_POINT('',#1472);
#1472 = CARTESIAN_POINT('',(18.23091604,-6.45671556,0.E+000));
#1473 = SURFACE_CURVE('',#1474,(#1478,#1485),.PCURVE_S1.);
#1474 = LINE('',#1475,#1476);
#1475 = CARTESIAN_POINT('',(18.23091604,-6.45671556,0.E+000));
#1476 = VECTOR('',#1477,1.);
#1477 = DIRECTION('',(0.E+000,0.E+000,1.));
#1478 = PCURVE('',#1410,#1479);
#1479 = DEFINITIONAL_REPRESENTATION('',(#1480),#1484);
#1480 = LINE('',#1481,#1482);
#1481 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#1482 = VECTOR('',#1483,1.);
#1483 = DIRECTION('',(0.E+000,-1.));
#1484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1485 = PCURVE('',#1486,#1491);
#1486 = PLANE('',#1487);
#1487 = AXIS2_PLACEMENT_3D('',#1488,#1489,#1490);
#1488 = CARTESIAN_POINT('',(18.23091604,-6.45671556,0.E+000));
#1489 = DIRECTION('',(0.290281519442,0.956941293638,-0.E+000));
#1490 = DIRECTION('',(0.956941293638,-0.290281519442,0.E+000));
#1491 = DEFINITIONAL_REPRESENTATION('',(#1492),#1496);
#1492 = LINE('',#1493,#1494);
#1493 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1494 = VECTOR('',#1495,1.);
#1495 = DIRECTION('',(0.E+000,-1.));
#1496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1497 = ORIENTED_EDGE('',*,*,#1498,.F.);
#1498 = EDGE_CURVE('',#1395,#1471,#1499,.T.);
#1499 = SURFACE_CURVE('',#1500,(#1504,#1511),.PCURVE_S1.);
#1500 = LINE('',#1501,#1502);
#1501 = CARTESIAN_POINT('',(17.88346944,-6.27100092,0.E+000));
#1502 = VECTOR('',#1503,1.);
#1503 = DIRECTION('',(0.881920670078,-0.471397848625,0.E+000));
#1504 = PCURVE('',#1410,#1505);
#1505 = DEFINITIONAL_REPRESENTATION('',(#1506),#1510);
#1506 = LINE('',#1507,#1508);
#1507 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1508 = VECTOR('',#1509,1.);
#1509 = DIRECTION('',(1.,0.E+000));
#1510 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1511 = PCURVE('',#137,#1512);
#1512 = DEFINITIONAL_REPRESENTATION('',(#1513),#1517);
#1513 = LINE('',#1514,#1515);
#1514 = CARTESIAN_POINT('',(17.88346944,5.32899874));
#1515 = VECTOR('',#1516,1.);
#1516 = DIRECTION('',(0.881920670078,-0.471397848625));
#1517 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1518 = ADVANCED_FACE('',(#1519),#1486,.F.);
#1519 = FACE_BOUND('',#1520,.F.);
#1520 = EDGE_LOOP('',(#1521,#1522,#1545,#1573));
#1521 = ORIENTED_EDGE('',*,*,#1470,.T.);
#1522 = ORIENTED_EDGE('',*,*,#1523,.T.);
#1523 = EDGE_CURVE('',#1448,#1524,#1526,.T.);
#1524 = VERTEX_POINT('',#1525);
#1525 = CARTESIAN_POINT('',(18.60791808,-6.57107652,1.12192054));
#1526 = SURFACE_CURVE('',#1527,(#1531,#1538),.PCURVE_S1.);
#1527 = LINE('',#1528,#1529);
#1528 = CARTESIAN_POINT('',(18.23091604,-6.45671556,1.12192054));
#1529 = VECTOR('',#1530,1.);
#1530 = DIRECTION('',(0.956941293638,-0.290281519442,0.E+000));
#1531 = PCURVE('',#1486,#1532);
#1532 = DEFINITIONAL_REPRESENTATION('',(#1533),#1537);
#1533 = LINE('',#1534,#1535);
#1534 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1535 = VECTOR('',#1536,1.);
#1536 = DIRECTION('',(1.,0.E+000));
#1537 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1538 = PCURVE('',#83,#1539);
#1539 = DEFINITIONAL_REPRESENTATION('',(#1540),#1544);
#1540 = LINE('',#1541,#1542);
#1541 = CARTESIAN_POINT('',(18.23091604,5.1432841));
#1542 = VECTOR('',#1543,1.);
#1543 = DIRECTION('',(0.956941293638,-0.290281519442));
#1544 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1545 = ORIENTED_EDGE('',*,*,#1546,.F.);
#1546 = EDGE_CURVE('',#1547,#1524,#1549,.T.);
#1547 = VERTEX_POINT('',#1548);
#1548 = CARTESIAN_POINT('',(18.60791808,-6.57107652,0.E+000));
#1549 = SURFACE_CURVE('',#1550,(#1554,#1561),.PCURVE_S1.);
#1550 = LINE('',#1551,#1552);
#1551 = CARTESIAN_POINT('',(18.60791808,-6.57107652,0.E+000));
#1552 = VECTOR('',#1553,1.);
#1553 = DIRECTION('',(0.E+000,0.E+000,1.));
#1554 = PCURVE('',#1486,#1555);
#1555 = DEFINITIONAL_REPRESENTATION('',(#1556),#1560);
#1556 = LINE('',#1557,#1558);
#1557 = CARTESIAN_POINT('',(0.393965693096,0.E+000));
#1558 = VECTOR('',#1559,1.);
#1559 = DIRECTION('',(0.E+000,-1.));
#1560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1561 = PCURVE('',#1562,#1567);
#1562 = PLANE('',#1563);
#1563 = AXIS2_PLACEMENT_3D('',#1564,#1565,#1566);
#1564 = CARTESIAN_POINT('',(18.60791808,-6.57107652,0.E+000));
#1565 = DIRECTION('',(9.801754873774E-002,0.995184686447,-0.E+000));
#1566 = DIRECTION('',(0.995184686447,-9.801754873774E-002,0.E+000));
#1567 = DEFINITIONAL_REPRESENTATION('',(#1568),#1572);
#1568 = LINE('',#1569,#1570);
#1569 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1570 = VECTOR('',#1571,1.);
#1571 = DIRECTION('',(0.E+000,-1.));
#1572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1573 = ORIENTED_EDGE('',*,*,#1574,.F.);
#1574 = EDGE_CURVE('',#1471,#1547,#1575,.T.);
#1575 = SURFACE_CURVE('',#1576,(#1580,#1587),.PCURVE_S1.);
#1576 = LINE('',#1577,#1578);
#1577 = CARTESIAN_POINT('',(18.23091604,-6.45671556,0.E+000));
#1578 = VECTOR('',#1579,1.);
#1579 = DIRECTION('',(0.956941293638,-0.290281519442,0.E+000));
#1580 = PCURVE('',#1486,#1581);
#1581 = DEFINITIONAL_REPRESENTATION('',(#1582),#1586);
#1582 = LINE('',#1583,#1584);
#1583 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1584 = VECTOR('',#1585,1.);
#1585 = DIRECTION('',(1.,0.E+000));
#1586 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1587 = PCURVE('',#137,#1588);
#1588 = DEFINITIONAL_REPRESENTATION('',(#1589),#1593);
#1589 = LINE('',#1590,#1591);
#1590 = CARTESIAN_POINT('',(18.23091604,5.1432841));
#1591 = VECTOR('',#1592,1.);
#1592 = DIRECTION('',(0.956941293638,-0.290281519442));
#1593 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1594 = ADVANCED_FACE('',(#1595),#1562,.F.);
#1595 = FACE_BOUND('',#1596,.F.);
#1596 = EDGE_LOOP('',(#1597,#1598,#1621,#1649));
#1597 = ORIENTED_EDGE('',*,*,#1546,.T.);
#1598 = ORIENTED_EDGE('',*,*,#1599,.T.);
#1599 = EDGE_CURVE('',#1524,#1600,#1602,.T.);
#1600 = VERTEX_POINT('',#1601);
#1601 = CARTESIAN_POINT('',(18.9999874,-6.60969214,1.12192054));
#1602 = SURFACE_CURVE('',#1603,(#1607,#1614),.PCURVE_S1.);
#1603 = LINE('',#1604,#1605);
#1604 = CARTESIAN_POINT('',(18.60791808,-6.57107652,1.12192054));
#1605 = VECTOR('',#1606,1.);
#1606 = DIRECTION('',(0.995184686447,-9.801754873774E-002,0.E+000));
#1607 = PCURVE('',#1562,#1608);
#1608 = DEFINITIONAL_REPRESENTATION('',(#1609),#1613);
#1609 = LINE('',#1610,#1611);
#1610 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1611 = VECTOR('',#1612,1.);
#1612 = DIRECTION('',(1.,0.E+000));
#1613 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1614 = PCURVE('',#83,#1615);
#1615 = DEFINITIONAL_REPRESENTATION('',(#1616),#1620);
#1616 = LINE('',#1617,#1618);
#1617 = CARTESIAN_POINT('',(18.60791808,5.02892314));
#1618 = VECTOR('',#1619,1.);
#1619 = DIRECTION('',(0.995184686447,-9.801754873774E-002));
#1620 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1621 = ORIENTED_EDGE('',*,*,#1622,.F.);
#1622 = EDGE_CURVE('',#1623,#1600,#1625,.T.);
#1623 = VERTEX_POINT('',#1624);
#1624 = CARTESIAN_POINT('',(18.9999874,-6.60969214,0.E+000));
#1625 = SURFACE_CURVE('',#1626,(#1630,#1637),.PCURVE_S1.);
#1626 = LINE('',#1627,#1628);
#1627 = CARTESIAN_POINT('',(18.9999874,-6.60969214,0.E+000));
#1628 = VECTOR('',#1629,1.);
#1629 = DIRECTION('',(0.E+000,0.E+000,1.));
#1630 = PCURVE('',#1562,#1631);
#1631 = DEFINITIONAL_REPRESENTATION('',(#1632),#1636);
#1632 = LINE('',#1633,#1634);
#1633 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#1634 = VECTOR('',#1635,1.);
#1635 = DIRECTION('',(0.E+000,-1.));
#1636 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1637 = PCURVE('',#1638,#1643);
#1638 = PLANE('',#1639);
#1639 = AXIS2_PLACEMENT_3D('',#1640,#1641,#1642);
#1640 = CARTESIAN_POINT('',(18.9999874,-6.60969214,0.E+000));
#1641 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1642 = DIRECTION('',(0.E+000,1.,0.E+000));
#1643 = DEFINITIONAL_REPRESENTATION('',(#1644),#1648);
#1644 = LINE('',#1645,#1646);
#1645 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1646 = VECTOR('',#1647,1.);
#1647 = DIRECTION('',(0.E+000,-1.));
#1648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1649 = ORIENTED_EDGE('',*,*,#1650,.F.);
#1650 = EDGE_CURVE('',#1547,#1623,#1651,.T.);
#1651 = SURFACE_CURVE('',#1652,(#1656,#1663),.PCURVE_S1.);
#1652 = LINE('',#1653,#1654);
#1653 = CARTESIAN_POINT('',(18.60791808,-6.57107652,0.E+000));
#1654 = VECTOR('',#1655,1.);
#1655 = DIRECTION('',(0.995184686447,-9.801754873774E-002,0.E+000));
#1656 = PCURVE('',#1562,#1657);
#1657 = DEFINITIONAL_REPRESENTATION('',(#1658),#1662);
#1658 = LINE('',#1659,#1660);
#1659 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1660 = VECTOR('',#1661,1.);
#1661 = DIRECTION('',(1.,0.E+000));
#1662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1663 = PCURVE('',#137,#1664);
#1664 = DEFINITIONAL_REPRESENTATION('',(#1665),#1669);
#1665 = LINE('',#1666,#1667);
#1666 = CARTESIAN_POINT('',(18.60791808,5.02892314));
#1667 = VECTOR('',#1668,1.);
#1668 = DIRECTION('',(0.995184686447,-9.801754873774E-002));
#1669 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1670 = ADVANCED_FACE('',(#1671),#1638,.F.);
#1671 = FACE_BOUND('',#1672,.F.);
#1672 = EDGE_LOOP('',(#1673,#1674,#1697,#1725));
#1673 = ORIENTED_EDGE('',*,*,#1622,.T.);
#1674 = ORIENTED_EDGE('',*,*,#1675,.T.);
#1675 = EDGE_CURVE('',#1600,#1676,#1678,.T.);
#1676 = VERTEX_POINT('',#1677);
#1677 = CARTESIAN_POINT('',(18.9999874,-6.6000122,1.12192054));
#1678 = SURFACE_CURVE('',#1679,(#1683,#1690),.PCURVE_S1.);
#1679 = LINE('',#1680,#1681);
#1680 = CARTESIAN_POINT('',(18.9999874,-6.60969214,1.12192054));
#1681 = VECTOR('',#1682,1.);
#1682 = DIRECTION('',(0.E+000,1.,0.E+000));
#1683 = PCURVE('',#1638,#1684);
#1684 = DEFINITIONAL_REPRESENTATION('',(#1685),#1689);
#1685 = LINE('',#1686,#1687);
#1686 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1687 = VECTOR('',#1688,1.);
#1688 = DIRECTION('',(1.,0.E+000));
#1689 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1690 = PCURVE('',#83,#1691);
#1691 = DEFINITIONAL_REPRESENTATION('',(#1692),#1696);
#1692 = LINE('',#1693,#1694);
#1693 = CARTESIAN_POINT('',(18.9999874,4.99030752));
#1694 = VECTOR('',#1695,1.);
#1695 = DIRECTION('',(0.E+000,1.));
#1696 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1697 = ORIENTED_EDGE('',*,*,#1698,.F.);
#1698 = EDGE_CURVE('',#1699,#1676,#1701,.T.);
#1699 = VERTEX_POINT('',#1700);
#1700 = CARTESIAN_POINT('',(18.9999874,-6.6000122,0.E+000));
#1701 = SURFACE_CURVE('',#1702,(#1706,#1713),.PCURVE_S1.);
#1702 = LINE('',#1703,#1704);
#1703 = CARTESIAN_POINT('',(18.9999874,-6.6000122,0.E+000));
#1704 = VECTOR('',#1705,1.);
#1705 = DIRECTION('',(0.E+000,0.E+000,1.));
#1706 = PCURVE('',#1638,#1707);
#1707 = DEFINITIONAL_REPRESENTATION('',(#1708),#1712);
#1708 = LINE('',#1709,#1710);
#1709 = CARTESIAN_POINT('',(9.679939999998E-003,0.E+000));
#1710 = VECTOR('',#1711,1.);
#1711 = DIRECTION('',(0.E+000,-1.));
#1712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1713 = PCURVE('',#1714,#1719);
#1714 = PLANE('',#1715);
#1715 = AXIS2_PLACEMENT_3D('',#1716,#1717,#1718);
#1716 = CARTESIAN_POINT('',(18.9999874,-6.6000122,0.E+000));
#1717 = DIRECTION('',(1.935471396762E-003,0.999998126973,-0.E+000));
#1718 = DIRECTION('',(0.999998126973,-1.935471396762E-003,0.E+000));
#1719 = DEFINITIONAL_REPRESENTATION('',(#1720),#1724);
#1720 = LINE('',#1721,#1722);
#1721 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1722 = VECTOR('',#1723,1.);
#1723 = DIRECTION('',(0.E+000,-1.));
#1724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1725 = ORIENTED_EDGE('',*,*,#1726,.F.);
#1726 = EDGE_CURVE('',#1623,#1699,#1727,.T.);
#1727 = SURFACE_CURVE('',#1728,(#1732,#1739),.PCURVE_S1.);
#1728 = LINE('',#1729,#1730);
#1729 = CARTESIAN_POINT('',(18.9999874,-6.60969214,0.E+000));
#1730 = VECTOR('',#1731,1.);
#1731 = DIRECTION('',(0.E+000,1.,0.E+000));
#1732 = PCURVE('',#1638,#1733);
#1733 = DEFINITIONAL_REPRESENTATION('',(#1734),#1738);
#1734 = LINE('',#1735,#1736);
#1735 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1736 = VECTOR('',#1737,1.);
#1737 = DIRECTION('',(1.,0.E+000));
#1738 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1739 = PCURVE('',#137,#1740);
#1740 = DEFINITIONAL_REPRESENTATION('',(#1741),#1745);
#1741 = LINE('',#1742,#1743);
#1742 = CARTESIAN_POINT('',(18.9999874,4.99030752));
#1743 = VECTOR('',#1744,1.);
#1744 = DIRECTION('',(0.E+000,1.));
#1745 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1746 = ADVANCED_FACE('',(#1747),#1714,.F.);
#1747 = FACE_BOUND('',#1748,.F.);
#1748 = EDGE_LOOP('',(#1749,#1750,#1773,#1801));
#1749 = ORIENTED_EDGE('',*,*,#1698,.T.);
#1750 = ORIENTED_EDGE('',*,*,#1751,.T.);
#1751 = EDGE_CURVE('',#1676,#1752,#1754,.T.);
#1752 = VERTEX_POINT('',#1753);
#1753 = CARTESIAN_POINT('',(24.00000026,-6.6096896,1.12192054));
#1754 = SURFACE_CURVE('',#1755,(#1759,#1766),.PCURVE_S1.);
#1755 = LINE('',#1756,#1757);
#1756 = CARTESIAN_POINT('',(18.9999874,-6.6000122,1.12192054));
#1757 = VECTOR('',#1758,1.);
#1758 = DIRECTION('',(0.999998126973,-1.935471396762E-003,0.E+000));
#1759 = PCURVE('',#1714,#1760);
#1760 = DEFINITIONAL_REPRESENTATION('',(#1761),#1765);
#1761 = LINE('',#1762,#1763);
#1762 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1763 = VECTOR('',#1764,1.);
#1764 = DIRECTION('',(1.,0.E+000));
#1765 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1766 = PCURVE('',#83,#1767);
#1767 = DEFINITIONAL_REPRESENTATION('',(#1768),#1772);
#1768 = LINE('',#1769,#1770);
#1769 = CARTESIAN_POINT('',(18.9999874,4.99998746));
#1770 = VECTOR('',#1771,1.);
#1771 = DIRECTION('',(0.999998126973,-1.935471396762E-003));
#1772 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1773 = ORIENTED_EDGE('',*,*,#1774,.F.);
#1774 = EDGE_CURVE('',#1775,#1752,#1777,.T.);
#1775 = VERTEX_POINT('',#1776);
#1776 = CARTESIAN_POINT('',(24.00000026,-6.6096896,0.E+000));
#1777 = SURFACE_CURVE('',#1778,(#1782,#1789),.PCURVE_S1.);
#1778 = LINE('',#1779,#1780);
#1779 = CARTESIAN_POINT('',(24.00000026,-6.6096896,0.E+000));
#1780 = VECTOR('',#1781,1.);
#1781 = DIRECTION('',(0.E+000,0.E+000,1.));
#1782 = PCURVE('',#1714,#1783);
#1783 = DEFINITIONAL_REPRESENTATION('',(#1784),#1788);
#1784 = LINE('',#1785,#1786);
#1785 = CARTESIAN_POINT('',(5.000022225174,0.E+000));
#1786 = VECTOR('',#1787,1.);
#1787 = DIRECTION('',(0.E+000,-1.));
#1788 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1789 = PCURVE('',#1790,#1795);
#1790 = PLANE('',#1791);
#1791 = AXIS2_PLACEMENT_3D('',#1792,#1793,#1794);
#1792 = CARTESIAN_POINT('',(24.00000026,-6.6096896,0.E+000));
#1793 = DIRECTION('',(-9.801754873774E-002,0.995184686447,0.E+000));
#1794 = DIRECTION('',(0.995184686447,9.801754873774E-002,0.E+000));
#1795 = DEFINITIONAL_REPRESENTATION('',(#1796),#1800);
#1796 = LINE('',#1797,#1798);
#1797 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1798 = VECTOR('',#1799,1.);
#1799 = DIRECTION('',(0.E+000,-1.));
#1800 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1801 = ORIENTED_EDGE('',*,*,#1802,.F.);
#1802 = EDGE_CURVE('',#1699,#1775,#1803,.T.);
#1803 = SURFACE_CURVE('',#1804,(#1808,#1815),.PCURVE_S1.);
#1804 = LINE('',#1805,#1806);
#1805 = CARTESIAN_POINT('',(18.9999874,-6.6000122,0.E+000));
#1806 = VECTOR('',#1807,1.);
#1807 = DIRECTION('',(0.999998126973,-1.935471396762E-003,0.E+000));
#1808 = PCURVE('',#1714,#1809);
#1809 = DEFINITIONAL_REPRESENTATION('',(#1810),#1814);
#1810 = LINE('',#1811,#1812);
#1811 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1812 = VECTOR('',#1813,1.);
#1813 = DIRECTION('',(1.,0.E+000));
#1814 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1815 = PCURVE('',#137,#1816);
#1816 = DEFINITIONAL_REPRESENTATION('',(#1817),#1821);
#1817 = LINE('',#1818,#1819);
#1818 = CARTESIAN_POINT('',(18.9999874,4.99998746));
#1819 = VECTOR('',#1820,1.);
#1820 = DIRECTION('',(0.999998126973,-1.935471396762E-003));
#1821 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1822 = ADVANCED_FACE('',(#1823),#1790,.F.);
#1823 = FACE_BOUND('',#1824,.F.);
#1824 = EDGE_LOOP('',(#1825,#1826,#1849,#1877));
#1825 = ORIENTED_EDGE('',*,*,#1774,.T.);
#1826 = ORIENTED_EDGE('',*,*,#1827,.T.);
#1827 = EDGE_CURVE('',#1752,#1828,#1830,.T.);
#1828 = VERTEX_POINT('',#1829);
#1829 = CARTESIAN_POINT('',(24.39206958,-6.57107398,1.12192054));
#1830 = SURFACE_CURVE('',#1831,(#1835,#1842),.PCURVE_S1.);
#1831 = LINE('',#1832,#1833);
#1832 = CARTESIAN_POINT('',(24.00000026,-6.6096896,1.12192054));
#1833 = VECTOR('',#1834,1.);
#1834 = DIRECTION('',(0.995184686447,9.801754873774E-002,0.E+000));
#1835 = PCURVE('',#1790,#1836);
#1836 = DEFINITIONAL_REPRESENTATION('',(#1837),#1841);
#1837 = LINE('',#1838,#1839);
#1838 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1839 = VECTOR('',#1840,1.);
#1840 = DIRECTION('',(1.,0.E+000));
#1841 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1842 = PCURVE('',#83,#1843);
#1843 = DEFINITIONAL_REPRESENTATION('',(#1844),#1848);
#1844 = LINE('',#1845,#1846);
#1845 = CARTESIAN_POINT('',(24.00000026,4.99031006));
#1846 = VECTOR('',#1847,1.);
#1847 = DIRECTION('',(0.995184686447,9.801754873774E-002));
#1848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1849 = ORIENTED_EDGE('',*,*,#1850,.F.);
#1850 = EDGE_CURVE('',#1851,#1828,#1853,.T.);
#1851 = VERTEX_POINT('',#1852);
#1852 = CARTESIAN_POINT('',(24.39206958,-6.57107398,0.E+000));
#1853 = SURFACE_CURVE('',#1854,(#1858,#1865),.PCURVE_S1.);
#1854 = LINE('',#1855,#1856);
#1855 = CARTESIAN_POINT('',(24.39206958,-6.57107398,0.E+000));
#1856 = VECTOR('',#1857,1.);
#1857 = DIRECTION('',(0.E+000,0.E+000,1.));
#1858 = PCURVE('',#1790,#1859);
#1859 = DEFINITIONAL_REPRESENTATION('',(#1860),#1864);
#1860 = LINE('',#1861,#1862);
#1861 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#1862 = VECTOR('',#1863,1.);
#1863 = DIRECTION('',(0.E+000,-1.));
#1864 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1865 = PCURVE('',#1866,#1871);
#1866 = PLANE('',#1867);
#1867 = AXIS2_PLACEMENT_3D('',#1868,#1869,#1870);
#1868 = CARTESIAN_POINT('',(24.39206958,-6.57107398,0.E+000));
#1869 = DIRECTION('',(-0.290283310389,0.956940750365,0.E+000));
#1870 = DIRECTION('',(0.956940750365,0.290283310389,0.E+000));
#1871 = DEFINITIONAL_REPRESENTATION('',(#1872),#1876);
#1872 = LINE('',#1873,#1874);
#1873 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1874 = VECTOR('',#1875,1.);
#1875 = DIRECTION('',(0.E+000,-1.));
#1876 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1877 = ORIENTED_EDGE('',*,*,#1878,.F.);
#1878 = EDGE_CURVE('',#1775,#1851,#1879,.T.);
#1879 = SURFACE_CURVE('',#1880,(#1884,#1891),.PCURVE_S1.);
#1880 = LINE('',#1881,#1882);
#1881 = CARTESIAN_POINT('',(24.00000026,-6.6096896,0.E+000));
#1882 = VECTOR('',#1883,1.);
#1883 = DIRECTION('',(0.995184686447,9.801754873774E-002,0.E+000));
#1884 = PCURVE('',#1790,#1885);
#1885 = DEFINITIONAL_REPRESENTATION('',(#1886),#1890);
#1886 = LINE('',#1887,#1888);
#1887 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1888 = VECTOR('',#1889,1.);
#1889 = DIRECTION('',(1.,0.E+000));
#1890 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1891 = PCURVE('',#137,#1892);
#1892 = DEFINITIONAL_REPRESENTATION('',(#1893),#1897);
#1893 = LINE('',#1894,#1895);
#1894 = CARTESIAN_POINT('',(24.00000026,4.99031006));
#1895 = VECTOR('',#1896,1.);
#1896 = DIRECTION('',(0.995184686447,9.801754873774E-002));
#1897 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1898 = ADVANCED_FACE('',(#1899),#1866,.F.);
#1899 = FACE_BOUND('',#1900,.F.);
#1900 = EDGE_LOOP('',(#1901,#1902,#1925,#1953));
#1901 = ORIENTED_EDGE('',*,*,#1850,.T.);
#1902 = ORIENTED_EDGE('',*,*,#1903,.T.);
#1903 = EDGE_CURVE('',#1828,#1904,#1906,.T.);
#1904 = VERTEX_POINT('',#1905);
#1905 = CARTESIAN_POINT('',(24.76906908,-6.45671302,1.12192054));
#1906 = SURFACE_CURVE('',#1907,(#1911,#1918),.PCURVE_S1.);
#1907 = LINE('',#1908,#1909);
#1908 = CARTESIAN_POINT('',(24.39206958,-6.57107398,1.12192054));
#1909 = VECTOR('',#1910,1.);
#1910 = DIRECTION('',(0.956940750365,0.290283310389,0.E+000));
#1911 = PCURVE('',#1866,#1912);
#1912 = DEFINITIONAL_REPRESENTATION('',(#1913),#1917);
#1913 = LINE('',#1914,#1915);
#1914 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1915 = VECTOR('',#1916,1.);
#1916 = DIRECTION('',(1.,0.E+000));
#1917 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1918 = PCURVE('',#83,#1919);
#1919 = DEFINITIONAL_REPRESENTATION('',(#1920),#1924);
#1920 = LINE('',#1921,#1922);
#1921 = CARTESIAN_POINT('',(24.39206958,5.02892568));
#1922 = VECTOR('',#1923,1.);
#1923 = DIRECTION('',(0.956940750365,0.290283310389));
#1924 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1925 = ORIENTED_EDGE('',*,*,#1926,.F.);
#1926 = EDGE_CURVE('',#1927,#1904,#1929,.T.);
#1927 = VERTEX_POINT('',#1928);
#1928 = CARTESIAN_POINT('',(24.76906908,-6.45671302,0.E+000));
#1929 = SURFACE_CURVE('',#1930,(#1934,#1941),.PCURVE_S1.);
#1930 = LINE('',#1931,#1932);
#1931 = CARTESIAN_POINT('',(24.76906908,-6.45671302,0.E+000));
#1932 = VECTOR('',#1933,1.);
#1933 = DIRECTION('',(0.E+000,0.E+000,1.));
#1934 = PCURVE('',#1866,#1935);
#1935 = DEFINITIONAL_REPRESENTATION('',(#1936),#1940);
#1936 = LINE('',#1937,#1938);
#1937 = CARTESIAN_POINT('',(0.393963262465,0.E+000));
#1938 = VECTOR('',#1939,1.);
#1939 = DIRECTION('',(0.E+000,-1.));
#1940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1941 = PCURVE('',#1942,#1947);
#1942 = PLANE('',#1943);
#1943 = AXIS2_PLACEMENT_3D('',#1944,#1945,#1946);
#1944 = CARTESIAN_POINT('',(24.76906908,-6.45671302,0.E+000));
#1945 = DIRECTION('',(-0.471397848625,0.881920670078,0.E+000));
#1946 = DIRECTION('',(0.881920670078,0.471397848625,0.E+000));
#1947 = DEFINITIONAL_REPRESENTATION('',(#1948),#1952);
#1948 = LINE('',#1949,#1950);
#1949 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1950 = VECTOR('',#1951,1.);
#1951 = DIRECTION('',(0.E+000,-1.));
#1952 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1953 = ORIENTED_EDGE('',*,*,#1954,.F.);
#1954 = EDGE_CURVE('',#1851,#1927,#1955,.T.);
#1955 = SURFACE_CURVE('',#1956,(#1960,#1967),.PCURVE_S1.);
#1956 = LINE('',#1957,#1958);
#1957 = CARTESIAN_POINT('',(24.39206958,-6.57107398,0.E+000));
#1958 = VECTOR('',#1959,1.);
#1959 = DIRECTION('',(0.956940750365,0.290283310389,0.E+000));
#1960 = PCURVE('',#1866,#1961);
#1961 = DEFINITIONAL_REPRESENTATION('',(#1962),#1966);
#1962 = LINE('',#1963,#1964);
#1963 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1964 = VECTOR('',#1965,1.);
#1965 = DIRECTION('',(1.,0.E+000));
#1966 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1967 = PCURVE('',#137,#1968);
#1968 = DEFINITIONAL_REPRESENTATION('',(#1969),#1973);
#1969 = LINE('',#1970,#1971);
#1970 = CARTESIAN_POINT('',(24.39206958,5.02892568));
#1971 = VECTOR('',#1972,1.);
#1972 = DIRECTION('',(0.956940750365,0.290283310389));
#1973 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1974 = ADVANCED_FACE('',(#1975),#1942,.F.);
#1975 = FACE_BOUND('',#1976,.F.);
#1976 = EDGE_LOOP('',(#1977,#1978,#2001,#2029));
#1977 = ORIENTED_EDGE('',*,*,#1926,.T.);
#1978 = ORIENTED_EDGE('',*,*,#1979,.T.);
#1979 = EDGE_CURVE('',#1904,#1980,#1982,.T.);
#1980 = VERTEX_POINT('',#1981);
#1981 = CARTESIAN_POINT('',(25.11651568,-6.27099838,1.12192054));
#1982 = SURFACE_CURVE('',#1983,(#1987,#1994),.PCURVE_S1.);
#1983 = LINE('',#1984,#1985);
#1984 = CARTESIAN_POINT('',(24.76906908,-6.45671302,1.12192054));
#1985 = VECTOR('',#1986,1.);
#1986 = DIRECTION('',(0.881920670078,0.471397848625,0.E+000));
#1987 = PCURVE('',#1942,#1988);
#1988 = DEFINITIONAL_REPRESENTATION('',(#1989),#1993);
#1989 = LINE('',#1990,#1991);
#1990 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#1991 = VECTOR('',#1992,1.);
#1992 = DIRECTION('',(1.,0.E+000));
#1993 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1994 = PCURVE('',#83,#1995);
#1995 = DEFINITIONAL_REPRESENTATION('',(#1996),#2000);
#1996 = LINE('',#1997,#1998);
#1997 = CARTESIAN_POINT('',(24.76906908,5.14328664));
#1998 = VECTOR('',#1999,1.);
#1999 = DIRECTION('',(0.881920670078,0.471397848625));
#2000 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2001 = ORIENTED_EDGE('',*,*,#2002,.F.);
#2002 = EDGE_CURVE('',#2003,#1980,#2005,.T.);
#2003 = VERTEX_POINT('',#2004);
#2004 = CARTESIAN_POINT('',(25.11651568,-6.27099838,0.E+000));
#2005 = SURFACE_CURVE('',#2006,(#2010,#2017),.PCURVE_S1.);
#2006 = LINE('',#2007,#2008);
#2007 = CARTESIAN_POINT('',(25.11651568,-6.27099838,0.E+000));
#2008 = VECTOR('',#2009,1.);
#2009 = DIRECTION('',(0.E+000,0.E+000,1.));
#2010 = PCURVE('',#1942,#2011);
#2011 = DEFINITIONAL_REPRESENTATION('',(#2012),#2016);
#2012 = LINE('',#2013,#2014);
#2013 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#2014 = VECTOR('',#2015,1.);
#2015 = DIRECTION('',(0.E+000,-1.));
#2016 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2017 = PCURVE('',#2018,#2023);
#2018 = PLANE('',#2019);
#2019 = AXIS2_PLACEMENT_3D('',#2020,#2021,#2022);
#2020 = CARTESIAN_POINT('',(25.11651568,-6.27099838,0.E+000));
#2021 = DIRECTION('',(-0.634390411475,0.773012810909,0.E+000));
#2022 = DIRECTION('',(0.773012810909,0.634390411475,0.E+000));
#2023 = DEFINITIONAL_REPRESENTATION('',(#2024),#2028);
#2024 = LINE('',#2025,#2026);
#2025 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2026 = VECTOR('',#2027,1.);
#2027 = DIRECTION('',(0.E+000,-1.));
#2028 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2029 = ORIENTED_EDGE('',*,*,#2030,.F.);
#2030 = EDGE_CURVE('',#1927,#2003,#2031,.T.);
#2031 = SURFACE_CURVE('',#2032,(#2036,#2043),.PCURVE_S1.);
#2032 = LINE('',#2033,#2034);
#2033 = CARTESIAN_POINT('',(24.76906908,-6.45671302,0.E+000));
#2034 = VECTOR('',#2035,1.);
#2035 = DIRECTION('',(0.881920670078,0.471397848625,0.E+000));
#2036 = PCURVE('',#1942,#2037);
#2037 = DEFINITIONAL_REPRESENTATION('',(#2038),#2042);
#2038 = LINE('',#2039,#2040);
#2039 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2040 = VECTOR('',#2041,1.);
#2041 = DIRECTION('',(1.,0.E+000));
#2042 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2043 = PCURVE('',#137,#2044);
#2044 = DEFINITIONAL_REPRESENTATION('',(#2045),#2049);
#2045 = LINE('',#2046,#2047);
#2046 = CARTESIAN_POINT('',(24.76906908,5.14328664));
#2047 = VECTOR('',#2048,1.);
#2048 = DIRECTION('',(0.881920670078,0.471397848625));
#2049 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2050 = ADVANCED_FACE('',(#2051),#2018,.F.);
#2051 = FACE_BOUND('',#2052,.F.);
#2052 = EDGE_LOOP('',(#2053,#2054,#2077,#2105));
#2053 = ORIENTED_EDGE('',*,*,#2002,.T.);
#2054 = ORIENTED_EDGE('',*,*,#2055,.T.);
#2055 = EDGE_CURVE('',#1980,#2056,#2058,.T.);
#2056 = VERTEX_POINT('',#2057);
#2057 = CARTESIAN_POINT('',(25.4210566,-6.02107,1.12192054));
#2058 = SURFACE_CURVE('',#2059,(#2063,#2070),.PCURVE_S1.);
#2059 = LINE('',#2060,#2061);
#2060 = CARTESIAN_POINT('',(25.11651568,-6.27099838,1.12192054));
#2061 = VECTOR('',#2062,1.);
#2062 = DIRECTION('',(0.773012810909,0.634390411475,0.E+000));
#2063 = PCURVE('',#2018,#2064);
#2064 = DEFINITIONAL_REPRESENTATION('',(#2065),#2069);
#2065 = LINE('',#2066,#2067);
#2066 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2067 = VECTOR('',#2068,1.);
#2068 = DIRECTION('',(1.,0.E+000));
#2069 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2070 = PCURVE('',#83,#2071);
#2071 = DEFINITIONAL_REPRESENTATION('',(#2072),#2076);
#2072 = LINE('',#2073,#2074);
#2073 = CARTESIAN_POINT('',(25.11651568,5.32900128));
#2074 = VECTOR('',#2075,1.);
#2075 = DIRECTION('',(0.773012810909,0.634390411475));
#2076 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2077 = ORIENTED_EDGE('',*,*,#2078,.F.);
#2078 = EDGE_CURVE('',#2079,#2056,#2081,.T.);
#2079 = VERTEX_POINT('',#2080);
#2080 = CARTESIAN_POINT('',(25.4210566,-6.02107,0.E+000));
#2081 = SURFACE_CURVE('',#2082,(#2086,#2093),.PCURVE_S1.);
#2082 = LINE('',#2083,#2084);
#2083 = CARTESIAN_POINT('',(25.4210566,-6.02107,0.E+000));
#2084 = VECTOR('',#2085,1.);
#2085 = DIRECTION('',(0.E+000,0.E+000,1.));
#2086 = PCURVE('',#2018,#2087);
#2087 = DEFINITIONAL_REPRESENTATION('',(#2088),#2092);
#2088 = LINE('',#2089,#2090);
#2089 = CARTESIAN_POINT('',(0.393966200433,0.E+000));
#2090 = VECTOR('',#2091,1.);
#2091 = DIRECTION('',(0.E+000,-1.));
#2092 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2093 = PCURVE('',#2094,#2099);
#2094 = PLANE('',#2095);
#2095 = AXIS2_PLACEMENT_3D('',#2096,#2097,#2098);
#2096 = CARTESIAN_POINT('',(25.4210566,-6.02107,0.E+000));
#2097 = DIRECTION('',(-0.773012810909,0.634390411475,0.E+000));
#2098 = DIRECTION('',(0.634390411475,0.773012810909,0.E+000));
#2099 = DEFINITIONAL_REPRESENTATION('',(#2100),#2104);
#2100 = LINE('',#2101,#2102);
#2101 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2102 = VECTOR('',#2103,1.);
#2103 = DIRECTION('',(0.E+000,-1.));
#2104 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2105 = ORIENTED_EDGE('',*,*,#2106,.F.);
#2106 = EDGE_CURVE('',#2003,#2079,#2107,.T.);
#2107 = SURFACE_CURVE('',#2108,(#2112,#2119),.PCURVE_S1.);
#2108 = LINE('',#2109,#2110);
#2109 = CARTESIAN_POINT('',(25.11651568,-6.27099838,0.E+000));
#2110 = VECTOR('',#2111,1.);
#2111 = DIRECTION('',(0.773012810909,0.634390411475,0.E+000));
#2112 = PCURVE('',#2018,#2113);
#2113 = DEFINITIONAL_REPRESENTATION('',(#2114),#2118);
#2114 = LINE('',#2115,#2116);
#2115 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2116 = VECTOR('',#2117,1.);
#2117 = DIRECTION('',(1.,0.E+000));
#2118 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2119 = PCURVE('',#137,#2120);
#2120 = DEFINITIONAL_REPRESENTATION('',(#2121),#2125);
#2121 = LINE('',#2122,#2123);
#2122 = CARTESIAN_POINT('',(25.11651568,5.32900128));
#2123 = VECTOR('',#2124,1.);
#2124 = DIRECTION('',(0.773012810909,0.634390411475));
#2125 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2126 = ADVANCED_FACE('',(#2127),#2094,.F.);
#2127 = FACE_BOUND('',#2128,.F.);
#2128 = EDGE_LOOP('',(#2129,#2130,#2153,#2181));
#2129 = ORIENTED_EDGE('',*,*,#2078,.T.);
#2130 = ORIENTED_EDGE('',*,*,#2131,.T.);
#2131 = EDGE_CURVE('',#2056,#2132,#2134,.T.);
#2132 = VERTEX_POINT('',#2133);
#2133 = CARTESIAN_POINT('',(25.67098498,-5.71652908,1.12192054));
#2134 = SURFACE_CURVE('',#2135,(#2139,#2146),.PCURVE_S1.);
#2135 = LINE('',#2136,#2137);
#2136 = CARTESIAN_POINT('',(25.4210566,-6.02107,1.12192054));
#2137 = VECTOR('',#2138,1.);
#2138 = DIRECTION('',(0.634390411475,0.773012810909,0.E+000));
#2139 = PCURVE('',#2094,#2140);
#2140 = DEFINITIONAL_REPRESENTATION('',(#2141),#2145);
#2141 = LINE('',#2142,#2143);
#2142 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2143 = VECTOR('',#2144,1.);
#2144 = DIRECTION('',(1.,0.E+000));
#2145 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2146 = PCURVE('',#83,#2147);
#2147 = DEFINITIONAL_REPRESENTATION('',(#2148),#2152);
#2148 = LINE('',#2149,#2150);
#2149 = CARTESIAN_POINT('',(25.4210566,5.57892966));
#2150 = VECTOR('',#2151,1.);
#2151 = DIRECTION('',(0.634390411475,0.773012810909));
#2152 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2153 = ORIENTED_EDGE('',*,*,#2154,.F.);
#2154 = EDGE_CURVE('',#2155,#2132,#2157,.T.);
#2155 = VERTEX_POINT('',#2156);
#2156 = CARTESIAN_POINT('',(25.67098498,-5.71652908,0.E+000));
#2157 = SURFACE_CURVE('',#2158,(#2162,#2169),.PCURVE_S1.);
#2158 = LINE('',#2159,#2160);
#2159 = CARTESIAN_POINT('',(25.67098498,-5.71652908,0.E+000));
#2160 = VECTOR('',#2161,1.);
#2161 = DIRECTION('',(0.E+000,0.E+000,1.));
#2162 = PCURVE('',#2094,#2163);
#2163 = DEFINITIONAL_REPRESENTATION('',(#2164),#2168);
#2164 = LINE('',#2165,#2166);
#2165 = CARTESIAN_POINT('',(0.393966200433,0.E+000));
#2166 = VECTOR('',#2167,1.);
#2167 = DIRECTION('',(0.E+000,-1.));
#2168 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2169 = PCURVE('',#2170,#2175);
#2170 = PLANE('',#2171);
#2171 = AXIS2_PLACEMENT_3D('',#2172,#2173,#2174);
#2172 = CARTESIAN_POINT('',(25.67098498,-5.71652908,0.E+000));
#2173 = DIRECTION('',(-0.881920670078,0.471397848625,0.E+000));
#2174 = DIRECTION('',(0.471397848625,0.881920670078,0.E+000));
#2175 = DEFINITIONAL_REPRESENTATION('',(#2176),#2180);
#2176 = LINE('',#2177,#2178);
#2177 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2178 = VECTOR('',#2179,1.);
#2179 = DIRECTION('',(0.E+000,-1.));
#2180 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2181 = ORIENTED_EDGE('',*,*,#2182,.F.);
#2182 = EDGE_CURVE('',#2079,#2155,#2183,.T.);
#2183 = SURFACE_CURVE('',#2184,(#2188,#2195),.PCURVE_S1.);
#2184 = LINE('',#2185,#2186);
#2185 = CARTESIAN_POINT('',(25.4210566,-6.02107,0.E+000));
#2186 = VECTOR('',#2187,1.);
#2187 = DIRECTION('',(0.634390411475,0.773012810909,0.E+000));
#2188 = PCURVE('',#2094,#2189);
#2189 = DEFINITIONAL_REPRESENTATION('',(#2190),#2194);
#2190 = LINE('',#2191,#2192);
#2191 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2192 = VECTOR('',#2193,1.);
#2193 = DIRECTION('',(1.,0.E+000));
#2194 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2195 = PCURVE('',#137,#2196);
#2196 = DEFINITIONAL_REPRESENTATION('',(#2197),#2201);
#2197 = LINE('',#2198,#2199);
#2198 = CARTESIAN_POINT('',(25.4210566,5.57892966));
#2199 = VECTOR('',#2200,1.);
#2200 = DIRECTION('',(0.634390411475,0.773012810909));
#2201 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2202 = ADVANCED_FACE('',(#2203),#2170,.F.);
#2203 = FACE_BOUND('',#2204,.F.);
#2204 = EDGE_LOOP('',(#2205,#2206,#2229,#2257));
#2205 = ORIENTED_EDGE('',*,*,#2154,.T.);
#2206 = ORIENTED_EDGE('',*,*,#2207,.T.);
#2207 = EDGE_CURVE('',#2132,#2208,#2210,.T.);
#2208 = VERTEX_POINT('',#2209);
#2209 = CARTESIAN_POINT('',(25.85669962,-5.36908248,1.12192054));
#2210 = SURFACE_CURVE('',#2211,(#2215,#2222),.PCURVE_S1.);
#2211 = LINE('',#2212,#2213);
#2212 = CARTESIAN_POINT('',(25.67098498,-5.71652908,1.12192054));
#2213 = VECTOR('',#2214,1.);
#2214 = DIRECTION('',(0.471397848625,0.881920670078,0.E+000));
#2215 = PCURVE('',#2170,#2216);
#2216 = DEFINITIONAL_REPRESENTATION('',(#2217),#2221);
#2217 = LINE('',#2218,#2219);
#2218 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2219 = VECTOR('',#2220,1.);
#2220 = DIRECTION('',(1.,0.E+000));
#2221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2222 = PCURVE('',#83,#2223);
#2223 = DEFINITIONAL_REPRESENTATION('',(#2224),#2228);
#2224 = LINE('',#2225,#2226);
#2225 = CARTESIAN_POINT('',(25.67098498,5.88347058));
#2226 = VECTOR('',#2227,1.);
#2227 = DIRECTION('',(0.471397848625,0.881920670078));
#2228 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2229 = ORIENTED_EDGE('',*,*,#2230,.F.);
#2230 = EDGE_CURVE('',#2231,#2208,#2233,.T.);
#2231 = VERTEX_POINT('',#2232);
#2232 = CARTESIAN_POINT('',(25.85669962,-5.36908248,0.E+000));
#2233 = SURFACE_CURVE('',#2234,(#2238,#2245),.PCURVE_S1.);
#2234 = LINE('',#2235,#2236);
#2235 = CARTESIAN_POINT('',(25.85669962,-5.36908248,0.E+000));
#2236 = VECTOR('',#2237,1.);
#2237 = DIRECTION('',(0.E+000,0.E+000,1.));
#2238 = PCURVE('',#2170,#2239);
#2239 = DEFINITIONAL_REPRESENTATION('',(#2240),#2244);
#2240 = LINE('',#2241,#2242);
#2241 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#2242 = VECTOR('',#2243,1.);
#2243 = DIRECTION('',(0.E+000,-1.));
#2244 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2245 = PCURVE('',#2246,#2251);
#2246 = PLANE('',#2247);
#2247 = AXIS2_PLACEMENT_3D('',#2248,#2249,#2250);
#2248 = CARTESIAN_POINT('',(25.85669962,-5.36908248,0.E+000));
#2249 = DIRECTION('',(-0.956940750365,0.290283310389,0.E+000));
#2250 = DIRECTION('',(0.290283310389,0.956940750365,0.E+000));
#2251 = DEFINITIONAL_REPRESENTATION('',(#2252),#2256);
#2252 = LINE('',#2253,#2254);
#2253 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2254 = VECTOR('',#2255,1.);
#2255 = DIRECTION('',(0.E+000,-1.));
#2256 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2257 = ORIENTED_EDGE('',*,*,#2258,.F.);
#2258 = EDGE_CURVE('',#2155,#2231,#2259,.T.);
#2259 = SURFACE_CURVE('',#2260,(#2264,#2271),.PCURVE_S1.);
#2260 = LINE('',#2261,#2262);
#2261 = CARTESIAN_POINT('',(25.67098498,-5.71652908,0.E+000));
#2262 = VECTOR('',#2263,1.);
#2263 = DIRECTION('',(0.471397848625,0.881920670078,0.E+000));
#2264 = PCURVE('',#2170,#2265);
#2265 = DEFINITIONAL_REPRESENTATION('',(#2266),#2270);
#2266 = LINE('',#2267,#2268);
#2267 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2268 = VECTOR('',#2269,1.);
#2269 = DIRECTION('',(1.,0.E+000));
#2270 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2271 = PCURVE('',#137,#2272);
#2272 = DEFINITIONAL_REPRESENTATION('',(#2273),#2277);
#2273 = LINE('',#2274,#2275);
#2274 = CARTESIAN_POINT('',(25.67098498,5.88347058));
#2275 = VECTOR('',#2276,1.);
#2276 = DIRECTION('',(0.471397848625,0.881920670078));
#2277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2278 = ADVANCED_FACE('',(#2279),#2246,.F.);
#2279 = FACE_BOUND('',#2280,.F.);
#2280 = EDGE_LOOP('',(#2281,#2282,#2305,#2333));
#2281 = ORIENTED_EDGE('',*,*,#2230,.T.);
#2282 = ORIENTED_EDGE('',*,*,#2283,.T.);
#2283 = EDGE_CURVE('',#2208,#2284,#2286,.T.);
#2284 = VERTEX_POINT('',#2285);
#2285 = CARTESIAN_POINT('',(25.97106058,-4.99208298,1.12192054));
#2286 = SURFACE_CURVE('',#2287,(#2291,#2298),.PCURVE_S1.);
#2287 = LINE('',#2288,#2289);
#2288 = CARTESIAN_POINT('',(25.85669962,-5.36908248,1.12192054));
#2289 = VECTOR('',#2290,1.);
#2290 = DIRECTION('',(0.290283310389,0.956940750365,0.E+000));
#2291 = PCURVE('',#2246,#2292);
#2292 = DEFINITIONAL_REPRESENTATION('',(#2293),#2297);
#2293 = LINE('',#2294,#2295);
#2294 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2295 = VECTOR('',#2296,1.);
#2296 = DIRECTION('',(1.,0.E+000));
#2297 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2298 = PCURVE('',#83,#2299);
#2299 = DEFINITIONAL_REPRESENTATION('',(#2300),#2304);
#2300 = LINE('',#2301,#2302);
#2301 = CARTESIAN_POINT('',(25.85669962,6.23091718));
#2302 = VECTOR('',#2303,1.);
#2303 = DIRECTION('',(0.290283310389,0.956940750365));
#2304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2305 = ORIENTED_EDGE('',*,*,#2306,.F.);
#2306 = EDGE_CURVE('',#2307,#2284,#2309,.T.);
#2307 = VERTEX_POINT('',#2308);
#2308 = CARTESIAN_POINT('',(25.97106058,-4.99208298,0.E+000));
#2309 = SURFACE_CURVE('',#2310,(#2314,#2321),.PCURVE_S1.);
#2310 = LINE('',#2311,#2312);
#2311 = CARTESIAN_POINT('',(25.97106058,-4.99208298,0.E+000));
#2312 = VECTOR('',#2313,1.);
#2313 = DIRECTION('',(0.E+000,0.E+000,1.));
#2314 = PCURVE('',#2246,#2315);
#2315 = DEFINITIONAL_REPRESENTATION('',(#2316),#2320);
#2316 = LINE('',#2317,#2318);
#2317 = CARTESIAN_POINT('',(0.393963262465,0.E+000));
#2318 = VECTOR('',#2319,1.);
#2319 = DIRECTION('',(0.E+000,-1.));
#2320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2321 = PCURVE('',#2322,#2327);
#2322 = PLANE('',#2323);
#2323 = AXIS2_PLACEMENT_3D('',#2324,#2325,#2326);
#2324 = CARTESIAN_POINT('',(25.97106058,-4.99208298,0.E+000));
#2325 = DIRECTION('',(-0.995184686447,9.801754873774E-002,0.E+000));
#2326 = DIRECTION('',(9.801754873774E-002,0.995184686447,0.E+000));
#2327 = DEFINITIONAL_REPRESENTATION('',(#2328),#2332);
#2328 = LINE('',#2329,#2330);
#2329 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2330 = VECTOR('',#2331,1.);
#2331 = DIRECTION('',(0.E+000,-1.));
#2332 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2333 = ORIENTED_EDGE('',*,*,#2334,.F.);
#2334 = EDGE_CURVE('',#2231,#2307,#2335,.T.);
#2335 = SURFACE_CURVE('',#2336,(#2340,#2347),.PCURVE_S1.);
#2336 = LINE('',#2337,#2338);
#2337 = CARTESIAN_POINT('',(25.85669962,-5.36908248,0.E+000));
#2338 = VECTOR('',#2339,1.);
#2339 = DIRECTION('',(0.290283310389,0.956940750365,0.E+000));
#2340 = PCURVE('',#2246,#2341);
#2341 = DEFINITIONAL_REPRESENTATION('',(#2342),#2346);
#2342 = LINE('',#2343,#2344);
#2343 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2344 = VECTOR('',#2345,1.);
#2345 = DIRECTION('',(1.,0.E+000));
#2346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2347 = PCURVE('',#137,#2348);
#2348 = DEFINITIONAL_REPRESENTATION('',(#2349),#2353);
#2349 = LINE('',#2350,#2351);
#2350 = CARTESIAN_POINT('',(25.85669962,6.23091718));
#2351 = VECTOR('',#2352,1.);
#2352 = DIRECTION('',(0.290283310389,0.956940750365));
#2353 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2354 = ADVANCED_FACE('',(#2355),#2322,.F.);
#2355 = FACE_BOUND('',#2356,.F.);
#2356 = EDGE_LOOP('',(#2357,#2358,#2381,#2409));
#2357 = ORIENTED_EDGE('',*,*,#2306,.T.);
#2358 = ORIENTED_EDGE('',*,*,#2359,.T.);
#2359 = EDGE_CURVE('',#2284,#2360,#2362,.T.);
#2360 = VERTEX_POINT('',#2361);
#2361 = CARTESIAN_POINT('',(26.0096762,-4.60001366,1.12192054));
#2362 = SURFACE_CURVE('',#2363,(#2367,#2374),.PCURVE_S1.);
#2363 = LINE('',#2364,#2365);
#2364 = CARTESIAN_POINT('',(25.97106058,-4.99208298,1.12192054));
#2365 = VECTOR('',#2366,1.);
#2366 = DIRECTION('',(9.801754873774E-002,0.995184686447,0.E+000));
#2367 = PCURVE('',#2322,#2368);
#2368 = DEFINITIONAL_REPRESENTATION('',(#2369),#2373);
#2369 = LINE('',#2370,#2371);
#2370 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2371 = VECTOR('',#2372,1.);
#2372 = DIRECTION('',(1.,0.E+000));
#2373 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2374 = PCURVE('',#83,#2375);
#2375 = DEFINITIONAL_REPRESENTATION('',(#2376),#2380);
#2376 = LINE('',#2377,#2378);
#2377 = CARTESIAN_POINT('',(25.97106058,6.60791668));
#2378 = VECTOR('',#2379,1.);
#2379 = DIRECTION('',(9.801754873774E-002,0.995184686447));
#2380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2381 = ORIENTED_EDGE('',*,*,#2382,.F.);
#2382 = EDGE_CURVE('',#2383,#2360,#2385,.T.);
#2383 = VERTEX_POINT('',#2384);
#2384 = CARTESIAN_POINT('',(26.0096762,-4.60001366,0.E+000));
#2385 = SURFACE_CURVE('',#2386,(#2390,#2397),.PCURVE_S1.);
#2386 = LINE('',#2387,#2388);
#2387 = CARTESIAN_POINT('',(26.0096762,-4.60001366,0.E+000));
#2388 = VECTOR('',#2389,1.);
#2389 = DIRECTION('',(0.E+000,0.E+000,1.));
#2390 = PCURVE('',#2322,#2391);
#2391 = DEFINITIONAL_REPRESENTATION('',(#2392),#2396);
#2392 = LINE('',#2393,#2394);
#2393 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#2394 = VECTOR('',#2395,1.);
#2395 = DIRECTION('',(0.E+000,-1.));
#2396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2397 = PCURVE('',#2398,#2403);
#2398 = PLANE('',#2399);
#2399 = AXIS2_PLACEMENT_3D('',#2400,#2401,#2402);
#2400 = CARTESIAN_POINT('',(26.0096762,-4.60001366,0.E+000));
#2401 = DIRECTION('',(-0.995184686447,-9.801754873774E-002,0.E+000));
#2402 = DIRECTION('',(-9.801754873774E-002,0.995184686447,0.E+000));
#2403 = DEFINITIONAL_REPRESENTATION('',(#2404),#2408);
#2404 = LINE('',#2405,#2406);
#2405 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2406 = VECTOR('',#2407,1.);
#2407 = DIRECTION('',(0.E+000,-1.));
#2408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2409 = ORIENTED_EDGE('',*,*,#2410,.F.);
#2410 = EDGE_CURVE('',#2307,#2383,#2411,.T.);
#2411 = SURFACE_CURVE('',#2412,(#2416,#2423),.PCURVE_S1.);
#2412 = LINE('',#2413,#2414);
#2413 = CARTESIAN_POINT('',(25.97106058,-4.99208298,0.E+000));
#2414 = VECTOR('',#2415,1.);
#2415 = DIRECTION('',(9.801754873774E-002,0.995184686447,0.E+000));
#2416 = PCURVE('',#2322,#2417);
#2417 = DEFINITIONAL_REPRESENTATION('',(#2418),#2422);
#2418 = LINE('',#2419,#2420);
#2419 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2420 = VECTOR('',#2421,1.);
#2421 = DIRECTION('',(1.,0.E+000));
#2422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2423 = PCURVE('',#137,#2424);
#2424 = DEFINITIONAL_REPRESENTATION('',(#2425),#2429);
#2425 = LINE('',#2426,#2427);
#2426 = CARTESIAN_POINT('',(25.97106058,6.60791668));
#2427 = VECTOR('',#2428,1.);
#2428 = DIRECTION('',(9.801754873774E-002,0.995184686447));
#2429 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2430 = ADVANCED_FACE('',(#2431),#2398,.F.);
#2431 = FACE_BOUND('',#2432,.F.);
#2432 = EDGE_LOOP('',(#2433,#2434,#2457,#2485));
#2433 = ORIENTED_EDGE('',*,*,#2382,.T.);
#2434 = ORIENTED_EDGE('',*,*,#2435,.T.);
#2435 = EDGE_CURVE('',#2360,#2436,#2438,.T.);
#2436 = VERTEX_POINT('',#2437);
#2437 = CARTESIAN_POINT('',(25.97106058,-4.20794434,1.12192054));
#2438 = SURFACE_CURVE('',#2439,(#2443,#2450),.PCURVE_S1.);
#2439 = LINE('',#2440,#2441);
#2440 = CARTESIAN_POINT('',(26.0096762,-4.60001366,1.12192054));
#2441 = VECTOR('',#2442,1.);
#2442 = DIRECTION('',(-9.801754873774E-002,0.995184686447,0.E+000));
#2443 = PCURVE('',#2398,#2444);
#2444 = DEFINITIONAL_REPRESENTATION('',(#2445),#2449);
#2445 = LINE('',#2446,#2447);
#2446 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2447 = VECTOR('',#2448,1.);
#2448 = DIRECTION('',(1.,0.E+000));
#2449 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2450 = PCURVE('',#83,#2451);
#2451 = DEFINITIONAL_REPRESENTATION('',(#2452),#2456);
#2452 = LINE('',#2453,#2454);
#2453 = CARTESIAN_POINT('',(26.0096762,6.999986));
#2454 = VECTOR('',#2455,1.);
#2455 = DIRECTION('',(-9.801754873774E-002,0.995184686447));
#2456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2457 = ORIENTED_EDGE('',*,*,#2458,.F.);
#2458 = EDGE_CURVE('',#2459,#2436,#2461,.T.);
#2459 = VERTEX_POINT('',#2460);
#2460 = CARTESIAN_POINT('',(25.97106058,-4.20794434,0.E+000));
#2461 = SURFACE_CURVE('',#2462,(#2466,#2473),.PCURVE_S1.);
#2462 = LINE('',#2463,#2464);
#2463 = CARTESIAN_POINT('',(25.97106058,-4.20794434,0.E+000));
#2464 = VECTOR('',#2465,1.);
#2465 = DIRECTION('',(0.E+000,0.E+000,1.));
#2466 = PCURVE('',#2398,#2467);
#2467 = DEFINITIONAL_REPRESENTATION('',(#2468),#2472);
#2468 = LINE('',#2469,#2470);
#2469 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#2470 = VECTOR('',#2471,1.);
#2471 = DIRECTION('',(0.E+000,-1.));
#2472 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2473 = PCURVE('',#2474,#2479);
#2474 = PLANE('',#2475);
#2475 = AXIS2_PLACEMENT_3D('',#2476,#2477,#2478);
#2476 = CARTESIAN_POINT('',(25.97106058,-4.20794434,0.E+000));
#2477 = DIRECTION('',(-0.956940750365,-0.290283310389,0.E+000));
#2478 = DIRECTION('',(-0.290283310389,0.956940750365,0.E+000));
#2479 = DEFINITIONAL_REPRESENTATION('',(#2480),#2484);
#2480 = LINE('',#2481,#2482);
#2481 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2482 = VECTOR('',#2483,1.);
#2483 = DIRECTION('',(0.E+000,-1.));
#2484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2485 = ORIENTED_EDGE('',*,*,#2486,.F.);
#2486 = EDGE_CURVE('',#2383,#2459,#2487,.T.);
#2487 = SURFACE_CURVE('',#2488,(#2492,#2499),.PCURVE_S1.);
#2488 = LINE('',#2489,#2490);
#2489 = CARTESIAN_POINT('',(26.0096762,-4.60001366,0.E+000));
#2490 = VECTOR('',#2491,1.);
#2491 = DIRECTION('',(-9.801754873774E-002,0.995184686447,0.E+000));
#2492 = PCURVE('',#2398,#2493);
#2493 = DEFINITIONAL_REPRESENTATION('',(#2494),#2498);
#2494 = LINE('',#2495,#2496);
#2495 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2496 = VECTOR('',#2497,1.);
#2497 = DIRECTION('',(1.,0.E+000));
#2498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2499 = PCURVE('',#137,#2500);
#2500 = DEFINITIONAL_REPRESENTATION('',(#2501),#2505);
#2501 = LINE('',#2502,#2503);
#2502 = CARTESIAN_POINT('',(26.0096762,6.999986));
#2503 = VECTOR('',#2504,1.);
#2504 = DIRECTION('',(-9.801754873774E-002,0.995184686447));
#2505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2506 = ADVANCED_FACE('',(#2507),#2474,.F.);
#2507 = FACE_BOUND('',#2508,.F.);
#2508 = EDGE_LOOP('',(#2509,#2510,#2533,#2561));
#2509 = ORIENTED_EDGE('',*,*,#2458,.T.);
#2510 = ORIENTED_EDGE('',*,*,#2511,.T.);
#2511 = EDGE_CURVE('',#2436,#2512,#2514,.T.);
#2512 = VERTEX_POINT('',#2513);
#2513 = CARTESIAN_POINT('',(25.85669962,-3.83094484,1.12192054));
#2514 = SURFACE_CURVE('',#2515,(#2519,#2526),.PCURVE_S1.);
#2515 = LINE('',#2516,#2517);
#2516 = CARTESIAN_POINT('',(25.97106058,-4.20794434,1.12192054));
#2517 = VECTOR('',#2518,1.);
#2518 = DIRECTION('',(-0.290283310389,0.956940750365,0.E+000));
#2519 = PCURVE('',#2474,#2520);
#2520 = DEFINITIONAL_REPRESENTATION('',(#2521),#2525);
#2521 = LINE('',#2522,#2523);
#2522 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2523 = VECTOR('',#2524,1.);
#2524 = DIRECTION('',(1.,0.E+000));
#2525 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2526 = PCURVE('',#83,#2527);
#2527 = DEFINITIONAL_REPRESENTATION('',(#2528),#2532);
#2528 = LINE('',#2529,#2530);
#2529 = CARTESIAN_POINT('',(25.97106058,7.39205532));
#2530 = VECTOR('',#2531,1.);
#2531 = DIRECTION('',(-0.290283310389,0.956940750365));
#2532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2533 = ORIENTED_EDGE('',*,*,#2534,.F.);
#2534 = EDGE_CURVE('',#2535,#2512,#2537,.T.);
#2535 = VERTEX_POINT('',#2536);
#2536 = CARTESIAN_POINT('',(25.85669962,-3.83094484,0.E+000));
#2537 = SURFACE_CURVE('',#2538,(#2542,#2549),.PCURVE_S1.);
#2538 = LINE('',#2539,#2540);
#2539 = CARTESIAN_POINT('',(25.85669962,-3.83094484,0.E+000));
#2540 = VECTOR('',#2541,1.);
#2541 = DIRECTION('',(0.E+000,0.E+000,1.));
#2542 = PCURVE('',#2474,#2543);
#2543 = DEFINITIONAL_REPRESENTATION('',(#2544),#2548);
#2544 = LINE('',#2545,#2546);
#2545 = CARTESIAN_POINT('',(0.393963262465,0.E+000));
#2546 = VECTOR('',#2547,1.);
#2547 = DIRECTION('',(0.E+000,-1.));
#2548 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2549 = PCURVE('',#2550,#2555);
#2550 = PLANE('',#2551);
#2551 = AXIS2_PLACEMENT_3D('',#2552,#2553,#2554);
#2552 = CARTESIAN_POINT('',(25.85669962,-3.83094484,0.E+000));
#2553 = DIRECTION('',(-0.881920670078,-0.471397848625,0.E+000));
#2554 = DIRECTION('',(-0.471397848625,0.881920670078,0.E+000));
#2555 = DEFINITIONAL_REPRESENTATION('',(#2556),#2560);
#2556 = LINE('',#2557,#2558);
#2557 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2558 = VECTOR('',#2559,1.);
#2559 = DIRECTION('',(0.E+000,-1.));
#2560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2561 = ORIENTED_EDGE('',*,*,#2562,.F.);
#2562 = EDGE_CURVE('',#2459,#2535,#2563,.T.);
#2563 = SURFACE_CURVE('',#2564,(#2568,#2575),.PCURVE_S1.);
#2564 = LINE('',#2565,#2566);
#2565 = CARTESIAN_POINT('',(25.97106058,-4.20794434,0.E+000));
#2566 = VECTOR('',#2567,1.);
#2567 = DIRECTION('',(-0.290283310389,0.956940750365,0.E+000));
#2568 = PCURVE('',#2474,#2569);
#2569 = DEFINITIONAL_REPRESENTATION('',(#2570),#2574);
#2570 = LINE('',#2571,#2572);
#2571 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2572 = VECTOR('',#2573,1.);
#2573 = DIRECTION('',(1.,0.E+000));
#2574 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2575 = PCURVE('',#137,#2576);
#2576 = DEFINITIONAL_REPRESENTATION('',(#2577),#2581);
#2577 = LINE('',#2578,#2579);
#2578 = CARTESIAN_POINT('',(25.97106058,7.39205532));
#2579 = VECTOR('',#2580,1.);
#2580 = DIRECTION('',(-0.290283310389,0.956940750365));
#2581 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2582 = ADVANCED_FACE('',(#2583),#2550,.F.);
#2583 = FACE_BOUND('',#2584,.F.);
#2584 = EDGE_LOOP('',(#2585,#2586,#2609,#2637));
#2585 = ORIENTED_EDGE('',*,*,#2534,.T.);
#2586 = ORIENTED_EDGE('',*,*,#2587,.T.);
#2587 = EDGE_CURVE('',#2512,#2588,#2590,.T.);
#2588 = VERTEX_POINT('',#2589);
#2589 = CARTESIAN_POINT('',(25.67098498,-3.48349824,1.12192054));
#2590 = SURFACE_CURVE('',#2591,(#2595,#2602),.PCURVE_S1.);
#2591 = LINE('',#2592,#2593);
#2592 = CARTESIAN_POINT('',(25.85669962,-3.83094484,1.12192054));
#2593 = VECTOR('',#2594,1.);
#2594 = DIRECTION('',(-0.471397848625,0.881920670078,0.E+000));
#2595 = PCURVE('',#2550,#2596);
#2596 = DEFINITIONAL_REPRESENTATION('',(#2597),#2601);
#2597 = LINE('',#2598,#2599);
#2598 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2599 = VECTOR('',#2600,1.);
#2600 = DIRECTION('',(1.,0.E+000));
#2601 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2602 = PCURVE('',#83,#2603);
#2603 = DEFINITIONAL_REPRESENTATION('',(#2604),#2608);
#2604 = LINE('',#2605,#2606);
#2605 = CARTESIAN_POINT('',(25.85669962,7.76905482));
#2606 = VECTOR('',#2607,1.);
#2607 = DIRECTION('',(-0.471397848625,0.881920670078));
#2608 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2609 = ORIENTED_EDGE('',*,*,#2610,.F.);
#2610 = EDGE_CURVE('',#2611,#2588,#2613,.T.);
#2611 = VERTEX_POINT('',#2612);
#2612 = CARTESIAN_POINT('',(25.67098498,-3.48349824,0.E+000));
#2613 = SURFACE_CURVE('',#2614,(#2618,#2625),.PCURVE_S1.);
#2614 = LINE('',#2615,#2616);
#2615 = CARTESIAN_POINT('',(25.67098498,-3.48349824,0.E+000));
#2616 = VECTOR('',#2617,1.);
#2617 = DIRECTION('',(0.E+000,0.E+000,1.));
#2618 = PCURVE('',#2550,#2619);
#2619 = DEFINITIONAL_REPRESENTATION('',(#2620),#2624);
#2620 = LINE('',#2621,#2622);
#2621 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#2622 = VECTOR('',#2623,1.);
#2623 = DIRECTION('',(0.E+000,-1.));
#2624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2625 = PCURVE('',#2626,#2631);
#2626 = PLANE('',#2627);
#2627 = AXIS2_PLACEMENT_3D('',#2628,#2629,#2630);
#2628 = CARTESIAN_POINT('',(25.67098498,-3.48349824,0.E+000));
#2629 = DIRECTION('',(-0.773012810909,-0.634390411475,0.E+000));
#2630 = DIRECTION('',(-0.634390411475,0.773012810909,0.E+000));
#2631 = DEFINITIONAL_REPRESENTATION('',(#2632),#2636);
#2632 = LINE('',#2633,#2634);
#2633 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2634 = VECTOR('',#2635,1.);
#2635 = DIRECTION('',(0.E+000,-1.));
#2636 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2637 = ORIENTED_EDGE('',*,*,#2638,.F.);
#2638 = EDGE_CURVE('',#2535,#2611,#2639,.T.);
#2639 = SURFACE_CURVE('',#2640,(#2644,#2651),.PCURVE_S1.);
#2640 = LINE('',#2641,#2642);
#2641 = CARTESIAN_POINT('',(25.85669962,-3.83094484,0.E+000));
#2642 = VECTOR('',#2643,1.);
#2643 = DIRECTION('',(-0.471397848625,0.881920670078,0.E+000));
#2644 = PCURVE('',#2550,#2645);
#2645 = DEFINITIONAL_REPRESENTATION('',(#2646),#2650);
#2646 = LINE('',#2647,#2648);
#2647 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2648 = VECTOR('',#2649,1.);
#2649 = DIRECTION('',(1.,0.E+000));
#2650 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2651 = PCURVE('',#137,#2652);
#2652 = DEFINITIONAL_REPRESENTATION('',(#2653),#2657);
#2653 = LINE('',#2654,#2655);
#2654 = CARTESIAN_POINT('',(25.85669962,7.76905482));
#2655 = VECTOR('',#2656,1.);
#2656 = DIRECTION('',(-0.471397848625,0.881920670078));
#2657 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2658 = ADVANCED_FACE('',(#2659),#2626,.F.);
#2659 = FACE_BOUND('',#2660,.F.);
#2660 = EDGE_LOOP('',(#2661,#2662,#2685,#2713));
#2661 = ORIENTED_EDGE('',*,*,#2610,.T.);
#2662 = ORIENTED_EDGE('',*,*,#2663,.T.);
#2663 = EDGE_CURVE('',#2588,#2664,#2666,.T.);
#2664 = VERTEX_POINT('',#2665);
#2665 = CARTESIAN_POINT('',(25.4210566,-3.17895732,1.12192054));
#2666 = SURFACE_CURVE('',#2667,(#2671,#2678),.PCURVE_S1.);
#2667 = LINE('',#2668,#2669);
#2668 = CARTESIAN_POINT('',(25.67098498,-3.48349824,1.12192054));
#2669 = VECTOR('',#2670,1.);
#2670 = DIRECTION('',(-0.634390411475,0.773012810909,0.E+000));
#2671 = PCURVE('',#2626,#2672);
#2672 = DEFINITIONAL_REPRESENTATION('',(#2673),#2677);
#2673 = LINE('',#2674,#2675);
#2674 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2675 = VECTOR('',#2676,1.);
#2676 = DIRECTION('',(1.,0.E+000));
#2677 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2678 = PCURVE('',#83,#2679);
#2679 = DEFINITIONAL_REPRESENTATION('',(#2680),#2684);
#2680 = LINE('',#2681,#2682);
#2681 = CARTESIAN_POINT('',(25.67098498,8.11650142));
#2682 = VECTOR('',#2683,1.);
#2683 = DIRECTION('',(-0.634390411475,0.773012810909));
#2684 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2685 = ORIENTED_EDGE('',*,*,#2686,.F.);
#2686 = EDGE_CURVE('',#2687,#2664,#2689,.T.);
#2687 = VERTEX_POINT('',#2688);
#2688 = CARTESIAN_POINT('',(25.4210566,-3.17895732,0.E+000));
#2689 = SURFACE_CURVE('',#2690,(#2694,#2701),.PCURVE_S1.);
#2690 = LINE('',#2691,#2692);
#2691 = CARTESIAN_POINT('',(25.4210566,-3.17895732,0.E+000));
#2692 = VECTOR('',#2693,1.);
#2693 = DIRECTION('',(0.E+000,0.E+000,1.));
#2694 = PCURVE('',#2626,#2695);
#2695 = DEFINITIONAL_REPRESENTATION('',(#2696),#2700);
#2696 = LINE('',#2697,#2698);
#2697 = CARTESIAN_POINT('',(0.393966200433,0.E+000));
#2698 = VECTOR('',#2699,1.);
#2699 = DIRECTION('',(0.E+000,-1.));
#2700 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2701 = PCURVE('',#2702,#2707);
#2702 = PLANE('',#2703);
#2703 = AXIS2_PLACEMENT_3D('',#2704,#2705,#2706);
#2704 = CARTESIAN_POINT('',(25.4210566,-3.17895732,0.E+000));
#2705 = DIRECTION('',(-0.634390411475,-0.773012810909,0.E+000));
#2706 = DIRECTION('',(-0.773012810909,0.634390411475,0.E+000));
#2707 = DEFINITIONAL_REPRESENTATION('',(#2708),#2712);
#2708 = LINE('',#2709,#2710);
#2709 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2710 = VECTOR('',#2711,1.);
#2711 = DIRECTION('',(0.E+000,-1.));
#2712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2713 = ORIENTED_EDGE('',*,*,#2714,.F.);
#2714 = EDGE_CURVE('',#2611,#2687,#2715,.T.);
#2715 = SURFACE_CURVE('',#2716,(#2720,#2727),.PCURVE_S1.);
#2716 = LINE('',#2717,#2718);
#2717 = CARTESIAN_POINT('',(25.67098498,-3.48349824,0.E+000));
#2718 = VECTOR('',#2719,1.);
#2719 = DIRECTION('',(-0.634390411475,0.773012810909,0.E+000));
#2720 = PCURVE('',#2626,#2721);
#2721 = DEFINITIONAL_REPRESENTATION('',(#2722),#2726);
#2722 = LINE('',#2723,#2724);
#2723 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2724 = VECTOR('',#2725,1.);
#2725 = DIRECTION('',(1.,0.E+000));
#2726 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2727 = PCURVE('',#137,#2728);
#2728 = DEFINITIONAL_REPRESENTATION('',(#2729),#2733);
#2729 = LINE('',#2730,#2731);
#2730 = CARTESIAN_POINT('',(25.67098498,8.11650142));
#2731 = VECTOR('',#2732,1.);
#2732 = DIRECTION('',(-0.634390411475,0.773012810909));
#2733 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2734 = ADVANCED_FACE('',(#2735),#2702,.F.);
#2735 = FACE_BOUND('',#2736,.F.);
#2736 = EDGE_LOOP('',(#2737,#2738,#2761,#2789));
#2737 = ORIENTED_EDGE('',*,*,#2686,.T.);
#2738 = ORIENTED_EDGE('',*,*,#2739,.T.);
#2739 = EDGE_CURVE('',#2664,#2740,#2742,.T.);
#2740 = VERTEX_POINT('',#2741);
#2741 = CARTESIAN_POINT('',(25.11651568,-2.92902894,1.12192054));
#2742 = SURFACE_CURVE('',#2743,(#2747,#2754),.PCURVE_S1.);
#2743 = LINE('',#2744,#2745);
#2744 = CARTESIAN_POINT('',(25.4210566,-3.17895732,1.12192054));
#2745 = VECTOR('',#2746,1.);
#2746 = DIRECTION('',(-0.773012810909,0.634390411475,0.E+000));
#2747 = PCURVE('',#2702,#2748);
#2748 = DEFINITIONAL_REPRESENTATION('',(#2749),#2753);
#2749 = LINE('',#2750,#2751);
#2750 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2751 = VECTOR('',#2752,1.);
#2752 = DIRECTION('',(1.,0.E+000));
#2753 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2754 = PCURVE('',#83,#2755);
#2755 = DEFINITIONAL_REPRESENTATION('',(#2756),#2760);
#2756 = LINE('',#2757,#2758);
#2757 = CARTESIAN_POINT('',(25.4210566,8.42104234));
#2758 = VECTOR('',#2759,1.);
#2759 = DIRECTION('',(-0.773012810909,0.634390411475));
#2760 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2761 = ORIENTED_EDGE('',*,*,#2762,.F.);
#2762 = EDGE_CURVE('',#2763,#2740,#2765,.T.);
#2763 = VERTEX_POINT('',#2764);
#2764 = CARTESIAN_POINT('',(25.11651568,-2.92902894,0.E+000));
#2765 = SURFACE_CURVE('',#2766,(#2770,#2777),.PCURVE_S1.);
#2766 = LINE('',#2767,#2768);
#2767 = CARTESIAN_POINT('',(25.11651568,-2.92902894,0.E+000));
#2768 = VECTOR('',#2769,1.);
#2769 = DIRECTION('',(0.E+000,0.E+000,1.));
#2770 = PCURVE('',#2702,#2771);
#2771 = DEFINITIONAL_REPRESENTATION('',(#2772),#2776);
#2772 = LINE('',#2773,#2774);
#2773 = CARTESIAN_POINT('',(0.393966200433,0.E+000));
#2774 = VECTOR('',#2775,1.);
#2775 = DIRECTION('',(0.E+000,-1.));
#2776 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2777 = PCURVE('',#2778,#2783);
#2778 = PLANE('',#2779);
#2779 = AXIS2_PLACEMENT_3D('',#2780,#2781,#2782);
#2780 = CARTESIAN_POINT('',(25.11651568,-2.92902894,0.E+000));
#2781 = DIRECTION('',(-0.471397848625,-0.881920670078,0.E+000));
#2782 = DIRECTION('',(-0.881920670078,0.471397848625,0.E+000));
#2783 = DEFINITIONAL_REPRESENTATION('',(#2784),#2788);
#2784 = LINE('',#2785,#2786);
#2785 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2786 = VECTOR('',#2787,1.);
#2787 = DIRECTION('',(0.E+000,-1.));
#2788 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2789 = ORIENTED_EDGE('',*,*,#2790,.F.);
#2790 = EDGE_CURVE('',#2687,#2763,#2791,.T.);
#2791 = SURFACE_CURVE('',#2792,(#2796,#2803),.PCURVE_S1.);
#2792 = LINE('',#2793,#2794);
#2793 = CARTESIAN_POINT('',(25.4210566,-3.17895732,0.E+000));
#2794 = VECTOR('',#2795,1.);
#2795 = DIRECTION('',(-0.773012810909,0.634390411475,0.E+000));
#2796 = PCURVE('',#2702,#2797);
#2797 = DEFINITIONAL_REPRESENTATION('',(#2798),#2802);
#2798 = LINE('',#2799,#2800);
#2799 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2800 = VECTOR('',#2801,1.);
#2801 = DIRECTION('',(1.,0.E+000));
#2802 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2803 = PCURVE('',#137,#2804);
#2804 = DEFINITIONAL_REPRESENTATION('',(#2805),#2809);
#2805 = LINE('',#2806,#2807);
#2806 = CARTESIAN_POINT('',(25.4210566,8.42104234));
#2807 = VECTOR('',#2808,1.);
#2808 = DIRECTION('',(-0.773012810909,0.634390411475));
#2809 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2810 = ADVANCED_FACE('',(#2811),#2778,.F.);
#2811 = FACE_BOUND('',#2812,.F.);
#2812 = EDGE_LOOP('',(#2813,#2814,#2837,#2865));
#2813 = ORIENTED_EDGE('',*,*,#2762,.T.);
#2814 = ORIENTED_EDGE('',*,*,#2815,.T.);
#2815 = EDGE_CURVE('',#2740,#2816,#2818,.T.);
#2816 = VERTEX_POINT('',#2817);
#2817 = CARTESIAN_POINT('',(24.76906908,-2.7433143,1.12192054));
#2818 = SURFACE_CURVE('',#2819,(#2823,#2830),.PCURVE_S1.);
#2819 = LINE('',#2820,#2821);
#2820 = CARTESIAN_POINT('',(25.11651568,-2.92902894,1.12192054));
#2821 = VECTOR('',#2822,1.);
#2822 = DIRECTION('',(-0.881920670078,0.471397848625,0.E+000));
#2823 = PCURVE('',#2778,#2824);
#2824 = DEFINITIONAL_REPRESENTATION('',(#2825),#2829);
#2825 = LINE('',#2826,#2827);
#2826 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2827 = VECTOR('',#2828,1.);
#2828 = DIRECTION('',(1.,0.E+000));
#2829 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2830 = PCURVE('',#83,#2831);
#2831 = DEFINITIONAL_REPRESENTATION('',(#2832),#2836);
#2832 = LINE('',#2833,#2834);
#2833 = CARTESIAN_POINT('',(25.11651568,8.67097072));
#2834 = VECTOR('',#2835,1.);
#2835 = DIRECTION('',(-0.881920670078,0.471397848625));
#2836 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2837 = ORIENTED_EDGE('',*,*,#2838,.F.);
#2838 = EDGE_CURVE('',#2839,#2816,#2841,.T.);
#2839 = VERTEX_POINT('',#2840);
#2840 = CARTESIAN_POINT('',(24.76906908,-2.7433143,0.E+000));
#2841 = SURFACE_CURVE('',#2842,(#2846,#2853),.PCURVE_S1.);
#2842 = LINE('',#2843,#2844);
#2843 = CARTESIAN_POINT('',(24.76906908,-2.7433143,0.E+000));
#2844 = VECTOR('',#2845,1.);
#2845 = DIRECTION('',(0.E+000,0.E+000,1.));
#2846 = PCURVE('',#2778,#2847);
#2847 = DEFINITIONAL_REPRESENTATION('',(#2848),#2852);
#2848 = LINE('',#2849,#2850);
#2849 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#2850 = VECTOR('',#2851,1.);
#2851 = DIRECTION('',(0.E+000,-1.));
#2852 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2853 = PCURVE('',#2854,#2859);
#2854 = PLANE('',#2855);
#2855 = AXIS2_PLACEMENT_3D('',#2856,#2857,#2858);
#2856 = CARTESIAN_POINT('',(24.76906908,-2.7433143,0.E+000));
#2857 = DIRECTION('',(-0.290283310389,-0.956940750365,0.E+000));
#2858 = DIRECTION('',(-0.956940750365,0.290283310389,0.E+000));
#2859 = DEFINITIONAL_REPRESENTATION('',(#2860),#2864);
#2860 = LINE('',#2861,#2862);
#2861 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2862 = VECTOR('',#2863,1.);
#2863 = DIRECTION('',(0.E+000,-1.));
#2864 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2865 = ORIENTED_EDGE('',*,*,#2866,.F.);
#2866 = EDGE_CURVE('',#2763,#2839,#2867,.T.);
#2867 = SURFACE_CURVE('',#2868,(#2872,#2879),.PCURVE_S1.);
#2868 = LINE('',#2869,#2870);
#2869 = CARTESIAN_POINT('',(25.11651568,-2.92902894,0.E+000));
#2870 = VECTOR('',#2871,1.);
#2871 = DIRECTION('',(-0.881920670078,0.471397848625,0.E+000));
#2872 = PCURVE('',#2778,#2873);
#2873 = DEFINITIONAL_REPRESENTATION('',(#2874),#2878);
#2874 = LINE('',#2875,#2876);
#2875 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2876 = VECTOR('',#2877,1.);
#2877 = DIRECTION('',(1.,0.E+000));
#2878 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2879 = PCURVE('',#137,#2880);
#2880 = DEFINITIONAL_REPRESENTATION('',(#2881),#2885);
#2881 = LINE('',#2882,#2883);
#2882 = CARTESIAN_POINT('',(25.11651568,8.67097072));
#2883 = VECTOR('',#2884,1.);
#2884 = DIRECTION('',(-0.881920670078,0.471397848625));
#2885 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2886 = ADVANCED_FACE('',(#2887),#2854,.F.);
#2887 = FACE_BOUND('',#2888,.F.);
#2888 = EDGE_LOOP('',(#2889,#2890,#2913,#2941));
#2889 = ORIENTED_EDGE('',*,*,#2838,.T.);
#2890 = ORIENTED_EDGE('',*,*,#2891,.T.);
#2891 = EDGE_CURVE('',#2816,#2892,#2894,.T.);
#2892 = VERTEX_POINT('',#2893);
#2893 = CARTESIAN_POINT('',(24.39206958,-2.62895334,1.12192054));
#2894 = SURFACE_CURVE('',#2895,(#2899,#2906),.PCURVE_S1.);
#2895 = LINE('',#2896,#2897);
#2896 = CARTESIAN_POINT('',(24.76906908,-2.7433143,1.12192054));
#2897 = VECTOR('',#2898,1.);
#2898 = DIRECTION('',(-0.956940750365,0.290283310389,0.E+000));
#2899 = PCURVE('',#2854,#2900);
#2900 = DEFINITIONAL_REPRESENTATION('',(#2901),#2905);
#2901 = LINE('',#2902,#2903);
#2902 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2903 = VECTOR('',#2904,1.);
#2904 = DIRECTION('',(1.,0.E+000));
#2905 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2906 = PCURVE('',#83,#2907);
#2907 = DEFINITIONAL_REPRESENTATION('',(#2908),#2912);
#2908 = LINE('',#2909,#2910);
#2909 = CARTESIAN_POINT('',(24.76906908,8.85668536));
#2910 = VECTOR('',#2911,1.);
#2911 = DIRECTION('',(-0.956940750365,0.290283310389));
#2912 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2913 = ORIENTED_EDGE('',*,*,#2914,.F.);
#2914 = EDGE_CURVE('',#2915,#2892,#2917,.T.);
#2915 = VERTEX_POINT('',#2916);
#2916 = CARTESIAN_POINT('',(24.39206958,-2.62895334,0.E+000));
#2917 = SURFACE_CURVE('',#2918,(#2922,#2929),.PCURVE_S1.);
#2918 = LINE('',#2919,#2920);
#2919 = CARTESIAN_POINT('',(24.39206958,-2.62895334,0.E+000));
#2920 = VECTOR('',#2921,1.);
#2921 = DIRECTION('',(0.E+000,0.E+000,1.));
#2922 = PCURVE('',#2854,#2923);
#2923 = DEFINITIONAL_REPRESENTATION('',(#2924),#2928);
#2924 = LINE('',#2925,#2926);
#2925 = CARTESIAN_POINT('',(0.393963262465,0.E+000));
#2926 = VECTOR('',#2927,1.);
#2927 = DIRECTION('',(0.E+000,-1.));
#2928 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2929 = PCURVE('',#2930,#2935);
#2930 = PLANE('',#2931);
#2931 = AXIS2_PLACEMENT_3D('',#2932,#2933,#2934);
#2932 = CARTESIAN_POINT('',(24.39206958,-2.62895334,0.E+000));
#2933 = DIRECTION('',(-9.801754873774E-002,-0.995184686447,0.E+000));
#2934 = DIRECTION('',(-0.995184686447,9.801754873774E-002,0.E+000));
#2935 = DEFINITIONAL_REPRESENTATION('',(#2936),#2940);
#2936 = LINE('',#2937,#2938);
#2937 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2938 = VECTOR('',#2939,1.);
#2939 = DIRECTION('',(0.E+000,-1.));
#2940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2941 = ORIENTED_EDGE('',*,*,#2942,.F.);
#2942 = EDGE_CURVE('',#2839,#2915,#2943,.T.);
#2943 = SURFACE_CURVE('',#2944,(#2948,#2955),.PCURVE_S1.);
#2944 = LINE('',#2945,#2946);
#2945 = CARTESIAN_POINT('',(24.76906908,-2.7433143,0.E+000));
#2946 = VECTOR('',#2947,1.);
#2947 = DIRECTION('',(-0.956940750365,0.290283310389,0.E+000));
#2948 = PCURVE('',#2854,#2949);
#2949 = DEFINITIONAL_REPRESENTATION('',(#2950),#2954);
#2950 = LINE('',#2951,#2952);
#2951 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2952 = VECTOR('',#2953,1.);
#2953 = DIRECTION('',(1.,0.E+000));
#2954 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2955 = PCURVE('',#137,#2956);
#2956 = DEFINITIONAL_REPRESENTATION('',(#2957),#2961);
#2957 = LINE('',#2958,#2959);
#2958 = CARTESIAN_POINT('',(24.76906908,8.85668536));
#2959 = VECTOR('',#2960,1.);
#2960 = DIRECTION('',(-0.956940750365,0.290283310389));
#2961 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2962 = ADVANCED_FACE('',(#2963),#2930,.F.);
#2963 = FACE_BOUND('',#2964,.F.);
#2964 = EDGE_LOOP('',(#2965,#2966,#2989,#3017));
#2965 = ORIENTED_EDGE('',*,*,#2914,.T.);
#2966 = ORIENTED_EDGE('',*,*,#2967,.T.);
#2967 = EDGE_CURVE('',#2892,#2968,#2970,.T.);
#2968 = VERTEX_POINT('',#2969);
#2969 = CARTESIAN_POINT('',(24.00000026,-2.59033772,1.12192054));
#2970 = SURFACE_CURVE('',#2971,(#2975,#2982),.PCURVE_S1.);
#2971 = LINE('',#2972,#2973);
#2972 = CARTESIAN_POINT('',(24.39206958,-2.62895334,1.12192054));
#2973 = VECTOR('',#2974,1.);
#2974 = DIRECTION('',(-0.995184686447,9.801754873774E-002,0.E+000));
#2975 = PCURVE('',#2930,#2976);
#2976 = DEFINITIONAL_REPRESENTATION('',(#2977),#2981);
#2977 = LINE('',#2978,#2979);
#2978 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#2979 = VECTOR('',#2980,1.);
#2980 = DIRECTION('',(1.,0.E+000));
#2981 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2982 = PCURVE('',#83,#2983);
#2983 = DEFINITIONAL_REPRESENTATION('',(#2984),#2988);
#2984 = LINE('',#2985,#2986);
#2985 = CARTESIAN_POINT('',(24.39206958,8.97104632));
#2986 = VECTOR('',#2987,1.);
#2987 = DIRECTION('',(-0.995184686447,9.801754873774E-002));
#2988 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2989 = ORIENTED_EDGE('',*,*,#2990,.F.);
#2990 = EDGE_CURVE('',#2991,#2968,#2993,.T.);
#2991 = VERTEX_POINT('',#2992);
#2992 = CARTESIAN_POINT('',(24.00000026,-2.59033772,0.E+000));
#2993 = SURFACE_CURVE('',#2994,(#2998,#3005),.PCURVE_S1.);
#2994 = LINE('',#2995,#2996);
#2995 = CARTESIAN_POINT('',(24.00000026,-2.59033772,0.E+000));
#2996 = VECTOR('',#2997,1.);
#2997 = DIRECTION('',(0.E+000,0.E+000,1.));
#2998 = PCURVE('',#2930,#2999);
#2999 = DEFINITIONAL_REPRESENTATION('',(#3000),#3004);
#3000 = LINE('',#3001,#3002);
#3001 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#3002 = VECTOR('',#3003,1.);
#3003 = DIRECTION('',(0.E+000,-1.));
#3004 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3005 = PCURVE('',#3006,#3011);
#3006 = PLANE('',#3007);
#3007 = AXIS2_PLACEMENT_3D('',#3008,#3009,#3010);
#3008 = CARTESIAN_POINT('',(24.00000026,-2.59033772,0.E+000));
#3009 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3010 = DIRECTION('',(0.E+000,-1.,0.E+000));
#3011 = DEFINITIONAL_REPRESENTATION('',(#3012),#3016);
#3012 = LINE('',#3013,#3014);
#3013 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3014 = VECTOR('',#3015,1.);
#3015 = DIRECTION('',(0.E+000,-1.));
#3016 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3017 = ORIENTED_EDGE('',*,*,#3018,.F.);
#3018 = EDGE_CURVE('',#2915,#2991,#3019,.T.);
#3019 = SURFACE_CURVE('',#3020,(#3024,#3031),.PCURVE_S1.);
#3020 = LINE('',#3021,#3022);
#3021 = CARTESIAN_POINT('',(24.39206958,-2.62895334,0.E+000));
#3022 = VECTOR('',#3023,1.);
#3023 = DIRECTION('',(-0.995184686447,9.801754873774E-002,0.E+000));
#3024 = PCURVE('',#2930,#3025);
#3025 = DEFINITIONAL_REPRESENTATION('',(#3026),#3030);
#3026 = LINE('',#3027,#3028);
#3027 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3028 = VECTOR('',#3029,1.);
#3029 = DIRECTION('',(1.,0.E+000));
#3030 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3031 = PCURVE('',#137,#3032);
#3032 = DEFINITIONAL_REPRESENTATION('',(#3033),#3037);
#3033 = LINE('',#3034,#3035);
#3034 = CARTESIAN_POINT('',(24.39206958,8.97104632));
#3035 = VECTOR('',#3036,1.);
#3036 = DIRECTION('',(-0.995184686447,9.801754873774E-002));
#3037 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3038 = ADVANCED_FACE('',(#3039),#3006,.F.);
#3039 = FACE_BOUND('',#3040,.F.);
#3040 = EDGE_LOOP('',(#3041,#3042,#3065,#3088));
#3041 = ORIENTED_EDGE('',*,*,#2990,.T.);
#3042 = ORIENTED_EDGE('',*,*,#3043,.T.);
#3043 = EDGE_CURVE('',#2968,#3044,#3046,.T.);
#3044 = VERTEX_POINT('',#3045);
#3045 = CARTESIAN_POINT('',(24.00000026,-2.60001258,1.12192054));
#3046 = SURFACE_CURVE('',#3047,(#3051,#3058),.PCURVE_S1.);
#3047 = LINE('',#3048,#3049);
#3048 = CARTESIAN_POINT('',(24.00000026,-2.59033772,1.12192054));
#3049 = VECTOR('',#3050,1.);
#3050 = DIRECTION('',(0.E+000,-1.,0.E+000));
#3051 = PCURVE('',#3006,#3052);
#3052 = DEFINITIONAL_REPRESENTATION('',(#3053),#3057);
#3053 = LINE('',#3054,#3055);
#3054 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3055 = VECTOR('',#3056,1.);
#3056 = DIRECTION('',(1.,0.E+000));
#3057 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3058 = PCURVE('',#83,#3059);
#3059 = DEFINITIONAL_REPRESENTATION('',(#3060),#3064);
#3060 = LINE('',#3061,#3062);
#3061 = CARTESIAN_POINT('',(24.00000026,9.00966194));
#3062 = VECTOR('',#3063,1.);
#3063 = DIRECTION('',(0.E+000,-1.));
#3064 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3065 = ORIENTED_EDGE('',*,*,#3066,.F.);
#3066 = EDGE_CURVE('',#3067,#3044,#3069,.T.);
#3067 = VERTEX_POINT('',#3068);
#3068 = CARTESIAN_POINT('',(24.00000026,-2.60001258,0.E+000));
#3069 = SURFACE_CURVE('',#3070,(#3074,#3081),.PCURVE_S1.);
#3070 = LINE('',#3071,#3072);
#3071 = CARTESIAN_POINT('',(24.00000026,-2.60001258,0.E+000));
#3072 = VECTOR('',#3073,1.);
#3073 = DIRECTION('',(0.E+000,0.E+000,1.));
#3074 = PCURVE('',#3006,#3075);
#3075 = DEFINITIONAL_REPRESENTATION('',(#3076),#3080);
#3076 = LINE('',#3077,#3078);
#3077 = CARTESIAN_POINT('',(9.674860000004E-003,0.E+000));
#3078 = VECTOR('',#3079,1.);
#3079 = DIRECTION('',(0.E+000,-1.));
#3080 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3081 = PCURVE('',#447,#3082);
#3082 = DEFINITIONAL_REPRESENTATION('',(#3083),#3087);
#3083 = LINE('',#3084,#3085);
#3084 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3085 = VECTOR('',#3086,1.);
#3086 = DIRECTION('',(0.E+000,-1.));
#3087 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3088 = ORIENTED_EDGE('',*,*,#3089,.F.);
#3089 = EDGE_CURVE('',#2991,#3067,#3090,.T.);
#3090 = SURFACE_CURVE('',#3091,(#3095,#3102),.PCURVE_S1.);
#3091 = LINE('',#3092,#3093);
#3092 = CARTESIAN_POINT('',(24.00000026,-2.59033772,0.E+000));
#3093 = VECTOR('',#3094,1.);
#3094 = DIRECTION('',(0.E+000,-1.,0.E+000));
#3095 = PCURVE('',#3006,#3096);
#3096 = DEFINITIONAL_REPRESENTATION('',(#3097),#3101);
#3097 = LINE('',#3098,#3099);
#3098 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3099 = VECTOR('',#3100,1.);
#3100 = DIRECTION('',(1.,0.E+000));
#3101 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3102 = PCURVE('',#137,#3103);
#3103 = DEFINITIONAL_REPRESENTATION('',(#3104),#3108);
#3104 = LINE('',#3105,#3106);
#3105 = CARTESIAN_POINT('',(24.00000026,9.00966194));
#3106 = VECTOR('',#3107,1.);
#3107 = DIRECTION('',(0.E+000,-1.));
#3108 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3109 = ADVANCED_FACE('',(#3110),#447,.F.);
#3110 = FACE_BOUND('',#3111,.F.);
#3111 = EDGE_LOOP('',(#3112,#3113,#3134,#3135));
#3112 = ORIENTED_EDGE('',*,*,#3066,.T.);
#3113 = ORIENTED_EDGE('',*,*,#3114,.T.);
#3114 = EDGE_CURVE('',#3044,#427,#3115,.T.);
#3115 = SURFACE_CURVE('',#3116,(#3120,#3127),.PCURVE_S1.);
#3116 = LINE('',#3117,#3118);
#3117 = CARTESIAN_POINT('',(24.00000026,-2.60001258,1.12192054));
#3118 = VECTOR('',#3119,1.);
#3119 = DIRECTION('',(-0.999998126973,1.935471396762E-003,0.E+000));
#3120 = PCURVE('',#447,#3121);
#3121 = DEFINITIONAL_REPRESENTATION('',(#3122),#3126);
#3122 = LINE('',#3123,#3124);
#3123 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3124 = VECTOR('',#3125,1.);
#3125 = DIRECTION('',(1.,0.E+000));
#3126 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3127 = PCURVE('',#83,#3128);
#3128 = DEFINITIONAL_REPRESENTATION('',(#3129),#3133);
#3129 = LINE('',#3130,#3131);
#3130 = CARTESIAN_POINT('',(24.00000026,8.99998708));
#3131 = VECTOR('',#3132,1.);
#3132 = DIRECTION('',(-0.999998126973,1.935471396762E-003));
#3133 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3134 = ORIENTED_EDGE('',*,*,#424,.F.);
#3135 = ORIENTED_EDGE('',*,*,#3136,.F.);
#3136 = EDGE_CURVE('',#3067,#425,#3137,.T.);
#3137 = SURFACE_CURVE('',#3138,(#3142,#3149),.PCURVE_S1.);
#3138 = LINE('',#3139,#3140);
#3139 = CARTESIAN_POINT('',(24.00000026,-2.60001258,0.E+000));
#3140 = VECTOR('',#3141,1.);
#3141 = DIRECTION('',(-0.999998126973,1.935471396762E-003,0.E+000));
#3142 = PCURVE('',#447,#3143);
#3143 = DEFINITIONAL_REPRESENTATION('',(#3144),#3148);
#3144 = LINE('',#3145,#3146);
#3145 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3146 = VECTOR('',#3147,1.);
#3147 = DIRECTION('',(1.,0.E+000));
#3148 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3149 = PCURVE('',#137,#3150);
#3150 = DEFINITIONAL_REPRESENTATION('',(#3151),#3155);
#3151 = LINE('',#3152,#3153);
#3152 = CARTESIAN_POINT('',(24.00000026,8.99998708));
#3153 = VECTOR('',#3154,1.);
#3154 = DIRECTION('',(-0.999998126973,1.935471396762E-003));
#3155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3156 = ADVANCED_FACE('',(#3157),#3171,.F.);
#3157 = FACE_BOUND('',#3158,.F.);
#3158 = EDGE_LOOP('',(#3159,#3194,#3217,#3245));
#3159 = ORIENTED_EDGE('',*,*,#3160,.T.);
#3160 = EDGE_CURVE('',#3161,#3163,#3165,.T.);
#3161 = VERTEX_POINT('',#3162);
#3162 = CARTESIAN_POINT('',(64.5000869,8.90861312,0.E+000));
#3163 = VERTEX_POINT('',#3164);
#3164 = CARTESIAN_POINT('',(64.5000869,8.90861312,1.12192054));
#3165 = SURFACE_CURVE('',#3166,(#3170,#3182),.PCURVE_S1.);
#3166 = LINE('',#3167,#3168);
#3167 = CARTESIAN_POINT('',(64.5000869,8.90861312,0.E+000));
#3168 = VECTOR('',#3169,1.);
#3169 = DIRECTION('',(0.E+000,0.E+000,1.));
#3170 = PCURVE('',#3171,#3176);
#3171 = PLANE('',#3172);
#3172 = AXIS2_PLACEMENT_3D('',#3173,#3174,#3175);
#3173 = CARTESIAN_POINT('',(64.5000869,8.90861312,0.E+000));
#3174 = DIRECTION('',(0.164398987604,-0.986393923782,0.E+000));
#3175 = DIRECTION('',(-0.986393923782,-0.164398987604,0.E+000));
#3176 = DEFINITIONAL_REPRESENTATION('',(#3177),#3181);
#3177 = LINE('',#3178,#3179);
#3178 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3179 = VECTOR('',#3180,1.);
#3180 = DIRECTION('',(0.E+000,-1.));
#3181 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3182 = PCURVE('',#3183,#3188);
#3183 = PLANE('',#3184);
#3184 = AXIS2_PLACEMENT_3D('',#3185,#3186,#3187);
#3185 = CARTESIAN_POINT('',(64.76111508,8.87424692,0.E+000));
#3186 = DIRECTION('',(-0.130530628242,-0.99144427735,0.E+000));
#3187 = DIRECTION('',(-0.99144427735,0.130530628242,0.E+000));
#3188 = DEFINITIONAL_REPRESENTATION('',(#3189),#3193);
#3189 = LINE('',#3190,#3191);
#3190 = CARTESIAN_POINT('',(0.263280736964,0.E+000));
#3191 = VECTOR('',#3192,1.);
#3192 = DIRECTION('',(0.E+000,-1.));
#3193 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3194 = ORIENTED_EDGE('',*,*,#3195,.T.);
#3195 = EDGE_CURVE('',#3163,#3196,#3198,.T.);
#3196 = VERTEX_POINT('',#3197);
#3197 = CARTESIAN_POINT('',(64.50007166,8.90861058,1.12192054));
#3198 = SURFACE_CURVE('',#3199,(#3203,#3210),.PCURVE_S1.);
#3199 = LINE('',#3200,#3201);
#3200 = CARTESIAN_POINT('',(64.5000869,8.90861312,1.12192054));
#3201 = VECTOR('',#3202,1.);
#3202 = DIRECTION('',(-0.986393923782,-0.164398987604,0.E+000));
#3203 = PCURVE('',#3171,#3204);
#3204 = DEFINITIONAL_REPRESENTATION('',(#3205),#3209);
#3205 = LINE('',#3206,#3207);
#3206 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3207 = VECTOR('',#3208,1.);
#3208 = DIRECTION('',(1.,0.E+000));
#3209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3210 = PCURVE('',#83,#3211);
#3211 = DEFINITIONAL_REPRESENTATION('',(#3212),#3216);
#3212 = LINE('',#3213,#3214);
#3213 = CARTESIAN_POINT('',(64.5000869,20.50861278));
#3214 = VECTOR('',#3215,1.);
#3215 = DIRECTION('',(-0.986393923782,-0.164398987604));
#3216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3217 = ORIENTED_EDGE('',*,*,#3218,.F.);
#3218 = EDGE_CURVE('',#3219,#3196,#3221,.T.);
#3219 = VERTEX_POINT('',#3220);
#3220 = CARTESIAN_POINT('',(64.50007166,8.90861058,0.E+000));
#3221 = SURFACE_CURVE('',#3222,(#3226,#3233),.PCURVE_S1.);
#3222 = LINE('',#3223,#3224);
#3223 = CARTESIAN_POINT('',(64.50007166,8.90861058,0.E+000));
#3224 = VECTOR('',#3225,1.);
#3225 = DIRECTION('',(0.E+000,0.E+000,1.));
#3226 = PCURVE('',#3171,#3227);
#3227 = DEFINITIONAL_REPRESENTATION('',(#3228),#3232);
#3228 = LINE('',#3229,#3230);
#3229 = CARTESIAN_POINT('',(1.545021682388E-005,0.E+000));
#3230 = VECTOR('',#3231,1.);
#3231 = DIRECTION('',(0.E+000,-1.));
#3232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3233 = PCURVE('',#3234,#3239);
#3234 = PLANE('',#3235);
#3235 = AXIS2_PLACEMENT_3D('',#3236,#3237,#3238);
#3236 = CARTESIAN_POINT('',(64.50007166,8.90861058,0.E+000));
#3237 = DIRECTION('',(0.999999826173,5.896225399695E-004,-0.E+000));
#3238 = DIRECTION('',(5.896225399695E-004,-0.999999826173,0.E+000));
#3239 = DEFINITIONAL_REPRESENTATION('',(#3240),#3244);
#3240 = LINE('',#3241,#3242);
#3241 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3242 = VECTOR('',#3243,1.);
#3243 = DIRECTION('',(0.E+000,-1.));
#3244 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3245 = ORIENTED_EDGE('',*,*,#3246,.F.);
#3246 = EDGE_CURVE('',#3161,#3219,#3247,.T.);
#3247 = SURFACE_CURVE('',#3248,(#3252,#3259),.PCURVE_S1.);
#3248 = LINE('',#3249,#3250);
#3249 = CARTESIAN_POINT('',(64.5000869,8.90861312,0.E+000));
#3250 = VECTOR('',#3251,1.);
#3251 = DIRECTION('',(-0.986393923782,-0.164398987604,0.E+000));
#3252 = PCURVE('',#3171,#3253);
#3253 = DEFINITIONAL_REPRESENTATION('',(#3254),#3258);
#3254 = LINE('',#3255,#3256);
#3255 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3256 = VECTOR('',#3257,1.);
#3257 = DIRECTION('',(1.,0.E+000));
#3258 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3259 = PCURVE('',#137,#3260);
#3260 = DEFINITIONAL_REPRESENTATION('',(#3261),#3265);
#3261 = LINE('',#3262,#3263);
#3262 = CARTESIAN_POINT('',(64.5000869,20.50861278));
#3263 = VECTOR('',#3264,1.);
#3264 = DIRECTION('',(-0.986393923782,-0.164398987604));
#3265 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3266 = ADVANCED_FACE('',(#3267),#3234,.F.);
#3267 = FACE_BOUND('',#3268,.F.);
#3268 = EDGE_LOOP('',(#3269,#3270,#3293,#3321));
#3269 = ORIENTED_EDGE('',*,*,#3218,.T.);
#3270 = ORIENTED_EDGE('',*,*,#3271,.T.);
#3271 = EDGE_CURVE('',#3196,#3272,#3274,.T.);
#3272 = VERTEX_POINT('',#3273);
#3273 = CARTESIAN_POINT('',(64.50007674,8.8999949,1.12192054));
#3274 = SURFACE_CURVE('',#3275,(#3279,#3286),.PCURVE_S1.);
#3275 = LINE('',#3276,#3277);
#3276 = CARTESIAN_POINT('',(64.50007166,8.90861058,1.12192054));
#3277 = VECTOR('',#3278,1.);
#3278 = DIRECTION('',(5.896225399695E-004,-0.999999826173,0.E+000));
#3279 = PCURVE('',#3234,#3280);
#3280 = DEFINITIONAL_REPRESENTATION('',(#3281),#3285);
#3281 = LINE('',#3282,#3283);
#3282 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3283 = VECTOR('',#3284,1.);
#3284 = DIRECTION('',(1.,0.E+000));
#3285 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3286 = PCURVE('',#83,#3287);
#3287 = DEFINITIONAL_REPRESENTATION('',(#3288),#3292);
#3288 = LINE('',#3289,#3290);
#3289 = CARTESIAN_POINT('',(64.50007166,20.50861024));
#3290 = VECTOR('',#3291,1.);
#3291 = DIRECTION('',(5.896225399695E-004,-0.999999826173));
#3292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3293 = ORIENTED_EDGE('',*,*,#3294,.F.);
#3294 = EDGE_CURVE('',#3295,#3272,#3297,.T.);
#3295 = VERTEX_POINT('',#3296);
#3296 = CARTESIAN_POINT('',(64.50007674,8.8999949,0.E+000));
#3297 = SURFACE_CURVE('',#3298,(#3302,#3309),.PCURVE_S1.);
#3298 = LINE('',#3299,#3300);
#3299 = CARTESIAN_POINT('',(64.50007674,8.8999949,0.E+000));
#3300 = VECTOR('',#3301,1.);
#3301 = DIRECTION('',(0.E+000,0.E+000,1.));
#3302 = PCURVE('',#3234,#3303);
#3303 = DEFINITIONAL_REPRESENTATION('',(#3304),#3308);
#3304 = LINE('',#3305,#3306);
#3305 = CARTESIAN_POINT('',(8.61568149764E-003,0.E+000));
#3306 = VECTOR('',#3307,1.);
#3307 = DIRECTION('',(0.E+000,-1.));
#3308 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3309 = PCURVE('',#3310,#3315);
#3310 = PLANE('',#3311);
#3311 = AXIS2_PLACEMENT_3D('',#3312,#3313,#3314);
#3312 = CARTESIAN_POINT('',(64.50007674,8.8999949,0.E+000));
#3313 = DIRECTION('',(-8.94067980796E-006,-0.99999999996,0.E+000));
#3314 = DIRECTION('',(-0.99999999996,8.94067980796E-006,0.E+000));
#3315 = DEFINITIONAL_REPRESENTATION('',(#3316),#3320);
#3316 = LINE('',#3317,#3318);
#3317 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3318 = VECTOR('',#3319,1.);
#3319 = DIRECTION('',(0.E+000,-1.));
#3320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3321 = ORIENTED_EDGE('',*,*,#3322,.F.);
#3322 = EDGE_CURVE('',#3219,#3295,#3323,.T.);
#3323 = SURFACE_CURVE('',#3324,(#3328,#3335),.PCURVE_S1.);
#3324 = LINE('',#3325,#3326);
#3325 = CARTESIAN_POINT('',(64.50007166,8.90861058,0.E+000));
#3326 = VECTOR('',#3327,1.);
#3327 = DIRECTION('',(5.896225399695E-004,-0.999999826173,0.E+000));
#3328 = PCURVE('',#3234,#3329);
#3329 = DEFINITIONAL_REPRESENTATION('',(#3330),#3334);
#3330 = LINE('',#3331,#3332);
#3331 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3332 = VECTOR('',#3333,1.);
#3333 = DIRECTION('',(1.,0.E+000));
#3334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3335 = PCURVE('',#137,#3336);
#3336 = DEFINITIONAL_REPRESENTATION('',(#3337),#3341);
#3337 = LINE('',#3338,#3339);
#3338 = CARTESIAN_POINT('',(64.50007166,20.50861024));
#3339 = VECTOR('',#3340,1.);
#3340 = DIRECTION('',(5.896225399695E-004,-0.999999826173));
#3341 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3342 = ADVANCED_FACE('',(#3343),#3310,.F.);
#3343 = FACE_BOUND('',#3344,.F.);
#3344 = EDGE_LOOP('',(#3345,#3346,#3369,#3397));
#3345 = ORIENTED_EDGE('',*,*,#3294,.T.);
#3346 = ORIENTED_EDGE('',*,*,#3347,.T.);
#3347 = EDGE_CURVE('',#3272,#3348,#3350,.T.);
#3348 = VERTEX_POINT('',#3349);
#3349 = CARTESIAN_POINT('',(58.24999272,8.90005078,1.12192054));
#3350 = SURFACE_CURVE('',#3351,(#3355,#3362),.PCURVE_S1.);
#3351 = LINE('',#3352,#3353);
#3352 = CARTESIAN_POINT('',(64.50007674,8.8999949,1.12192054));
#3353 = VECTOR('',#3354,1.);
#3354 = DIRECTION('',(-0.99999999996,8.94067980796E-006,0.E+000));
#3355 = PCURVE('',#3310,#3356);
#3356 = DEFINITIONAL_REPRESENTATION('',(#3357),#3361);
#3357 = LINE('',#3358,#3359);
#3358 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3359 = VECTOR('',#3360,1.);
#3360 = DIRECTION('',(1.,0.E+000));
#3361 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3362 = PCURVE('',#83,#3363);
#3363 = DEFINITIONAL_REPRESENTATION('',(#3364),#3368);
#3364 = LINE('',#3365,#3366);
#3365 = CARTESIAN_POINT('',(64.50007674,20.49999456));
#3366 = VECTOR('',#3367,1.);
#3367 = DIRECTION('',(-0.99999999996,8.94067980796E-006));
#3368 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3369 = ORIENTED_EDGE('',*,*,#3370,.F.);
#3370 = EDGE_CURVE('',#3371,#3348,#3373,.T.);
#3371 = VERTEX_POINT('',#3372);
#3372 = CARTESIAN_POINT('',(58.24999272,8.90005078,0.E+000));
#3373 = SURFACE_CURVE('',#3374,(#3378,#3385),.PCURVE_S1.);
#3374 = LINE('',#3375,#3376);
#3375 = CARTESIAN_POINT('',(58.24999272,8.90005078,0.E+000));
#3376 = VECTOR('',#3377,1.);
#3377 = DIRECTION('',(0.E+000,0.E+000,1.));
#3378 = PCURVE('',#3310,#3379);
#3379 = DEFINITIONAL_REPRESENTATION('',(#3380),#3384);
#3380 = LINE('',#3381,#3382);
#3381 = CARTESIAN_POINT('',(6.25008402025,0.E+000));
#3382 = VECTOR('',#3383,1.);
#3383 = DIRECTION('',(0.E+000,-1.));
#3384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3385 = PCURVE('',#3386,#3391);
#3386 = PLANE('',#3387);
#3387 = AXIS2_PLACEMENT_3D('',#3388,#3389,#3390);
#3388 = CARTESIAN_POINT('',(58.24999272,8.90005078,0.E+000));
#3389 = DIRECTION('',(0.130524122184,-0.991445133897,0.E+000));
#3390 = DIRECTION('',(-0.991445133897,-0.130524122184,0.E+000));
#3391 = DEFINITIONAL_REPRESENTATION('',(#3392),#3396);
#3392 = LINE('',#3393,#3394);
#3393 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3394 = VECTOR('',#3395,1.);
#3395 = DIRECTION('',(0.E+000,-1.));
#3396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3397 = ORIENTED_EDGE('',*,*,#3398,.F.);
#3398 = EDGE_CURVE('',#3295,#3371,#3399,.T.);
#3399 = SURFACE_CURVE('',#3400,(#3404,#3411),.PCURVE_S1.);
#3400 = LINE('',#3401,#3402);
#3401 = CARTESIAN_POINT('',(64.50007674,8.8999949,0.E+000));
#3402 = VECTOR('',#3403,1.);
#3403 = DIRECTION('',(-0.99999999996,8.94067980796E-006,0.E+000));
#3404 = PCURVE('',#3310,#3405);
#3405 = DEFINITIONAL_REPRESENTATION('',(#3406),#3410);
#3406 = LINE('',#3407,#3408);
#3407 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3408 = VECTOR('',#3409,1.);
#3409 = DIRECTION('',(1.,0.E+000));
#3410 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3411 = PCURVE('',#137,#3412);
#3412 = DEFINITIONAL_REPRESENTATION('',(#3413),#3417);
#3413 = LINE('',#3414,#3415);
#3414 = CARTESIAN_POINT('',(64.50007674,20.49999456));
#3415 = VECTOR('',#3416,1.);
#3416 = DIRECTION('',(-0.99999999996,8.94067980796E-006));
#3417 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3418 = ADVANCED_FACE('',(#3419),#3386,.F.);
#3419 = FACE_BOUND('',#3420,.F.);
#3420 = EDGE_LOOP('',(#3421,#3422,#3445,#3473));
#3421 = ORIENTED_EDGE('',*,*,#3370,.T.);
#3422 = ORIENTED_EDGE('',*,*,#3423,.T.);
#3423 = EDGE_CURVE('',#3348,#3424,#3426,.T.);
#3424 = VERTEX_POINT('',#3425);
#3425 = CARTESIAN_POINT('',(58.1442449,8.88612904,1.12192054));
#3426 = SURFACE_CURVE('',#3427,(#3431,#3438),.PCURVE_S1.);
#3427 = LINE('',#3428,#3429);
#3428 = CARTESIAN_POINT('',(58.24999272,8.90005078,1.12192054));
#3429 = VECTOR('',#3430,1.);
#3430 = DIRECTION('',(-0.991445133897,-0.130524122184,0.E+000));
#3431 = PCURVE('',#3386,#3432);
#3432 = DEFINITIONAL_REPRESENTATION('',(#3433),#3437);
#3433 = LINE('',#3434,#3435);
#3434 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3435 = VECTOR('',#3436,1.);
#3436 = DIRECTION('',(1.,0.E+000));
#3437 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3438 = PCURVE('',#83,#3439);
#3439 = DEFINITIONAL_REPRESENTATION('',(#3440),#3444);
#3440 = LINE('',#3441,#3442);
#3441 = CARTESIAN_POINT('',(58.24999272,20.50005044));
#3442 = VECTOR('',#3443,1.);
#3443 = DIRECTION('',(-0.991445133897,-0.130524122184));
#3444 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3445 = ORIENTED_EDGE('',*,*,#3446,.F.);
#3446 = EDGE_CURVE('',#3447,#3424,#3449,.T.);
#3447 = VERTEX_POINT('',#3448);
#3448 = CARTESIAN_POINT('',(58.1442449,8.88612904,0.E+000));
#3449 = SURFACE_CURVE('',#3450,(#3454,#3461),.PCURVE_S1.);
#3450 = LINE('',#3451,#3452);
#3451 = CARTESIAN_POINT('',(58.1442449,8.88612904,0.E+000));
#3452 = VECTOR('',#3453,1.);
#3453 = DIRECTION('',(0.E+000,0.E+000,1.));
#3454 = PCURVE('',#3386,#3455);
#3455 = DEFINITIONAL_REPRESENTATION('',(#3456),#3460);
#3456 = LINE('',#3457,#3458);
#3457 = CARTESIAN_POINT('',(0.106660284452,0.E+000));
#3458 = VECTOR('',#3459,1.);
#3459 = DIRECTION('',(0.E+000,-1.));
#3460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3461 = PCURVE('',#3462,#3467);
#3462 = PLANE('',#3463);
#3463 = AXIS2_PLACEMENT_3D('',#3464,#3465,#3466);
#3464 = CARTESIAN_POINT('',(58.1442449,8.88612904,0.E+000));
#3465 = DIRECTION('',(0.382677914969,-0.923881817872,0.E+000));
#3466 = DIRECTION('',(-0.923881817872,-0.382677914969,0.E+000));
#3467 = DEFINITIONAL_REPRESENTATION('',(#3468),#3472);
#3468 = LINE('',#3469,#3470);
#3469 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3470 = VECTOR('',#3471,1.);
#3471 = DIRECTION('',(0.E+000,-1.));
#3472 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3473 = ORIENTED_EDGE('',*,*,#3474,.F.);
#3474 = EDGE_CURVE('',#3371,#3447,#3475,.T.);
#3475 = SURFACE_CURVE('',#3476,(#3480,#3487),.PCURVE_S1.);
#3476 = LINE('',#3477,#3478);
#3477 = CARTESIAN_POINT('',(58.24999272,8.90005078,0.E+000));
#3478 = VECTOR('',#3479,1.);
#3479 = DIRECTION('',(-0.991445133897,-0.130524122184,0.E+000));
#3480 = PCURVE('',#3386,#3481);
#3481 = DEFINITIONAL_REPRESENTATION('',(#3482),#3486);
#3482 = LINE('',#3483,#3484);
#3483 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3484 = VECTOR('',#3485,1.);
#3485 = DIRECTION('',(1.,0.E+000));
#3486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3487 = PCURVE('',#137,#3488);
#3488 = DEFINITIONAL_REPRESENTATION('',(#3489),#3493);
#3489 = LINE('',#3490,#3491);
#3490 = CARTESIAN_POINT('',(58.24999272,20.50005044));
#3491 = VECTOR('',#3492,1.);
#3492 = DIRECTION('',(-0.991445133897,-0.130524122184));
#3493 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3494 = ADVANCED_FACE('',(#3495),#3462,.F.);
#3495 = FACE_BOUND('',#3496,.F.);
#3496 = EDGE_LOOP('',(#3497,#3498,#3521,#3549));
#3497 = ORIENTED_EDGE('',*,*,#3446,.T.);
#3498 = ORIENTED_EDGE('',*,*,#3499,.T.);
#3499 = EDGE_CURVE('',#3424,#3500,#3502,.T.);
#3500 = VERTEX_POINT('',#3501);
#3501 = CARTESIAN_POINT('',(57.92923136,8.79706902,1.12192054));
#3502 = SURFACE_CURVE('',#3503,(#3507,#3514),.PCURVE_S1.);
#3503 = LINE('',#3504,#3505);
#3504 = CARTESIAN_POINT('',(58.1442449,8.88612904,1.12192054));
#3505 = VECTOR('',#3506,1.);
#3506 = DIRECTION('',(-0.923881817872,-0.382677914969,0.E+000));
#3507 = PCURVE('',#3462,#3508);
#3508 = DEFINITIONAL_REPRESENTATION('',(#3509),#3513);
#3509 = LINE('',#3510,#3511);
#3510 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3511 = VECTOR('',#3512,1.);
#3512 = DIRECTION('',(1.,0.E+000));
#3513 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3514 = PCURVE('',#83,#3515);
#3515 = DEFINITIONAL_REPRESENTATION('',(#3516),#3520);
#3516 = LINE('',#3517,#3518);
#3517 = CARTESIAN_POINT('',(58.1442449,20.4861287));
#3518 = VECTOR('',#3519,1.);
#3519 = DIRECTION('',(-0.923881817872,-0.382677914969));
#3520 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3521 = ORIENTED_EDGE('',*,*,#3522,.F.);
#3522 = EDGE_CURVE('',#3523,#3500,#3525,.T.);
#3523 = VERTEX_POINT('',#3524);
#3524 = CARTESIAN_POINT('',(57.92923136,8.79706902,0.E+000));
#3525 = SURFACE_CURVE('',#3526,(#3530,#3537),.PCURVE_S1.);
#3526 = LINE('',#3527,#3528);
#3527 = CARTESIAN_POINT('',(57.92923136,8.79706902,0.E+000));
#3528 = VECTOR('',#3529,1.);
#3529 = DIRECTION('',(0.E+000,0.E+000,1.));
#3530 = PCURVE('',#3462,#3531);
#3531 = DEFINITIONAL_REPRESENTATION('',(#3532),#3536);
#3532 = LINE('',#3533,#3534);
#3533 = CARTESIAN_POINT('',(0.232728402963,0.E+000));
#3534 = VECTOR('',#3535,1.);
#3535 = DIRECTION('',(0.E+000,-1.));
#3536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3537 = PCURVE('',#3538,#3543);
#3538 = PLANE('',#3539);
#3539 = AXIS2_PLACEMENT_3D('',#3540,#3541,#3542);
#3540 = CARTESIAN_POINT('',(57.92923136,8.79706902,0.E+000));
#3541 = DIRECTION('',(0.608763035875,-0.793352107297,0.E+000));
#3542 = DIRECTION('',(-0.793352107297,-0.608763035875,0.E+000));
#3543 = DEFINITIONAL_REPRESENTATION('',(#3544),#3548);
#3544 = LINE('',#3545,#3546);
#3545 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3546 = VECTOR('',#3547,1.);
#3547 = DIRECTION('',(0.E+000,-1.));
#3548 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3549 = ORIENTED_EDGE('',*,*,#3550,.F.);
#3550 = EDGE_CURVE('',#3447,#3523,#3551,.T.);
#3551 = SURFACE_CURVE('',#3552,(#3556,#3563),.PCURVE_S1.);
#3552 = LINE('',#3553,#3554);
#3553 = CARTESIAN_POINT('',(58.1442449,8.88612904,0.E+000));
#3554 = VECTOR('',#3555,1.);
#3555 = DIRECTION('',(-0.923881817872,-0.382677914969,0.E+000));
#3556 = PCURVE('',#3462,#3557);
#3557 = DEFINITIONAL_REPRESENTATION('',(#3558),#3562);
#3558 = LINE('',#3559,#3560);
#3559 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3560 = VECTOR('',#3561,1.);
#3561 = DIRECTION('',(1.,0.E+000));
#3562 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3563 = PCURVE('',#137,#3564);
#3564 = DEFINITIONAL_REPRESENTATION('',(#3565),#3569);
#3565 = LINE('',#3566,#3567);
#3566 = CARTESIAN_POINT('',(58.1442449,20.4861287));
#3567 = VECTOR('',#3568,1.);
#3568 = DIRECTION('',(-0.923881817872,-0.382677914969));
#3569 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3570 = ADVANCED_FACE('',(#3571),#3538,.F.);
#3571 = FACE_BOUND('',#3572,.F.);
#3572 = EDGE_LOOP('',(#3573,#3574,#3597,#3625));
#3573 = ORIENTED_EDGE('',*,*,#3522,.T.);
#3574 = ORIENTED_EDGE('',*,*,#3575,.T.);
#3575 = EDGE_CURVE('',#3500,#3576,#3578,.T.);
#3576 = VERTEX_POINT('',#3577);
#3577 = CARTESIAN_POINT('',(57.74459622,8.6553929,1.12192054));
#3578 = SURFACE_CURVE('',#3579,(#3583,#3590),.PCURVE_S1.);
#3579 = LINE('',#3580,#3581);
#3580 = CARTESIAN_POINT('',(57.92923136,8.79706902,1.12192054));
#3581 = VECTOR('',#3582,1.);
#3582 = DIRECTION('',(-0.793352107297,-0.608763035875,0.E+000));
#3583 = PCURVE('',#3538,#3584);
#3584 = DEFINITIONAL_REPRESENTATION('',(#3585),#3589);
#3585 = LINE('',#3586,#3587);
#3586 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3587 = VECTOR('',#3588,1.);
#3588 = DIRECTION('',(1.,0.E+000));
#3589 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3590 = PCURVE('',#83,#3591);
#3591 = DEFINITIONAL_REPRESENTATION('',(#3592),#3596);
#3592 = LINE('',#3593,#3594);
#3593 = CARTESIAN_POINT('',(57.92923136,20.39706868));
#3594 = VECTOR('',#3595,1.);
#3595 = DIRECTION('',(-0.793352107297,-0.608763035875));
#3596 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3597 = ORIENTED_EDGE('',*,*,#3598,.F.);
#3598 = EDGE_CURVE('',#3599,#3576,#3601,.T.);
#3599 = VERTEX_POINT('',#3600);
#3600 = CARTESIAN_POINT('',(57.74459622,8.6553929,0.E+000));
#3601 = SURFACE_CURVE('',#3602,(#3606,#3613),.PCURVE_S1.);
#3602 = LINE('',#3603,#3604);
#3603 = CARTESIAN_POINT('',(57.74459622,8.6553929,0.E+000));
#3604 = VECTOR('',#3605,1.);
#3605 = DIRECTION('',(0.E+000,0.E+000,1.));
#3606 = PCURVE('',#3538,#3607);
#3607 = DEFINITIONAL_REPRESENTATION('',(#3608),#3612);
#3608 = LINE('',#3609,#3610);
#3609 = CARTESIAN_POINT('',(0.232727862322,0.E+000));
#3610 = VECTOR('',#3611,1.);
#3611 = DIRECTION('',(0.E+000,-1.));
#3612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3613 = PCURVE('',#3614,#3619);
#3614 = PLANE('',#3615);
#3615 = AXIS2_PLACEMENT_3D('',#3616,#3617,#3618);
#3616 = CARTESIAN_POINT('',(57.74459622,8.6553929,0.E+000));
#3617 = DIRECTION('',(0.793356151903,-0.608757764828,0.E+000));
#3618 = DIRECTION('',(-0.608757764828,-0.793356151903,0.E+000));
#3619 = DEFINITIONAL_REPRESENTATION('',(#3620),#3624);
#3620 = LINE('',#3621,#3622);
#3621 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3622 = VECTOR('',#3623,1.);
#3623 = DIRECTION('',(0.E+000,-1.));
#3624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3625 = ORIENTED_EDGE('',*,*,#3626,.F.);
#3626 = EDGE_CURVE('',#3523,#3599,#3627,.T.);
#3627 = SURFACE_CURVE('',#3628,(#3632,#3639),.PCURVE_S1.);
#3628 = LINE('',#3629,#3630);
#3629 = CARTESIAN_POINT('',(57.92923136,8.79706902,0.E+000));
#3630 = VECTOR('',#3631,1.);
#3631 = DIRECTION('',(-0.793352107297,-0.608763035875,0.E+000));
#3632 = PCURVE('',#3538,#3633);
#3633 = DEFINITIONAL_REPRESENTATION('',(#3634),#3638);
#3634 = LINE('',#3635,#3636);
#3635 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3636 = VECTOR('',#3637,1.);
#3637 = DIRECTION('',(1.,0.E+000));
#3638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3639 = PCURVE('',#137,#3640);
#3640 = DEFINITIONAL_REPRESENTATION('',(#3641),#3645);
#3641 = LINE('',#3642,#3643);
#3642 = CARTESIAN_POINT('',(57.92923136,20.39706868));
#3643 = VECTOR('',#3644,1.);
#3644 = DIRECTION('',(-0.793352107297,-0.608763035875));
#3645 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3646 = ADVANCED_FACE('',(#3647),#3614,.F.);
#3647 = FACE_BOUND('',#3648,.F.);
#3648 = EDGE_LOOP('',(#3649,#3650,#3673,#3701));
#3649 = ORIENTED_EDGE('',*,*,#3598,.T.);
#3650 = ORIENTED_EDGE('',*,*,#3651,.T.);
#3651 = EDGE_CURVE('',#3576,#3652,#3654,.T.);
#3652 = VERTEX_POINT('',#3653);
#3653 = CARTESIAN_POINT('',(57.6029201,8.47075522,1.12192054));
#3654 = SURFACE_CURVE('',#3655,(#3659,#3666),.PCURVE_S1.);
#3655 = LINE('',#3656,#3657);
#3656 = CARTESIAN_POINT('',(57.74459622,8.6553929,1.12192054));
#3657 = VECTOR('',#3658,1.);
#3658 = DIRECTION('',(-0.608757764828,-0.793356151903,0.E+000));
#3659 = PCURVE('',#3614,#3660);
#3660 = DEFINITIONAL_REPRESENTATION('',(#3661),#3665);
#3661 = LINE('',#3662,#3663);
#3662 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3663 = VECTOR('',#3664,1.);
#3664 = DIRECTION('',(1.,0.E+000));
#3665 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3666 = PCURVE('',#83,#3667);
#3667 = DEFINITIONAL_REPRESENTATION('',(#3668),#3672);
#3668 = LINE('',#3669,#3670);
#3669 = CARTESIAN_POINT('',(57.74459622,20.25539256));
#3670 = VECTOR('',#3671,1.);
#3671 = DIRECTION('',(-0.608757764828,-0.793356151903));
#3672 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3673 = ORIENTED_EDGE('',*,*,#3674,.F.);
#3674 = EDGE_CURVE('',#3675,#3652,#3677,.T.);
#3675 = VERTEX_POINT('',#3676);
#3676 = CARTESIAN_POINT('',(57.6029201,8.47075522,0.E+000));
#3677 = SURFACE_CURVE('',#3678,(#3682,#3689),.PCURVE_S1.);
#3678 = LINE('',#3679,#3680);
#3679 = CARTESIAN_POINT('',(57.6029201,8.47075522,0.E+000));
#3680 = VECTOR('',#3681,1.);
#3681 = DIRECTION('',(0.E+000,0.E+000,1.));
#3682 = PCURVE('',#3614,#3683);
#3683 = DEFINITIONAL_REPRESENTATION('',(#3684),#3688);
#3684 = LINE('',#3685,#3686);
#3685 = CARTESIAN_POINT('',(0.232729877442,0.E+000));
#3686 = VECTOR('',#3687,1.);
#3687 = DIRECTION('',(0.E+000,-1.));
#3688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3689 = PCURVE('',#3690,#3695);
#3690 = PLANE('',#3691);
#3691 = AXIS2_PLACEMENT_3D('',#3692,#3693,#3694);
#3692 = CARTESIAN_POINT('',(57.6029201,8.47075522,0.E+000));
#3693 = DIRECTION('',(0.923880219574,-0.382681773644,0.E+000));
#3694 = DIRECTION('',(-0.382681773644,-0.923880219574,0.E+000));
#3695 = DEFINITIONAL_REPRESENTATION('',(#3696),#3700);
#3696 = LINE('',#3697,#3698);
#3697 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3698 = VECTOR('',#3699,1.);
#3699 = DIRECTION('',(0.E+000,-1.));
#3700 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3701 = ORIENTED_EDGE('',*,*,#3702,.F.);
#3702 = EDGE_CURVE('',#3599,#3675,#3703,.T.);
#3703 = SURFACE_CURVE('',#3704,(#3708,#3715),.PCURVE_S1.);
#3704 = LINE('',#3705,#3706);
#3705 = CARTESIAN_POINT('',(57.74459622,8.6553929,0.E+000));
#3706 = VECTOR('',#3707,1.);
#3707 = DIRECTION('',(-0.608757764828,-0.793356151903,0.E+000));
#3708 = PCURVE('',#3614,#3709);
#3709 = DEFINITIONAL_REPRESENTATION('',(#3710),#3714);
#3710 = LINE('',#3711,#3712);
#3711 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3712 = VECTOR('',#3713,1.);
#3713 = DIRECTION('',(1.,0.E+000));
#3714 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3715 = PCURVE('',#137,#3716);
#3716 = DEFINITIONAL_REPRESENTATION('',(#3717),#3721);
#3717 = LINE('',#3718,#3719);
#3718 = CARTESIAN_POINT('',(57.74459622,20.25539256));
#3719 = VECTOR('',#3720,1.);
#3720 = DIRECTION('',(-0.608757764828,-0.793356151903));
#3721 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3722 = ADVANCED_FACE('',(#3723),#3690,.F.);
#3723 = FACE_BOUND('',#3724,.F.);
#3724 = EDGE_LOOP('',(#3725,#3726,#3749,#3777));
#3725 = ORIENTED_EDGE('',*,*,#3674,.T.);
#3726 = ORIENTED_EDGE('',*,*,#3727,.T.);
#3727 = EDGE_CURVE('',#3652,#3728,#3730,.T.);
#3728 = VERTEX_POINT('',#3729);
#3729 = CARTESIAN_POINT('',(57.51386008,8.25574422,1.12192054));
#3730 = SURFACE_CURVE('',#3731,(#3735,#3742),.PCURVE_S1.);
#3731 = LINE('',#3732,#3733);
#3732 = CARTESIAN_POINT('',(57.6029201,8.47075522,1.12192054));
#3733 = VECTOR('',#3734,1.);
#3734 = DIRECTION('',(-0.382681773644,-0.923880219574,0.E+000));
#3735 = PCURVE('',#3690,#3736);
#3736 = DEFINITIONAL_REPRESENTATION('',(#3737),#3741);
#3737 = LINE('',#3738,#3739);
#3738 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3739 = VECTOR('',#3740,1.);
#3740 = DIRECTION('',(1.,0.E+000));
#3741 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3742 = PCURVE('',#83,#3743);
#3743 = DEFINITIONAL_REPRESENTATION('',(#3744),#3748);
#3744 = LINE('',#3745,#3746);
#3745 = CARTESIAN_POINT('',(57.6029201,20.07075488));
#3746 = VECTOR('',#3747,1.);
#3747 = DIRECTION('',(-0.382681773644,-0.923880219574));
#3748 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3749 = ORIENTED_EDGE('',*,*,#3750,.F.);
#3750 = EDGE_CURVE('',#3751,#3728,#3753,.T.);
#3751 = VERTEX_POINT('',#3752);
#3752 = CARTESIAN_POINT('',(57.51386008,8.25574422,0.E+000));
#3753 = SURFACE_CURVE('',#3754,(#3758,#3765),.PCURVE_S1.);
#3754 = LINE('',#3755,#3756);
#3755 = CARTESIAN_POINT('',(57.51386008,8.25574422,0.E+000));
#3756 = VECTOR('',#3757,1.);
#3757 = DIRECTION('',(0.E+000,0.E+000,1.));
#3758 = PCURVE('',#3690,#3759);
#3759 = DEFINITIONAL_REPRESENTATION('',(#3760),#3764);
#3760 = LINE('',#3761,#3762);
#3761 = CARTESIAN_POINT('',(0.232726056305,0.E+000));
#3762 = VECTOR('',#3763,1.);
#3763 = DIRECTION('',(0.E+000,-1.));
#3764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3765 = PCURVE('',#3766,#3771);
#3766 = PLANE('',#3767);
#3767 = AXIS2_PLACEMENT_3D('',#3768,#3769,#3770);
#3768 = CARTESIAN_POINT('',(57.51386008,8.25574422,0.E+000));
#3769 = DIRECTION('',(0.991444262782,-0.130530738897,0.E+000));
#3770 = DIRECTION('',(-0.130530738897,-0.991444262782,0.E+000));
#3771 = DEFINITIONAL_REPRESENTATION('',(#3772),#3776);
#3772 = LINE('',#3773,#3774);
#3773 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3774 = VECTOR('',#3775,1.);
#3775 = DIRECTION('',(0.E+000,-1.));
#3776 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3777 = ORIENTED_EDGE('',*,*,#3778,.F.);
#3778 = EDGE_CURVE('',#3675,#3751,#3779,.T.);
#3779 = SURFACE_CURVE('',#3780,(#3784,#3791),.PCURVE_S1.);
#3780 = LINE('',#3781,#3782);
#3781 = CARTESIAN_POINT('',(57.6029201,8.47075522,0.E+000));
#3782 = VECTOR('',#3783,1.);
#3783 = DIRECTION('',(-0.382681773644,-0.923880219574,0.E+000));
#3784 = PCURVE('',#3690,#3785);
#3785 = DEFINITIONAL_REPRESENTATION('',(#3786),#3790);
#3786 = LINE('',#3787,#3788);
#3787 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3788 = VECTOR('',#3789,1.);
#3789 = DIRECTION('',(1.,0.E+000));
#3790 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3791 = PCURVE('',#137,#3792);
#3792 = DEFINITIONAL_REPRESENTATION('',(#3793),#3797);
#3793 = LINE('',#3794,#3795);
#3794 = CARTESIAN_POINT('',(57.6029201,20.07075488));
#3795 = VECTOR('',#3796,1.);
#3796 = DIRECTION('',(-0.382681773644,-0.923880219574));
#3797 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3798 = ADVANCED_FACE('',(#3799),#3766,.F.);
#3799 = FACE_BOUND('',#3800,.F.);
#3800 = EDGE_LOOP('',(#3801,#3802,#3825,#3853));
#3801 = ORIENTED_EDGE('',*,*,#3750,.T.);
#3802 = ORIENTED_EDGE('',*,*,#3803,.T.);
#3803 = EDGE_CURVE('',#3728,#3804,#3806,.T.);
#3804 = VERTEX_POINT('',#3805);
#3805 = CARTESIAN_POINT('',(57.48348168,8.02500554,1.12192054));
#3806 = SURFACE_CURVE('',#3807,(#3811,#3818),.PCURVE_S1.);
#3807 = LINE('',#3808,#3809);
#3808 = CARTESIAN_POINT('',(57.51386008,8.25574422,1.12192054));
#3809 = VECTOR('',#3810,1.);
#3810 = DIRECTION('',(-0.130530738897,-0.991444262782,0.E+000));
#3811 = PCURVE('',#3766,#3812);
#3812 = DEFINITIONAL_REPRESENTATION('',(#3813),#3817);
#3813 = LINE('',#3814,#3815);
#3814 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3815 = VECTOR('',#3816,1.);
#3816 = DIRECTION('',(1.,0.E+000));
#3817 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3818 = PCURVE('',#83,#3819);
#3819 = DEFINITIONAL_REPRESENTATION('',(#3820),#3824);
#3820 = LINE('',#3821,#3822);
#3821 = CARTESIAN_POINT('',(57.51386008,19.85574388));
#3822 = VECTOR('',#3823,1.);
#3823 = DIRECTION('',(-0.130530738897,-0.991444262782));
#3824 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3825 = ORIENTED_EDGE('',*,*,#3826,.F.);
#3826 = EDGE_CURVE('',#3827,#3804,#3829,.T.);
#3827 = VERTEX_POINT('',#3828);
#3828 = CARTESIAN_POINT('',(57.48348168,8.02500554,0.E+000));
#3829 = SURFACE_CURVE('',#3830,(#3834,#3841),.PCURVE_S1.);
#3830 = LINE('',#3831,#3832);
#3831 = CARTESIAN_POINT('',(57.48348168,8.02500554,0.E+000));
#3832 = VECTOR('',#3833,1.);
#3833 = DIRECTION('',(0.E+000,0.E+000,1.));
#3834 = PCURVE('',#3766,#3835);
#3835 = DEFINITIONAL_REPRESENTATION('',(#3836),#3840);
#3836 = LINE('',#3837,#3838);
#3837 = CARTESIAN_POINT('',(0.232729855486,0.E+000));
#3838 = VECTOR('',#3839,1.);
#3839 = DIRECTION('',(0.E+000,-1.));
#3840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3841 = PCURVE('',#3842,#3847);
#3842 = PLANE('',#3843);
#3843 = AXIS2_PLACEMENT_3D('',#3844,#3845,#3846);
#3844 = CARTESIAN_POINT('',(57.48348168,8.02500554,0.E+000));
#3845 = DIRECTION('',(0.991443460432,0.130536832989,-0.E+000));
#3846 = DIRECTION('',(0.130536832989,-0.991443460432,0.E+000));
#3847 = DEFINITIONAL_REPRESENTATION('',(#3848),#3852);
#3848 = LINE('',#3849,#3850);
#3849 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3850 = VECTOR('',#3851,1.);
#3851 = DIRECTION('',(0.E+000,-1.));
#3852 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3853 = ORIENTED_EDGE('',*,*,#3854,.F.);
#3854 = EDGE_CURVE('',#3751,#3827,#3855,.T.);
#3855 = SURFACE_CURVE('',#3856,(#3860,#3867),.PCURVE_S1.);
#3856 = LINE('',#3857,#3858);
#3857 = CARTESIAN_POINT('',(57.51386008,8.25574422,0.E+000));
#3858 = VECTOR('',#3859,1.);
#3859 = DIRECTION('',(-0.130530738897,-0.991444262782,0.E+000));
#3860 = PCURVE('',#3766,#3861);
#3861 = DEFINITIONAL_REPRESENTATION('',(#3862),#3866);
#3862 = LINE('',#3863,#3864);
#3863 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3864 = VECTOR('',#3865,1.);
#3865 = DIRECTION('',(1.,0.E+000));
#3866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3867 = PCURVE('',#137,#3868);
#3868 = DEFINITIONAL_REPRESENTATION('',(#3869),#3873);
#3869 = LINE('',#3870,#3871);
#3870 = CARTESIAN_POINT('',(57.51386008,19.85574388));
#3871 = VECTOR('',#3872,1.);
#3872 = DIRECTION('',(-0.130530738897,-0.991444262782));
#3873 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3874 = ADVANCED_FACE('',(#3875),#3842,.F.);
#3875 = FACE_BOUND('',#3876,.F.);
#3876 = EDGE_LOOP('',(#3877,#3878,#3901,#3929));
#3877 = ORIENTED_EDGE('',*,*,#3826,.T.);
#3878 = ORIENTED_EDGE('',*,*,#3879,.T.);
#3879 = EDGE_CURVE('',#3804,#3880,#3882,.T.);
#3880 = VERTEX_POINT('',#3881);
#3881 = CARTESIAN_POINT('',(57.49994342,7.89997658,1.12192054));
#3882 = SURFACE_CURVE('',#3883,(#3887,#3894),.PCURVE_S1.);
#3883 = LINE('',#3884,#3885);
#3884 = CARTESIAN_POINT('',(57.48348168,8.02500554,1.12192054));
#3885 = VECTOR('',#3886,1.);
#3886 = DIRECTION('',(0.130536832989,-0.991443460432,0.E+000));
#3887 = PCURVE('',#3842,#3888);
#3888 = DEFINITIONAL_REPRESENTATION('',(#3889),#3893);
#3889 = LINE('',#3890,#3891);
#3890 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3891 = VECTOR('',#3892,1.);
#3892 = DIRECTION('',(1.,0.E+000));
#3893 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3894 = PCURVE('',#83,#3895);
#3895 = DEFINITIONAL_REPRESENTATION('',(#3896),#3900);
#3896 = LINE('',#3897,#3898);
#3897 = CARTESIAN_POINT('',(57.48348168,19.6250052));
#3898 = VECTOR('',#3899,1.);
#3899 = DIRECTION('',(0.130536832989,-0.991443460432));
#3900 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3901 = ORIENTED_EDGE('',*,*,#3902,.F.);
#3902 = EDGE_CURVE('',#3903,#3880,#3905,.T.);
#3903 = VERTEX_POINT('',#3904);
#3904 = CARTESIAN_POINT('',(57.49994342,7.89997658,0.E+000));
#3905 = SURFACE_CURVE('',#3906,(#3910,#3917),.PCURVE_S1.);
#3906 = LINE('',#3907,#3908);
#3907 = CARTESIAN_POINT('',(57.49994342,7.89997658,0.E+000));
#3908 = VECTOR('',#3909,1.);
#3909 = DIRECTION('',(0.E+000,0.E+000,1.));
#3910 = PCURVE('',#3842,#3911);
#3911 = DEFINITIONAL_REPRESENTATION('',(#3912),#3916);
#3912 = LINE('',#3913,#3914);
#3913 = CARTESIAN_POINT('',(0.126108008162,0.E+000));
#3914 = VECTOR('',#3915,1.);
#3915 = DIRECTION('',(0.E+000,-1.));
#3916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3917 = PCURVE('',#3918,#3923);
#3918 = PLANE('',#3919);
#3919 = AXIS2_PLACEMENT_3D('',#3920,#3921,#3922);
#3920 = CARTESIAN_POINT('',(57.49994342,7.89997658,0.E+000));
#3921 = DIRECTION('',(-9.950371904856E-002,0.995037190207,0.E+000));
#3922 = DIRECTION('',(0.995037190207,9.950371904856E-002,0.E+000));
#3923 = DEFINITIONAL_REPRESENTATION('',(#3924),#3928);
#3924 = LINE('',#3925,#3926);
#3925 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3926 = VECTOR('',#3927,1.);
#3927 = DIRECTION('',(0.E+000,-1.));
#3928 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3929 = ORIENTED_EDGE('',*,*,#3930,.F.);
#3930 = EDGE_CURVE('',#3827,#3903,#3931,.T.);
#3931 = SURFACE_CURVE('',#3932,(#3936,#3943),.PCURVE_S1.);
#3932 = LINE('',#3933,#3934);
#3933 = CARTESIAN_POINT('',(57.48348168,8.02500554,0.E+000));
#3934 = VECTOR('',#3935,1.);
#3935 = DIRECTION('',(0.130536832989,-0.991443460432,0.E+000));
#3936 = PCURVE('',#3842,#3937);
#3937 = DEFINITIONAL_REPRESENTATION('',(#3938),#3942);
#3938 = LINE('',#3939,#3940);
#3939 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3940 = VECTOR('',#3941,1.);
#3941 = DIRECTION('',(1.,0.E+000));
#3942 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3943 = PCURVE('',#137,#3944);
#3944 = DEFINITIONAL_REPRESENTATION('',(#3945),#3949);
#3945 = LINE('',#3946,#3947);
#3946 = CARTESIAN_POINT('',(57.48348168,19.6250052));
#3947 = VECTOR('',#3948,1.);
#3948 = DIRECTION('',(0.130536832989,-0.991443460432));
#3949 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3950 = ADVANCED_FACE('',(#3951),#3918,.F.);
#3951 = FACE_BOUND('',#3952,.F.);
#3952 = EDGE_LOOP('',(#3953,#3954,#3977,#4005));
#3953 = ORIENTED_EDGE('',*,*,#3902,.T.);
#3954 = ORIENTED_EDGE('',*,*,#3955,.T.);
#3955 = EDGE_CURVE('',#3880,#3956,#3958,.T.);
#3956 = VERTEX_POINT('',#3957);
#3957 = CARTESIAN_POINT('',(57.49999422,7.89998166,1.12192054));
#3958 = SURFACE_CURVE('',#3959,(#3963,#3970),.PCURVE_S1.);
#3959 = LINE('',#3960,#3961);
#3960 = CARTESIAN_POINT('',(57.49994342,7.89997658,1.12192054));
#3961 = VECTOR('',#3962,1.);
#3962 = DIRECTION('',(0.995037190207,9.950371904856E-002,0.E+000));
#3963 = PCURVE('',#3918,#3964);
#3964 = DEFINITIONAL_REPRESENTATION('',(#3965),#3969);
#3965 = LINE('',#3966,#3967);
#3966 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#3967 = VECTOR('',#3968,1.);
#3968 = DIRECTION('',(1.,0.E+000));
#3969 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3970 = PCURVE('',#83,#3971);
#3971 = DEFINITIONAL_REPRESENTATION('',(#3972),#3976);
#3972 = LINE('',#3973,#3974);
#3973 = CARTESIAN_POINT('',(57.49994342,19.49997624));
#3974 = VECTOR('',#3975,1.);
#3975 = DIRECTION('',(0.995037190207,9.950371904856E-002));
#3976 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3977 = ORIENTED_EDGE('',*,*,#3978,.F.);
#3978 = EDGE_CURVE('',#3979,#3956,#3981,.T.);
#3979 = VERTEX_POINT('',#3980);
#3980 = CARTESIAN_POINT('',(57.49999422,7.89998166,0.E+000));
#3981 = SURFACE_CURVE('',#3982,(#3986,#3993),.PCURVE_S1.);
#3982 = LINE('',#3983,#3984);
#3983 = CARTESIAN_POINT('',(57.49999422,7.89998166,0.E+000));
#3984 = VECTOR('',#3985,1.);
#3985 = DIRECTION('',(0.E+000,0.E+000,1.));
#3986 = PCURVE('',#3918,#3987);
#3987 = DEFINITIONAL_REPRESENTATION('',(#3988),#3992);
#3988 = LINE('',#3989,#3990);
#3989 = CARTESIAN_POINT('',(5.105336815222E-005,0.E+000));
#3990 = VECTOR('',#3991,1.);
#3991 = DIRECTION('',(0.E+000,-1.));
#3992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3993 = PCURVE('',#3994,#3999);
#3994 = PLANE('',#3995);
#3995 = AXIS2_PLACEMENT_3D('',#3996,#3997,#3998);
#3996 = CARTESIAN_POINT('',(57.49999422,7.89998166,0.E+000));
#3997 = DIRECTION('',(0.999999999993,-3.712315116897E-006,0.E+000));
#3998 = DIRECTION('',(-3.712315116897E-006,-0.999999999993,0.E+000));
#3999 = DEFINITIONAL_REPRESENTATION('',(#4000),#4004);
#4000 = LINE('',#4001,#4002);
#4001 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4002 = VECTOR('',#4003,1.);
#4003 = DIRECTION('',(0.E+000,-1.));
#4004 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4005 = ORIENTED_EDGE('',*,*,#4006,.F.);
#4006 = EDGE_CURVE('',#3903,#3979,#4007,.T.);
#4007 = SURFACE_CURVE('',#4008,(#4012,#4019),.PCURVE_S1.);
#4008 = LINE('',#4009,#4010);
#4009 = CARTESIAN_POINT('',(57.49994342,7.89997658,0.E+000));
#4010 = VECTOR('',#4011,1.);
#4011 = DIRECTION('',(0.995037190207,9.950371904856E-002,0.E+000));
#4012 = PCURVE('',#3918,#4013);
#4013 = DEFINITIONAL_REPRESENTATION('',(#4014),#4018);
#4014 = LINE('',#4015,#4016);
#4015 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4016 = VECTOR('',#4017,1.);
#4017 = DIRECTION('',(1.,0.E+000));
#4018 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4019 = PCURVE('',#137,#4020);
#4020 = DEFINITIONAL_REPRESENTATION('',(#4021),#4025);
#4021 = LINE('',#4022,#4023);
#4022 = CARTESIAN_POINT('',(57.49994342,19.49997624));
#4023 = VECTOR('',#4024,1.);
#4024 = DIRECTION('',(0.995037190207,9.950371904856E-002));
#4025 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4026 = ADVANCED_FACE('',(#4027),#3994,.F.);
#4027 = FACE_BOUND('',#4028,.F.);
#4028 = EDGE_LOOP('',(#4029,#4030,#4053,#4081));
#4029 = ORIENTED_EDGE('',*,*,#3978,.T.);
#4030 = ORIENTED_EDGE('',*,*,#4031,.T.);
#4031 = EDGE_CURVE('',#3956,#4032,#4034,.T.);
#4032 = VERTEX_POINT('',#4033);
#4033 = CARTESIAN_POINT('',(57.49994596,-5.09999234,1.12192054));
#4034 = SURFACE_CURVE('',#4035,(#4039,#4046),.PCURVE_S1.);
#4035 = LINE('',#4036,#4037);
#4036 = CARTESIAN_POINT('',(57.49999422,7.89998166,1.12192054));
#4037 = VECTOR('',#4038,1.);
#4038 = DIRECTION('',(-3.712315116897E-006,-0.999999999993,0.E+000));
#4039 = PCURVE('',#3994,#4040);
#4040 = DEFINITIONAL_REPRESENTATION('',(#4041),#4045);
#4041 = LINE('',#4042,#4043);
#4042 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4043 = VECTOR('',#4044,1.);
#4044 = DIRECTION('',(1.,0.E+000));
#4045 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4046 = PCURVE('',#83,#4047);
#4047 = DEFINITIONAL_REPRESENTATION('',(#4048),#4052);
#4048 = LINE('',#4049,#4050);
#4049 = CARTESIAN_POINT('',(57.49999422,19.49998132));
#4050 = VECTOR('',#4051,1.);
#4051 = DIRECTION('',(-3.712315116897E-006,-0.999999999993));
#4052 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4053 = ORIENTED_EDGE('',*,*,#4054,.F.);
#4054 = EDGE_CURVE('',#4055,#4032,#4057,.T.);
#4055 = VERTEX_POINT('',#4056);
#4056 = CARTESIAN_POINT('',(57.49994596,-5.09999234,0.E+000));
#4057 = SURFACE_CURVE('',#4058,(#4062,#4069),.PCURVE_S1.);
#4058 = LINE('',#4059,#4060);
#4059 = CARTESIAN_POINT('',(57.49994596,-5.09999234,0.E+000));
#4060 = VECTOR('',#4061,1.);
#4061 = DIRECTION('',(0.E+000,0.E+000,1.));
#4062 = PCURVE('',#3994,#4063);
#4063 = DEFINITIONAL_REPRESENTATION('',(#4064),#4068);
#4064 = LINE('',#4065,#4066);
#4065 = CARTESIAN_POINT('',(12.99997400009,0.E+000));
#4066 = VECTOR('',#4067,1.);
#4067 = DIRECTION('',(0.E+000,-1.));
#4068 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4069 = PCURVE('',#4070,#4075);
#4070 = PLANE('',#4071);
#4071 = AXIS2_PLACEMENT_3D('',#4072,#4073,#4074);
#4072 = CARTESIAN_POINT('',(57.49994596,-5.09999234,0.E+000));
#4073 = DIRECTION('',(0.991443803629,-0.130534226335,0.E+000));
#4074 = DIRECTION('',(-0.130534226335,-0.991443803629,0.E+000));
#4075 = DEFINITIONAL_REPRESENTATION('',(#4076),#4080);
#4076 = LINE('',#4077,#4078);
#4077 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4078 = VECTOR('',#4079,1.);
#4079 = DIRECTION('',(0.E+000,-1.));
#4080 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4081 = ORIENTED_EDGE('',*,*,#4082,.F.);
#4082 = EDGE_CURVE('',#3979,#4055,#4083,.T.);
#4083 = SURFACE_CURVE('',#4084,(#4088,#4095),.PCURVE_S1.);
#4084 = LINE('',#4085,#4086);
#4085 = CARTESIAN_POINT('',(57.49999422,7.89998166,0.E+000));
#4086 = VECTOR('',#4087,1.);
#4087 = DIRECTION('',(-3.712315116897E-006,-0.999999999993,0.E+000));
#4088 = PCURVE('',#3994,#4089);
#4089 = DEFINITIONAL_REPRESENTATION('',(#4090),#4094);
#4090 = LINE('',#4091,#4092);
#4091 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4092 = VECTOR('',#4093,1.);
#4093 = DIRECTION('',(1.,0.E+000));
#4094 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4095 = PCURVE('',#137,#4096);
#4096 = DEFINITIONAL_REPRESENTATION('',(#4097),#4101);
#4097 = LINE('',#4098,#4099);
#4098 = CARTESIAN_POINT('',(57.49999422,19.49998132));
#4099 = VECTOR('',#4100,1.);
#4100 = DIRECTION('',(-3.712315116897E-006,-0.999999999993));
#4101 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4102 = ADVANCED_FACE('',(#4103),#4070,.F.);
#4103 = FACE_BOUND('',#4104,.F.);
#4104 = EDGE_LOOP('',(#4105,#4106,#4129,#4157));
#4105 = ORIENTED_EDGE('',*,*,#4054,.T.);
#4106 = ORIENTED_EDGE('',*,*,#4107,.T.);
#4107 = EDGE_CURVE('',#4032,#4108,#4110,.T.);
#4108 = VERTEX_POINT('',#4109);
#4109 = CARTESIAN_POINT('',(57.48348422,-5.22502384,1.12192054));
#4110 = SURFACE_CURVE('',#4111,(#4115,#4122),.PCURVE_S1.);
#4111 = LINE('',#4112,#4113);
#4112 = CARTESIAN_POINT('',(57.49994596,-5.09999234,1.12192054));
#4113 = VECTOR('',#4114,1.);
#4114 = DIRECTION('',(-0.130534226335,-0.991443803629,0.E+000));
#4115 = PCURVE('',#4070,#4116);
#4116 = DEFINITIONAL_REPRESENTATION('',(#4117),#4121);
#4117 = LINE('',#4118,#4119);
#4118 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4119 = VECTOR('',#4120,1.);
#4120 = DIRECTION('',(1.,0.E+000));
#4121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4122 = PCURVE('',#83,#4123);
#4123 = DEFINITIONAL_REPRESENTATION('',(#4124),#4128);
#4124 = LINE('',#4125,#4126);
#4125 = CARTESIAN_POINT('',(57.49994596,6.50000732));
#4126 = VECTOR('',#4127,1.);
#4127 = DIRECTION('',(-0.130534226335,-0.991443803629));
#4128 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4129 = ORIENTED_EDGE('',*,*,#4130,.F.);
#4130 = EDGE_CURVE('',#4131,#4108,#4133,.T.);
#4131 = VERTEX_POINT('',#4132);
#4132 = CARTESIAN_POINT('',(57.48348422,-5.22502384,0.E+000));
#4133 = SURFACE_CURVE('',#4134,(#4138,#4145),.PCURVE_S1.);
#4134 = LINE('',#4135,#4136);
#4135 = CARTESIAN_POINT('',(57.48348422,-5.22502384,0.E+000));
#4136 = VECTOR('',#4137,1.);
#4137 = DIRECTION('',(0.E+000,0.E+000,1.));
#4138 = PCURVE('',#4070,#4139);
#4139 = DEFINITIONAL_REPRESENTATION('',(#4140),#4144);
#4140 = LINE('',#4141,#4142);
#4141 = CARTESIAN_POINT('',(0.126110526429,0.E+000));
#4142 = VECTOR('',#4143,1.);
#4143 = DIRECTION('',(0.E+000,-1.));
#4144 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4145 = PCURVE('',#4146,#4151);
#4146 = PLANE('',#4147);
#4147 = AXIS2_PLACEMENT_3D('',#4148,#4149,#4150);
#4148 = CARTESIAN_POINT('',(57.48348422,-5.22502384,0.E+000));
#4149 = DIRECTION('',(0.991445303281,0.130522835556,-0.E+000));
#4150 = DIRECTION('',(0.130522835556,-0.991445303281,0.E+000));
#4151 = DEFINITIONAL_REPRESENTATION('',(#4152),#4156);
#4152 = LINE('',#4153,#4154);
#4153 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4154 = VECTOR('',#4155,1.);
#4155 = DIRECTION('',(0.E+000,-1.));
#4156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4157 = ORIENTED_EDGE('',*,*,#4158,.F.);
#4158 = EDGE_CURVE('',#4055,#4131,#4159,.T.);
#4159 = SURFACE_CURVE('',#4160,(#4164,#4171),.PCURVE_S1.);
#4160 = LINE('',#4161,#4162);
#4161 = CARTESIAN_POINT('',(57.49994596,-5.09999234,0.E+000));
#4162 = VECTOR('',#4163,1.);
#4163 = DIRECTION('',(-0.130534226335,-0.991443803629,0.E+000));
#4164 = PCURVE('',#4070,#4165);
#4165 = DEFINITIONAL_REPRESENTATION('',(#4166),#4170);
#4166 = LINE('',#4167,#4168);
#4167 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4168 = VECTOR('',#4169,1.);
#4169 = DIRECTION('',(1.,0.E+000));
#4170 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4171 = PCURVE('',#137,#4172);
#4172 = DEFINITIONAL_REPRESENTATION('',(#4173),#4177);
#4173 = LINE('',#4174,#4175);
#4174 = CARTESIAN_POINT('',(57.49994596,6.50000732));
#4175 = VECTOR('',#4176,1.);
#4176 = DIRECTION('',(-0.130534226335,-0.991443803629));
#4177 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4178 = ADVANCED_FACE('',(#4179),#4146,.F.);
#4179 = FACE_BOUND('',#4180,.F.);
#4180 = EDGE_LOOP('',(#4181,#4182,#4205,#4233));
#4181 = ORIENTED_EDGE('',*,*,#4130,.T.);
#4182 = ORIENTED_EDGE('',*,*,#4183,.T.);
#4183 = EDGE_CURVE('',#4108,#4184,#4186,.T.);
#4184 = VERTEX_POINT('',#4185);
#4185 = CARTESIAN_POINT('',(57.51386008,-5.45575744,1.12192054));
#4186 = SURFACE_CURVE('',#4187,(#4191,#4198),.PCURVE_S1.);
#4187 = LINE('',#4188,#4189);
#4188 = CARTESIAN_POINT('',(57.48348422,-5.22502384,1.12192054));
#4189 = VECTOR('',#4190,1.);
#4190 = DIRECTION('',(0.130522835556,-0.991445303281,0.E+000));
#4191 = PCURVE('',#4146,#4192);
#4192 = DEFINITIONAL_REPRESENTATION('',(#4193),#4197);
#4193 = LINE('',#4194,#4195);
#4194 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4195 = VECTOR('',#4196,1.);
#4196 = DIRECTION('',(1.,0.E+000));
#4197 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4198 = PCURVE('',#83,#4199);
#4199 = DEFINITIONAL_REPRESENTATION('',(#4200),#4204);
#4200 = LINE('',#4201,#4202);
#4201 = CARTESIAN_POINT('',(57.48348422,6.37497582));
#4202 = VECTOR('',#4203,1.);
#4203 = DIRECTION('',(0.130522835556,-0.991445303281));
#4204 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4205 = ORIENTED_EDGE('',*,*,#4206,.F.);
#4206 = EDGE_CURVE('',#4207,#4184,#4209,.T.);
#4207 = VERTEX_POINT('',#4208);
#4208 = CARTESIAN_POINT('',(57.51386008,-5.45575744,0.E+000));
#4209 = SURFACE_CURVE('',#4210,(#4214,#4221),.PCURVE_S1.);
#4210 = LINE('',#4211,#4212);
#4211 = CARTESIAN_POINT('',(57.51386008,-5.45575744,0.E+000));
#4212 = VECTOR('',#4213,1.);
#4213 = DIRECTION('',(0.E+000,0.E+000,1.));
#4214 = PCURVE('',#4146,#4215);
#4215 = DEFINITIONAL_REPRESENTATION('',(#4216),#4220);
#4216 = LINE('',#4217,#4218);
#4217 = CARTESIAN_POINT('',(0.232724487409,0.E+000));
#4218 = VECTOR('',#4219,1.);
#4219 = DIRECTION('',(0.E+000,-1.));
#4220 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4221 = PCURVE('',#4222,#4227);
#4222 = PLANE('',#4223);
#4223 = AXIS2_PLACEMENT_3D('',#4224,#4225,#4226);
#4224 = CARTESIAN_POINT('',(57.51386008,-5.45575744,0.E+000));
#4225 = DIRECTION('',(0.923878621228,0.382685632389,-0.E+000));
#4226 = DIRECTION('',(0.382685632389,-0.923878621228,0.E+000));
#4227 = DEFINITIONAL_REPRESENTATION('',(#4228),#4232);
#4228 = LINE('',#4229,#4230);
#4229 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4230 = VECTOR('',#4231,1.);
#4231 = DIRECTION('',(0.E+000,-1.));
#4232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4233 = ORIENTED_EDGE('',*,*,#4234,.F.);
#4234 = EDGE_CURVE('',#4131,#4207,#4235,.T.);
#4235 = SURFACE_CURVE('',#4236,(#4240,#4247),.PCURVE_S1.);
#4236 = LINE('',#4237,#4238);
#4237 = CARTESIAN_POINT('',(57.48348422,-5.22502384,0.E+000));
#4238 = VECTOR('',#4239,1.);
#4239 = DIRECTION('',(0.130522835556,-0.991445303281,0.E+000));
#4240 = PCURVE('',#4146,#4241);
#4241 = DEFINITIONAL_REPRESENTATION('',(#4242),#4246);
#4242 = LINE('',#4243,#4244);
#4243 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4244 = VECTOR('',#4245,1.);
#4245 = DIRECTION('',(1.,0.E+000));
#4246 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4247 = PCURVE('',#137,#4248);
#4248 = DEFINITIONAL_REPRESENTATION('',(#4249),#4253);
#4249 = LINE('',#4250,#4251);
#4250 = CARTESIAN_POINT('',(57.48348422,6.37497582));
#4251 = VECTOR('',#4252,1.);
#4252 = DIRECTION('',(0.130522835556,-0.991445303281));
#4253 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4254 = ADVANCED_FACE('',(#4255),#4222,.F.);
#4255 = FACE_BOUND('',#4256,.F.);
#4256 = EDGE_LOOP('',(#4257,#4258,#4281,#4309));
#4257 = ORIENTED_EDGE('',*,*,#4206,.T.);
#4258 = ORIENTED_EDGE('',*,*,#4259,.T.);
#4259 = EDGE_CURVE('',#4184,#4260,#4262,.T.);
#4260 = VERTEX_POINT('',#4261);
#4261 = CARTESIAN_POINT('',(57.6029201,-5.6707659,1.12192054));
#4262 = SURFACE_CURVE('',#4263,(#4267,#4274),.PCURVE_S1.);
#4263 = LINE('',#4264,#4265);
#4264 = CARTESIAN_POINT('',(57.51386008,-5.45575744,1.12192054));
#4265 = VECTOR('',#4266,1.);
#4266 = DIRECTION('',(0.382685632389,-0.923878621228,0.E+000));
#4267 = PCURVE('',#4222,#4268);
#4268 = DEFINITIONAL_REPRESENTATION('',(#4269),#4273);
#4269 = LINE('',#4270,#4271);
#4270 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4271 = VECTOR('',#4272,1.);
#4272 = DIRECTION('',(1.,0.E+000));
#4273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4274 = PCURVE('',#83,#4275);
#4275 = DEFINITIONAL_REPRESENTATION('',(#4276),#4280);
#4276 = LINE('',#4277,#4278);
#4277 = CARTESIAN_POINT('',(57.51386008,6.14424222));
#4278 = VECTOR('',#4279,1.);
#4279 = DIRECTION('',(0.382685632389,-0.923878621228));
#4280 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4281 = ORIENTED_EDGE('',*,*,#4282,.F.);
#4282 = EDGE_CURVE('',#4283,#4260,#4285,.T.);
#4283 = VERTEX_POINT('',#4284);
#4284 = CARTESIAN_POINT('',(57.6029201,-5.6707659,0.E+000));
#4285 = SURFACE_CURVE('',#4286,(#4290,#4297),.PCURVE_S1.);
#4286 = LINE('',#4287,#4288);
#4287 = CARTESIAN_POINT('',(57.6029201,-5.6707659,0.E+000));
#4288 = VECTOR('',#4289,1.);
#4289 = DIRECTION('',(0.E+000,0.E+000,1.));
#4290 = PCURVE('',#4222,#4291);
#4291 = DEFINITIONAL_REPRESENTATION('',(#4292),#4296);
#4292 = LINE('',#4293,#4294);
#4293 = CARTESIAN_POINT('',(0.232723709652,0.E+000));
#4294 = VECTOR('',#4295,1.);
#4295 = DIRECTION('',(0.E+000,-1.));
#4296 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4297 = PCURVE('',#4298,#4303);
#4298 = PLANE('',#4299);
#4299 = AXIS2_PLACEMENT_3D('',#4300,#4301,#4302);
#4300 = CARTESIAN_POINT('',(57.6029201,-5.6707659,0.E+000));
#4301 = DIRECTION('',(0.793353333735,0.608761437553,-0.E+000));
#4302 = DIRECTION('',(0.608761437553,-0.793353333735,0.E+000));
#4303 = DEFINITIONAL_REPRESENTATION('',(#4304),#4308);
#4304 = LINE('',#4305,#4306);
#4305 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4306 = VECTOR('',#4307,1.);
#4307 = DIRECTION('',(0.E+000,-1.));
#4308 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4309 = ORIENTED_EDGE('',*,*,#4310,.F.);
#4310 = EDGE_CURVE('',#4207,#4283,#4311,.T.);
#4311 = SURFACE_CURVE('',#4312,(#4316,#4323),.PCURVE_S1.);
#4312 = LINE('',#4313,#4314);
#4313 = CARTESIAN_POINT('',(57.51386008,-5.45575744,0.E+000));
#4314 = VECTOR('',#4315,1.);
#4315 = DIRECTION('',(0.382685632389,-0.923878621228,0.E+000));
#4316 = PCURVE('',#4222,#4317);
#4317 = DEFINITIONAL_REPRESENTATION('',(#4318),#4322);
#4318 = LINE('',#4319,#4320);
#4319 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4320 = VECTOR('',#4321,1.);
#4321 = DIRECTION('',(1.,0.E+000));
#4322 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4323 = PCURVE('',#137,#4324);
#4324 = DEFINITIONAL_REPRESENTATION('',(#4325),#4329);
#4325 = LINE('',#4326,#4327);
#4326 = CARTESIAN_POINT('',(57.51386008,6.14424222));
#4327 = VECTOR('',#4328,1.);
#4328 = DIRECTION('',(0.382685632389,-0.923878621228));
#4329 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4330 = ADVANCED_FACE('',(#4331),#4298,.F.);
#4331 = FACE_BOUND('',#4332,.F.);
#4332 = EDGE_LOOP('',(#4333,#4334,#4357,#4385));
#4333 = ORIENTED_EDGE('',*,*,#4282,.T.);
#4334 = ORIENTED_EDGE('',*,*,#4335,.T.);
#4335 = EDGE_CURVE('',#4260,#4336,#4338,.T.);
#4336 = VERTEX_POINT('',#4337);
#4337 = CARTESIAN_POINT('',(57.74459368,-5.8553985,1.12192054));
#4338 = SURFACE_CURVE('',#4339,(#4343,#4350),.PCURVE_S1.);
#4339 = LINE('',#4340,#4341);
#4340 = CARTESIAN_POINT('',(57.6029201,-5.6707659,1.12192054));
#4341 = VECTOR('',#4342,1.);
#4342 = DIRECTION('',(0.608761437553,-0.793353333735,0.E+000));
#4343 = PCURVE('',#4298,#4344);
#4344 = DEFINITIONAL_REPRESENTATION('',(#4345),#4349);
#4345 = LINE('',#4346,#4347);
#4346 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4347 = VECTOR('',#4348,1.);
#4348 = DIRECTION('',(1.,0.E+000));
#4349 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4350 = PCURVE('',#83,#4351);
#4351 = DEFINITIONAL_REPRESENTATION('',(#4352),#4356);
#4352 = LINE('',#4353,#4354);
#4353 = CARTESIAN_POINT('',(57.6029201,5.92923376));
#4354 = VECTOR('',#4355,1.);
#4355 = DIRECTION('',(0.608761437553,-0.793353333735));
#4356 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4357 = ORIENTED_EDGE('',*,*,#4358,.F.);
#4358 = EDGE_CURVE('',#4359,#4336,#4361,.T.);
#4359 = VERTEX_POINT('',#4360);
#4360 = CARTESIAN_POINT('',(57.74459368,-5.8553985,0.E+000));
#4361 = SURFACE_CURVE('',#4362,(#4366,#4373),.PCURVE_S1.);
#4362 = LINE('',#4363,#4364);
#4363 = CARTESIAN_POINT('',(57.74459368,-5.8553985,0.E+000));
#4364 = VECTOR('',#4365,1.);
#4365 = DIRECTION('',(0.E+000,0.E+000,1.));
#4366 = PCURVE('',#4298,#4367);
#4367 = DEFINITIONAL_REPRESENTATION('',(#4368),#4372);
#4368 = LINE('',#4369,#4370);
#4369 = CARTESIAN_POINT('',(0.23272430095,0.E+000));
#4370 = VECTOR('',#4371,1.);
#4371 = DIRECTION('',(0.E+000,-1.));
#4372 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4373 = PCURVE('',#4374,#4379);
#4374 = PLANE('',#4375);
#4375 = AXIS2_PLACEMENT_3D('',#4376,#4377,#4378);
#4376 = CARTESIAN_POINT('',(57.74459368,-5.8553985,0.E+000));
#4377 = DIRECTION('',(0.608761437553,0.793353333735,-0.E+000));
#4378 = DIRECTION('',(0.793353333735,-0.608761437553,0.E+000));
#4379 = DEFINITIONAL_REPRESENTATION('',(#4380),#4384);
#4380 = LINE('',#4381,#4382);
#4381 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4382 = VECTOR('',#4383,1.);
#4383 = DIRECTION('',(0.E+000,-1.));
#4384 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4385 = ORIENTED_EDGE('',*,*,#4386,.F.);
#4386 = EDGE_CURVE('',#4283,#4359,#4387,.T.);
#4387 = SURFACE_CURVE('',#4388,(#4392,#4399),.PCURVE_S1.);
#4388 = LINE('',#4389,#4390);
#4389 = CARTESIAN_POINT('',(57.6029201,-5.6707659,0.E+000));
#4390 = VECTOR('',#4391,1.);
#4391 = DIRECTION('',(0.608761437553,-0.793353333735,0.E+000));
#4392 = PCURVE('',#4298,#4393);
#4393 = DEFINITIONAL_REPRESENTATION('',(#4394),#4398);
#4394 = LINE('',#4395,#4396);
#4395 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4396 = VECTOR('',#4397,1.);
#4397 = DIRECTION('',(1.,0.E+000));
#4398 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4399 = PCURVE('',#137,#4400);
#4400 = DEFINITIONAL_REPRESENTATION('',(#4401),#4405);
#4401 = LINE('',#4402,#4403);
#4402 = CARTESIAN_POINT('',(57.6029201,5.92923376));
#4403 = VECTOR('',#4404,1.);
#4404 = DIRECTION('',(0.608761437553,-0.793353333735));
#4405 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4406 = ADVANCED_FACE('',(#4407),#4374,.F.);
#4407 = FACE_BOUND('',#4408,.F.);
#4408 = EDGE_LOOP('',(#4409,#4410,#4433,#4461));
#4409 = ORIENTED_EDGE('',*,*,#4358,.T.);
#4410 = ORIENTED_EDGE('',*,*,#4411,.T.);
#4411 = EDGE_CURVE('',#4336,#4412,#4414,.T.);
#4412 = VERTEX_POINT('',#4413);
#4413 = CARTESIAN_POINT('',(57.92922628,-5.99707208,1.12192054));
#4414 = SURFACE_CURVE('',#4415,(#4419,#4426),.PCURVE_S1.);
#4415 = LINE('',#4416,#4417);
#4416 = CARTESIAN_POINT('',(57.74459368,-5.8553985,1.12192054));
#4417 = VECTOR('',#4418,1.);
#4418 = DIRECTION('',(0.793353333735,-0.608761437553,0.E+000));
#4419 = PCURVE('',#4374,#4420);
#4420 = DEFINITIONAL_REPRESENTATION('',(#4421),#4425);
#4421 = LINE('',#4422,#4423);
#4422 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4423 = VECTOR('',#4424,1.);
#4424 = DIRECTION('',(1.,0.E+000));
#4425 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4426 = PCURVE('',#83,#4427);
#4427 = DEFINITIONAL_REPRESENTATION('',(#4428),#4432);
#4428 = LINE('',#4429,#4430);
#4429 = CARTESIAN_POINT('',(57.74459368,5.74460116));
#4430 = VECTOR('',#4431,1.);
#4431 = DIRECTION('',(0.793353333735,-0.608761437553));
#4432 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4433 = ORIENTED_EDGE('',*,*,#4434,.F.);
#4434 = EDGE_CURVE('',#4435,#4412,#4437,.T.);
#4435 = VERTEX_POINT('',#4436);
#4436 = CARTESIAN_POINT('',(57.92922628,-5.99707208,0.E+000));
#4437 = SURFACE_CURVE('',#4438,(#4442,#4449),.PCURVE_S1.);
#4438 = LINE('',#4439,#4440);
#4439 = CARTESIAN_POINT('',(57.92922628,-5.99707208,0.E+000));
#4440 = VECTOR('',#4441,1.);
#4441 = DIRECTION('',(0.E+000,0.E+000,1.));
#4442 = PCURVE('',#4374,#4443);
#4443 = DEFINITIONAL_REPRESENTATION('',(#4444),#4448);
#4444 = LINE('',#4445,#4446);
#4445 = CARTESIAN_POINT('',(0.23272430095,0.E+000));
#4446 = VECTOR('',#4447,1.);
#4447 = DIRECTION('',(0.E+000,-1.));
#4448 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4449 = PCURVE('',#4450,#4455);
#4450 = PLANE('',#4451);
#4451 = AXIS2_PLACEMENT_3D('',#4452,#4453,#4454);
#4452 = CARTESIAN_POINT('',(57.92922628,-5.99707208,0.E+000));
#4453 = DIRECTION('',(0.382685632389,0.923878621228,-0.E+000));
#4454 = DIRECTION('',(0.923878621228,-0.382685632389,0.E+000));
#4455 = DEFINITIONAL_REPRESENTATION('',(#4456),#4460);
#4456 = LINE('',#4457,#4458);
#4457 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4458 = VECTOR('',#4459,1.);
#4459 = DIRECTION('',(0.E+000,-1.));
#4460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4461 = ORIENTED_EDGE('',*,*,#4462,.F.);
#4462 = EDGE_CURVE('',#4359,#4435,#4463,.T.);
#4463 = SURFACE_CURVE('',#4464,(#4468,#4475),.PCURVE_S1.);
#4464 = LINE('',#4465,#4466);
#4465 = CARTESIAN_POINT('',(57.74459368,-5.8553985,0.E+000));
#4466 = VECTOR('',#4467,1.);
#4467 = DIRECTION('',(0.793353333735,-0.608761437553,0.E+000));
#4468 = PCURVE('',#4374,#4469);
#4469 = DEFINITIONAL_REPRESENTATION('',(#4470),#4474);
#4470 = LINE('',#4471,#4472);
#4471 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4472 = VECTOR('',#4473,1.);
#4473 = DIRECTION('',(1.,0.E+000));
#4474 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4475 = PCURVE('',#137,#4476);
#4476 = DEFINITIONAL_REPRESENTATION('',(#4477),#4481);
#4477 = LINE('',#4478,#4479);
#4478 = CARTESIAN_POINT('',(57.74459368,5.74460116));
#4479 = VECTOR('',#4480,1.);
#4480 = DIRECTION('',(0.793353333735,-0.608761437553));
#4481 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4482 = ADVANCED_FACE('',(#4483),#4450,.F.);
#4483 = FACE_BOUND('',#4484,.F.);
#4484 = EDGE_LOOP('',(#4485,#4486,#4509,#4537));
#4485 = ORIENTED_EDGE('',*,*,#4434,.T.);
#4486 = ORIENTED_EDGE('',*,*,#4487,.T.);
#4487 = EDGE_CURVE('',#4412,#4488,#4490,.T.);
#4488 = VERTEX_POINT('',#4489);
#4489 = CARTESIAN_POINT('',(58.14423474,-6.0861321,1.12192054));
#4490 = SURFACE_CURVE('',#4491,(#4495,#4502),.PCURVE_S1.);
#4491 = LINE('',#4492,#4493);
#4492 = CARTESIAN_POINT('',(57.92922628,-5.99707208,1.12192054));
#4493 = VECTOR('',#4494,1.);
#4494 = DIRECTION('',(0.923878621228,-0.382685632389,0.E+000));
#4495 = PCURVE('',#4450,#4496);
#4496 = DEFINITIONAL_REPRESENTATION('',(#4497),#4501);
#4497 = LINE('',#4498,#4499);
#4498 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4499 = VECTOR('',#4500,1.);
#4500 = DIRECTION('',(1.,0.E+000));
#4501 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4502 = PCURVE('',#83,#4503);
#4503 = DEFINITIONAL_REPRESENTATION('',(#4504),#4508);
#4504 = LINE('',#4505,#4506);
#4505 = CARTESIAN_POINT('',(57.92922628,5.60292758));
#4506 = VECTOR('',#4507,1.);
#4507 = DIRECTION('',(0.923878621228,-0.382685632389));
#4508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4509 = ORIENTED_EDGE('',*,*,#4510,.F.);
#4510 = EDGE_CURVE('',#4511,#4488,#4513,.T.);
#4511 = VERTEX_POINT('',#4512);
#4512 = CARTESIAN_POINT('',(58.14423474,-6.0861321,0.E+000));
#4513 = SURFACE_CURVE('',#4514,(#4518,#4525),.PCURVE_S1.);
#4514 = LINE('',#4515,#4516);
#4515 = CARTESIAN_POINT('',(58.14423474,-6.0861321,0.E+000));
#4516 = VECTOR('',#4517,1.);
#4517 = DIRECTION('',(0.E+000,0.E+000,1.));
#4518 = PCURVE('',#4450,#4519);
#4519 = DEFINITIONAL_REPRESENTATION('',(#4520),#4524);
#4520 = LINE('',#4521,#4522);
#4521 = CARTESIAN_POINT('',(0.232723709652,0.E+000));
#4522 = VECTOR('',#4523,1.);
#4523 = DIRECTION('',(0.E+000,-1.));
#4524 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4525 = PCURVE('',#4526,#4531);
#4526 = PLANE('',#4527);
#4527 = AXIS2_PLACEMENT_3D('',#4528,#4529,#4530);
#4528 = CARTESIAN_POINT('',(58.14423474,-6.0861321,0.E+000));
#4529 = DIRECTION('',(0.130517959071,0.991445945254,-0.E+000));
#4530 = DIRECTION('',(0.991445945254,-0.130517959071,0.E+000));
#4531 = DEFINITIONAL_REPRESENTATION('',(#4532),#4536);
#4532 = LINE('',#4533,#4534);
#4533 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4534 = VECTOR('',#4535,1.);
#4535 = DIRECTION('',(0.E+000,-1.));
#4536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4537 = ORIENTED_EDGE('',*,*,#4538,.F.);
#4538 = EDGE_CURVE('',#4435,#4511,#4539,.T.);
#4539 = SURFACE_CURVE('',#4540,(#4544,#4551),.PCURVE_S1.);
#4540 = LINE('',#4541,#4542);
#4541 = CARTESIAN_POINT('',(57.92922628,-5.99707208,0.E+000));
#4542 = VECTOR('',#4543,1.);
#4543 = DIRECTION('',(0.923878621228,-0.382685632389,0.E+000));
#4544 = PCURVE('',#4450,#4545);
#4545 = DEFINITIONAL_REPRESENTATION('',(#4546),#4550);
#4546 = LINE('',#4547,#4548);
#4547 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4548 = VECTOR('',#4549,1.);
#4549 = DIRECTION('',(1.,0.E+000));
#4550 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4551 = PCURVE('',#137,#4552);
#4552 = DEFINITIONAL_REPRESENTATION('',(#4553),#4557);
#4553 = LINE('',#4554,#4555);
#4554 = CARTESIAN_POINT('',(57.92922628,5.60292758));
#4555 = VECTOR('',#4556,1.);
#4556 = DIRECTION('',(0.923878621228,-0.382685632389));
#4557 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4558 = ADVANCED_FACE('',(#4559),#4526,.F.);
#4559 = FACE_BOUND('',#4560,.F.);
#4560 = EDGE_LOOP('',(#4561,#4562,#4585,#4613));
#4561 = ORIENTED_EDGE('',*,*,#4510,.T.);
#4562 = ORIENTED_EDGE('',*,*,#4563,.T.);
#4563 = EDGE_CURVE('',#4488,#4564,#4566,.T.);
#4564 = VERTEX_POINT('',#4565);
#4565 = CARTESIAN_POINT('',(58.24998764,-6.10005384,1.12192054));
#4566 = SURFACE_CURVE('',#4567,(#4571,#4578),.PCURVE_S1.);
#4567 = LINE('',#4568,#4569);
#4568 = CARTESIAN_POINT('',(58.14423474,-6.0861321,1.12192054));
#4569 = VECTOR('',#4570,1.);
#4570 = DIRECTION('',(0.991445945254,-0.130517959071,0.E+000));
#4571 = PCURVE('',#4526,#4572);
#4572 = DEFINITIONAL_REPRESENTATION('',(#4573),#4577);
#4573 = LINE('',#4574,#4575);
#4574 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4575 = VECTOR('',#4576,1.);
#4576 = DIRECTION('',(1.,0.E+000));
#4577 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4578 = PCURVE('',#83,#4579);
#4579 = DEFINITIONAL_REPRESENTATION('',(#4580),#4584);
#4580 = LINE('',#4581,#4582);
#4581 = CARTESIAN_POINT('',(58.14423474,5.51386756));
#4582 = VECTOR('',#4583,1.);
#4583 = DIRECTION('',(0.991445945254,-0.130517959071));
#4584 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4585 = ORIENTED_EDGE('',*,*,#4586,.F.);
#4586 = EDGE_CURVE('',#4587,#4564,#4589,.T.);
#4587 = VERTEX_POINT('',#4588);
#4588 = CARTESIAN_POINT('',(58.24998764,-6.10005384,0.E+000));
#4589 = SURFACE_CURVE('',#4590,(#4594,#4601),.PCURVE_S1.);
#4590 = LINE('',#4591,#4592);
#4591 = CARTESIAN_POINT('',(58.24998764,-6.10005384,0.E+000));
#4592 = VECTOR('',#4593,1.);
#4593 = DIRECTION('',(0.E+000,0.E+000,1.));
#4594 = PCURVE('',#4526,#4595);
#4595 = DEFINITIONAL_REPRESENTATION('',(#4596),#4600);
#4596 = LINE('',#4597,#4598);
#4597 = CARTESIAN_POINT('',(0.106665320995,0.E+000));
#4598 = VECTOR('',#4599,1.);
#4599 = DIRECTION('',(0.E+000,-1.));
#4600 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4601 = PCURVE('',#4602,#4607);
#4602 = PLANE('',#4603);
#4603 = AXIS2_PLACEMENT_3D('',#4604,#4605,#4606);
#4604 = CARTESIAN_POINT('',(58.24998764,-6.10005384,0.E+000));
#4605 = DIRECTION('',(-0.97280621469,0.231620527289,0.E+000));
#4606 = DIRECTION('',(0.231620527289,0.97280621469,0.E+000));
#4607 = DEFINITIONAL_REPRESENTATION('',(#4608),#4612);
#4608 = LINE('',#4609,#4610);
#4609 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4610 = VECTOR('',#4611,1.);
#4611 = DIRECTION('',(0.E+000,-1.));
#4612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4613 = ORIENTED_EDGE('',*,*,#4614,.F.);
#4614 = EDGE_CURVE('',#4511,#4587,#4615,.T.);
#4615 = SURFACE_CURVE('',#4616,(#4620,#4627),.PCURVE_S1.);
#4616 = LINE('',#4617,#4618);
#4617 = CARTESIAN_POINT('',(58.14423474,-6.0861321,0.E+000));
#4618 = VECTOR('',#4619,1.);
#4619 = DIRECTION('',(0.991445945254,-0.130517959071,0.E+000));
#4620 = PCURVE('',#4526,#4621);
#4621 = DEFINITIONAL_REPRESENTATION('',(#4622),#4626);
#4622 = LINE('',#4623,#4624);
#4623 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4624 = VECTOR('',#4625,1.);
#4625 = DIRECTION('',(1.,0.E+000));
#4626 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4627 = PCURVE('',#137,#4628);
#4628 = DEFINITIONAL_REPRESENTATION('',(#4629),#4633);
#4629 = LINE('',#4630,#4631);
#4630 = CARTESIAN_POINT('',(58.14423474,5.51386756));
#4631 = VECTOR('',#4632,1.);
#4632 = DIRECTION('',(0.991445945254,-0.130517959071));
#4633 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4634 = ADVANCED_FACE('',(#4635),#4602,.F.);
#4635 = FACE_BOUND('',#4636,.F.);
#4636 = EDGE_LOOP('',(#4637,#4638,#4661,#4689));
#4637 = ORIENTED_EDGE('',*,*,#4586,.T.);
#4638 = ORIENTED_EDGE('',*,*,#4639,.T.);
#4639 = EDGE_CURVE('',#4564,#4640,#4642,.T.);
#4640 = VERTEX_POINT('',#4641);
#4641 = CARTESIAN_POINT('',(58.25000034,-6.1000005,1.12192054));
#4642 = SURFACE_CURVE('',#4643,(#4647,#4654),.PCURVE_S1.);
#4643 = LINE('',#4644,#4645);
#4644 = CARTESIAN_POINT('',(58.24998764,-6.10005384,1.12192054));
#4645 = VECTOR('',#4646,1.);
#4646 = DIRECTION('',(0.231620527289,0.97280621469,0.E+000));
#4647 = PCURVE('',#4602,#4648);
#4648 = DEFINITIONAL_REPRESENTATION('',(#4649),#4653);
#4649 = LINE('',#4650,#4651);
#4650 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4651 = VECTOR('',#4652,1.);
#4652 = DIRECTION('',(1.,0.E+000));
#4653 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4654 = PCURVE('',#83,#4655);
#4655 = DEFINITIONAL_REPRESENTATION('',(#4656),#4660);
#4656 = LINE('',#4657,#4658);
#4657 = CARTESIAN_POINT('',(58.24998764,5.49994582));
#4658 = VECTOR('',#4659,1.);
#4659 = DIRECTION('',(0.231620527289,0.97280621469));
#4660 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4661 = ORIENTED_EDGE('',*,*,#4662,.F.);
#4662 = EDGE_CURVE('',#4663,#4640,#4665,.T.);
#4663 = VERTEX_POINT('',#4664);
#4664 = CARTESIAN_POINT('',(58.25000034,-6.1000005,0.E+000));
#4665 = SURFACE_CURVE('',#4666,(#4670,#4677),.PCURVE_S1.);
#4666 = LINE('',#4667,#4668);
#4667 = CARTESIAN_POINT('',(58.25000034,-6.1000005,0.E+000));
#4668 = VECTOR('',#4669,1.);
#4669 = DIRECTION('',(0.E+000,0.E+000,1.));
#4670 = PCURVE('',#4602,#4671);
#4671 = DEFINITIONAL_REPRESENTATION('',(#4672),#4676);
#4672 = LINE('',#4673,#4674);
#4673 = CARTESIAN_POINT('',(5.48310641888E-005,0.E+000));
#4674 = VECTOR('',#4675,1.);
#4675 = DIRECTION('',(0.E+000,-1.));
#4676 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4677 = PCURVE('',#4678,#4683);
#4678 = PLANE('',#4679);
#4679 = AXIS2_PLACEMENT_3D('',#4680,#4681,#4682);
#4680 = CARTESIAN_POINT('',(58.25000034,-6.1000005,0.E+000));
#4681 = DIRECTION('',(1.380133041418E-003,0.999999047616,-0.E+000));
#4682 = DIRECTION('',(0.999999047616,-1.380133041418E-003,0.E+000));
#4683 = DEFINITIONAL_REPRESENTATION('',(#4684),#4688);
#4684 = LINE('',#4685,#4686);
#4685 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4686 = VECTOR('',#4687,1.);
#4687 = DIRECTION('',(0.E+000,-1.));
#4688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4689 = ORIENTED_EDGE('',*,*,#4690,.F.);
#4690 = EDGE_CURVE('',#4587,#4663,#4691,.T.);
#4691 = SURFACE_CURVE('',#4692,(#4696,#4703),.PCURVE_S1.);
#4692 = LINE('',#4693,#4694);
#4693 = CARTESIAN_POINT('',(58.24998764,-6.10005384,0.E+000));
#4694 = VECTOR('',#4695,1.);
#4695 = DIRECTION('',(0.231620527289,0.97280621469,0.E+000));
#4696 = PCURVE('',#4602,#4697);
#4697 = DEFINITIONAL_REPRESENTATION('',(#4698),#4702);
#4698 = LINE('',#4699,#4700);
#4699 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4700 = VECTOR('',#4701,1.);
#4701 = DIRECTION('',(1.,0.E+000));
#4702 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4703 = PCURVE('',#137,#4704);
#4704 = DEFINITIONAL_REPRESENTATION('',(#4705),#4709);
#4705 = LINE('',#4706,#4707);
#4706 = CARTESIAN_POINT('',(58.24998764,5.49994582));
#4707 = VECTOR('',#4708,1.);
#4708 = DIRECTION('',(0.231620527289,0.97280621469));
#4709 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4710 = ADVANCED_FACE('',(#4711),#4678,.F.);
#4711 = FACE_BOUND('',#4712,.F.);
#4712 = EDGE_LOOP('',(#4713,#4714,#4737,#4765));
#4713 = ORIENTED_EDGE('',*,*,#4662,.T.);
#4714 = ORIENTED_EDGE('',*,*,#4715,.T.);
#4715 = EDGE_CURVE('',#4640,#4716,#4718,.T.);
#4716 = VERTEX_POINT('',#4717);
#4717 = CARTESIAN_POINT('',(64.50000054,-6.10862634,1.12192054));
#4718 = SURFACE_CURVE('',#4719,(#4723,#4730),.PCURVE_S1.);
#4719 = LINE('',#4720,#4721);
#4720 = CARTESIAN_POINT('',(58.25000034,-6.1000005,1.12192054));
#4721 = VECTOR('',#4722,1.);
#4722 = DIRECTION('',(0.999999047616,-1.380133041418E-003,0.E+000));
#4723 = PCURVE('',#4678,#4724);
#4724 = DEFINITIONAL_REPRESENTATION('',(#4725),#4729);
#4725 = LINE('',#4726,#4727);
#4726 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4727 = VECTOR('',#4728,1.);
#4728 = DIRECTION('',(1.,0.E+000));
#4729 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4730 = PCURVE('',#83,#4731);
#4731 = DEFINITIONAL_REPRESENTATION('',(#4732),#4736);
#4732 = LINE('',#4733,#4734);
#4733 = CARTESIAN_POINT('',(58.25000034,5.49999916));
#4734 = VECTOR('',#4735,1.);
#4735 = DIRECTION('',(0.999999047616,-1.380133041418E-003));
#4736 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4737 = ORIENTED_EDGE('',*,*,#4738,.F.);
#4738 = EDGE_CURVE('',#4739,#4716,#4741,.T.);
#4739 = VERTEX_POINT('',#4740);
#4740 = CARTESIAN_POINT('',(64.50000054,-6.10862634,0.E+000));
#4741 = SURFACE_CURVE('',#4742,(#4746,#4753),.PCURVE_S1.);
#4742 = LINE('',#4743,#4744);
#4743 = CARTESIAN_POINT('',(64.50000054,-6.10862634,0.E+000));
#4744 = VECTOR('',#4745,1.);
#4745 = DIRECTION('',(0.E+000,0.E+000,1.));
#4746 = PCURVE('',#4678,#4747);
#4747 = DEFINITIONAL_REPRESENTATION('',(#4748),#4752);
#4748 = LINE('',#4749,#4750);
#4749 = CARTESIAN_POINT('',(6.250006152406,0.E+000));
#4750 = VECTOR('',#4751,1.);
#4751 = DIRECTION('',(0.E+000,-1.));
#4752 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4753 = PCURVE('',#4754,#4759);
#4754 = PLANE('',#4755);
#4755 = AXIS2_PLACEMENT_3D('',#4756,#4757,#4758);
#4756 = CARTESIAN_POINT('',(64.50000054,-6.10862634,0.E+000));
#4757 = DIRECTION('',(-0.130525634353,0.991444934818,0.E+000));
#4758 = DIRECTION('',(0.991444934818,0.130525634353,0.E+000));
#4759 = DEFINITIONAL_REPRESENTATION('',(#4760),#4764);
#4760 = LINE('',#4761,#4762);
#4761 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4762 = VECTOR('',#4763,1.);
#4763 = DIRECTION('',(0.E+000,-1.));
#4764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4765 = ORIENTED_EDGE('',*,*,#4766,.F.);
#4766 = EDGE_CURVE('',#4663,#4739,#4767,.T.);
#4767 = SURFACE_CURVE('',#4768,(#4772,#4779),.PCURVE_S1.);
#4768 = LINE('',#4769,#4770);
#4769 = CARTESIAN_POINT('',(58.25000034,-6.1000005,0.E+000));
#4770 = VECTOR('',#4771,1.);
#4771 = DIRECTION('',(0.999999047616,-1.380133041418E-003,0.E+000));
#4772 = PCURVE('',#4678,#4773);
#4773 = DEFINITIONAL_REPRESENTATION('',(#4774),#4778);
#4774 = LINE('',#4775,#4776);
#4775 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4776 = VECTOR('',#4777,1.);
#4777 = DIRECTION('',(1.,0.E+000));
#4778 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4779 = PCURVE('',#137,#4780);
#4780 = DEFINITIONAL_REPRESENTATION('',(#4781),#4785);
#4781 = LINE('',#4782,#4783);
#4782 = CARTESIAN_POINT('',(58.25000034,5.49999916));
#4783 = VECTOR('',#4784,1.);
#4784 = DIRECTION('',(0.999999047616,-1.380133041418E-003));
#4785 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4786 = ADVANCED_FACE('',(#4787),#4754,.F.);
#4787 = FACE_BOUND('',#4788,.F.);
#4788 = EDGE_LOOP('',(#4789,#4790,#4813,#4841));
#4789 = ORIENTED_EDGE('',*,*,#4738,.T.);
#4790 = ORIENTED_EDGE('',*,*,#4791,.T.);
#4791 = EDGE_CURVE('',#4716,#4792,#4794,.T.);
#4792 = VERTEX_POINT('',#4793);
#4793 = CARTESIAN_POINT('',(64.76103888,-6.07426014,1.12192054));
#4794 = SURFACE_CURVE('',#4795,(#4799,#4806),.PCURVE_S1.);
#4795 = LINE('',#4796,#4797);
#4796 = CARTESIAN_POINT('',(64.50000054,-6.10862634,1.12192054));
#4797 = VECTOR('',#4798,1.);
#4798 = DIRECTION('',(0.991444934818,0.130525634353,0.E+000));
#4799 = PCURVE('',#4754,#4800);
#4800 = DEFINITIONAL_REPRESENTATION('',(#4801),#4805);
#4801 = LINE('',#4802,#4803);
#4802 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4803 = VECTOR('',#4804,1.);
#4804 = DIRECTION('',(1.,0.E+000));
#4805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4806 = PCURVE('',#83,#4807);
#4807 = DEFINITIONAL_REPRESENTATION('',(#4808),#4812);
#4808 = LINE('',#4809,#4810);
#4809 = CARTESIAN_POINT('',(64.50000054,5.49137332));
#4810 = VECTOR('',#4811,1.);
#4811 = DIRECTION('',(0.991444934818,0.130525634353));
#4812 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4813 = ORIENTED_EDGE('',*,*,#4814,.F.);
#4814 = EDGE_CURVE('',#4815,#4792,#4817,.T.);
#4815 = VERTEX_POINT('',#4816);
#4816 = CARTESIAN_POINT('',(64.76103888,-6.07426014,0.E+000));
#4817 = SURFACE_CURVE('',#4818,(#4822,#4829),.PCURVE_S1.);
#4818 = LINE('',#4819,#4820);
#4819 = CARTESIAN_POINT('',(64.76103888,-6.07426014,0.E+000));
#4820 = VECTOR('',#4821,1.);
#4821 = DIRECTION('',(0.E+000,0.E+000,1.));
#4822 = PCURVE('',#4754,#4823);
#4823 = DEFINITIONAL_REPRESENTATION('',(#4824),#4828);
#4824 = LINE('',#4825,#4826);
#4825 = CARTESIAN_POINT('',(0.263290810042,0.E+000));
#4826 = VECTOR('',#4827,1.);
#4827 = DIRECTION('',(0.E+000,-1.));
#4828 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4829 = PCURVE('',#4830,#4835);
#4830 = PLANE('',#4831);
#4831 = AXIS2_PLACEMENT_3D('',#4832,#4833,#4834);
#4832 = CARTESIAN_POINT('',(64.76103888,-6.07426014,0.E+000));
#4833 = DIRECTION('',(-0.382682927661,0.923879741566,0.E+000));
#4834 = DIRECTION('',(0.923879741566,0.382682927661,0.E+000));
#4835 = DEFINITIONAL_REPRESENTATION('',(#4836),#4840);
#4836 = LINE('',#4837,#4838);
#4837 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4838 = VECTOR('',#4839,1.);
#4839 = DIRECTION('',(0.E+000,-1.));
#4840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4841 = ORIENTED_EDGE('',*,*,#4842,.F.);
#4842 = EDGE_CURVE('',#4739,#4815,#4843,.T.);
#4843 = SURFACE_CURVE('',#4844,(#4848,#4855),.PCURVE_S1.);
#4844 = LINE('',#4845,#4846);
#4845 = CARTESIAN_POINT('',(64.50000054,-6.10862634,0.E+000));
#4846 = VECTOR('',#4847,1.);
#4847 = DIRECTION('',(0.991444934818,0.130525634353,0.E+000));
#4848 = PCURVE('',#4754,#4849);
#4849 = DEFINITIONAL_REPRESENTATION('',(#4850),#4854);
#4850 = LINE('',#4851,#4852);
#4851 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4852 = VECTOR('',#4853,1.);
#4853 = DIRECTION('',(1.,0.E+000));
#4854 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4855 = PCURVE('',#137,#4856);
#4856 = DEFINITIONAL_REPRESENTATION('',(#4857),#4861);
#4857 = LINE('',#4858,#4859);
#4858 = CARTESIAN_POINT('',(64.50000054,5.49137332));
#4859 = VECTOR('',#4860,1.);
#4860 = DIRECTION('',(0.991444934818,0.130525634353));
#4861 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4862 = ADVANCED_FACE('',(#4863),#4830,.F.);
#4863 = FACE_BOUND('',#4864,.F.);
#4864 = EDGE_LOOP('',(#4865,#4866,#4889,#4917));
#4865 = ORIENTED_EDGE('',*,*,#4814,.T.);
#4866 = ORIENTED_EDGE('',*,*,#4867,.T.);
#4867 = EDGE_CURVE('',#4792,#4868,#4870,.T.);
#4868 = VERTEX_POINT('',#4869);
#4869 = CARTESIAN_POINT('',(65.00429976,-5.97349834,1.12192054));
#4870 = SURFACE_CURVE('',#4871,(#4875,#4882),.PCURVE_S1.);
#4871 = LINE('',#4872,#4873);
#4872 = CARTESIAN_POINT('',(64.76103888,-6.07426014,1.12192054));
#4873 = VECTOR('',#4874,1.);
#4874 = DIRECTION('',(0.923879741566,0.382682927661,0.E+000));
#4875 = PCURVE('',#4830,#4876);
#4876 = DEFINITIONAL_REPRESENTATION('',(#4877),#4881);
#4877 = LINE('',#4878,#4879);
#4878 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4879 = VECTOR('',#4880,1.);
#4880 = DIRECTION('',(1.,0.E+000));
#4881 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4882 = PCURVE('',#83,#4883);
#4883 = DEFINITIONAL_REPRESENTATION('',(#4884),#4888);
#4884 = LINE('',#4885,#4886);
#4885 = CARTESIAN_POINT('',(64.76103888,5.52573952));
#4886 = VECTOR('',#4887,1.);
#4887 = DIRECTION('',(0.923879741566,0.382682927661));
#4888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4889 = ORIENTED_EDGE('',*,*,#4890,.F.);
#4890 = EDGE_CURVE('',#4891,#4868,#4893,.T.);
#4891 = VERTEX_POINT('',#4892);
#4892 = CARTESIAN_POINT('',(65.00429976,-5.97349834,0.E+000));
#4893 = SURFACE_CURVE('',#4894,(#4898,#4905),.PCURVE_S1.);
#4894 = LINE('',#4895,#4896);
#4895 = CARTESIAN_POINT('',(65.00429976,-5.97349834,0.E+000));
#4896 = VECTOR('',#4897,1.);
#4897 = DIRECTION('',(0.E+000,0.E+000,1.));
#4898 = PCURVE('',#4830,#4899);
#4899 = DEFINITIONAL_REPRESENTATION('',(#4900),#4904);
#4900 = LINE('',#4901,#4902);
#4901 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#4902 = VECTOR('',#4903,1.);
#4903 = DIRECTION('',(0.E+000,-1.));
#4904 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4905 = PCURVE('',#4906,#4911);
#4906 = PLANE('',#4907);
#4907 = AXIS2_PLACEMENT_3D('',#4908,#4909,#4910);
#4908 = CARTESIAN_POINT('',(65.00429976,-5.97349834,0.E+000));
#4909 = DIRECTION('',(-0.608764427505,0.793351039455,0.E+000));
#4910 = DIRECTION('',(0.793351039455,0.608764427505,0.E+000));
#4911 = DEFINITIONAL_REPRESENTATION('',(#4912),#4916);
#4912 = LINE('',#4913,#4914);
#4913 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4914 = VECTOR('',#4915,1.);
#4915 = DIRECTION('',(0.E+000,-1.));
#4916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4917 = ORIENTED_EDGE('',*,*,#4918,.F.);
#4918 = EDGE_CURVE('',#4815,#4891,#4919,.T.);
#4919 = SURFACE_CURVE('',#4920,(#4924,#4931),.PCURVE_S1.);
#4920 = LINE('',#4921,#4922);
#4921 = CARTESIAN_POINT('',(64.76103888,-6.07426014,0.E+000));
#4922 = VECTOR('',#4923,1.);
#4923 = DIRECTION('',(0.923879741566,0.382682927661,0.E+000));
#4924 = PCURVE('',#4830,#4925);
#4925 = DEFINITIONAL_REPRESENTATION('',(#4926),#4930);
#4926 = LINE('',#4927,#4928);
#4927 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4928 = VECTOR('',#4929,1.);
#4929 = DIRECTION('',(1.,0.E+000));
#4930 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4931 = PCURVE('',#137,#4932);
#4932 = DEFINITIONAL_REPRESENTATION('',(#4933),#4937);
#4933 = LINE('',#4934,#4935);
#4934 = CARTESIAN_POINT('',(64.76103888,5.52573952));
#4935 = VECTOR('',#4936,1.);
#4936 = DIRECTION('',(0.923879741566,0.382682927661));
#4937 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4938 = ADVANCED_FACE('',(#4939),#4906,.F.);
#4939 = FACE_BOUND('',#4940,.F.);
#4940 = EDGE_LOOP('',(#4941,#4942,#4965,#4993));
#4941 = ORIENTED_EDGE('',*,*,#4890,.T.);
#4942 = ORIENTED_EDGE('',*,*,#4943,.T.);
#4943 = EDGE_CURVE('',#4868,#4944,#4946,.T.);
#4944 = VERTEX_POINT('',#4945);
#4945 = CARTESIAN_POINT('',(65.21319444,-5.81320656,1.12192054));
#4946 = SURFACE_CURVE('',#4947,(#4951,#4958),.PCURVE_S1.);
#4947 = LINE('',#4948,#4949);
#4948 = CARTESIAN_POINT('',(65.00429976,-5.97349834,1.12192054));
#4949 = VECTOR('',#4950,1.);
#4950 = DIRECTION('',(0.793351039455,0.608764427505,0.E+000));
#4951 = PCURVE('',#4906,#4952);
#4952 = DEFINITIONAL_REPRESENTATION('',(#4953),#4957);
#4953 = LINE('',#4954,#4955);
#4954 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#4955 = VECTOR('',#4956,1.);
#4956 = DIRECTION('',(1.,0.E+000));
#4957 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4958 = PCURVE('',#83,#4959);
#4959 = DEFINITIONAL_REPRESENTATION('',(#4960),#4964);
#4960 = LINE('',#4961,#4962);
#4961 = CARTESIAN_POINT('',(65.00429976,5.62650132));
#4962 = VECTOR('',#4963,1.);
#4963 = DIRECTION('',(0.793351039455,0.608764427505));
#4964 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4965 = ORIENTED_EDGE('',*,*,#4966,.F.);
#4966 = EDGE_CURVE('',#4967,#4944,#4969,.T.);
#4967 = VERTEX_POINT('',#4968);
#4968 = CARTESIAN_POINT('',(65.21319444,-5.81320656,0.E+000));
#4969 = SURFACE_CURVE('',#4970,(#4974,#4981),.PCURVE_S1.);
#4970 = LINE('',#4971,#4972);
#4971 = CARTESIAN_POINT('',(65.21319444,-5.81320656,0.E+000));
#4972 = VECTOR('',#4973,1.);
#4973 = DIRECTION('',(0.E+000,0.E+000,1.));
#4974 = PCURVE('',#4906,#4975);
#4975 = DEFINITIONAL_REPRESENTATION('',(#4976),#4980);
#4976 = LINE('',#4977,#4978);
#4977 = CARTESIAN_POINT('',(0.2633067452,0.E+000));
#4978 = VECTOR('',#4979,1.);
#4979 = DIRECTION('',(0.E+000,-1.));
#4980 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4981 = PCURVE('',#4982,#4987);
#4982 = PLANE('',#4983);
#4983 = AXIS2_PLACEMENT_3D('',#4984,#4985,#4986);
#4984 = CARTESIAN_POINT('',(65.21319444,-5.81320656,0.E+000));
#4985 = DIRECTION('',(-0.793347464462,0.608769086463,0.E+000));
#4986 = DIRECTION('',(0.608769086463,0.793347464462,0.E+000));
#4987 = DEFINITIONAL_REPRESENTATION('',(#4988),#4992);
#4988 = LINE('',#4989,#4990);
#4989 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4990 = VECTOR('',#4991,1.);
#4991 = DIRECTION('',(0.E+000,-1.));
#4992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4993 = ORIENTED_EDGE('',*,*,#4994,.F.);
#4994 = EDGE_CURVE('',#4891,#4967,#4995,.T.);
#4995 = SURFACE_CURVE('',#4996,(#5000,#5007),.PCURVE_S1.);
#4996 = LINE('',#4997,#4998);
#4997 = CARTESIAN_POINT('',(65.00429976,-5.97349834,0.E+000));
#4998 = VECTOR('',#4999,1.);
#4999 = DIRECTION('',(0.793351039455,0.608764427505,0.E+000));
#5000 = PCURVE('',#4906,#5001);
#5001 = DEFINITIONAL_REPRESENTATION('',(#5002),#5006);
#5002 = LINE('',#5003,#5004);
#5003 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5004 = VECTOR('',#5005,1.);
#5005 = DIRECTION('',(1.,0.E+000));
#5006 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5007 = PCURVE('',#137,#5008);
#5008 = DEFINITIONAL_REPRESENTATION('',(#5009),#5013);
#5009 = LINE('',#5010,#5011);
#5010 = CARTESIAN_POINT('',(65.00429976,5.62650132));
#5011 = VECTOR('',#5012,1.);
#5012 = DIRECTION('',(0.793351039455,0.608764427505));
#5013 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5014 = ADVANCED_FACE('',(#5015),#4982,.F.);
#5015 = FACE_BOUND('',#5016,.F.);
#5016 = EDGE_LOOP('',(#5017,#5018,#5041,#5069));
#5017 = ORIENTED_EDGE('',*,*,#4966,.T.);
#5018 = ORIENTED_EDGE('',*,*,#5019,.T.);
#5019 = EDGE_CURVE('',#4944,#5020,#5022,.T.);
#5020 = VERTEX_POINT('',#5021);
#5021 = CARTESIAN_POINT('',(65.37348622,-5.60431442,1.12192054));
#5022 = SURFACE_CURVE('',#5023,(#5027,#5034),.PCURVE_S1.);
#5023 = LINE('',#5024,#5025);
#5024 = CARTESIAN_POINT('',(65.21319444,-5.81320656,1.12192054));
#5025 = VECTOR('',#5026,1.);
#5026 = DIRECTION('',(0.608769086463,0.793347464462,0.E+000));
#5027 = PCURVE('',#4982,#5028);
#5028 = DEFINITIONAL_REPRESENTATION('',(#5029),#5033);
#5029 = LINE('',#5030,#5031);
#5030 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5031 = VECTOR('',#5032,1.);
#5032 = DIRECTION('',(1.,0.E+000));
#5033 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5034 = PCURVE('',#83,#5035);
#5035 = DEFINITIONAL_REPRESENTATION('',(#5036),#5040);
#5036 = LINE('',#5037,#5038);
#5037 = CARTESIAN_POINT('',(65.21319444,5.7867931));
#5038 = VECTOR('',#5039,1.);
#5039 = DIRECTION('',(0.608769086463,0.793347464462));
#5040 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5041 = ORIENTED_EDGE('',*,*,#5042,.F.);
#5042 = EDGE_CURVE('',#5043,#5020,#5045,.T.);
#5043 = VERTEX_POINT('',#5044);
#5044 = CARTESIAN_POINT('',(65.37348622,-5.60431442,0.E+000));
#5045 = SURFACE_CURVE('',#5046,(#5050,#5057),.PCURVE_S1.);
#5046 = LINE('',#5047,#5048);
#5047 = CARTESIAN_POINT('',(65.37348622,-5.60431442,0.E+000));
#5048 = VECTOR('',#5049,1.);
#5049 = DIRECTION('',(0.E+000,0.E+000,1.));
#5050 = PCURVE('',#4982,#5051);
#5051 = DEFINITIONAL_REPRESENTATION('',(#5052),#5056);
#5052 = LINE('',#5053,#5054);
#5053 = CARTESIAN_POINT('',(0.263304730093,0.E+000));
#5054 = VECTOR('',#5055,1.);
#5055 = DIRECTION('',(0.E+000,-1.));
#5056 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5057 = PCURVE('',#5058,#5063);
#5058 = PLANE('',#5059);
#5059 = AXIS2_PLACEMENT_3D('',#5060,#5061,#5062);
#5060 = CARTESIAN_POINT('',(65.37348622,-5.60431442,0.E+000));
#5061 = DIRECTION('',(-0.923881154264,0.382679517084,0.E+000));
#5062 = DIRECTION('',(0.382679517084,0.923881154264,0.E+000));
#5063 = DEFINITIONAL_REPRESENTATION('',(#5064),#5068);
#5064 = LINE('',#5065,#5066);
#5065 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5066 = VECTOR('',#5067,1.);
#5067 = DIRECTION('',(0.E+000,-1.));
#5068 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5069 = ORIENTED_EDGE('',*,*,#5070,.F.);
#5070 = EDGE_CURVE('',#4967,#5043,#5071,.T.);
#5071 = SURFACE_CURVE('',#5072,(#5076,#5083),.PCURVE_S1.);
#5072 = LINE('',#5073,#5074);
#5073 = CARTESIAN_POINT('',(65.21319444,-5.81320656,0.E+000));
#5074 = VECTOR('',#5075,1.);
#5075 = DIRECTION('',(0.608769086463,0.793347464462,0.E+000));
#5076 = PCURVE('',#4982,#5077);
#5077 = DEFINITIONAL_REPRESENTATION('',(#5078),#5082);
#5078 = LINE('',#5079,#5080);
#5079 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5080 = VECTOR('',#5081,1.);
#5081 = DIRECTION('',(1.,0.E+000));
#5082 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5083 = PCURVE('',#137,#5084);
#5084 = DEFINITIONAL_REPRESENTATION('',(#5085),#5089);
#5085 = LINE('',#5086,#5087);
#5086 = CARTESIAN_POINT('',(65.21319444,5.7867931));
#5087 = VECTOR('',#5088,1.);
#5088 = DIRECTION('',(0.608769086463,0.793347464462));
#5089 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5090 = ADVANCED_FACE('',(#5091),#5058,.F.);
#5091 = FACE_BOUND('',#5092,.F.);
#5092 = EDGE_LOOP('',(#5093,#5094,#5117,#5145));
#5093 = ORIENTED_EDGE('',*,*,#5042,.T.);
#5094 = ORIENTED_EDGE('',*,*,#5095,.T.);
#5095 = EDGE_CURVE('',#5020,#5096,#5098,.T.);
#5096 = VERTEX_POINT('',#5097);
#5097 = CARTESIAN_POINT('',(65.47424802,-5.361051,1.12192054));
#5098 = SURFACE_CURVE('',#5099,(#5103,#5110),.PCURVE_S1.);
#5099 = LINE('',#5100,#5101);
#5100 = CARTESIAN_POINT('',(65.37348622,-5.60431442,1.12192054));
#5101 = VECTOR('',#5102,1.);
#5102 = DIRECTION('',(0.382679517084,0.923881154264,0.E+000));
#5103 = PCURVE('',#5058,#5104);
#5104 = DEFINITIONAL_REPRESENTATION('',(#5105),#5109);
#5105 = LINE('',#5106,#5107);
#5106 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5107 = VECTOR('',#5108,1.);
#5108 = DIRECTION('',(1.,0.E+000));
#5109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5110 = PCURVE('',#83,#5111);
#5111 = DEFINITIONAL_REPRESENTATION('',(#5112),#5116);
#5112 = LINE('',#5113,#5114);
#5113 = CARTESIAN_POINT('',(65.37348622,5.99568524));
#5114 = VECTOR('',#5115,1.);
#5115 = DIRECTION('',(0.382679517084,0.923881154264));
#5116 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5117 = ORIENTED_EDGE('',*,*,#5118,.F.);
#5118 = EDGE_CURVE('',#5119,#5096,#5121,.T.);
#5119 = VERTEX_POINT('',#5120);
#5120 = CARTESIAN_POINT('',(65.47424802,-5.361051,0.E+000));
#5121 = SURFACE_CURVE('',#5122,(#5126,#5133),.PCURVE_S1.);
#5122 = LINE('',#5123,#5124);
#5123 = CARTESIAN_POINT('',(65.47424802,-5.361051,0.E+000));
#5124 = VECTOR('',#5125,1.);
#5125 = DIRECTION('',(0.E+000,0.E+000,1.));
#5126 = PCURVE('',#5058,#5127);
#5127 = DEFINITIONAL_REPRESENTATION('',(#5128),#5132);
#5128 = LINE('',#5129,#5130);
#5129 = CARTESIAN_POINT('',(0.263305966224,0.E+000));
#5130 = VECTOR('',#5131,1.);
#5131 = DIRECTION('',(0.E+000,-1.));
#5132 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5133 = PCURVE('',#5134,#5139);
#5134 = PLANE('',#5135);
#5135 = AXIS2_PLACEMENT_3D('',#5136,#5137,#5138);
#5136 = CARTESIAN_POINT('',(65.47424802,-5.361051,0.E+000));
#5137 = DIRECTION('',(-0.991444934818,0.130525634353,0.E+000));
#5138 = DIRECTION('',(0.130525634353,0.991444934818,0.E+000));
#5139 = DEFINITIONAL_REPRESENTATION('',(#5140),#5144);
#5140 = LINE('',#5141,#5142);
#5141 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5142 = VECTOR('',#5143,1.);
#5143 = DIRECTION('',(0.E+000,-1.));
#5144 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5145 = ORIENTED_EDGE('',*,*,#5146,.F.);
#5146 = EDGE_CURVE('',#5043,#5119,#5147,.T.);
#5147 = SURFACE_CURVE('',#5148,(#5152,#5159),.PCURVE_S1.);
#5148 = LINE('',#5149,#5150);
#5149 = CARTESIAN_POINT('',(65.37348622,-5.60431442,0.E+000));
#5150 = VECTOR('',#5151,1.);
#5151 = DIRECTION('',(0.382679517084,0.923881154264,0.E+000));
#5152 = PCURVE('',#5058,#5153);
#5153 = DEFINITIONAL_REPRESENTATION('',(#5154),#5158);
#5154 = LINE('',#5155,#5156);
#5155 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5156 = VECTOR('',#5157,1.);
#5157 = DIRECTION('',(1.,0.E+000));
#5158 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5159 = PCURVE('',#137,#5160);
#5160 = DEFINITIONAL_REPRESENTATION('',(#5161),#5165);
#5161 = LINE('',#5162,#5163);
#5162 = CARTESIAN_POINT('',(65.37348622,5.99568524));
#5163 = VECTOR('',#5164,1.);
#5164 = DIRECTION('',(0.382679517084,0.923881154264));
#5165 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5166 = ADVANCED_FACE('',(#5167),#5134,.F.);
#5167 = FACE_BOUND('',#5168,.F.);
#5168 = EDGE_LOOP('',(#5169,#5170,#5193,#5221));
#5169 = ORIENTED_EDGE('',*,*,#5118,.T.);
#5170 = ORIENTED_EDGE('',*,*,#5171,.T.);
#5171 = EDGE_CURVE('',#5096,#5172,#5174,.T.);
#5172 = VERTEX_POINT('',#5173);
#5173 = CARTESIAN_POINT('',(65.50861422,-5.10001266,1.12192054));
#5174 = SURFACE_CURVE('',#5175,(#5179,#5186),.PCURVE_S1.);
#5175 = LINE('',#5176,#5177);
#5176 = CARTESIAN_POINT('',(65.47424802,-5.361051,1.12192054));
#5177 = VECTOR('',#5178,1.);
#5178 = DIRECTION('',(0.130525634353,0.991444934818,0.E+000));
#5179 = PCURVE('',#5134,#5180);
#5180 = DEFINITIONAL_REPRESENTATION('',(#5181),#5185);
#5181 = LINE('',#5182,#5183);
#5182 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5183 = VECTOR('',#5184,1.);
#5184 = DIRECTION('',(1.,0.E+000));
#5185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5186 = PCURVE('',#83,#5187);
#5187 = DEFINITIONAL_REPRESENTATION('',(#5188),#5192);
#5188 = LINE('',#5189,#5190);
#5189 = CARTESIAN_POINT('',(65.47424802,6.23894866));
#5190 = VECTOR('',#5191,1.);
#5191 = DIRECTION('',(0.130525634353,0.991444934818));
#5192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5193 = ORIENTED_EDGE('',*,*,#5194,.F.);
#5194 = EDGE_CURVE('',#5195,#5172,#5197,.T.);
#5195 = VERTEX_POINT('',#5196);
#5196 = CARTESIAN_POINT('',(65.50861422,-5.10001266,0.E+000));
#5197 = SURFACE_CURVE('',#5198,(#5202,#5209),.PCURVE_S1.);
#5198 = LINE('',#5199,#5200);
#5199 = CARTESIAN_POINT('',(65.50861422,-5.10001266,0.E+000));
#5200 = VECTOR('',#5201,1.);
#5201 = DIRECTION('',(0.E+000,0.E+000,1.));
#5202 = PCURVE('',#5134,#5203);
#5203 = DEFINITIONAL_REPRESENTATION('',(#5204),#5208);
#5204 = LINE('',#5205,#5206);
#5205 = CARTESIAN_POINT('',(0.263290810042,0.E+000));
#5206 = VECTOR('',#5207,1.);
#5207 = DIRECTION('',(0.E+000,-1.));
#5208 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5209 = PCURVE('',#5210,#5215);
#5210 = PLANE('',#5211);
#5211 = AXIS2_PLACEMENT_3D('',#5212,#5213,#5214);
#5212 = CARTESIAN_POINT('',(65.50861422,-5.10001266,0.E+000));
#5213 = DIRECTION('',(-1.474489697548E-003,-0.999998912939,0.E+000));
#5214 = DIRECTION('',(-0.999998912939,1.474489697548E-003,0.E+000));
#5215 = DEFINITIONAL_REPRESENTATION('',(#5216),#5220);
#5216 = LINE('',#5217,#5218);
#5217 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5218 = VECTOR('',#5219,1.);
#5219 = DIRECTION('',(0.E+000,-1.));
#5220 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5221 = ORIENTED_EDGE('',*,*,#5222,.F.);
#5222 = EDGE_CURVE('',#5119,#5195,#5223,.T.);
#5223 = SURFACE_CURVE('',#5224,(#5228,#5235),.PCURVE_S1.);
#5224 = LINE('',#5225,#5226);
#5225 = CARTESIAN_POINT('',(65.47424802,-5.361051,0.E+000));
#5226 = VECTOR('',#5227,1.);
#5227 = DIRECTION('',(0.130525634353,0.991444934818,0.E+000));
#5228 = PCURVE('',#5134,#5229);
#5229 = DEFINITIONAL_REPRESENTATION('',(#5230),#5234);
#5230 = LINE('',#5231,#5232);
#5231 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5232 = VECTOR('',#5233,1.);
#5233 = DIRECTION('',(1.,0.E+000));
#5234 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5235 = PCURVE('',#137,#5236);
#5236 = DEFINITIONAL_REPRESENTATION('',(#5237),#5241);
#5237 = LINE('',#5238,#5239);
#5238 = CARTESIAN_POINT('',(65.47424802,6.23894866));
#5239 = VECTOR('',#5240,1.);
#5240 = DIRECTION('',(0.130525634353,0.991444934818));
#5241 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5242 = ADVANCED_FACE('',(#5243),#5210,.F.);
#5243 = FACE_BOUND('',#5244,.F.);
#5244 = EDGE_LOOP('',(#5245,#5246,#5269,#5297));
#5245 = ORIENTED_EDGE('',*,*,#5194,.T.);
#5246 = ORIENTED_EDGE('',*,*,#5247,.T.);
#5247 = EDGE_CURVE('',#5172,#5248,#5250,.T.);
#5248 = VERTEX_POINT('',#5249);
#5249 = CARTESIAN_POINT('',(65.50000108,-5.09999996,1.12192054));
#5250 = SURFACE_CURVE('',#5251,(#5255,#5262),.PCURVE_S1.);
#5251 = LINE('',#5252,#5253);
#5252 = CARTESIAN_POINT('',(65.50861422,-5.10001266,1.12192054));
#5253 = VECTOR('',#5254,1.);
#5254 = DIRECTION('',(-0.999998912939,1.474489697548E-003,0.E+000));
#5255 = PCURVE('',#5210,#5256);
#5256 = DEFINITIONAL_REPRESENTATION('',(#5257),#5261);
#5257 = LINE('',#5258,#5259);
#5258 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5259 = VECTOR('',#5260,1.);
#5260 = DIRECTION('',(1.,0.E+000));
#5261 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5262 = PCURVE('',#83,#5263);
#5263 = DEFINITIONAL_REPRESENTATION('',(#5264),#5268);
#5264 = LINE('',#5265,#5266);
#5265 = CARTESIAN_POINT('',(65.50861422,6.499987));
#5266 = VECTOR('',#5267,1.);
#5267 = DIRECTION('',(-0.999998912939,1.474489697548E-003));
#5268 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5269 = ORIENTED_EDGE('',*,*,#5270,.F.);
#5270 = EDGE_CURVE('',#5271,#5248,#5273,.T.);
#5271 = VERTEX_POINT('',#5272);
#5272 = CARTESIAN_POINT('',(65.50000108,-5.09999996,0.E+000));
#5273 = SURFACE_CURVE('',#5274,(#5278,#5285),.PCURVE_S1.);
#5274 = LINE('',#5275,#5276);
#5275 = CARTESIAN_POINT('',(65.50000108,-5.09999996,0.E+000));
#5276 = VECTOR('',#5277,1.);
#5277 = DIRECTION('',(0.E+000,0.E+000,1.));
#5278 = PCURVE('',#5210,#5279);
#5279 = DEFINITIONAL_REPRESENTATION('',(#5280),#5284);
#5280 = LINE('',#5281,#5282);
#5281 = CARTESIAN_POINT('',(8.613149363009E-003,0.E+000));
#5282 = VECTOR('',#5283,1.);
#5283 = DIRECTION('',(0.E+000,-1.));
#5284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5285 = PCURVE('',#5286,#5291);
#5286 = PLANE('',#5287);
#5287 = AXIS2_PLACEMENT_3D('',#5288,#5289,#5290);
#5288 = CARTESIAN_POINT('',(65.50000108,-5.09999996,0.E+000));
#5289 = DIRECTION('',(-0.999999780385,6.627446299145E-004,0.E+000));
#5290 = DIRECTION('',(6.627446299145E-004,0.999999780385,0.E+000));
#5291 = DEFINITIONAL_REPRESENTATION('',(#5292),#5296);
#5292 = LINE('',#5293,#5294);
#5293 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5294 = VECTOR('',#5295,1.);
#5295 = DIRECTION('',(0.E+000,-1.));
#5296 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5297 = ORIENTED_EDGE('',*,*,#5298,.F.);
#5298 = EDGE_CURVE('',#5195,#5271,#5299,.T.);
#5299 = SURFACE_CURVE('',#5300,(#5304,#5311),.PCURVE_S1.);
#5300 = LINE('',#5301,#5302);
#5301 = CARTESIAN_POINT('',(65.50861422,-5.10001266,0.E+000));
#5302 = VECTOR('',#5303,1.);
#5303 = DIRECTION('',(-0.999998912939,1.474489697548E-003,0.E+000));
#5304 = PCURVE('',#5210,#5305);
#5305 = DEFINITIONAL_REPRESENTATION('',(#5306),#5310);
#5306 = LINE('',#5307,#5308);
#5307 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5308 = VECTOR('',#5309,1.);
#5309 = DIRECTION('',(1.,0.E+000));
#5310 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5311 = PCURVE('',#137,#5312);
#5312 = DEFINITIONAL_REPRESENTATION('',(#5313),#5317);
#5313 = LINE('',#5314,#5315);
#5314 = CARTESIAN_POINT('',(65.50861422,6.499987));
#5315 = VECTOR('',#5316,1.);
#5316 = DIRECTION('',(-0.999998912939,1.474489697548E-003));
#5317 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5318 = ADVANCED_FACE('',(#5319),#5286,.F.);
#5319 = FACE_BOUND('',#5320,.F.);
#5320 = EDGE_LOOP('',(#5321,#5322,#5345,#5373));
#5321 = ORIENTED_EDGE('',*,*,#5270,.T.);
#5322 = ORIENTED_EDGE('',*,*,#5323,.T.);
#5323 = EDGE_CURVE('',#5248,#5324,#5326,.T.);
#5324 = VERTEX_POINT('',#5325);
#5325 = CARTESIAN_POINT('',(65.50861676,7.8999969,1.12192054));
#5326 = SURFACE_CURVE('',#5327,(#5331,#5338),.PCURVE_S1.);
#5327 = LINE('',#5328,#5329);
#5328 = CARTESIAN_POINT('',(65.50000108,-5.09999996,1.12192054));
#5329 = VECTOR('',#5330,1.);
#5330 = DIRECTION('',(6.627446299145E-004,0.999999780385,0.E+000));
#5331 = PCURVE('',#5286,#5332);
#5332 = DEFINITIONAL_REPRESENTATION('',(#5333),#5337);
#5333 = LINE('',#5334,#5335);
#5334 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5335 = VECTOR('',#5336,1.);
#5336 = DIRECTION('',(1.,0.E+000));
#5337 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5338 = PCURVE('',#83,#5339);
#5339 = DEFINITIONAL_REPRESENTATION('',(#5340),#5344);
#5340 = LINE('',#5341,#5342);
#5341 = CARTESIAN_POINT('',(65.50000108,6.4999997));
#5342 = VECTOR('',#5343,1.);
#5343 = DIRECTION('',(6.627446299145E-004,0.999999780385));
#5344 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5345 = ORIENTED_EDGE('',*,*,#5346,.F.);
#5346 = EDGE_CURVE('',#5347,#5324,#5349,.T.);
#5347 = VERTEX_POINT('',#5348);
#5348 = CARTESIAN_POINT('',(65.50861676,7.8999969,0.E+000));
#5349 = SURFACE_CURVE('',#5350,(#5354,#5361),.PCURVE_S1.);
#5350 = LINE('',#5351,#5352);
#5351 = CARTESIAN_POINT('',(65.50861676,7.8999969,0.E+000));
#5352 = VECTOR('',#5353,1.);
#5353 = DIRECTION('',(0.E+000,0.E+000,1.));
#5354 = PCURVE('',#5286,#5355);
#5355 = DEFINITIONAL_REPRESENTATION('',(#5356),#5360);
#5356 = LINE('',#5357,#5358);
#5357 = CARTESIAN_POINT('',(12.999999714998,0.E+000));
#5358 = VECTOR('',#5359,1.);
#5359 = DIRECTION('',(0.E+000,-1.));
#5360 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5361 = PCURVE('',#5362,#5367);
#5362 = PLANE('',#5363);
#5363 = AXIS2_PLACEMENT_3D('',#5364,#5365,#5366);
#5364 = CARTESIAN_POINT('',(65.50861676,7.8999969,0.E+000));
#5365 = DIRECTION('',(-0.991227900678,0.132163720127,0.E+000));
#5366 = DIRECTION('',(0.132163720127,0.991227900678,0.E+000));
#5367 = DEFINITIONAL_REPRESENTATION('',(#5368),#5372);
#5368 = LINE('',#5369,#5370);
#5369 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5370 = VECTOR('',#5371,1.);
#5371 = DIRECTION('',(0.E+000,-1.));
#5372 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5373 = ORIENTED_EDGE('',*,*,#5374,.F.);
#5374 = EDGE_CURVE('',#5271,#5347,#5375,.T.);
#5375 = SURFACE_CURVE('',#5376,(#5380,#5387),.PCURVE_S1.);
#5376 = LINE('',#5377,#5378);
#5377 = CARTESIAN_POINT('',(65.50000108,-5.09999996,0.E+000));
#5378 = VECTOR('',#5379,1.);
#5379 = DIRECTION('',(6.627446299145E-004,0.999999780385,0.E+000));
#5380 = PCURVE('',#5286,#5381);
#5381 = DEFINITIONAL_REPRESENTATION('',(#5382),#5386);
#5382 = LINE('',#5383,#5384);
#5383 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5384 = VECTOR('',#5385,1.);
#5385 = DIRECTION('',(1.,0.E+000));
#5386 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5387 = PCURVE('',#137,#5388);
#5388 = DEFINITIONAL_REPRESENTATION('',(#5389),#5393);
#5389 = LINE('',#5390,#5391);
#5390 = CARTESIAN_POINT('',(65.50000108,6.4999997));
#5391 = VECTOR('',#5392,1.);
#5392 = DIRECTION('',(6.627446299145E-004,0.999999780385));
#5393 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5394 = ADVANCED_FACE('',(#5395),#5362,.F.);
#5395 = FACE_BOUND('',#5396,.F.);
#5396 = EDGE_LOOP('',(#5397,#5398,#5421,#5449));
#5397 = ORIENTED_EDGE('',*,*,#5346,.T.);
#5398 = ORIENTED_EDGE('',*,*,#5399,.T.);
#5399 = EDGE_CURVE('',#5324,#5400,#5402,.T.);
#5400 = VERTEX_POINT('',#5401);
#5401 = CARTESIAN_POINT('',(65.50862692,7.9000731,1.12192054));
#5402 = SURFACE_CURVE('',#5403,(#5407,#5414),.PCURVE_S1.);
#5403 = LINE('',#5404,#5405);
#5404 = CARTESIAN_POINT('',(65.50861676,7.8999969,1.12192054));
#5405 = VECTOR('',#5406,1.);
#5406 = DIRECTION('',(0.132163720127,0.991227900678,0.E+000));
#5407 = PCURVE('',#5362,#5408);
#5408 = DEFINITIONAL_REPRESENTATION('',(#5409),#5413);
#5409 = LINE('',#5410,#5411);
#5410 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5411 = VECTOR('',#5412,1.);
#5412 = DIRECTION('',(1.,0.E+000));
#5413 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5414 = PCURVE('',#83,#5415);
#5415 = DEFINITIONAL_REPRESENTATION('',(#5416),#5420);
#5416 = LINE('',#5417,#5418);
#5417 = CARTESIAN_POINT('',(65.50861676,19.49999656));
#5418 = VECTOR('',#5419,1.);
#5419 = DIRECTION('',(0.132163720127,0.991227900678));
#5420 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5421 = ORIENTED_EDGE('',*,*,#5422,.F.);
#5422 = EDGE_CURVE('',#5423,#5400,#5425,.T.);
#5423 = VERTEX_POINT('',#5424);
#5424 = CARTESIAN_POINT('',(65.50862692,7.9000731,0.E+000));
#5425 = SURFACE_CURVE('',#5426,(#5430,#5437),.PCURVE_S1.);
#5426 = LINE('',#5427,#5428);
#5427 = CARTESIAN_POINT('',(65.50862692,7.9000731,0.E+000));
#5428 = VECTOR('',#5429,1.);
#5429 = DIRECTION('',(0.E+000,0.E+000,1.));
#5430 = PCURVE('',#5362,#5431);
#5431 = DEFINITIONAL_REPRESENTATION('',(#5432),#5436);
#5432 = LINE('',#5433,#5434);
#5433 = CARTESIAN_POINT('',(7.687434942367E-005,0.E+000));
#5434 = VECTOR('',#5435,1.);
#5435 = DIRECTION('',(0.E+000,-1.));
#5436 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5437 = PCURVE('',#5438,#5443);
#5438 = PLANE('',#5439);
#5439 = AXIS2_PLACEMENT_3D('',#5440,#5441,#5442);
#5440 = CARTESIAN_POINT('',(65.50862692,7.9000731,0.E+000));
#5441 = DIRECTION('',(-0.99144427735,-0.130530628242,0.E+000));
#5442 = DIRECTION('',(-0.130530628242,0.99144427735,0.E+000));
#5443 = DEFINITIONAL_REPRESENTATION('',(#5444),#5448);
#5444 = LINE('',#5445,#5446);
#5445 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5446 = VECTOR('',#5447,1.);
#5447 = DIRECTION('',(0.E+000,-1.));
#5448 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5449 = ORIENTED_EDGE('',*,*,#5450,.F.);
#5450 = EDGE_CURVE('',#5347,#5423,#5451,.T.);
#5451 = SURFACE_CURVE('',#5452,(#5456,#5463),.PCURVE_S1.);
#5452 = LINE('',#5453,#5454);
#5453 = CARTESIAN_POINT('',(65.50861676,7.8999969,0.E+000));
#5454 = VECTOR('',#5455,1.);
#5455 = DIRECTION('',(0.132163720127,0.991227900678,0.E+000));
#5456 = PCURVE('',#5362,#5457);
#5457 = DEFINITIONAL_REPRESENTATION('',(#5458),#5462);
#5458 = LINE('',#5459,#5460);
#5459 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5460 = VECTOR('',#5461,1.);
#5461 = DIRECTION('',(1.,0.E+000));
#5462 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5463 = PCURVE('',#137,#5464);
#5464 = DEFINITIONAL_REPRESENTATION('',(#5465),#5469);
#5465 = LINE('',#5466,#5467);
#5466 = CARTESIAN_POINT('',(65.50861676,19.49999656));
#5467 = VECTOR('',#5468,1.);
#5468 = DIRECTION('',(0.132163720127,0.991227900678));
#5469 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5470 = ADVANCED_FACE('',(#5471),#5438,.F.);
#5471 = FACE_BOUND('',#5472,.F.);
#5472 = EDGE_LOOP('',(#5473,#5474,#5497,#5525));
#5473 = ORIENTED_EDGE('',*,*,#5422,.T.);
#5474 = ORIENTED_EDGE('',*,*,#5475,.T.);
#5475 = EDGE_CURVE('',#5400,#5476,#5478,.T.);
#5476 = VERTEX_POINT('',#5477);
#5477 = CARTESIAN_POINT('',(65.47426072,8.16110128,1.12192054));
#5478 = SURFACE_CURVE('',#5479,(#5483,#5490),.PCURVE_S1.);
#5479 = LINE('',#5480,#5481);
#5480 = CARTESIAN_POINT('',(65.50862692,7.9000731,1.12192054));
#5481 = VECTOR('',#5482,1.);
#5482 = DIRECTION('',(-0.130530628242,0.99144427735,0.E+000));
#5483 = PCURVE('',#5438,#5484);
#5484 = DEFINITIONAL_REPRESENTATION('',(#5485),#5489);
#5485 = LINE('',#5486,#5487);
#5486 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5487 = VECTOR('',#5488,1.);
#5488 = DIRECTION('',(1.,0.E+000));
#5489 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5490 = PCURVE('',#83,#5491);
#5491 = DEFINITIONAL_REPRESENTATION('',(#5492),#5496);
#5492 = LINE('',#5493,#5494);
#5493 = CARTESIAN_POINT('',(65.50862692,19.50007276));
#5494 = VECTOR('',#5495,1.);
#5495 = DIRECTION('',(-0.130530628242,0.99144427735));
#5496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5497 = ORIENTED_EDGE('',*,*,#5498,.F.);
#5498 = EDGE_CURVE('',#5499,#5476,#5501,.T.);
#5499 = VERTEX_POINT('',#5500);
#5500 = CARTESIAN_POINT('',(65.47426072,8.16110128,0.E+000));
#5501 = SURFACE_CURVE('',#5502,(#5506,#5513),.PCURVE_S1.);
#5502 = LINE('',#5503,#5504);
#5503 = CARTESIAN_POINT('',(65.47426072,8.16110128,0.E+000));
#5504 = VECTOR('',#5505,1.);
#5505 = DIRECTION('',(0.E+000,0.E+000,1.));
#5506 = PCURVE('',#5438,#5507);
#5507 = DEFINITIONAL_REPRESENTATION('',(#5508),#5512);
#5508 = LINE('',#5509,#5510);
#5509 = CARTESIAN_POINT('',(0.263280736964,0.E+000));
#5510 = VECTOR('',#5511,1.);
#5511 = DIRECTION('',(0.E+000,-1.));
#5512 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5513 = PCURVE('',#5514,#5519);
#5514 = PLANE('',#5515);
#5515 = AXIS2_PLACEMENT_3D('',#5516,#5517,#5518);
#5516 = CARTESIAN_POINT('',(65.47426072,8.16110128,0.E+000));
#5517 = DIRECTION('',(-0.923883495215,-0.382673865412,0.E+000));
#5518 = DIRECTION('',(-0.382673865412,0.923883495215,0.E+000));
#5519 = DEFINITIONAL_REPRESENTATION('',(#5520),#5524);
#5520 = LINE('',#5521,#5522);
#5521 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5522 = VECTOR('',#5523,1.);
#5523 = DIRECTION('',(0.E+000,-1.));
#5524 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5525 = ORIENTED_EDGE('',*,*,#5526,.F.);
#5526 = EDGE_CURVE('',#5423,#5499,#5527,.T.);
#5527 = SURFACE_CURVE('',#5528,(#5532,#5539),.PCURVE_S1.);
#5528 = LINE('',#5529,#5530);
#5529 = CARTESIAN_POINT('',(65.50862692,7.9000731,0.E+000));
#5530 = VECTOR('',#5531,1.);
#5531 = DIRECTION('',(-0.130530628242,0.99144427735,0.E+000));
#5532 = PCURVE('',#5438,#5533);
#5533 = DEFINITIONAL_REPRESENTATION('',(#5534),#5538);
#5534 = LINE('',#5535,#5536);
#5535 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5536 = VECTOR('',#5537,1.);
#5537 = DIRECTION('',(1.,0.E+000));
#5538 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5539 = PCURVE('',#137,#5540);
#5540 = DEFINITIONAL_REPRESENTATION('',(#5541),#5545);
#5541 = LINE('',#5542,#5543);
#5542 = CARTESIAN_POINT('',(65.50862692,19.50007276));
#5543 = VECTOR('',#5544,1.);
#5544 = DIRECTION('',(-0.130530628242,0.99144427735));
#5545 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5546 = ADVANCED_FACE('',(#5547),#5514,.F.);
#5547 = FACE_BOUND('',#5548,.F.);
#5548 = EDGE_LOOP('',(#5549,#5550,#5573,#5601));
#5549 = ORIENTED_EDGE('',*,*,#5498,.T.);
#5550 = ORIENTED_EDGE('',*,*,#5551,.T.);
#5551 = EDGE_CURVE('',#5476,#5552,#5554,.T.);
#5552 = VERTEX_POINT('',#5553);
#5553 = CARTESIAN_POINT('',(65.37350908,8.40434438,1.12192054));
#5554 = SURFACE_CURVE('',#5555,(#5559,#5566),.PCURVE_S1.);
#5555 = LINE('',#5556,#5557);
#5556 = CARTESIAN_POINT('',(65.47426072,8.16110128,1.12192054));
#5557 = VECTOR('',#5558,1.);
#5558 = DIRECTION('',(-0.382673865412,0.923883495215,0.E+000));
#5559 = PCURVE('',#5514,#5560);
#5560 = DEFINITIONAL_REPRESENTATION('',(#5561),#5565);
#5561 = LINE('',#5562,#5563);
#5562 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5563 = VECTOR('',#5564,1.);
#5564 = DIRECTION('',(1.,0.E+000));
#5565 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5566 = PCURVE('',#83,#5567);
#5567 = DEFINITIONAL_REPRESENTATION('',(#5568),#5572);
#5568 = LINE('',#5569,#5570);
#5569 = CARTESIAN_POINT('',(65.47426072,19.76110094));
#5570 = VECTOR('',#5571,1.);
#5571 = DIRECTION('',(-0.382673865412,0.923883495215));
#5572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5573 = ORIENTED_EDGE('',*,*,#5574,.F.);
#5574 = EDGE_CURVE('',#5575,#5552,#5577,.T.);
#5575 = VERTEX_POINT('',#5576);
#5576 = CARTESIAN_POINT('',(65.37350908,8.40434438,0.E+000));
#5577 = SURFACE_CURVE('',#5578,(#5582,#5589),.PCURVE_S1.);
#5578 = LINE('',#5579,#5580);
#5579 = CARTESIAN_POINT('',(65.37350908,8.40434438,0.E+000));
#5580 = VECTOR('',#5581,1.);
#5581 = DIRECTION('',(0.E+000,0.E+000,1.));
#5582 = PCURVE('',#5514,#5583);
#5583 = DEFINITIONAL_REPRESENTATION('',(#5584),#5588);
#5584 = LINE('',#5585,#5586);
#5585 = CARTESIAN_POINT('',(0.26328330494,0.E+000));
#5586 = VECTOR('',#5587,1.);
#5587 = DIRECTION('',(0.E+000,-1.));
#5588 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5589 = PCURVE('',#5590,#5595);
#5590 = PLANE('',#5591);
#5591 = AXIS2_PLACEMENT_3D('',#5592,#5593,#5594);
#5592 = CARTESIAN_POINT('',(65.37350908,8.40434438,0.E+000));
#5593 = DIRECTION('',(-0.793350393366,-0.608765269497,0.E+000));
#5594 = DIRECTION('',(-0.608765269497,0.793350393366,0.E+000));
#5595 = DEFINITIONAL_REPRESENTATION('',(#5596),#5600);
#5596 = LINE('',#5597,#5598);
#5597 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5598 = VECTOR('',#5599,1.);
#5599 = DIRECTION('',(0.E+000,-1.));
#5600 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5601 = ORIENTED_EDGE('',*,*,#5602,.F.);
#5602 = EDGE_CURVE('',#5499,#5575,#5603,.T.);
#5603 = SURFACE_CURVE('',#5604,(#5608,#5615),.PCURVE_S1.);
#5604 = LINE('',#5605,#5606);
#5605 = CARTESIAN_POINT('',(65.47426072,8.16110128,0.E+000));
#5606 = VECTOR('',#5607,1.);
#5607 = DIRECTION('',(-0.382673865412,0.923883495215,0.E+000));
#5608 = PCURVE('',#5514,#5609);
#5609 = DEFINITIONAL_REPRESENTATION('',(#5610),#5614);
#5610 = LINE('',#5611,#5612);
#5611 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5612 = VECTOR('',#5613,1.);
#5613 = DIRECTION('',(1.,0.E+000));
#5614 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5615 = PCURVE('',#137,#5616);
#5616 = DEFINITIONAL_REPRESENTATION('',(#5617),#5621);
#5617 = LINE('',#5618,#5619);
#5618 = CARTESIAN_POINT('',(65.47426072,19.76110094));
#5619 = VECTOR('',#5620,1.);
#5620 = DIRECTION('',(-0.382673865412,0.923883495215));
#5621 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5622 = ADVANCED_FACE('',(#5623),#5590,.F.);
#5623 = FACE_BOUND('',#5624,.F.);
#5624 = EDGE_LOOP('',(#5625,#5626,#5649,#5677));
#5625 = ORIENTED_EDGE('',*,*,#5574,.T.);
#5626 = ORIENTED_EDGE('',*,*,#5627,.T.);
#5627 = EDGE_CURVE('',#5552,#5628,#5630,.T.);
#5628 = VERTEX_POINT('',#5629);
#5629 = CARTESIAN_POINT('',(65.21323254,8.61321874,1.12192054));
#5630 = SURFACE_CURVE('',#5631,(#5635,#5642),.PCURVE_S1.);
#5631 = LINE('',#5632,#5633);
#5632 = CARTESIAN_POINT('',(65.37350908,8.40434438,1.12192054));
#5633 = VECTOR('',#5634,1.);
#5634 = DIRECTION('',(-0.608765269497,0.793350393366,0.E+000));
#5635 = PCURVE('',#5590,#5636);
#5636 = DEFINITIONAL_REPRESENTATION('',(#5637),#5641);
#5637 = LINE('',#5638,#5639);
#5638 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5639 = VECTOR('',#5640,1.);
#5640 = DIRECTION('',(1.,0.E+000));
#5641 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5642 = PCURVE('',#83,#5643);
#5643 = DEFINITIONAL_REPRESENTATION('',(#5644),#5648);
#5644 = LINE('',#5645,#5646);
#5645 = CARTESIAN_POINT('',(65.37350908,20.00434404));
#5646 = VECTOR('',#5647,1.);
#5647 = DIRECTION('',(-0.608765269497,0.793350393366));
#5648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5649 = ORIENTED_EDGE('',*,*,#5650,.F.);
#5650 = EDGE_CURVE('',#5651,#5628,#5653,.T.);
#5651 = VERTEX_POINT('',#5652);
#5652 = CARTESIAN_POINT('',(65.21323254,8.61321874,0.E+000));
#5653 = SURFACE_CURVE('',#5654,(#5658,#5665),.PCURVE_S1.);
#5654 = LINE('',#5655,#5656);
#5655 = CARTESIAN_POINT('',(65.21323254,8.61321874,0.E+000));
#5656 = VECTOR('',#5657,1.);
#5657 = DIRECTION('',(0.E+000,0.E+000,1.));
#5658 = PCURVE('',#5590,#5659);
#5659 = DEFINITIONAL_REPRESENTATION('',(#5660),#5664);
#5660 = LINE('',#5661,#5662);
#5661 = CARTESIAN_POINT('',(0.263281346737,0.E+000));
#5662 = VECTOR('',#5663,1.);
#5663 = DIRECTION('',(0.E+000,-1.));
#5664 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5665 = PCURVE('',#5666,#5671);
#5666 = PLANE('',#5667);
#5667 = AXIS2_PLACEMENT_3D('',#5668,#5669,#5670);
#5668 = CARTESIAN_POINT('',(65.21323254,8.61321874,0.E+000));
#5669 = DIRECTION('',(-0.608760610138,-0.793353968632,0.E+000));
#5670 = DIRECTION('',(-0.793353968632,0.608760610138,0.E+000));
#5671 = DEFINITIONAL_REPRESENTATION('',(#5672),#5676);
#5672 = LINE('',#5673,#5674);
#5673 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5674 = VECTOR('',#5675,1.);
#5675 = DIRECTION('',(0.E+000,-1.));
#5676 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5677 = ORIENTED_EDGE('',*,*,#5678,.F.);
#5678 = EDGE_CURVE('',#5575,#5651,#5679,.T.);
#5679 = SURFACE_CURVE('',#5680,(#5684,#5691),.PCURVE_S1.);
#5680 = LINE('',#5681,#5682);
#5681 = CARTESIAN_POINT('',(65.37350908,8.40434438,0.E+000));
#5682 = VECTOR('',#5683,1.);
#5683 = DIRECTION('',(-0.608765269497,0.793350393366,0.E+000));
#5684 = PCURVE('',#5590,#5685);
#5685 = DEFINITIONAL_REPRESENTATION('',(#5686),#5690);
#5686 = LINE('',#5687,#5688);
#5687 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5688 = VECTOR('',#5689,1.);
#5689 = DIRECTION('',(1.,0.E+000));
#5690 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5691 = PCURVE('',#137,#5692);
#5692 = DEFINITIONAL_REPRESENTATION('',(#5693),#5697);
#5693 = LINE('',#5694,#5695);
#5694 = CARTESIAN_POINT('',(65.37350908,20.00434404));
#5695 = VECTOR('',#5696,1.);
#5696 = DIRECTION('',(-0.608765269497,0.793350393366));
#5697 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5698 = ADVANCED_FACE('',(#5699),#5666,.F.);
#5699 = FACE_BOUND('',#5700,.F.);
#5700 = EDGE_LOOP('',(#5701,#5702,#5725,#5753));
#5701 = ORIENTED_EDGE('',*,*,#5650,.T.);
#5702 = ORIENTED_EDGE('',*,*,#5703,.T.);
#5703 = EDGE_CURVE('',#5628,#5704,#5706,.T.);
#5704 = VERTEX_POINT('',#5705);
#5705 = CARTESIAN_POINT('',(65.00435564,8.77349528,1.12192054));
#5706 = SURFACE_CURVE('',#5707,(#5711,#5718),.PCURVE_S1.);
#5707 = LINE('',#5708,#5709);
#5708 = CARTESIAN_POINT('',(65.21323254,8.61321874,1.12192054));
#5709 = VECTOR('',#5710,1.);
#5710 = DIRECTION('',(-0.793353968632,0.608760610138,0.E+000));
#5711 = PCURVE('',#5666,#5712);
#5712 = DEFINITIONAL_REPRESENTATION('',(#5713),#5717);
#5713 = LINE('',#5714,#5715);
#5714 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5715 = VECTOR('',#5716,1.);
#5716 = DIRECTION('',(1.,0.E+000));
#5717 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5718 = PCURVE('',#83,#5719);
#5719 = DEFINITIONAL_REPRESENTATION('',(#5720),#5724);
#5720 = LINE('',#5721,#5722);
#5721 = CARTESIAN_POINT('',(65.21323254,20.2132184));
#5722 = VECTOR('',#5723,1.);
#5723 = DIRECTION('',(-0.793353968632,0.608760610138));
#5724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5725 = ORIENTED_EDGE('',*,*,#5726,.F.);
#5726 = EDGE_CURVE('',#5727,#5704,#5729,.T.);
#5727 = VERTEX_POINT('',#5728);
#5728 = CARTESIAN_POINT('',(65.00435564,8.77349528,0.E+000));
#5729 = SURFACE_CURVE('',#5730,(#5734,#5741),.PCURVE_S1.);
#5730 = LINE('',#5731,#5732);
#5731 = CARTESIAN_POINT('',(65.00435564,8.77349528,0.E+000));
#5732 = VECTOR('',#5733,1.);
#5733 = DIRECTION('',(0.E+000,0.E+000,1.));
#5734 = PCURVE('',#5666,#5735);
#5735 = DEFINITIONAL_REPRESENTATION('',(#5736),#5740);
#5736 = LINE('',#5737,#5738);
#5737 = CARTESIAN_POINT('',(0.263283361852,0.E+000));
#5738 = VECTOR('',#5739,1.);
#5739 = DIRECTION('',(0.E+000,-1.));
#5740 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5741 = PCURVE('',#5742,#5747);
#5742 = PLANE('',#5743);
#5743 = AXIS2_PLACEMENT_3D('',#5744,#5745,#5746);
#5744 = CARTESIAN_POINT('',(65.00435564,8.77349528,0.E+000));
#5745 = DIRECTION('',(-0.382677276241,-0.923882082437,0.E+000));
#5746 = DIRECTION('',(-0.923882082437,0.382677276241,0.E+000));
#5747 = DEFINITIONAL_REPRESENTATION('',(#5748),#5752);
#5748 = LINE('',#5749,#5750);
#5749 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5750 = VECTOR('',#5751,1.);
#5751 = DIRECTION('',(0.E+000,-1.));
#5752 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5753 = ORIENTED_EDGE('',*,*,#5754,.F.);
#5754 = EDGE_CURVE('',#5651,#5727,#5755,.T.);
#5755 = SURFACE_CURVE('',#5756,(#5760,#5767),.PCURVE_S1.);
#5756 = LINE('',#5757,#5758);
#5757 = CARTESIAN_POINT('',(65.21323254,8.61321874,0.E+000));
#5758 = VECTOR('',#5759,1.);
#5759 = DIRECTION('',(-0.793353968632,0.608760610138,0.E+000));
#5760 = PCURVE('',#5666,#5761);
#5761 = DEFINITIONAL_REPRESENTATION('',(#5762),#5766);
#5762 = LINE('',#5763,#5764);
#5763 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5764 = VECTOR('',#5765,1.);
#5765 = DIRECTION('',(1.,0.E+000));
#5766 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5767 = PCURVE('',#137,#5768);
#5768 = DEFINITIONAL_REPRESENTATION('',(#5769),#5773);
#5769 = LINE('',#5770,#5771);
#5770 = CARTESIAN_POINT('',(65.21323254,20.2132184));
#5771 = VECTOR('',#5772,1.);
#5772 = DIRECTION('',(-0.793353968632,0.608760610138));
#5773 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5774 = ADVANCED_FACE('',(#5775),#5742,.F.);
#5775 = FACE_BOUND('',#5776,.F.);
#5776 = EDGE_LOOP('',(#5777,#5778,#5801,#5824));
#5777 = ORIENTED_EDGE('',*,*,#5726,.T.);
#5778 = ORIENTED_EDGE('',*,*,#5779,.T.);
#5779 = EDGE_CURVE('',#5704,#5780,#5782,.T.);
#5780 = VERTEX_POINT('',#5781);
#5781 = CARTESIAN_POINT('',(64.76111508,8.87424692,1.12192054));
#5782 = SURFACE_CURVE('',#5783,(#5787,#5794),.PCURVE_S1.);
#5783 = LINE('',#5784,#5785);
#5784 = CARTESIAN_POINT('',(65.00435564,8.77349528,1.12192054));
#5785 = VECTOR('',#5786,1.);
#5786 = DIRECTION('',(-0.923882082437,0.382677276241,0.E+000));
#5787 = PCURVE('',#5742,#5788);
#5788 = DEFINITIONAL_REPRESENTATION('',(#5789),#5793);
#5789 = LINE('',#5790,#5791);
#5790 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5791 = VECTOR('',#5792,1.);
#5792 = DIRECTION('',(1.,0.E+000));
#5793 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5794 = PCURVE('',#83,#5795);
#5795 = DEFINITIONAL_REPRESENTATION('',(#5796),#5800);
#5796 = LINE('',#5797,#5798);
#5797 = CARTESIAN_POINT('',(65.00435564,20.37349494));
#5798 = VECTOR('',#5799,1.);
#5799 = DIRECTION('',(-0.923882082437,0.382677276241));
#5800 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5801 = ORIENTED_EDGE('',*,*,#5802,.F.);
#5802 = EDGE_CURVE('',#5803,#5780,#5805,.T.);
#5803 = VERTEX_POINT('',#5804);
#5804 = CARTESIAN_POINT('',(64.76111508,8.87424692,0.E+000));
#5805 = SURFACE_CURVE('',#5806,(#5810,#5817),.PCURVE_S1.);
#5806 = LINE('',#5807,#5808);
#5807 = CARTESIAN_POINT('',(64.76111508,8.87424692,0.E+000));
#5808 = VECTOR('',#5809,1.);
#5809 = DIRECTION('',(0.E+000,0.E+000,1.));
#5810 = PCURVE('',#5742,#5811);
#5811 = DEFINITIONAL_REPRESENTATION('',(#5812),#5816);
#5812 = LINE('',#5813,#5814);
#5813 = CARTESIAN_POINT('',(0.263280958278,0.E+000));
#5814 = VECTOR('',#5815,1.);
#5815 = DIRECTION('',(0.E+000,-1.));
#5816 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5817 = PCURVE('',#3183,#5818);
#5818 = DEFINITIONAL_REPRESENTATION('',(#5819),#5823);
#5819 = LINE('',#5820,#5821);
#5820 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5821 = VECTOR('',#5822,1.);
#5822 = DIRECTION('',(0.E+000,-1.));
#5823 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5824 = ORIENTED_EDGE('',*,*,#5825,.F.);
#5825 = EDGE_CURVE('',#5727,#5803,#5826,.T.);
#5826 = SURFACE_CURVE('',#5827,(#5831,#5838),.PCURVE_S1.);
#5827 = LINE('',#5828,#5829);
#5828 = CARTESIAN_POINT('',(65.00435564,8.77349528,0.E+000));
#5829 = VECTOR('',#5830,1.);
#5830 = DIRECTION('',(-0.923882082437,0.382677276241,0.E+000));
#5831 = PCURVE('',#5742,#5832);
#5832 = DEFINITIONAL_REPRESENTATION('',(#5833),#5837);
#5833 = LINE('',#5834,#5835);
#5834 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5835 = VECTOR('',#5836,1.);
#5836 = DIRECTION('',(1.,0.E+000));
#5837 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5838 = PCURVE('',#137,#5839);
#5839 = DEFINITIONAL_REPRESENTATION('',(#5840),#5844);
#5840 = LINE('',#5841,#5842);
#5841 = CARTESIAN_POINT('',(65.00435564,20.37349494));
#5842 = VECTOR('',#5843,1.);
#5843 = DIRECTION('',(-0.923882082437,0.382677276241));
#5844 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5845 = ADVANCED_FACE('',(#5846),#3183,.F.);
#5846 = FACE_BOUND('',#5847,.F.);
#5847 = EDGE_LOOP('',(#5848,#5849,#5870,#5871));
#5848 = ORIENTED_EDGE('',*,*,#5802,.T.);
#5849 = ORIENTED_EDGE('',*,*,#5850,.T.);
#5850 = EDGE_CURVE('',#5780,#3163,#5851,.T.);
#5851 = SURFACE_CURVE('',#5852,(#5856,#5863),.PCURVE_S1.);
#5852 = LINE('',#5853,#5854);
#5853 = CARTESIAN_POINT('',(64.76111508,8.87424692,1.12192054));
#5854 = VECTOR('',#5855,1.);
#5855 = DIRECTION('',(-0.99144427735,0.130530628242,0.E+000));
#5856 = PCURVE('',#3183,#5857);
#5857 = DEFINITIONAL_REPRESENTATION('',(#5858),#5862);
#5858 = LINE('',#5859,#5860);
#5859 = CARTESIAN_POINT('',(0.E+000,-1.12192054));
#5860 = VECTOR('',#5861,1.);
#5861 = DIRECTION('',(1.,0.E+000));
#5862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5863 = PCURVE('',#83,#5864);
#5864 = DEFINITIONAL_REPRESENTATION('',(#5865),#5869);
#5865 = LINE('',#5866,#5867);
#5866 = CARTESIAN_POINT('',(64.76111508,20.47424658));
#5867 = VECTOR('',#5868,1.);
#5868 = DIRECTION('',(-0.99144427735,0.130530628242));
#5869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5870 = ORIENTED_EDGE('',*,*,#3160,.F.);
#5871 = ORIENTED_EDGE('',*,*,#5872,.F.);
#5872 = EDGE_CURVE('',#5803,#3161,#5873,.T.);
#5873 = SURFACE_CURVE('',#5874,(#5878,#5885),.PCURVE_S1.);
#5874 = LINE('',#5875,#5876);
#5875 = CARTESIAN_POINT('',(64.76111508,8.87424692,0.E+000));
#5876 = VECTOR('',#5877,1.);
#5877 = DIRECTION('',(-0.99144427735,0.130530628242,0.E+000));
#5878 = PCURVE('',#3183,#5879);
#5879 = DEFINITIONAL_REPRESENTATION('',(#5880),#5884);
#5880 = LINE('',#5881,#5882);
#5881 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5882 = VECTOR('',#5883,1.);
#5883 = DIRECTION('',(1.,0.E+000));
#5884 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5885 = PCURVE('',#137,#5886);
#5886 = DEFINITIONAL_REPRESENTATION('',(#5887),#5891);
#5887 = LINE('',#5888,#5889);
#5888 = CARTESIAN_POINT('',(64.76111508,20.47424658));
#5889 = VECTOR('',#5890,1.);
#5890 = DIRECTION('',(-0.99144427735,0.130530628242));
#5891 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5892 = ADVANCED_FACE('',(#5893),#5907,.T.);
#5893 = FACE_BOUND('',#5894,.F.);
#5894 = EDGE_LOOP('',(#5895,#5925,#5947,#5948));
#5895 = ORIENTED_EDGE('',*,*,#5896,.T.);
#5896 = EDGE_CURVE('',#5897,#5899,#5901,.T.);
#5897 = VERTEX_POINT('',#5898);
#5898 = CARTESIAN_POINT('',(41.24999878,-0.32999934,0.E+000));
#5899 = VERTEX_POINT('',#5900);
#5900 = CARTESIAN_POINT('',(41.24999878,-0.32999934,1.12192054));
#5901 = SEAM_CURVE('',#5902,(#5906,#5918),.PCURVE_S1.);
#5902 = LINE('',#5903,#5904);
#5903 = CARTESIAN_POINT('',(41.24999878,-0.32999934,0.E+000));
#5904 = VECTOR('',#5905,1.);
#5905 = DIRECTION('',(0.E+000,0.E+000,1.));
#5906 = PCURVE('',#5907,#5912);
#5907 = CYLINDRICAL_SURFACE('',#5908,4.74999812);
#5908 = AXIS2_PLACEMENT_3D('',#5909,#5910,#5911);
#5909 = CARTESIAN_POINT('',(36.50000066,-0.32999934,0.E+000));
#5910 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#5911 = DIRECTION('',(1.,0.E+000,-0.E+000));
#5912 = DEFINITIONAL_REPRESENTATION('',(#5913),#5917);
#5913 = LINE('',#5914,#5915);
#5914 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#5915 = VECTOR('',#5916,1.);
#5916 = DIRECTION('',(-0.E+000,-1.));
#5917 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5918 = PCURVE('',#5907,#5919);
#5919 = DEFINITIONAL_REPRESENTATION('',(#5920),#5924);
#5920 = LINE('',#5921,#5922);
#5921 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#5922 = VECTOR('',#5923,1.);
#5923 = DIRECTION('',(-0.E+000,-1.));
#5924 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5925 = ORIENTED_EDGE('',*,*,#5926,.T.);
#5926 = EDGE_CURVE('',#5899,#5899,#5927,.T.);
#5927 = SURFACE_CURVE('',#5928,(#5933,#5940),.PCURVE_S1.);
#5928 = CIRCLE('',#5929,4.74999812);
#5929 = AXIS2_PLACEMENT_3D('',#5930,#5931,#5932);
#5930 = CARTESIAN_POINT('',(36.50000066,-0.32999934,1.12192054));
#5931 = DIRECTION('',(0.E+000,0.E+000,1.));
#5932 = DIRECTION('',(1.,0.E+000,-0.E+000));
#5933 = PCURVE('',#5907,#5934);
#5934 = DEFINITIONAL_REPRESENTATION('',(#5935),#5939);
#5935 = LINE('',#5936,#5937);
#5936 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#5937 = VECTOR('',#5938,1.);
#5938 = DIRECTION('',(-1.,0.E+000));
#5939 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5940 = PCURVE('',#83,#5941);
#5941 = DEFINITIONAL_REPRESENTATION('',(#5942),#5946);
#5942 = CIRCLE('',#5943,4.74999812);
#5943 = AXIS2_PLACEMENT_2D('',#5944,#5945);
#5944 = CARTESIAN_POINT('',(36.50000066,11.27000032));
#5945 = DIRECTION('',(1.,0.E+000));
#5946 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5947 = ORIENTED_EDGE('',*,*,#5896,.F.);
#5948 = ORIENTED_EDGE('',*,*,#5949,.F.);
#5949 = EDGE_CURVE('',#5897,#5897,#5950,.T.);
#5950 = SURFACE_CURVE('',#5951,(#5956,#5963),.PCURVE_S1.);
#5951 = CIRCLE('',#5952,4.74999812);
#5952 = AXIS2_PLACEMENT_3D('',#5953,#5954,#5955);
#5953 = CARTESIAN_POINT('',(36.50000066,-0.32999934,0.E+000));
#5954 = DIRECTION('',(0.E+000,0.E+000,1.));
#5955 = DIRECTION('',(1.,0.E+000,-0.E+000));
#5956 = PCURVE('',#5907,#5957);
#5957 = DEFINITIONAL_REPRESENTATION('',(#5958),#5962);
#5958 = LINE('',#5959,#5960);
#5959 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#5960 = VECTOR('',#5961,1.);
#5961 = DIRECTION('',(-1.,0.E+000));
#5962 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5963 = PCURVE('',#137,#5964);
#5964 = DEFINITIONAL_REPRESENTATION('',(#5965),#5969);
#5965 = CIRCLE('',#5966,4.74999812);
#5966 = AXIS2_PLACEMENT_2D('',#5967,#5968);
#5967 = CARTESIAN_POINT('',(36.50000066,11.27000032));
#5968 = DIRECTION('',(1.,0.E+000));
#5969 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5970 = ADVANCED_FACE('',(#5971),#5985,.T.);
#5971 = FACE_BOUND('',#5972,.F.);
#5972 = EDGE_LOOP('',(#5973,#6003,#6025,#6026));
#5973 = ORIENTED_EDGE('',*,*,#5974,.T.);
#5974 = EDGE_CURVE('',#5975,#5977,#5979,.T.);
#5975 = VERTEX_POINT('',#5976);
#5976 = CARTESIAN_POINT('',(8.19998614,6.5999868,0.E+000));
#5977 = VERTEX_POINT('',#5978);
#5978 = CARTESIAN_POINT('',(8.19998614,6.5999868,1.12192054));
#5979 = SEAM_CURVE('',#5980,(#5984,#5996),.PCURVE_S1.);
#5980 = LINE('',#5981,#5982);
#5981 = CARTESIAN_POINT('',(8.19998614,6.5999868,0.E+000));
#5982 = VECTOR('',#5983,1.);
#5983 = DIRECTION('',(0.E+000,0.E+000,1.));
#5984 = PCURVE('',#5985,#5990);
#5985 = CYLINDRICAL_SURFACE('',#5986,1.59999934);
#5986 = AXIS2_PLACEMENT_3D('',#5987,#5988,#5989);
#5987 = CARTESIAN_POINT('',(6.5999868,6.5999868,0.E+000));
#5988 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#5989 = DIRECTION('',(1.,0.E+000,-0.E+000));
#5990 = DEFINITIONAL_REPRESENTATION('',(#5991),#5995);
#5991 = LINE('',#5992,#5993);
#5992 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#5993 = VECTOR('',#5994,1.);
#5994 = DIRECTION('',(-0.E+000,-1.));
#5995 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5996 = PCURVE('',#5985,#5997);
#5997 = DEFINITIONAL_REPRESENTATION('',(#5998),#6002);
#5998 = LINE('',#5999,#6000);
#5999 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6000 = VECTOR('',#6001,1.);
#6001 = DIRECTION('',(-0.E+000,-1.));
#6002 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6003 = ORIENTED_EDGE('',*,*,#6004,.T.);
#6004 = EDGE_CURVE('',#5977,#5977,#6005,.T.);
#6005 = SURFACE_CURVE('',#6006,(#6011,#6018),.PCURVE_S1.);
#6006 = CIRCLE('',#6007,1.59999934);
#6007 = AXIS2_PLACEMENT_3D('',#6008,#6009,#6010);
#6008 = CARTESIAN_POINT('',(6.5999868,6.5999868,1.12192054));
#6009 = DIRECTION('',(0.E+000,0.E+000,1.));
#6010 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6011 = PCURVE('',#5985,#6012);
#6012 = DEFINITIONAL_REPRESENTATION('',(#6013),#6017);
#6013 = LINE('',#6014,#6015);
#6014 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6015 = VECTOR('',#6016,1.);
#6016 = DIRECTION('',(-1.,0.E+000));
#6017 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6018 = PCURVE('',#83,#6019);
#6019 = DEFINITIONAL_REPRESENTATION('',(#6020),#6024);
#6020 = CIRCLE('',#6021,1.59999934);
#6021 = AXIS2_PLACEMENT_2D('',#6022,#6023);
#6022 = CARTESIAN_POINT('',(6.5999868,18.19998646));
#6023 = DIRECTION('',(1.,0.E+000));
#6024 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6025 = ORIENTED_EDGE('',*,*,#5974,.F.);
#6026 = ORIENTED_EDGE('',*,*,#6027,.F.);
#6027 = EDGE_CURVE('',#5975,#5975,#6028,.T.);
#6028 = SURFACE_CURVE('',#6029,(#6034,#6041),.PCURVE_S1.);
#6029 = CIRCLE('',#6030,1.59999934);
#6030 = AXIS2_PLACEMENT_3D('',#6031,#6032,#6033);
#6031 = CARTESIAN_POINT('',(6.5999868,6.5999868,0.E+000));
#6032 = DIRECTION('',(0.E+000,0.E+000,1.));
#6033 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6034 = PCURVE('',#5985,#6035);
#6035 = DEFINITIONAL_REPRESENTATION('',(#6036),#6040);
#6036 = LINE('',#6037,#6038);
#6037 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6038 = VECTOR('',#6039,1.);
#6039 = DIRECTION('',(-1.,0.E+000));
#6040 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6041 = PCURVE('',#137,#6042);
#6042 = DEFINITIONAL_REPRESENTATION('',(#6043),#6047);
#6043 = CIRCLE('',#6044,1.59999934);
#6044 = AXIS2_PLACEMENT_2D('',#6045,#6046);
#6045 = CARTESIAN_POINT('',(6.5999868,18.19998646));
#6046 = DIRECTION('',(1.,0.E+000));
#6047 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6048 = ADVANCED_FACE('',(#6049),#6063,.T.);
#6049 = FACE_BOUND('',#6050,.F.);
#6050 = EDGE_LOOP('',(#6051,#6081,#6103,#6104));
#6051 = ORIENTED_EDGE('',*,*,#6052,.T.);
#6052 = EDGE_CURVE('',#6053,#6055,#6057,.T.);
#6053 = VERTEX_POINT('',#6054);
#6054 = CARTESIAN_POINT('',(75.59999866,-2.74999958,0.E+000));
#6055 = VERTEX_POINT('',#6056);
#6056 = CARTESIAN_POINT('',(75.59999866,-2.74999958,1.12192054));
#6057 = SEAM_CURVE('',#6058,(#6062,#6074),.PCURVE_S1.);
#6058 = LINE('',#6059,#6060);
#6059 = CARTESIAN_POINT('',(75.59999866,-2.74999958,0.E+000));
#6060 = VECTOR('',#6061,1.);
#6061 = DIRECTION('',(0.E+000,0.E+000,1.));
#6062 = PCURVE('',#6063,#6068);
#6063 = CYLINDRICAL_SURFACE('',#6064,1.59999934);
#6064 = AXIS2_PLACEMENT_3D('',#6065,#6066,#6067);
#6065 = CARTESIAN_POINT('',(73.99999932,-2.74999958,0.E+000));
#6066 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#6067 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6068 = DEFINITIONAL_REPRESENTATION('',(#6069),#6073);
#6069 = LINE('',#6070,#6071);
#6070 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6071 = VECTOR('',#6072,1.);
#6072 = DIRECTION('',(-0.E+000,-1.));
#6073 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6074 = PCURVE('',#6063,#6075);
#6075 = DEFINITIONAL_REPRESENTATION('',(#6076),#6080);
#6076 = LINE('',#6077,#6078);
#6077 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6078 = VECTOR('',#6079,1.);
#6079 = DIRECTION('',(-0.E+000,-1.));
#6080 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6081 = ORIENTED_EDGE('',*,*,#6082,.T.);
#6082 = EDGE_CURVE('',#6055,#6055,#6083,.T.);
#6083 = SURFACE_CURVE('',#6084,(#6089,#6096),.PCURVE_S1.);
#6084 = CIRCLE('',#6085,1.59999934);
#6085 = AXIS2_PLACEMENT_3D('',#6086,#6087,#6088);
#6086 = CARTESIAN_POINT('',(73.99999932,-2.74999958,1.12192054));
#6087 = DIRECTION('',(0.E+000,0.E+000,1.));
#6088 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6089 = PCURVE('',#6063,#6090);
#6090 = DEFINITIONAL_REPRESENTATION('',(#6091),#6095);
#6091 = LINE('',#6092,#6093);
#6092 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6093 = VECTOR('',#6094,1.);
#6094 = DIRECTION('',(-1.,0.E+000));
#6095 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6096 = PCURVE('',#83,#6097);
#6097 = DEFINITIONAL_REPRESENTATION('',(#6098),#6102);
#6098 = CIRCLE('',#6099,1.59999934);
#6099 = AXIS2_PLACEMENT_2D('',#6100,#6101);
#6100 = CARTESIAN_POINT('',(73.99999932,8.85000008));
#6101 = DIRECTION('',(1.,0.E+000));
#6102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6103 = ORIENTED_EDGE('',*,*,#6052,.F.);
#6104 = ORIENTED_EDGE('',*,*,#6105,.F.);
#6105 = EDGE_CURVE('',#6053,#6053,#6106,.T.);
#6106 = SURFACE_CURVE('',#6107,(#6112,#6119),.PCURVE_S1.);
#6107 = CIRCLE('',#6108,1.59999934);
#6108 = AXIS2_PLACEMENT_3D('',#6109,#6110,#6111);
#6109 = CARTESIAN_POINT('',(73.99999932,-2.74999958,0.E+000));
#6110 = DIRECTION('',(0.E+000,0.E+000,1.));
#6111 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6112 = PCURVE('',#6063,#6113);
#6113 = DEFINITIONAL_REPRESENTATION('',(#6114),#6118);
#6114 = LINE('',#6115,#6116);
#6115 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6116 = VECTOR('',#6117,1.);
#6117 = DIRECTION('',(-1.,0.E+000));
#6118 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6119 = PCURVE('',#137,#6120);
#6120 = DEFINITIONAL_REPRESENTATION('',(#6121),#6125);
#6121 = CIRCLE('',#6122,1.59999934);
#6122 = AXIS2_PLACEMENT_2D('',#6123,#6124);
#6123 = CARTESIAN_POINT('',(73.99999932,8.85000008));
#6124 = DIRECTION('',(1.,0.E+000));
#6125 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6126 = ADVANCED_FACE('',(#6127),#6141,.T.);
#6127 = FACE_BOUND('',#6128,.F.);
#6128 = EDGE_LOOP('',(#6129,#6159,#6181,#6182));
#6129 = ORIENTED_EDGE('',*,*,#6130,.T.);
#6130 = EDGE_CURVE('',#6131,#6133,#6135,.T.);
#6131 = VERTEX_POINT('',#6132);
#6132 = CARTESIAN_POINT('',(52.70999872,-2.84999938,0.E+000));
#6133 = VERTEX_POINT('',#6134);
#6134 = CARTESIAN_POINT('',(52.70999872,-2.84999938,1.12192054));
#6135 = SEAM_CURVE('',#6136,(#6140,#6152),.PCURVE_S1.);
#6136 = LINE('',#6137,#6138);
#6137 = CARTESIAN_POINT('',(52.70999872,-2.84999938,0.E+000));
#6138 = VECTOR('',#6139,1.);
#6139 = DIRECTION('',(0.E+000,0.E+000,1.));
#6140 = PCURVE('',#6141,#6146);
#6141 = CYLINDRICAL_SURFACE('',#6142,3.24999858);
#6142 = AXIS2_PLACEMENT_3D('',#6143,#6144,#6145);
#6143 = CARTESIAN_POINT('',(49.46000014,-2.84999938,0.E+000));
#6144 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#6145 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6146 = DEFINITIONAL_REPRESENTATION('',(#6147),#6151);
#6147 = LINE('',#6148,#6149);
#6148 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6149 = VECTOR('',#6150,1.);
#6150 = DIRECTION('',(-0.E+000,-1.));
#6151 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6152 = PCURVE('',#6141,#6153);
#6153 = DEFINITIONAL_REPRESENTATION('',(#6154),#6158);
#6154 = LINE('',#6155,#6156);
#6155 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6156 = VECTOR('',#6157,1.);
#6157 = DIRECTION('',(-0.E+000,-1.));
#6158 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6159 = ORIENTED_EDGE('',*,*,#6160,.T.);
#6160 = EDGE_CURVE('',#6133,#6133,#6161,.T.);
#6161 = SURFACE_CURVE('',#6162,(#6167,#6174),.PCURVE_S1.);
#6162 = CIRCLE('',#6163,3.24999858);
#6163 = AXIS2_PLACEMENT_3D('',#6164,#6165,#6166);
#6164 = CARTESIAN_POINT('',(49.46000014,-2.84999938,1.12192054));
#6165 = DIRECTION('',(0.E+000,0.E+000,1.));
#6166 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6167 = PCURVE('',#6141,#6168);
#6168 = DEFINITIONAL_REPRESENTATION('',(#6169),#6173);
#6169 = LINE('',#6170,#6171);
#6170 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6171 = VECTOR('',#6172,1.);
#6172 = DIRECTION('',(-1.,0.E+000));
#6173 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6174 = PCURVE('',#83,#6175);
#6175 = DEFINITIONAL_REPRESENTATION('',(#6176),#6180);
#6176 = CIRCLE('',#6177,3.24999858);
#6177 = AXIS2_PLACEMENT_2D('',#6178,#6179);
#6178 = CARTESIAN_POINT('',(49.46000014,8.75000028));
#6179 = DIRECTION('',(1.,0.E+000));
#6180 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6181 = ORIENTED_EDGE('',*,*,#6130,.F.);
#6182 = ORIENTED_EDGE('',*,*,#6183,.F.);
#6183 = EDGE_CURVE('',#6131,#6131,#6184,.T.);
#6184 = SURFACE_CURVE('',#6185,(#6190,#6197),.PCURVE_S1.);
#6185 = CIRCLE('',#6186,3.24999858);
#6186 = AXIS2_PLACEMENT_3D('',#6187,#6188,#6189);
#6187 = CARTESIAN_POINT('',(49.46000014,-2.84999938,0.E+000));
#6188 = DIRECTION('',(0.E+000,0.E+000,1.));
#6189 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6190 = PCURVE('',#6141,#6191);
#6191 = DEFINITIONAL_REPRESENTATION('',(#6192),#6196);
#6192 = LINE('',#6193,#6194);
#6193 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6194 = VECTOR('',#6195,1.);
#6195 = DIRECTION('',(-1.,0.E+000));
#6196 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6197 = PCURVE('',#137,#6198);
#6198 = DEFINITIONAL_REPRESENTATION('',(#6199),#6203);
#6199 = CIRCLE('',#6200,3.24999858);
#6200 = AXIS2_PLACEMENT_2D('',#6201,#6202);
#6201 = CARTESIAN_POINT('',(49.46000014,8.75000028));
#6202 = DIRECTION('',(1.,0.E+000));
#6203 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6204 = ADVANCED_FACE('',(#6205),#6219,.T.);
#6205 = FACE_BOUND('',#6206,.F.);
#6206 = EDGE_LOOP('',(#6207,#6237,#6259,#6260));
#6207 = ORIENTED_EDGE('',*,*,#6208,.T.);
#6208 = EDGE_CURVE('',#6209,#6211,#6213,.T.);
#6209 = VERTEX_POINT('',#6210);
#6210 = CARTESIAN_POINT('',(75.59999866,1.30999992,0.E+000));
#6211 = VERTEX_POINT('',#6212);
#6212 = CARTESIAN_POINT('',(75.59999866,1.30999992,1.12192054));
#6213 = SEAM_CURVE('',#6214,(#6218,#6230),.PCURVE_S1.);
#6214 = LINE('',#6215,#6216);
#6215 = CARTESIAN_POINT('',(75.59999866,1.30999992,0.E+000));
#6216 = VECTOR('',#6217,1.);
#6217 = DIRECTION('',(0.E+000,0.E+000,1.));
#6218 = PCURVE('',#6219,#6224);
#6219 = CYLINDRICAL_SURFACE('',#6220,1.59999934);
#6220 = AXIS2_PLACEMENT_3D('',#6221,#6222,#6223);
#6221 = CARTESIAN_POINT('',(73.99999932,1.30999992,0.E+000));
#6222 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#6223 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6224 = DEFINITIONAL_REPRESENTATION('',(#6225),#6229);
#6225 = LINE('',#6226,#6227);
#6226 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6227 = VECTOR('',#6228,1.);
#6228 = DIRECTION('',(-0.E+000,-1.));
#6229 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6230 = PCURVE('',#6219,#6231);
#6231 = DEFINITIONAL_REPRESENTATION('',(#6232),#6236);
#6232 = LINE('',#6233,#6234);
#6233 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6234 = VECTOR('',#6235,1.);
#6235 = DIRECTION('',(-0.E+000,-1.));
#6236 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6237 = ORIENTED_EDGE('',*,*,#6238,.T.);
#6238 = EDGE_CURVE('',#6211,#6211,#6239,.T.);
#6239 = SURFACE_CURVE('',#6240,(#6245,#6252),.PCURVE_S1.);
#6240 = CIRCLE('',#6241,1.59999934);
#6241 = AXIS2_PLACEMENT_3D('',#6242,#6243,#6244);
#6242 = CARTESIAN_POINT('',(73.99999932,1.30999992,1.12192054));
#6243 = DIRECTION('',(0.E+000,0.E+000,1.));
#6244 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6245 = PCURVE('',#6219,#6246);
#6246 = DEFINITIONAL_REPRESENTATION('',(#6247),#6251);
#6247 = LINE('',#6248,#6249);
#6248 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6249 = VECTOR('',#6250,1.);
#6250 = DIRECTION('',(-1.,0.E+000));
#6251 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6252 = PCURVE('',#83,#6253);
#6253 = DEFINITIONAL_REPRESENTATION('',(#6254),#6258);
#6254 = CIRCLE('',#6255,1.59999934);
#6255 = AXIS2_PLACEMENT_2D('',#6256,#6257);
#6256 = CARTESIAN_POINT('',(73.99999932,12.90999958));
#6257 = DIRECTION('',(1.,0.E+000));
#6258 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6259 = ORIENTED_EDGE('',*,*,#6208,.F.);
#6260 = ORIENTED_EDGE('',*,*,#6261,.F.);
#6261 = EDGE_CURVE('',#6209,#6209,#6262,.T.);
#6262 = SURFACE_CURVE('',#6263,(#6268,#6275),.PCURVE_S1.);
#6263 = CIRCLE('',#6264,1.59999934);
#6264 = AXIS2_PLACEMENT_3D('',#6265,#6266,#6267);
#6265 = CARTESIAN_POINT('',(73.99999932,1.30999992,0.E+000));
#6266 = DIRECTION('',(0.E+000,0.E+000,1.));
#6267 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6268 = PCURVE('',#6219,#6269);
#6269 = DEFINITIONAL_REPRESENTATION('',(#6270),#6274);
#6270 = LINE('',#6271,#6272);
#6271 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6272 = VECTOR('',#6273,1.);
#6273 = DIRECTION('',(-1.,0.E+000));
#6274 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6275 = PCURVE('',#137,#6276);
#6276 = DEFINITIONAL_REPRESENTATION('',(#6277),#6281);
#6277 = CIRCLE('',#6278,1.59999934);
#6278 = AXIS2_PLACEMENT_2D('',#6279,#6280);
#6279 = CARTESIAN_POINT('',(73.99999932,12.90999958));
#6280 = DIRECTION('',(1.,0.E+000));
#6281 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6282 = ADVANCED_FACE('',(#6283),#6297,.T.);
#6283 = FACE_BOUND('',#6284,.F.);
#6284 = EDGE_LOOP('',(#6285,#6315,#6337,#6338));
#6285 = ORIENTED_EDGE('',*,*,#6286,.T.);
#6286 = EDGE_CURVE('',#6287,#6289,#6291,.T.);
#6287 = VERTEX_POINT('',#6288);
#6288 = CARTESIAN_POINT('',(124.99998622,-6.6000122,0.E+000));
#6289 = VERTEX_POINT('',#6290);
#6290 = CARTESIAN_POINT('',(124.99998622,-6.6000122,1.12192054));
#6291 = SEAM_CURVE('',#6292,(#6296,#6308),.PCURVE_S1.);
#6292 = LINE('',#6293,#6294);
#6293 = CARTESIAN_POINT('',(124.99998622,-6.6000122,0.E+000));
#6294 = VECTOR('',#6295,1.);
#6295 = DIRECTION('',(0.E+000,0.E+000,1.));
#6296 = PCURVE('',#6297,#6302);
#6297 = CYLINDRICAL_SURFACE('',#6298,1.59999934);
#6298 = AXIS2_PLACEMENT_3D('',#6299,#6300,#6301);
#6299 = CARTESIAN_POINT('',(123.39998688,-6.6000122,0.E+000));
#6300 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#6301 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6302 = DEFINITIONAL_REPRESENTATION('',(#6303),#6307);
#6303 = LINE('',#6304,#6305);
#6304 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6305 = VECTOR('',#6306,1.);
#6306 = DIRECTION('',(-0.E+000,-1.));
#6307 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6308 = PCURVE('',#6297,#6309);
#6309 = DEFINITIONAL_REPRESENTATION('',(#6310),#6314);
#6310 = LINE('',#6311,#6312);
#6311 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6312 = VECTOR('',#6313,1.);
#6313 = DIRECTION('',(-0.E+000,-1.));
#6314 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6315 = ORIENTED_EDGE('',*,*,#6316,.T.);
#6316 = EDGE_CURVE('',#6289,#6289,#6317,.T.);
#6317 = SURFACE_CURVE('',#6318,(#6323,#6330),.PCURVE_S1.);
#6318 = CIRCLE('',#6319,1.59999934);
#6319 = AXIS2_PLACEMENT_3D('',#6320,#6321,#6322);
#6320 = CARTESIAN_POINT('',(123.39998688,-6.6000122,1.12192054));
#6321 = DIRECTION('',(0.E+000,0.E+000,1.));
#6322 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6323 = PCURVE('',#6297,#6324);
#6324 = DEFINITIONAL_REPRESENTATION('',(#6325),#6329);
#6325 = LINE('',#6326,#6327);
#6326 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6327 = VECTOR('',#6328,1.);
#6328 = DIRECTION('',(-1.,0.E+000));
#6329 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6330 = PCURVE('',#83,#6331);
#6331 = DEFINITIONAL_REPRESENTATION('',(#6332),#6336);
#6332 = CIRCLE('',#6333,1.59999934);
#6333 = AXIS2_PLACEMENT_2D('',#6334,#6335);
#6334 = CARTESIAN_POINT('',(123.39998688,4.99998746));
#6335 = DIRECTION('',(1.,0.E+000));
#6336 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6337 = ORIENTED_EDGE('',*,*,#6286,.F.);
#6338 = ORIENTED_EDGE('',*,*,#6339,.F.);
#6339 = EDGE_CURVE('',#6287,#6287,#6340,.T.);
#6340 = SURFACE_CURVE('',#6341,(#6346,#6353),.PCURVE_S1.);
#6341 = CIRCLE('',#6342,1.59999934);
#6342 = AXIS2_PLACEMENT_3D('',#6343,#6344,#6345);
#6343 = CARTESIAN_POINT('',(123.39998688,-6.6000122,0.E+000));
#6344 = DIRECTION('',(0.E+000,0.E+000,1.));
#6345 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6346 = PCURVE('',#6297,#6347);
#6347 = DEFINITIONAL_REPRESENTATION('',(#6348),#6352);
#6348 = LINE('',#6349,#6350);
#6349 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6350 = VECTOR('',#6351,1.);
#6351 = DIRECTION('',(-1.,0.E+000));
#6352 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6353 = PCURVE('',#137,#6354);
#6354 = DEFINITIONAL_REPRESENTATION('',(#6355),#6359);
#6355 = CIRCLE('',#6356,1.59999934);
#6356 = AXIS2_PLACEMENT_2D('',#6357,#6358);
#6357 = CARTESIAN_POINT('',(123.39998688,4.99998746));
#6358 = DIRECTION('',(1.,0.E+000));
#6359 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6360 = ADVANCED_FACE('',(#6361),#6375,.T.);
#6361 = FACE_BOUND('',#6362,.F.);
#6362 = EDGE_LOOP('',(#6363,#6393,#6415,#6416));
#6363 = ORIENTED_EDGE('',*,*,#6364,.T.);
#6364 = EDGE_CURVE('',#6365,#6367,#6369,.T.);
#6365 = VERTEX_POINT('',#6366);
#6366 = CARTESIAN_POINT('',(113.09999732,-2.74999958,0.E+000));
#6367 = VERTEX_POINT('',#6368);
#6368 = CARTESIAN_POINT('',(113.09999732,-2.74999958,1.12192054));
#6369 = SEAM_CURVE('',#6370,(#6374,#6386),.PCURVE_S1.);
#6370 = LINE('',#6371,#6372);
#6371 = CARTESIAN_POINT('',(113.09999732,-2.74999958,0.E+000));
#6372 = VECTOR('',#6373,1.);
#6373 = DIRECTION('',(0.E+000,0.E+000,1.));
#6374 = PCURVE('',#6375,#6380);
#6375 = CYLINDRICAL_SURFACE('',#6376,1.59999934);
#6376 = AXIS2_PLACEMENT_3D('',#6377,#6378,#6379);
#6377 = CARTESIAN_POINT('',(111.49999798,-2.74999958,0.E+000));
#6378 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#6379 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6380 = DEFINITIONAL_REPRESENTATION('',(#6381),#6385);
#6381 = LINE('',#6382,#6383);
#6382 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6383 = VECTOR('',#6384,1.);
#6384 = DIRECTION('',(-0.E+000,-1.));
#6385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6386 = PCURVE('',#6375,#6387);
#6387 = DEFINITIONAL_REPRESENTATION('',(#6388),#6392);
#6388 = LINE('',#6389,#6390);
#6389 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6390 = VECTOR('',#6391,1.);
#6391 = DIRECTION('',(-0.E+000,-1.));
#6392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6393 = ORIENTED_EDGE('',*,*,#6394,.T.);
#6394 = EDGE_CURVE('',#6367,#6367,#6395,.T.);
#6395 = SURFACE_CURVE('',#6396,(#6401,#6408),.PCURVE_S1.);
#6396 = CIRCLE('',#6397,1.59999934);
#6397 = AXIS2_PLACEMENT_3D('',#6398,#6399,#6400);
#6398 = CARTESIAN_POINT('',(111.49999798,-2.74999958,1.12192054));
#6399 = DIRECTION('',(0.E+000,0.E+000,1.));
#6400 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6401 = PCURVE('',#6375,#6402);
#6402 = DEFINITIONAL_REPRESENTATION('',(#6403),#6407);
#6403 = LINE('',#6404,#6405);
#6404 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6405 = VECTOR('',#6406,1.);
#6406 = DIRECTION('',(-1.,0.E+000));
#6407 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6408 = PCURVE('',#83,#6409);
#6409 = DEFINITIONAL_REPRESENTATION('',(#6410),#6414);
#6410 = CIRCLE('',#6411,1.59999934);
#6411 = AXIS2_PLACEMENT_2D('',#6412,#6413);
#6412 = CARTESIAN_POINT('',(111.49999798,8.85000008));
#6413 = DIRECTION('',(1.,0.E+000));
#6414 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6415 = ORIENTED_EDGE('',*,*,#6364,.F.);
#6416 = ORIENTED_EDGE('',*,*,#6417,.F.);
#6417 = EDGE_CURVE('',#6365,#6365,#6418,.T.);
#6418 = SURFACE_CURVE('',#6419,(#6424,#6431),.PCURVE_S1.);
#6419 = CIRCLE('',#6420,1.59999934);
#6420 = AXIS2_PLACEMENT_3D('',#6421,#6422,#6423);
#6421 = CARTESIAN_POINT('',(111.49999798,-2.74999958,0.E+000));
#6422 = DIRECTION('',(0.E+000,0.E+000,1.));
#6423 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6424 = PCURVE('',#6375,#6425);
#6425 = DEFINITIONAL_REPRESENTATION('',(#6426),#6430);
#6426 = LINE('',#6427,#6428);
#6427 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6428 = VECTOR('',#6429,1.);
#6429 = DIRECTION('',(-1.,0.E+000));
#6430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6431 = PCURVE('',#137,#6432);
#6432 = DEFINITIONAL_REPRESENTATION('',(#6433),#6437);
#6433 = CIRCLE('',#6434,1.59999934);
#6434 = AXIS2_PLACEMENT_2D('',#6435,#6436);
#6435 = CARTESIAN_POINT('',(111.49999798,8.85000008));
#6436 = DIRECTION('',(1.,0.E+000));
#6437 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6438 = ADVANCED_FACE('',(#6439),#6453,.T.);
#6439 = FACE_BOUND('',#6440,.F.);
#6440 = EDGE_LOOP('',(#6441,#6471,#6493,#6494));
#6441 = ORIENTED_EDGE('',*,*,#6442,.T.);
#6442 = EDGE_CURVE('',#6443,#6445,#6447,.T.);
#6443 = VERTEX_POINT('',#6444);
#6444 = CARTESIAN_POINT('',(103.2499967,4.14999932,0.E+000));
#6445 = VERTEX_POINT('',#6446);
#6446 = CARTESIAN_POINT('',(103.2499967,4.14999932,1.12192054));
#6447 = SEAM_CURVE('',#6448,(#6452,#6464),.PCURVE_S1.);
#6448 = LINE('',#6449,#6450);
#6449 = CARTESIAN_POINT('',(103.2499967,4.14999932,0.E+000));
#6450 = VECTOR('',#6451,1.);
#6451 = DIRECTION('',(0.E+000,0.E+000,1.));
#6452 = PCURVE('',#6453,#6458);
#6453 = CYLINDRICAL_SURFACE('',#6454,3.24999858);
#6454 = AXIS2_PLACEMENT_3D('',#6455,#6456,#6457);
#6455 = CARTESIAN_POINT('',(99.99999812,4.14999932,0.E+000));
#6456 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#6457 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6458 = DEFINITIONAL_REPRESENTATION('',(#6459),#6463);
#6459 = LINE('',#6460,#6461);
#6460 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6461 = VECTOR('',#6462,1.);
#6462 = DIRECTION('',(-0.E+000,-1.));
#6463 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6464 = PCURVE('',#6453,#6465);
#6465 = DEFINITIONAL_REPRESENTATION('',(#6466),#6470);
#6466 = LINE('',#6467,#6468);
#6467 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6468 = VECTOR('',#6469,1.);
#6469 = DIRECTION('',(-0.E+000,-1.));
#6470 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6471 = ORIENTED_EDGE('',*,*,#6472,.T.);
#6472 = EDGE_CURVE('',#6445,#6445,#6473,.T.);
#6473 = SURFACE_CURVE('',#6474,(#6479,#6486),.PCURVE_S1.);
#6474 = CIRCLE('',#6475,3.24999858);
#6475 = AXIS2_PLACEMENT_3D('',#6476,#6477,#6478);
#6476 = CARTESIAN_POINT('',(99.99999812,4.14999932,1.12192054));
#6477 = DIRECTION('',(0.E+000,0.E+000,1.));
#6478 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6479 = PCURVE('',#6453,#6480);
#6480 = DEFINITIONAL_REPRESENTATION('',(#6481),#6485);
#6481 = LINE('',#6482,#6483);
#6482 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6483 = VECTOR('',#6484,1.);
#6484 = DIRECTION('',(-1.,0.E+000));
#6485 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6486 = PCURVE('',#83,#6487);
#6487 = DEFINITIONAL_REPRESENTATION('',(#6488),#6492);
#6488 = CIRCLE('',#6489,3.24999858);
#6489 = AXIS2_PLACEMENT_2D('',#6490,#6491);
#6490 = CARTESIAN_POINT('',(99.99999812,15.74999898));
#6491 = DIRECTION('',(1.,0.E+000));
#6492 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6493 = ORIENTED_EDGE('',*,*,#6442,.F.);
#6494 = ORIENTED_EDGE('',*,*,#6495,.F.);
#6495 = EDGE_CURVE('',#6443,#6443,#6496,.T.);
#6496 = SURFACE_CURVE('',#6497,(#6502,#6509),.PCURVE_S1.);
#6497 = CIRCLE('',#6498,3.24999858);
#6498 = AXIS2_PLACEMENT_3D('',#6499,#6500,#6501);
#6499 = CARTESIAN_POINT('',(99.99999812,4.14999932,0.E+000));
#6500 = DIRECTION('',(0.E+000,0.E+000,1.));
#6501 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6502 = PCURVE('',#6453,#6503);
#6503 = DEFINITIONAL_REPRESENTATION('',(#6504),#6508);
#6504 = LINE('',#6505,#6506);
#6505 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6506 = VECTOR('',#6507,1.);
#6507 = DIRECTION('',(-1.,0.E+000));
#6508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6509 = PCURVE('',#137,#6510);
#6510 = DEFINITIONAL_REPRESENTATION('',(#6511),#6515);
#6511 = CIRCLE('',#6512,3.24999858);
#6512 = AXIS2_PLACEMENT_2D('',#6513,#6514);
#6513 = CARTESIAN_POINT('',(99.99999812,15.74999898));
#6514 = DIRECTION('',(1.,0.E+000));
#6515 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6516 = ADVANCED_FACE('',(#6517),#6531,.T.);
#6517 = FACE_BOUND('',#6518,.F.);
#6518 = EDGE_LOOP('',(#6519,#6549,#6571,#6572));
#6519 = ORIENTED_EDGE('',*,*,#6520,.T.);
#6520 = EDGE_CURVE('',#6521,#6523,#6525,.T.);
#6521 = VERTEX_POINT('',#6522);
#6522 = CARTESIAN_POINT('',(88.74999776,4.14999932,0.E+000));
#6523 = VERTEX_POINT('',#6524);
#6524 = CARTESIAN_POINT('',(88.74999776,4.14999932,1.12192054));
#6525 = SEAM_CURVE('',#6526,(#6530,#6542),.PCURVE_S1.);
#6526 = LINE('',#6527,#6528);
#6527 = CARTESIAN_POINT('',(88.74999776,4.14999932,0.E+000));
#6528 = VECTOR('',#6529,1.);
#6529 = DIRECTION('',(0.E+000,0.E+000,1.));
#6530 = PCURVE('',#6531,#6536);
#6531 = CYLINDRICAL_SURFACE('',#6532,3.24999858);
#6532 = AXIS2_PLACEMENT_3D('',#6533,#6534,#6535);
#6533 = CARTESIAN_POINT('',(85.49999918,4.14999932,0.E+000));
#6534 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#6535 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6536 = DEFINITIONAL_REPRESENTATION('',(#6537),#6541);
#6537 = LINE('',#6538,#6539);
#6538 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6539 = VECTOR('',#6540,1.);
#6540 = DIRECTION('',(-0.E+000,-1.));
#6541 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6542 = PCURVE('',#6531,#6543);
#6543 = DEFINITIONAL_REPRESENTATION('',(#6544),#6548);
#6544 = LINE('',#6545,#6546);
#6545 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6546 = VECTOR('',#6547,1.);
#6547 = DIRECTION('',(-0.E+000,-1.));
#6548 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6549 = ORIENTED_EDGE('',*,*,#6550,.T.);
#6550 = EDGE_CURVE('',#6523,#6523,#6551,.T.);
#6551 = SURFACE_CURVE('',#6552,(#6557,#6564),.PCURVE_S1.);
#6552 = CIRCLE('',#6553,3.24999858);
#6553 = AXIS2_PLACEMENT_3D('',#6554,#6555,#6556);
#6554 = CARTESIAN_POINT('',(85.49999918,4.14999932,1.12192054));
#6555 = DIRECTION('',(0.E+000,0.E+000,1.));
#6556 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6557 = PCURVE('',#6531,#6558);
#6558 = DEFINITIONAL_REPRESENTATION('',(#6559),#6563);
#6559 = LINE('',#6560,#6561);
#6560 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6561 = VECTOR('',#6562,1.);
#6562 = DIRECTION('',(-1.,0.E+000));
#6563 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6564 = PCURVE('',#83,#6565);
#6565 = DEFINITIONAL_REPRESENTATION('',(#6566),#6570);
#6566 = CIRCLE('',#6567,3.24999858);
#6567 = AXIS2_PLACEMENT_2D('',#6568,#6569);
#6568 = CARTESIAN_POINT('',(85.49999918,15.74999898));
#6569 = DIRECTION('',(1.,0.E+000));
#6570 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6571 = ORIENTED_EDGE('',*,*,#6520,.F.);
#6572 = ORIENTED_EDGE('',*,*,#6573,.F.);
#6573 = EDGE_CURVE('',#6521,#6521,#6574,.T.);
#6574 = SURFACE_CURVE('',#6575,(#6580,#6587),.PCURVE_S1.);
#6575 = CIRCLE('',#6576,3.24999858);
#6576 = AXIS2_PLACEMENT_3D('',#6577,#6578,#6579);
#6577 = CARTESIAN_POINT('',(85.49999918,4.14999932,0.E+000));
#6578 = DIRECTION('',(0.E+000,0.E+000,1.));
#6579 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6580 = PCURVE('',#6531,#6581);
#6581 = DEFINITIONAL_REPRESENTATION('',(#6582),#6586);
#6582 = LINE('',#6583,#6584);
#6583 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6584 = VECTOR('',#6585,1.);
#6585 = DIRECTION('',(-1.,0.E+000));
#6586 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6587 = PCURVE('',#137,#6588);
#6588 = DEFINITIONAL_REPRESENTATION('',(#6589),#6593);
#6589 = CIRCLE('',#6590,3.24999858);
#6590 = AXIS2_PLACEMENT_2D('',#6591,#6592);
#6591 = CARTESIAN_POINT('',(85.49999918,15.74999898));
#6592 = DIRECTION('',(1.,0.E+000));
#6593 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6594 = ADVANCED_FACE('',(#6595),#6609,.T.);
#6595 = FACE_BOUND('',#6596,.F.);
#6596 = EDGE_LOOP('',(#6597,#6627,#6649,#6650));
#6597 = ORIENTED_EDGE('',*,*,#6598,.T.);
#6598 = EDGE_CURVE('',#6599,#6601,#6603,.T.);
#6599 = VERTEX_POINT('',#6600);
#6600 = CARTESIAN_POINT('',(113.09999732,1.30999992,0.E+000));
#6601 = VERTEX_POINT('',#6602);
#6602 = CARTESIAN_POINT('',(113.09999732,1.30999992,1.12192054));
#6603 = SEAM_CURVE('',#6604,(#6608,#6620),.PCURVE_S1.);
#6604 = LINE('',#6605,#6606);
#6605 = CARTESIAN_POINT('',(113.09999732,1.30999992,0.E+000));
#6606 = VECTOR('',#6607,1.);
#6607 = DIRECTION('',(0.E+000,0.E+000,1.));
#6608 = PCURVE('',#6609,#6614);
#6609 = CYLINDRICAL_SURFACE('',#6610,1.59999934);
#6610 = AXIS2_PLACEMENT_3D('',#6611,#6612,#6613);
#6611 = CARTESIAN_POINT('',(111.49999798,1.30999992,0.E+000));
#6612 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#6613 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6614 = DEFINITIONAL_REPRESENTATION('',(#6615),#6619);
#6615 = LINE('',#6616,#6617);
#6616 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6617 = VECTOR('',#6618,1.);
#6618 = DIRECTION('',(-0.E+000,-1.));
#6619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6620 = PCURVE('',#6609,#6621);
#6621 = DEFINITIONAL_REPRESENTATION('',(#6622),#6626);
#6622 = LINE('',#6623,#6624);
#6623 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6624 = VECTOR('',#6625,1.);
#6625 = DIRECTION('',(-0.E+000,-1.));
#6626 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6627 = ORIENTED_EDGE('',*,*,#6628,.T.);
#6628 = EDGE_CURVE('',#6601,#6601,#6629,.T.);
#6629 = SURFACE_CURVE('',#6630,(#6635,#6642),.PCURVE_S1.);
#6630 = CIRCLE('',#6631,1.59999934);
#6631 = AXIS2_PLACEMENT_3D('',#6632,#6633,#6634);
#6632 = CARTESIAN_POINT('',(111.49999798,1.30999992,1.12192054));
#6633 = DIRECTION('',(0.E+000,0.E+000,1.));
#6634 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6635 = PCURVE('',#6609,#6636);
#6636 = DEFINITIONAL_REPRESENTATION('',(#6637),#6641);
#6637 = LINE('',#6638,#6639);
#6638 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6639 = VECTOR('',#6640,1.);
#6640 = DIRECTION('',(-1.,0.E+000));
#6641 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6642 = PCURVE('',#83,#6643);
#6643 = DEFINITIONAL_REPRESENTATION('',(#6644),#6648);
#6644 = CIRCLE('',#6645,1.59999934);
#6645 = AXIS2_PLACEMENT_2D('',#6646,#6647);
#6646 = CARTESIAN_POINT('',(111.49999798,12.90999958));
#6647 = DIRECTION('',(1.,0.E+000));
#6648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6649 = ORIENTED_EDGE('',*,*,#6598,.F.);
#6650 = ORIENTED_EDGE('',*,*,#6651,.F.);
#6651 = EDGE_CURVE('',#6599,#6599,#6652,.T.);
#6652 = SURFACE_CURVE('',#6653,(#6658,#6665),.PCURVE_S1.);
#6653 = CIRCLE('',#6654,1.59999934);
#6654 = AXIS2_PLACEMENT_3D('',#6655,#6656,#6657);
#6655 = CARTESIAN_POINT('',(111.49999798,1.30999992,0.E+000));
#6656 = DIRECTION('',(0.E+000,0.E+000,1.));
#6657 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6658 = PCURVE('',#6609,#6659);
#6659 = DEFINITIONAL_REPRESENTATION('',(#6660),#6664);
#6660 = LINE('',#6661,#6662);
#6661 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6662 = VECTOR('',#6663,1.);
#6663 = DIRECTION('',(-1.,0.E+000));
#6664 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6665 = PCURVE('',#137,#6666);
#6666 = DEFINITIONAL_REPRESENTATION('',(#6667),#6671);
#6667 = CIRCLE('',#6668,1.59999934);
#6668 = AXIS2_PLACEMENT_2D('',#6669,#6670);
#6669 = CARTESIAN_POINT('',(111.49999798,12.90999958));
#6670 = DIRECTION('',(1.,0.E+000));
#6671 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6672 = ADVANCED_FACE('',(#6673),#6687,.T.);
#6673 = FACE_BOUND('',#6674,.F.);
#6674 = EDGE_LOOP('',(#6675,#6705,#6727,#6728));
#6675 = ORIENTED_EDGE('',*,*,#6676,.T.);
#6676 = EDGE_CURVE('',#6677,#6679,#6681,.T.);
#6677 = VERTEX_POINT('',#6678);
#6678 = CARTESIAN_POINT('',(124.99998622,6.5999868,0.E+000));
#6679 = VERTEX_POINT('',#6680);
#6680 = CARTESIAN_POINT('',(124.99998622,6.5999868,1.12192054));
#6681 = SEAM_CURVE('',#6682,(#6686,#6698),.PCURVE_S1.);
#6682 = LINE('',#6683,#6684);
#6683 = CARTESIAN_POINT('',(124.99998622,6.5999868,0.E+000));
#6684 = VECTOR('',#6685,1.);
#6685 = DIRECTION('',(0.E+000,0.E+000,1.));
#6686 = PCURVE('',#6687,#6692);
#6687 = CYLINDRICAL_SURFACE('',#6688,1.59999934);
#6688 = AXIS2_PLACEMENT_3D('',#6689,#6690,#6691);
#6689 = CARTESIAN_POINT('',(123.39998688,6.5999868,0.E+000));
#6690 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#6691 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6692 = DEFINITIONAL_REPRESENTATION('',(#6693),#6697);
#6693 = LINE('',#6694,#6695);
#6694 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6695 = VECTOR('',#6696,1.);
#6696 = DIRECTION('',(-0.E+000,-1.));
#6697 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6698 = PCURVE('',#6687,#6699);
#6699 = DEFINITIONAL_REPRESENTATION('',(#6700),#6704);
#6700 = LINE('',#6701,#6702);
#6701 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#6702 = VECTOR('',#6703,1.);
#6703 = DIRECTION('',(-0.E+000,-1.));
#6704 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6705 = ORIENTED_EDGE('',*,*,#6706,.T.);
#6706 = EDGE_CURVE('',#6679,#6679,#6707,.T.);
#6707 = SURFACE_CURVE('',#6708,(#6713,#6720),.PCURVE_S1.);
#6708 = CIRCLE('',#6709,1.59999934);
#6709 = AXIS2_PLACEMENT_3D('',#6710,#6711,#6712);
#6710 = CARTESIAN_POINT('',(123.39998688,6.5999868,1.12192054));
#6711 = DIRECTION('',(0.E+000,0.E+000,1.));
#6712 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6713 = PCURVE('',#6687,#6714);
#6714 = DEFINITIONAL_REPRESENTATION('',(#6715),#6719);
#6715 = LINE('',#6716,#6717);
#6716 = CARTESIAN_POINT('',(-0.E+000,-1.12192054));
#6717 = VECTOR('',#6718,1.);
#6718 = DIRECTION('',(-1.,0.E+000));
#6719 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6720 = PCURVE('',#83,#6721);
#6721 = DEFINITIONAL_REPRESENTATION('',(#6722),#6726);
#6722 = CIRCLE('',#6723,1.59999934);
#6723 = AXIS2_PLACEMENT_2D('',#6724,#6725);
#6724 = CARTESIAN_POINT('',(123.39998688,18.19998646));
#6725 = DIRECTION('',(1.,0.E+000));
#6726 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6727 = ORIENTED_EDGE('',*,*,#6676,.F.);
#6728 = ORIENTED_EDGE('',*,*,#6729,.F.);
#6729 = EDGE_CURVE('',#6677,#6677,#6730,.T.);
#6730 = SURFACE_CURVE('',#6731,(#6736,#6743),.PCURVE_S1.);
#6731 = CIRCLE('',#6732,1.59999934);
#6732 = AXIS2_PLACEMENT_3D('',#6733,#6734,#6735);
#6733 = CARTESIAN_POINT('',(123.39998688,6.5999868,0.E+000));
#6734 = DIRECTION('',(0.E+000,0.E+000,1.));
#6735 = DIRECTION('',(1.,0.E+000,-0.E+000));
#6736 = PCURVE('',#6687,#6737);
#6737 = DEFINITIONAL_REPRESENTATION('',(#6738),#6742);
#6738 = LINE('',#6739,#6740);
#6739 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#6740 = VECTOR('',#6741,1.);
#6741 = DIRECTION('',(-1.,0.E+000));
#6742 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6743 = PCURVE('',#137,#6744);
#6744 = DEFINITIONAL_REPRESENTATION('',(#6745),#6749);
#6745 = CIRCLE('',#6746,1.59999934);
#6746 = AXIS2_PLACEMENT_2D('',#6747,#6748);
#6747 = CARTESIAN_POINT('',(123.39998688,18.19998646));
#6748 = DIRECTION('',(1.,0.E+000));
#6749 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6750 = ADVANCED_FACE('',(#6751,#6757,#6760,#6798,#6836,#6839,#6842,
    #6845,#6848,#6851,#6854,#6857,#6860,#6863,#6866),#137,.F.);
#6751 = FACE_BOUND('',#6752,.T.);
#6752 = EDGE_LOOP('',(#6753,#6754,#6755,#6756));
#6753 = ORIENTED_EDGE('',*,*,#123,.T.);
#6754 = ORIENTED_EDGE('',*,*,#204,.T.);
#6755 = ORIENTED_EDGE('',*,*,#275,.T.);
#6756 = ORIENTED_EDGE('',*,*,#322,.T.);
#6757 = FACE_BOUND('',#6758,.F.);
#6758 = EDGE_LOOP('',(#6759));
#6759 = ORIENTED_EDGE('',*,*,#399,.T.);
#6760 = FACE_BOUND('',#6761,.F.);
#6761 = EDGE_LOOP('',(#6762,#6763,#6764,#6765,#6766,#6767,#6768,#6769,
    #6770,#6771,#6772,#6773,#6774,#6775,#6776,#6777,#6778,#6779,#6780,
    #6781,#6782,#6783,#6784,#6785,#6786,#6787,#6788,#6789,#6790,#6791,
    #6792,#6793,#6794,#6795,#6796,#6797));
#6762 = ORIENTED_EDGE('',*,*,#510,.T.);
#6763 = ORIENTED_EDGE('',*,*,#586,.T.);
#6764 = ORIENTED_EDGE('',*,*,#662,.T.);
#6765 = ORIENTED_EDGE('',*,*,#738,.T.);
#6766 = ORIENTED_EDGE('',*,*,#814,.T.);
#6767 = ORIENTED_EDGE('',*,*,#890,.T.);
#6768 = ORIENTED_EDGE('',*,*,#966,.T.);
#6769 = ORIENTED_EDGE('',*,*,#1042,.T.);
#6770 = ORIENTED_EDGE('',*,*,#1118,.T.);
#6771 = ORIENTED_EDGE('',*,*,#1194,.T.);
#6772 = ORIENTED_EDGE('',*,*,#1270,.T.);
#6773 = ORIENTED_EDGE('',*,*,#1346,.T.);
#6774 = ORIENTED_EDGE('',*,*,#1422,.T.);
#6775 = ORIENTED_EDGE('',*,*,#1498,.T.);
#6776 = ORIENTED_EDGE('',*,*,#1574,.T.);
#6777 = ORIENTED_EDGE('',*,*,#1650,.T.);
#6778 = ORIENTED_EDGE('',*,*,#1726,.T.);
#6779 = ORIENTED_EDGE('',*,*,#1802,.T.);
#6780 = ORIENTED_EDGE('',*,*,#1878,.T.);
#6781 = ORIENTED_EDGE('',*,*,#1954,.T.);
#6782 = ORIENTED_EDGE('',*,*,#2030,.T.);
#6783 = ORIENTED_EDGE('',*,*,#2106,.T.);
#6784 = ORIENTED_EDGE('',*,*,#2182,.T.);
#6785 = ORIENTED_EDGE('',*,*,#2258,.T.);
#6786 = ORIENTED_EDGE('',*,*,#2334,.T.);
#6787 = ORIENTED_EDGE('',*,*,#2410,.T.);
#6788 = ORIENTED_EDGE('',*,*,#2486,.T.);
#6789 = ORIENTED_EDGE('',*,*,#2562,.T.);
#6790 = ORIENTED_EDGE('',*,*,#2638,.T.);
#6791 = ORIENTED_EDGE('',*,*,#2714,.T.);
#6792 = ORIENTED_EDGE('',*,*,#2790,.T.);
#6793 = ORIENTED_EDGE('',*,*,#2866,.T.);
#6794 = ORIENTED_EDGE('',*,*,#2942,.T.);
#6795 = ORIENTED_EDGE('',*,*,#3018,.T.);
#6796 = ORIENTED_EDGE('',*,*,#3089,.T.);
#6797 = ORIENTED_EDGE('',*,*,#3136,.T.);
#6798 = FACE_BOUND('',#6799,.F.);
#6799 = EDGE_LOOP('',(#6800,#6801,#6802,#6803,#6804,#6805,#6806,#6807,
    #6808,#6809,#6810,#6811,#6812,#6813,#6814,#6815,#6816,#6817,#6818,
    #6819,#6820,#6821,#6822,#6823,#6824,#6825,#6826,#6827,#6828,#6829,
    #6830,#6831,#6832,#6833,#6834,#6835));
#6800 = ORIENTED_EDGE('',*,*,#3246,.T.);
#6801 = ORIENTED_EDGE('',*,*,#3322,.T.);
#6802 = ORIENTED_EDGE('',*,*,#3398,.T.);
#6803 = ORIENTED_EDGE('',*,*,#3474,.T.);
#6804 = ORIENTED_EDGE('',*,*,#3550,.T.);
#6805 = ORIENTED_EDGE('',*,*,#3626,.T.);
#6806 = ORIENTED_EDGE('',*,*,#3702,.T.);
#6807 = ORIENTED_EDGE('',*,*,#3778,.T.);
#6808 = ORIENTED_EDGE('',*,*,#3854,.T.);
#6809 = ORIENTED_EDGE('',*,*,#3930,.T.);
#6810 = ORIENTED_EDGE('',*,*,#4006,.T.);
#6811 = ORIENTED_EDGE('',*,*,#4082,.T.);
#6812 = ORIENTED_EDGE('',*,*,#4158,.T.);
#6813 = ORIENTED_EDGE('',*,*,#4234,.T.);
#6814 = ORIENTED_EDGE('',*,*,#4310,.T.);
#6815 = ORIENTED_EDGE('',*,*,#4386,.T.);
#6816 = ORIENTED_EDGE('',*,*,#4462,.T.);
#6817 = ORIENTED_EDGE('',*,*,#4538,.T.);
#6818 = ORIENTED_EDGE('',*,*,#4614,.T.);
#6819 = ORIENTED_EDGE('',*,*,#4690,.T.);
#6820 = ORIENTED_EDGE('',*,*,#4766,.T.);
#6821 = ORIENTED_EDGE('',*,*,#4842,.T.);
#6822 = ORIENTED_EDGE('',*,*,#4918,.T.);
#6823 = ORIENTED_EDGE('',*,*,#4994,.T.);
#6824 = ORIENTED_EDGE('',*,*,#5070,.T.);
#6825 = ORIENTED_EDGE('',*,*,#5146,.T.);
#6826 = ORIENTED_EDGE('',*,*,#5222,.T.);
#6827 = ORIENTED_EDGE('',*,*,#5298,.T.);
#6828 = ORIENTED_EDGE('',*,*,#5374,.T.);
#6829 = ORIENTED_EDGE('',*,*,#5450,.T.);
#6830 = ORIENTED_EDGE('',*,*,#5526,.T.);
#6831 = ORIENTED_EDGE('',*,*,#5602,.T.);
#6832 = ORIENTED_EDGE('',*,*,#5678,.T.);
#6833 = ORIENTED_EDGE('',*,*,#5754,.T.);
#6834 = ORIENTED_EDGE('',*,*,#5825,.T.);
#6835 = ORIENTED_EDGE('',*,*,#5872,.T.);
#6836 = FACE_BOUND('',#6837,.F.);
#6837 = EDGE_LOOP('',(#6838));
#6838 = ORIENTED_EDGE('',*,*,#5949,.T.);
#6839 = FACE_BOUND('',#6840,.F.);
#6840 = EDGE_LOOP('',(#6841));
#6841 = ORIENTED_EDGE('',*,*,#6027,.T.);
#6842 = FACE_BOUND('',#6843,.F.);
#6843 = EDGE_LOOP('',(#6844));
#6844 = ORIENTED_EDGE('',*,*,#6105,.T.);
#6845 = FACE_BOUND('',#6846,.F.);
#6846 = EDGE_LOOP('',(#6847));
#6847 = ORIENTED_EDGE('',*,*,#6183,.T.);
#6848 = FACE_BOUND('',#6849,.F.);
#6849 = EDGE_LOOP('',(#6850));
#6850 = ORIENTED_EDGE('',*,*,#6261,.T.);
#6851 = FACE_BOUND('',#6852,.F.);
#6852 = EDGE_LOOP('',(#6853));
#6853 = ORIENTED_EDGE('',*,*,#6339,.T.);
#6854 = FACE_BOUND('',#6855,.F.);
#6855 = EDGE_LOOP('',(#6856));
#6856 = ORIENTED_EDGE('',*,*,#6417,.T.);
#6857 = FACE_BOUND('',#6858,.F.);
#6858 = EDGE_LOOP('',(#6859));
#6859 = ORIENTED_EDGE('',*,*,#6495,.T.);
#6860 = FACE_BOUND('',#6861,.F.);
#6861 = EDGE_LOOP('',(#6862));
#6862 = ORIENTED_EDGE('',*,*,#6573,.T.);
#6863 = FACE_BOUND('',#6864,.F.);
#6864 = EDGE_LOOP('',(#6865));
#6865 = ORIENTED_EDGE('',*,*,#6651,.T.);
#6866 = FACE_BOUND('',#6867,.F.);
#6867 = EDGE_LOOP('',(#6868));
#6868 = ORIENTED_EDGE('',*,*,#6729,.T.);
#6869 = ADVANCED_FACE('',(#6870,#6876,#6879,#6917,#6955,#6958,#6961,
    #6964,#6967,#6970,#6973,#6976,#6979,#6982,#6985),#83,.T.);
#6870 = FACE_BOUND('',#6871,.F.);
#6871 = EDGE_LOOP('',(#6872,#6873,#6874,#6875));
#6872 = ORIENTED_EDGE('',*,*,#67,.T.);
#6873 = ORIENTED_EDGE('',*,*,#153,.T.);
#6874 = ORIENTED_EDGE('',*,*,#229,.T.);
#6875 = ORIENTED_EDGE('',*,*,#300,.T.);
#6876 = FACE_BOUND('',#6877,.T.);
#6877 = EDGE_LOOP('',(#6878));
#6878 = ORIENTED_EDGE('',*,*,#376,.T.);
#6879 = FACE_BOUND('',#6880,.T.);
#6880 = EDGE_LOOP('',(#6881,#6882,#6883,#6884,#6885,#6886,#6887,#6888,
    #6889,#6890,#6891,#6892,#6893,#6894,#6895,#6896,#6897,#6898,#6899,
    #6900,#6901,#6902,#6903,#6904,#6905,#6906,#6907,#6908,#6909,#6910,
    #6911,#6912,#6913,#6914,#6915,#6916));
#6881 = ORIENTED_EDGE('',*,*,#459,.T.);
#6882 = ORIENTED_EDGE('',*,*,#535,.T.);
#6883 = ORIENTED_EDGE('',*,*,#611,.T.);
#6884 = ORIENTED_EDGE('',*,*,#687,.T.);
#6885 = ORIENTED_EDGE('',*,*,#763,.T.);
#6886 = ORIENTED_EDGE('',*,*,#839,.T.);
#6887 = ORIENTED_EDGE('',*,*,#915,.T.);
#6888 = ORIENTED_EDGE('',*,*,#991,.T.);
#6889 = ORIENTED_EDGE('',*,*,#1067,.T.);
#6890 = ORIENTED_EDGE('',*,*,#1143,.T.);
#6891 = ORIENTED_EDGE('',*,*,#1219,.T.);
#6892 = ORIENTED_EDGE('',*,*,#1295,.T.);
#6893 = ORIENTED_EDGE('',*,*,#1371,.T.);
#6894 = ORIENTED_EDGE('',*,*,#1447,.T.);
#6895 = ORIENTED_EDGE('',*,*,#1523,.T.);
#6896 = ORIENTED_EDGE('',*,*,#1599,.T.);
#6897 = ORIENTED_EDGE('',*,*,#1675,.T.);
#6898 = ORIENTED_EDGE('',*,*,#1751,.T.);
#6899 = ORIENTED_EDGE('',*,*,#1827,.T.);
#6900 = ORIENTED_EDGE('',*,*,#1903,.T.);
#6901 = ORIENTED_EDGE('',*,*,#1979,.T.);
#6902 = ORIENTED_EDGE('',*,*,#2055,.T.);
#6903 = ORIENTED_EDGE('',*,*,#2131,.T.);
#6904 = ORIENTED_EDGE('',*,*,#2207,.T.);
#6905 = ORIENTED_EDGE('',*,*,#2283,.T.);
#6906 = ORIENTED_EDGE('',*,*,#2359,.T.);
#6907 = ORIENTED_EDGE('',*,*,#2435,.T.);
#6908 = ORIENTED_EDGE('',*,*,#2511,.T.);
#6909 = ORIENTED_EDGE('',*,*,#2587,.T.);
#6910 = ORIENTED_EDGE('',*,*,#2663,.T.);
#6911 = ORIENTED_EDGE('',*,*,#2739,.T.);
#6912 = ORIENTED_EDGE('',*,*,#2815,.T.);
#6913 = ORIENTED_EDGE('',*,*,#2891,.T.);
#6914 = ORIENTED_EDGE('',*,*,#2967,.T.);
#6915 = ORIENTED_EDGE('',*,*,#3043,.T.);
#6916 = ORIENTED_EDGE('',*,*,#3114,.T.);
#6917 = FACE_BOUND('',#6918,.T.);
#6918 = EDGE_LOOP('',(#6919,#6920,#6921,#6922,#6923,#6924,#6925,#6926,
    #6927,#6928,#6929,#6930,#6931,#6932,#6933,#6934,#6935,#6936,#6937,
    #6938,#6939,#6940,#6941,#6942,#6943,#6944,#6945,#6946,#6947,#6948,
    #6949,#6950,#6951,#6952,#6953,#6954));
#6919 = ORIENTED_EDGE('',*,*,#3195,.T.);
#6920 = ORIENTED_EDGE('',*,*,#3271,.T.);
#6921 = ORIENTED_EDGE('',*,*,#3347,.T.);
#6922 = ORIENTED_EDGE('',*,*,#3423,.T.);
#6923 = ORIENTED_EDGE('',*,*,#3499,.T.);
#6924 = ORIENTED_EDGE('',*,*,#3575,.T.);
#6925 = ORIENTED_EDGE('',*,*,#3651,.T.);
#6926 = ORIENTED_EDGE('',*,*,#3727,.T.);
#6927 = ORIENTED_EDGE('',*,*,#3803,.T.);
#6928 = ORIENTED_EDGE('',*,*,#3879,.T.);
#6929 = ORIENTED_EDGE('',*,*,#3955,.T.);
#6930 = ORIENTED_EDGE('',*,*,#4031,.T.);
#6931 = ORIENTED_EDGE('',*,*,#4107,.T.);
#6932 = ORIENTED_EDGE('',*,*,#4183,.T.);
#6933 = ORIENTED_EDGE('',*,*,#4259,.T.);
#6934 = ORIENTED_EDGE('',*,*,#4335,.T.);
#6935 = ORIENTED_EDGE('',*,*,#4411,.T.);
#6936 = ORIENTED_EDGE('',*,*,#4487,.T.);
#6937 = ORIENTED_EDGE('',*,*,#4563,.T.);
#6938 = ORIENTED_EDGE('',*,*,#4639,.T.);
#6939 = ORIENTED_EDGE('',*,*,#4715,.T.);
#6940 = ORIENTED_EDGE('',*,*,#4791,.T.);
#6941 = ORIENTED_EDGE('',*,*,#4867,.T.);
#6942 = ORIENTED_EDGE('',*,*,#4943,.T.);
#6943 = ORIENTED_EDGE('',*,*,#5019,.T.);
#6944 = ORIENTED_EDGE('',*,*,#5095,.T.);
#6945 = ORIENTED_EDGE('',*,*,#5171,.T.);
#6946 = ORIENTED_EDGE('',*,*,#5247,.T.);
#6947 = ORIENTED_EDGE('',*,*,#5323,.T.);
#6948 = ORIENTED_EDGE('',*,*,#5399,.T.);
#6949 = ORIENTED_EDGE('',*,*,#5475,.T.);
#6950 = ORIENTED_EDGE('',*,*,#5551,.T.);
#6951 = ORIENTED_EDGE('',*,*,#5627,.T.);
#6952 = ORIENTED_EDGE('',*,*,#5703,.T.);
#6953 = ORIENTED_EDGE('',*,*,#5779,.T.);
#6954 = ORIENTED_EDGE('',*,*,#5850,.T.);
#6955 = FACE_BOUND('',#6956,.T.);
#6956 = EDGE_LOOP('',(#6957));
#6957 = ORIENTED_EDGE('',*,*,#5926,.T.);
#6958 = FACE_BOUND('',#6959,.T.);
#6959 = EDGE_LOOP('',(#6960));
#6960 = ORIENTED_EDGE('',*,*,#6004,.T.);
#6961 = FACE_BOUND('',#6962,.T.);
#6962 = EDGE_LOOP('',(#6963));
#6963 = ORIENTED_EDGE('',*,*,#6082,.T.);
#6964 = FACE_BOUND('',#6965,.T.);
#6965 = EDGE_LOOP('',(#6966));
#6966 = ORIENTED_EDGE('',*,*,#6160,.T.);
#6967 = FACE_BOUND('',#6968,.T.);
#6968 = EDGE_LOOP('',(#6969));
#6969 = ORIENTED_EDGE('',*,*,#6238,.T.);
#6970 = FACE_BOUND('',#6971,.T.);
#6971 = EDGE_LOOP('',(#6972));
#6972 = ORIENTED_EDGE('',*,*,#6316,.T.);
#6973 = FACE_BOUND('',#6974,.T.);
#6974 = EDGE_LOOP('',(#6975));
#6975 = ORIENTED_EDGE('',*,*,#6394,.T.);
#6976 = FACE_BOUND('',#6977,.T.);
#6977 = EDGE_LOOP('',(#6978));
#6978 = ORIENTED_EDGE('',*,*,#6472,.T.);
#6979 = FACE_BOUND('',#6980,.T.);
#6980 = EDGE_LOOP('',(#6981));
#6981 = ORIENTED_EDGE('',*,*,#6550,.T.);
#6982 = FACE_BOUND('',#6983,.T.);
#6983 = EDGE_LOOP('',(#6984));
#6984 = ORIENTED_EDGE('',*,*,#6628,.T.);
#6985 = FACE_BOUND('',#6986,.T.);
#6986 = EDGE_LOOP('',(#6987));
#6987 = ORIENTED_EDGE('',*,*,#6706,.T.);
#6988 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#6992)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#6989,#6990,#6991)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#6989 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6990 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#6991 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#6992 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#6989,
  'distance_accuracy_value','confusion accuracy');
#6993 = SHAPE_DEFINITION_REPRESENTATION(#6994,#25);
#6994 = PRODUCT_DEFINITION_SHAPE('','',#6995);
#6995 = PRODUCT_DEFINITION('design','',#6996,#6999);
#6996 = PRODUCT_DEFINITION_FORMATION('','',#6997);
#6997 = PRODUCT('Board','Board','',(#6998));
#6998 = PRODUCT_CONTEXT('',#2,'mechanical');
#6999 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#7000 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#7001,#7003);
#7001 = ( REPRESENTATION_RELATIONSHIP('','',#25,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#7002) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#7002 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#7003 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #7004);
#7004 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('6','=>[0:1:1:2]','',#5,#6995,$);
#7005 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#6997));
#7006 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #7007),#6988);
#7007 = STYLED_ITEM('color',(#7008),#26);
#7008 = PRESENTATION_STYLE_ASSIGNMENT((#7009,#7015));
#7009 = SURFACE_STYLE_USAGE(.BOTH.,#7010);
#7010 = SURFACE_SIDE_STYLE('',(#7011));
#7011 = SURFACE_STYLE_FILL_AREA(#7012);
#7012 = FILL_AREA_STYLE('',(#7013));
#7013 = FILL_AREA_STYLE_COLOUR('',#7014);
#7014 = COLOUR_RGB('',0.E+000,0.501960813999,0.E+000);
#7015 = CURVE_STYLE('',#7016,POSITIVE_LENGTH_MEASURE(0.1),#7014);
#7016 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
