ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2018-12-13T20:01:37',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 6.5','Open CASCADE 6.5'
  ,'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('PCB','PCB','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15),#19);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#13 = DIRECTION('',(0.E+000,0.E+000,1.));
#14 = DIRECTION('',(1.,0.E+000,-0.E+000));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.E+000,0.E+000,-1.64592));
#17 = DIRECTION('',(0.E+000,0.E+000,1.));
#18 = DIRECTION('',(1.,0.E+000,-0.E+000));
#19 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#23)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#20,#21,#22)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#20 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#21 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#22 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#23 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#20,
  'distance_accuracy_value','confusion accuracy');
#24 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#25 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#26),#524);
#26 = MANIFOLD_SOLID_BREP('',#27);
#27 = CLOSED_SHELL('',(#28,#148,#224,#295,#342,#420,#498,#511));
#28 = ADVANCED_FACE('',(#29),#43,.T.);
#29 = FACE_BOUND('',#30,.T.);
#30 = EDGE_LOOP('',(#31,#66,#94,#122));
#31 = ORIENTED_EDGE('',*,*,#32,.T.);
#32 = EDGE_CURVE('',#33,#35,#37,.T.);
#33 = VERTEX_POINT('',#34);
#34 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#35 = VERTEX_POINT('',#36);
#36 = CARTESIAN_POINT('',(0.E+000,0.E+000,1.64592));
#37 = SURFACE_CURVE('',#38,(#42,#54),.PCURVE_S1.);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.E+000,0.E+000,1.));
#42 = PCURVE('',#43,#48);
#43 = PLANE('',#44);
#44 = AXIS2_PLACEMENT_3D('',#45,#46,#47);
#45 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#46 = DIRECTION('',(-1.,0.E+000,0.E+000));
#47 = DIRECTION('',(0.E+000,1.,0.E+000));
#48 = DEFINITIONAL_REPRESENTATION('',(#49),#53);
#49 = LINE('',#50,#51);
#50 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#51 = VECTOR('',#52,1.);
#52 = DIRECTION('',(0.E+000,-1.));
#53 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#54 = PCURVE('',#55,#60);
#55 = PLANE('',#56);
#56 = AXIS2_PLACEMENT_3D('',#57,#58,#59);
#57 = CARTESIAN_POINT('',(157.79999936,0.E+000,0.E+000));
#58 = DIRECTION('',(0.E+000,-1.,0.E+000));
#59 = DIRECTION('',(-1.,0.E+000,0.E+000));
#60 = DEFINITIONAL_REPRESENTATION('',(#61),#65);
#61 = LINE('',#62,#63);
#62 = CARTESIAN_POINT('',(157.79999936,0.E+000));
#63 = VECTOR('',#64,1.);
#64 = DIRECTION('',(0.E+000,-1.));
#65 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#66 = ORIENTED_EDGE('',*,*,#67,.T.);
#67 = EDGE_CURVE('',#35,#68,#70,.T.);
#68 = VERTEX_POINT('',#69);
#69 = CARTESIAN_POINT('',(0.E+000,20.00000064,1.64592));
#70 = SURFACE_CURVE('',#71,(#75,#82),.PCURVE_S1.);
#71 = LINE('',#72,#73);
#72 = CARTESIAN_POINT('',(0.E+000,0.E+000,1.64592));
#73 = VECTOR('',#74,1.);
#74 = DIRECTION('',(0.E+000,1.,0.E+000));
#75 = PCURVE('',#43,#76);
#76 = DEFINITIONAL_REPRESENTATION('',(#77),#81);
#77 = LINE('',#78,#79);
#78 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#79 = VECTOR('',#80,1.);
#80 = DIRECTION('',(1.,0.E+000));
#81 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#82 = PCURVE('',#83,#88);
#83 = PLANE('',#84);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(0.E+000,0.E+000,1.64592));
#86 = DIRECTION('',(0.E+000,0.E+000,1.));
#87 = DIRECTION('',(1.,0.E+000,-0.E+000));
#88 = DEFINITIONAL_REPRESENTATION('',(#89),#93);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(0.E+000,1.));
#93 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#94 = ORIENTED_EDGE('',*,*,#95,.F.);
#95 = EDGE_CURVE('',#96,#68,#98,.T.);
#96 = VERTEX_POINT('',#97);
#97 = CARTESIAN_POINT('',(0.E+000,20.00000064,0.E+000));
#98 = SURFACE_CURVE('',#99,(#103,#110),.PCURVE_S1.);
#99 = LINE('',#100,#101);
#100 = CARTESIAN_POINT('',(0.E+000,20.00000064,0.E+000));
#101 = VECTOR('',#102,1.);
#102 = DIRECTION('',(0.E+000,0.E+000,1.));
#103 = PCURVE('',#43,#104);
#104 = DEFINITIONAL_REPRESENTATION('',(#105),#109);
#105 = LINE('',#106,#107);
#106 = CARTESIAN_POINT('',(20.00000064,0.E+000));
#107 = VECTOR('',#108,1.);
#108 = DIRECTION('',(0.E+000,-1.));
#109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#110 = PCURVE('',#111,#116);
#111 = PLANE('',#112);
#112 = AXIS2_PLACEMENT_3D('',#113,#114,#115);
#113 = CARTESIAN_POINT('',(0.E+000,20.00000064,0.E+000));
#114 = DIRECTION('',(0.E+000,1.,0.E+000));
#115 = DIRECTION('',(1.,0.E+000,0.E+000));
#116 = DEFINITIONAL_REPRESENTATION('',(#117),#121);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(0.E+000,-1.));
#121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#122 = ORIENTED_EDGE('',*,*,#123,.F.);
#123 = EDGE_CURVE('',#33,#96,#124,.T.);
#124 = SURFACE_CURVE('',#125,(#129,#136),.PCURVE_S1.);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(0.E+000,1.,0.E+000));
#129 = PCURVE('',#43,#130);
#130 = DEFINITIONAL_REPRESENTATION('',(#131),#135);
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(1.,0.E+000));
#135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#136 = PCURVE('',#137,#142);
#137 = PLANE('',#138);
#138 = AXIS2_PLACEMENT_3D('',#139,#140,#141);
#139 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#140 = DIRECTION('',(0.E+000,0.E+000,1.));
#141 = DIRECTION('',(1.,0.E+000,-0.E+000));
#142 = DEFINITIONAL_REPRESENTATION('',(#143),#147);
#143 = LINE('',#144,#145);
#144 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#145 = VECTOR('',#146,1.);
#146 = DIRECTION('',(0.E+000,1.));
#147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#148 = ADVANCED_FACE('',(#149),#111,.T.);
#149 = FACE_BOUND('',#150,.T.);
#150 = EDGE_LOOP('',(#151,#152,#175,#203));
#151 = ORIENTED_EDGE('',*,*,#95,.T.);
#152 = ORIENTED_EDGE('',*,*,#153,.T.);
#153 = EDGE_CURVE('',#68,#154,#156,.T.);
#154 = VERTEX_POINT('',#155);
#155 = CARTESIAN_POINT('',(157.79999936,20.00000064,1.64592));
#156 = SURFACE_CURVE('',#157,(#161,#168),.PCURVE_S1.);
#157 = LINE('',#158,#159);
#158 = CARTESIAN_POINT('',(0.E+000,20.00000064,1.64592));
#159 = VECTOR('',#160,1.);
#160 = DIRECTION('',(1.,0.E+000,0.E+000));
#161 = PCURVE('',#111,#162);
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(1.,0.E+000));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = PCURVE('',#83,#169);
#169 = DEFINITIONAL_REPRESENTATION('',(#170),#174);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(0.E+000,20.00000064));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(1.,0.E+000));
#174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#175 = ORIENTED_EDGE('',*,*,#176,.F.);
#176 = EDGE_CURVE('',#177,#154,#179,.T.);
#177 = VERTEX_POINT('',#178);
#178 = CARTESIAN_POINT('',(157.79999936,20.00000064,0.E+000));
#179 = SURFACE_CURVE('',#180,(#184,#191),.PCURVE_S1.);
#180 = LINE('',#181,#182);
#181 = CARTESIAN_POINT('',(157.79999936,20.00000064,0.E+000));
#182 = VECTOR('',#183,1.);
#183 = DIRECTION('',(0.E+000,0.E+000,1.));
#184 = PCURVE('',#111,#185);
#185 = DEFINITIONAL_REPRESENTATION('',(#186),#190);
#186 = LINE('',#187,#188);
#187 = CARTESIAN_POINT('',(157.79999936,0.E+000));
#188 = VECTOR('',#189,1.);
#189 = DIRECTION('',(0.E+000,-1.));
#190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#191 = PCURVE('',#192,#197);
#192 = PLANE('',#193);
#193 = AXIS2_PLACEMENT_3D('',#194,#195,#196);
#194 = CARTESIAN_POINT('',(157.79999936,20.00000064,0.E+000));
#195 = DIRECTION('',(1.,0.E+000,-0.E+000));
#196 = DIRECTION('',(0.E+000,-1.,0.E+000));
#197 = DEFINITIONAL_REPRESENTATION('',(#198),#202);
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(0.E+000,-1.));
#202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#203 = ORIENTED_EDGE('',*,*,#204,.F.);
#204 = EDGE_CURVE('',#96,#177,#205,.T.);
#205 = SURFACE_CURVE('',#206,(#210,#217),.PCURVE_S1.);
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(0.E+000,20.00000064,0.E+000));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(1.,0.E+000,0.E+000));
#210 = PCURVE('',#111,#211);
#211 = DEFINITIONAL_REPRESENTATION('',(#212),#216);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(1.,0.E+000));
#216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#217 = PCURVE('',#137,#218);
#218 = DEFINITIONAL_REPRESENTATION('',(#219),#223);
#219 = LINE('',#220,#221);
#220 = CARTESIAN_POINT('',(0.E+000,20.00000064));
#221 = VECTOR('',#222,1.);
#222 = DIRECTION('',(1.,0.E+000));
#223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#224 = ADVANCED_FACE('',(#225),#192,.T.);
#225 = FACE_BOUND('',#226,.T.);
#226 = EDGE_LOOP('',(#227,#228,#251,#274));
#227 = ORIENTED_EDGE('',*,*,#176,.T.);
#228 = ORIENTED_EDGE('',*,*,#229,.T.);
#229 = EDGE_CURVE('',#154,#230,#232,.T.);
#230 = VERTEX_POINT('',#231);
#231 = CARTESIAN_POINT('',(157.79999936,0.E+000,1.64592));
#232 = SURFACE_CURVE('',#233,(#237,#244),.PCURVE_S1.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(157.79999936,20.00000064,1.64592));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(0.E+000,-1.,0.E+000));
#237 = PCURVE('',#192,#238);
#238 = DEFINITIONAL_REPRESENTATION('',(#239),#243);
#239 = LINE('',#240,#241);
#240 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#241 = VECTOR('',#242,1.);
#242 = DIRECTION('',(1.,0.E+000));
#243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#244 = PCURVE('',#83,#245);
#245 = DEFINITIONAL_REPRESENTATION('',(#246),#250);
#246 = LINE('',#247,#248);
#247 = CARTESIAN_POINT('',(157.79999936,20.00000064));
#248 = VECTOR('',#249,1.);
#249 = DIRECTION('',(0.E+000,-1.));
#250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#251 = ORIENTED_EDGE('',*,*,#252,.F.);
#252 = EDGE_CURVE('',#253,#230,#255,.T.);
#253 = VERTEX_POINT('',#254);
#254 = CARTESIAN_POINT('',(157.79999936,0.E+000,0.E+000));
#255 = SURFACE_CURVE('',#256,(#260,#267),.PCURVE_S1.);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(157.79999936,0.E+000,0.E+000));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(0.E+000,0.E+000,1.));
#260 = PCURVE('',#192,#261);
#261 = DEFINITIONAL_REPRESENTATION('',(#262),#266);
#262 = LINE('',#263,#264);
#263 = CARTESIAN_POINT('',(20.00000064,0.E+000));
#264 = VECTOR('',#265,1.);
#265 = DIRECTION('',(0.E+000,-1.));
#266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#267 = PCURVE('',#55,#268);
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = LINE('',#270,#271);
#270 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#271 = VECTOR('',#272,1.);
#272 = DIRECTION('',(0.E+000,-1.));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = ORIENTED_EDGE('',*,*,#275,.F.);
#275 = EDGE_CURVE('',#177,#253,#276,.T.);
#276 = SURFACE_CURVE('',#277,(#281,#288),.PCURVE_S1.);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(157.79999936,20.00000064,0.E+000));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(0.E+000,-1.,0.E+000));
#281 = PCURVE('',#192,#282);
#282 = DEFINITIONAL_REPRESENTATION('',(#283),#287);
#283 = LINE('',#284,#285);
#284 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#285 = VECTOR('',#286,1.);
#286 = DIRECTION('',(1.,0.E+000));
#287 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#288 = PCURVE('',#137,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#294);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(157.79999936,20.00000064));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(0.E+000,-1.));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = ADVANCED_FACE('',(#296),#55,.T.);
#296 = FACE_BOUND('',#297,.T.);
#297 = EDGE_LOOP('',(#298,#299,#320,#321));
#298 = ORIENTED_EDGE('',*,*,#252,.T.);
#299 = ORIENTED_EDGE('',*,*,#300,.T.);
#300 = EDGE_CURVE('',#230,#35,#301,.T.);
#301 = SURFACE_CURVE('',#302,(#306,#313),.PCURVE_S1.);
#302 = LINE('',#303,#304);
#303 = CARTESIAN_POINT('',(157.79999936,0.E+000,1.64592));
#304 = VECTOR('',#305,1.);
#305 = DIRECTION('',(-1.,0.E+000,0.E+000));
#306 = PCURVE('',#55,#307);
#307 = DEFINITIONAL_REPRESENTATION('',(#308),#312);
#308 = LINE('',#309,#310);
#309 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#310 = VECTOR('',#311,1.);
#311 = DIRECTION('',(1.,0.E+000));
#312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#313 = PCURVE('',#83,#314);
#314 = DEFINITIONAL_REPRESENTATION('',(#315),#319);
#315 = LINE('',#316,#317);
#316 = CARTESIAN_POINT('',(157.79999936,0.E+000));
#317 = VECTOR('',#318,1.);
#318 = DIRECTION('',(-1.,0.E+000));
#319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#320 = ORIENTED_EDGE('',*,*,#32,.F.);
#321 = ORIENTED_EDGE('',*,*,#322,.F.);
#322 = EDGE_CURVE('',#253,#33,#323,.T.);
#323 = SURFACE_CURVE('',#324,(#328,#335),.PCURVE_S1.);
#324 = LINE('',#325,#326);
#325 = CARTESIAN_POINT('',(157.79999936,0.E+000,0.E+000));
#326 = VECTOR('',#327,1.);
#327 = DIRECTION('',(-1.,0.E+000,0.E+000));
#328 = PCURVE('',#55,#329);
#329 = DEFINITIONAL_REPRESENTATION('',(#330),#334);
#330 = LINE('',#331,#332);
#331 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#332 = VECTOR('',#333,1.);
#333 = DIRECTION('',(1.,0.E+000));
#334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#335 = PCURVE('',#137,#336);
#336 = DEFINITIONAL_REPRESENTATION('',(#337),#341);
#337 = LINE('',#338,#339);
#338 = CARTESIAN_POINT('',(157.79999936,0.E+000));
#339 = VECTOR('',#340,1.);
#340 = DIRECTION('',(-1.,0.E+000));
#341 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#342 = ADVANCED_FACE('',(#343),#357,.T.);
#343 = FACE_BOUND('',#344,.F.);
#344 = EDGE_LOOP('',(#345,#375,#397,#398));
#345 = ORIENTED_EDGE('',*,*,#346,.T.);
#346 = EDGE_CURVE('',#347,#349,#351,.T.);
#347 = VERTEX_POINT('',#348);
#348 = CARTESIAN_POINT('',(7.60000004,10.00000032,0.E+000));
#349 = VERTEX_POINT('',#350);
#350 = CARTESIAN_POINT('',(7.60000004,10.00000032,1.64592));
#351 = SEAM_CURVE('',#352,(#356,#368),.PCURVE_S1.);
#352 = LINE('',#353,#354);
#353 = CARTESIAN_POINT('',(7.60000004,10.00000032,0.E+000));
#354 = VECTOR('',#355,1.);
#355 = DIRECTION('',(0.E+000,0.E+000,1.));
#356 = PCURVE('',#357,#362);
#357 = CYLINDRICAL_SURFACE('',#358,1.59999934);
#358 = AXIS2_PLACEMENT_3D('',#359,#360,#361);
#359 = CARTESIAN_POINT('',(6.0000007,10.00000032,0.E+000));
#360 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#361 = DIRECTION('',(1.,0.E+000,-0.E+000));
#362 = DEFINITIONAL_REPRESENTATION('',(#363),#367);
#363 = LINE('',#364,#365);
#364 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#365 = VECTOR('',#366,1.);
#366 = DIRECTION('',(-0.E+000,-1.));
#367 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#368 = PCURVE('',#357,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(-0.E+000,-1.));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = ORIENTED_EDGE('',*,*,#376,.T.);
#376 = EDGE_CURVE('',#349,#349,#377,.T.);
#377 = SURFACE_CURVE('',#378,(#383,#390),.PCURVE_S1.);
#378 = CIRCLE('',#379,1.59999934);
#379 = AXIS2_PLACEMENT_3D('',#380,#381,#382);
#380 = CARTESIAN_POINT('',(6.0000007,10.00000032,1.64592));
#381 = DIRECTION('',(0.E+000,0.E+000,1.));
#382 = DIRECTION('',(1.,0.E+000,-0.E+000));
#383 = PCURVE('',#357,#384);
#384 = DEFINITIONAL_REPRESENTATION('',(#385),#389);
#385 = LINE('',#386,#387);
#386 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#387 = VECTOR('',#388,1.);
#388 = DIRECTION('',(-1.,0.E+000));
#389 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#390 = PCURVE('',#83,#391);
#391 = DEFINITIONAL_REPRESENTATION('',(#392),#396);
#392 = CIRCLE('',#393,1.59999934);
#393 = AXIS2_PLACEMENT_2D('',#394,#395);
#394 = CARTESIAN_POINT('',(6.0000007,10.00000032));
#395 = DIRECTION('',(1.,0.E+000));
#396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#397 = ORIENTED_EDGE('',*,*,#346,.F.);
#398 = ORIENTED_EDGE('',*,*,#399,.F.);
#399 = EDGE_CURVE('',#347,#347,#400,.T.);
#400 = SURFACE_CURVE('',#401,(#406,#413),.PCURVE_S1.);
#401 = CIRCLE('',#402,1.59999934);
#402 = AXIS2_PLACEMENT_3D('',#403,#404,#405);
#403 = CARTESIAN_POINT('',(6.0000007,10.00000032,0.E+000));
#404 = DIRECTION('',(0.E+000,0.E+000,1.));
#405 = DIRECTION('',(1.,0.E+000,-0.E+000));
#406 = PCURVE('',#357,#407);
#407 = DEFINITIONAL_REPRESENTATION('',(#408),#412);
#408 = LINE('',#409,#410);
#409 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#410 = VECTOR('',#411,1.);
#411 = DIRECTION('',(-1.,0.E+000));
#412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#413 = PCURVE('',#137,#414);
#414 = DEFINITIONAL_REPRESENTATION('',(#415),#419);
#415 = CIRCLE('',#416,1.59999934);
#416 = AXIS2_PLACEMENT_2D('',#417,#418);
#417 = CARTESIAN_POINT('',(6.0000007,10.00000032));
#418 = DIRECTION('',(1.,0.E+000));
#419 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#420 = ADVANCED_FACE('',(#421),#435,.T.);
#421 = FACE_BOUND('',#422,.F.);
#422 = EDGE_LOOP('',(#423,#453,#475,#476));
#423 = ORIENTED_EDGE('',*,*,#424,.T.);
#424 = EDGE_CURVE('',#425,#427,#429,.T.);
#425 = VERTEX_POINT('',#426);
#426 = CARTESIAN_POINT('',(152.4,10.00000032,0.E+000));
#427 = VERTEX_POINT('',#428);
#428 = CARTESIAN_POINT('',(152.4,10.00000032,1.64592));
#429 = SEAM_CURVE('',#430,(#434,#446),.PCURVE_S1.);
#430 = LINE('',#431,#432);
#431 = CARTESIAN_POINT('',(152.4,10.00000032,0.E+000));
#432 = VECTOR('',#433,1.);
#433 = DIRECTION('',(0.E+000,0.E+000,1.));
#434 = PCURVE('',#435,#440);
#435 = CYLINDRICAL_SURFACE('',#436,1.59999934);
#436 = AXIS2_PLACEMENT_3D('',#437,#438,#439);
#437 = CARTESIAN_POINT('',(150.80000066,10.00000032,0.E+000));
#438 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#439 = DIRECTION('',(1.,0.E+000,-0.E+000));
#440 = DEFINITIONAL_REPRESENTATION('',(#441),#445);
#441 = LINE('',#442,#443);
#442 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#443 = VECTOR('',#444,1.);
#444 = DIRECTION('',(-0.E+000,-1.));
#445 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#446 = PCURVE('',#435,#447);
#447 = DEFINITIONAL_REPRESENTATION('',(#448),#452);
#448 = LINE('',#449,#450);
#449 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#450 = VECTOR('',#451,1.);
#451 = DIRECTION('',(-0.E+000,-1.));
#452 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#453 = ORIENTED_EDGE('',*,*,#454,.T.);
#454 = EDGE_CURVE('',#427,#427,#455,.T.);
#455 = SURFACE_CURVE('',#456,(#461,#468),.PCURVE_S1.);
#456 = CIRCLE('',#457,1.59999934);
#457 = AXIS2_PLACEMENT_3D('',#458,#459,#460);
#458 = CARTESIAN_POINT('',(150.80000066,10.00000032,1.64592));
#459 = DIRECTION('',(0.E+000,0.E+000,1.));
#460 = DIRECTION('',(1.,0.E+000,-0.E+000));
#461 = PCURVE('',#435,#462);
#462 = DEFINITIONAL_REPRESENTATION('',(#463),#467);
#463 = LINE('',#464,#465);
#464 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#465 = VECTOR('',#466,1.);
#466 = DIRECTION('',(-1.,0.E+000));
#467 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#468 = PCURVE('',#83,#469);
#469 = DEFINITIONAL_REPRESENTATION('',(#470),#474);
#470 = CIRCLE('',#471,1.59999934);
#471 = AXIS2_PLACEMENT_2D('',#472,#473);
#472 = CARTESIAN_POINT('',(150.80000066,10.00000032));
#473 = DIRECTION('',(1.,0.E+000));
#474 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#475 = ORIENTED_EDGE('',*,*,#424,.F.);
#476 = ORIENTED_EDGE('',*,*,#477,.F.);
#477 = EDGE_CURVE('',#425,#425,#478,.T.);
#478 = SURFACE_CURVE('',#479,(#484,#491),.PCURVE_S1.);
#479 = CIRCLE('',#480,1.59999934);
#480 = AXIS2_PLACEMENT_3D('',#481,#482,#483);
#481 = CARTESIAN_POINT('',(150.80000066,10.00000032,0.E+000));
#482 = DIRECTION('',(0.E+000,0.E+000,1.));
#483 = DIRECTION('',(1.,0.E+000,-0.E+000));
#484 = PCURVE('',#435,#485);
#485 = DEFINITIONAL_REPRESENTATION('',(#486),#490);
#486 = LINE('',#487,#488);
#487 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#488 = VECTOR('',#489,1.);
#489 = DIRECTION('',(-1.,0.E+000));
#490 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#491 = PCURVE('',#137,#492);
#492 = DEFINITIONAL_REPRESENTATION('',(#493),#497);
#493 = CIRCLE('',#494,1.59999934);
#494 = AXIS2_PLACEMENT_2D('',#495,#496);
#495 = CARTESIAN_POINT('',(150.80000066,10.00000032));
#496 = DIRECTION('',(1.,0.E+000));
#497 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#498 = ADVANCED_FACE('',(#499,#505,#508),#137,.F.);
#499 = FACE_BOUND('',#500,.T.);
#500 = EDGE_LOOP('',(#501,#502,#503,#504));
#501 = ORIENTED_EDGE('',*,*,#123,.T.);
#502 = ORIENTED_EDGE('',*,*,#204,.T.);
#503 = ORIENTED_EDGE('',*,*,#275,.T.);
#504 = ORIENTED_EDGE('',*,*,#322,.T.);
#505 = FACE_BOUND('',#506,.F.);
#506 = EDGE_LOOP('',(#507));
#507 = ORIENTED_EDGE('',*,*,#399,.T.);
#508 = FACE_BOUND('',#509,.F.);
#509 = EDGE_LOOP('',(#510));
#510 = ORIENTED_EDGE('',*,*,#477,.T.);
#511 = ADVANCED_FACE('',(#512,#518,#521),#83,.T.);
#512 = FACE_BOUND('',#513,.F.);
#513 = EDGE_LOOP('',(#514,#515,#516,#517));
#514 = ORIENTED_EDGE('',*,*,#67,.T.);
#515 = ORIENTED_EDGE('',*,*,#153,.T.);
#516 = ORIENTED_EDGE('',*,*,#229,.T.);
#517 = ORIENTED_EDGE('',*,*,#300,.T.);
#518 = FACE_BOUND('',#519,.T.);
#519 = EDGE_LOOP('',(#520));
#520 = ORIENTED_EDGE('',*,*,#376,.T.);
#521 = FACE_BOUND('',#522,.T.);
#522 = EDGE_LOOP('',(#523));
#523 = ORIENTED_EDGE('',*,*,#454,.T.);
#524 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#528)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#525,#526,#527)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#525 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#526 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#527 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#528 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#525,
  'distance_accuracy_value','confusion accuracy');
#529 = SHAPE_DEFINITION_REPRESENTATION(#530,#25);
#530 = PRODUCT_DEFINITION_SHAPE('','',#531);
#531 = PRODUCT_DEFINITION('design','',#532,#535);
#532 = PRODUCT_DEFINITION_FORMATION('','',#533);
#533 = PRODUCT('Board','Board','',(#534));
#534 = PRODUCT_CONTEXT('',#2,'mechanical');
#535 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#536 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#537,#539);
#537 = ( REPRESENTATION_RELATIONSHIP('','',#25,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#538) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#538 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#539 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#540
  );
#540 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('3','=>[0:1:1:2]','',#5,#531,$);
#541 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#533));
#542 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#543)
  ,#524);
#543 = STYLED_ITEM('color',(#544),#26);
#544 = PRESENTATION_STYLE_ASSIGNMENT((#545,#551));
#545 = SURFACE_STYLE_USAGE(.BOTH.,#546);
#546 = SURFACE_SIDE_STYLE('',(#547));
#547 = SURFACE_STYLE_FILL_AREA(#548);
#548 = FILL_AREA_STYLE('',(#549));
#549 = FILL_AREA_STYLE_COLOUR('',#550);
#550 = COLOUR_RGB('',0.E+000,0.501960813999,0.E+000);
#551 = CURVE_STYLE('',#552,POSITIVE_LENGTH_MEASURE(0.1),#550);
#552 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
