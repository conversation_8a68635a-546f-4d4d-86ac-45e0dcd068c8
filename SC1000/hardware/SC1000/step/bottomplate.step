ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2018-12-13T20:00:42',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 6.5','Open CASCADE 6.5'
  ,'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('PCB','PCB','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15),#19);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#13 = DIRECTION('',(0.E+000,0.E+000,1.));
#14 = DIRECTION('',(1.,0.E+000,-0.E+000));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.E+000,0.E+000,-1.64592));
#17 = DIRECTION('',(0.E+000,0.E+000,1.));
#18 = DIRECTION('',(1.,0.E+000,-0.E+000));
#19 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#23)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#20,#21,#22)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#20 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#21 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#22 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#23 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#20,
  'distance_accuracy_value','confusion accuracy');
#24 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#25 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#26),#1532);
#26 = MANIFOLD_SOLID_BREP('',#27);
#27 = CLOSED_SHELL('',(#28,#148,#224,#295,#342,#420,#498,#576,#654,#732,
    #810,#888,#966,#1044,#1122,#1200,#1278,#1356,#1434,#1483));
#28 = ADVANCED_FACE('',(#29),#43,.T.);
#29 = FACE_BOUND('',#30,.T.);
#30 = EDGE_LOOP('',(#31,#66,#94,#122));
#31 = ORIENTED_EDGE('',*,*,#32,.T.);
#32 = EDGE_CURVE('',#33,#35,#37,.T.);
#33 = VERTEX_POINT('',#34);
#34 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#35 = VERTEX_POINT('',#36);
#36 = CARTESIAN_POINT('',(0.E+000,0.E+000,1.64592));
#37 = SURFACE_CURVE('',#38,(#42,#54),.PCURVE_S1.);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.E+000,0.E+000,1.));
#42 = PCURVE('',#43,#48);
#43 = PLANE('',#44);
#44 = AXIS2_PLACEMENT_3D('',#45,#46,#47);
#45 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#46 = DIRECTION('',(-1.,0.E+000,0.E+000));
#47 = DIRECTION('',(0.E+000,1.,0.E+000));
#48 = DEFINITIONAL_REPRESENTATION('',(#49),#53);
#49 = LINE('',#50,#51);
#50 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#51 = VECTOR('',#52,1.);
#52 = DIRECTION('',(0.E+000,-1.));
#53 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#54 = PCURVE('',#55,#60);
#55 = PLANE('',#56);
#56 = AXIS2_PLACEMENT_3D('',#57,#58,#59);
#57 = CARTESIAN_POINT('',(129.99999908,0.E+000,0.E+000));
#58 = DIRECTION('',(0.E+000,-1.,0.E+000));
#59 = DIRECTION('',(-1.,0.E+000,0.E+000));
#60 = DEFINITIONAL_REPRESENTATION('',(#61),#65);
#61 = LINE('',#62,#63);
#62 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#63 = VECTOR('',#64,1.);
#64 = DIRECTION('',(0.E+000,-1.));
#65 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#66 = ORIENTED_EDGE('',*,*,#67,.T.);
#67 = EDGE_CURVE('',#35,#68,#70,.T.);
#68 = VERTEX_POINT('',#69);
#69 = CARTESIAN_POINT('',(0.E+000,157.79999936,1.64592));
#70 = SURFACE_CURVE('',#71,(#75,#82),.PCURVE_S1.);
#71 = LINE('',#72,#73);
#72 = CARTESIAN_POINT('',(0.E+000,0.E+000,1.64592));
#73 = VECTOR('',#74,1.);
#74 = DIRECTION('',(0.E+000,1.,0.E+000));
#75 = PCURVE('',#43,#76);
#76 = DEFINITIONAL_REPRESENTATION('',(#77),#81);
#77 = LINE('',#78,#79);
#78 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#79 = VECTOR('',#80,1.);
#80 = DIRECTION('',(1.,0.E+000));
#81 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#82 = PCURVE('',#83,#88);
#83 = PLANE('',#84);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(0.E+000,0.E+000,1.64592));
#86 = DIRECTION('',(0.E+000,0.E+000,1.));
#87 = DIRECTION('',(1.,0.E+000,-0.E+000));
#88 = DEFINITIONAL_REPRESENTATION('',(#89),#93);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(0.E+000,1.));
#93 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#94 = ORIENTED_EDGE('',*,*,#95,.F.);
#95 = EDGE_CURVE('',#96,#68,#98,.T.);
#96 = VERTEX_POINT('',#97);
#97 = CARTESIAN_POINT('',(0.E+000,157.79999936,0.E+000));
#98 = SURFACE_CURVE('',#99,(#103,#110),.PCURVE_S1.);
#99 = LINE('',#100,#101);
#100 = CARTESIAN_POINT('',(0.E+000,157.79999936,0.E+000));
#101 = VECTOR('',#102,1.);
#102 = DIRECTION('',(0.E+000,0.E+000,1.));
#103 = PCURVE('',#43,#104);
#104 = DEFINITIONAL_REPRESENTATION('',(#105),#109);
#105 = LINE('',#106,#107);
#106 = CARTESIAN_POINT('',(157.79999936,0.E+000));
#107 = VECTOR('',#108,1.);
#108 = DIRECTION('',(0.E+000,-1.));
#109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#110 = PCURVE('',#111,#116);
#111 = PLANE('',#112);
#112 = AXIS2_PLACEMENT_3D('',#113,#114,#115);
#113 = CARTESIAN_POINT('',(0.E+000,157.79999936,0.E+000));
#114 = DIRECTION('',(0.E+000,1.,0.E+000));
#115 = DIRECTION('',(1.,0.E+000,0.E+000));
#116 = DEFINITIONAL_REPRESENTATION('',(#117),#121);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(0.E+000,-1.));
#121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#122 = ORIENTED_EDGE('',*,*,#123,.F.);
#123 = EDGE_CURVE('',#33,#96,#124,.T.);
#124 = SURFACE_CURVE('',#125,(#129,#136),.PCURVE_S1.);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(0.E+000,1.,0.E+000));
#129 = PCURVE('',#43,#130);
#130 = DEFINITIONAL_REPRESENTATION('',(#131),#135);
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(1.,0.E+000));
#135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#136 = PCURVE('',#137,#142);
#137 = PLANE('',#138);
#138 = AXIS2_PLACEMENT_3D('',#139,#140,#141);
#139 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#140 = DIRECTION('',(0.E+000,0.E+000,1.));
#141 = DIRECTION('',(1.,0.E+000,-0.E+000));
#142 = DEFINITIONAL_REPRESENTATION('',(#143),#147);
#143 = LINE('',#144,#145);
#144 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#145 = VECTOR('',#146,1.);
#146 = DIRECTION('',(0.E+000,1.));
#147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#148 = ADVANCED_FACE('',(#149),#111,.T.);
#149 = FACE_BOUND('',#150,.T.);
#150 = EDGE_LOOP('',(#151,#152,#175,#203));
#151 = ORIENTED_EDGE('',*,*,#95,.T.);
#152 = ORIENTED_EDGE('',*,*,#153,.T.);
#153 = EDGE_CURVE('',#68,#154,#156,.T.);
#154 = VERTEX_POINT('',#155);
#155 = CARTESIAN_POINT('',(129.99999908,157.79999936,1.64592));
#156 = SURFACE_CURVE('',#157,(#161,#168),.PCURVE_S1.);
#157 = LINE('',#158,#159);
#158 = CARTESIAN_POINT('',(0.E+000,157.79999936,1.64592));
#159 = VECTOR('',#160,1.);
#160 = DIRECTION('',(1.,0.E+000,0.E+000));
#161 = PCURVE('',#111,#162);
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(1.,0.E+000));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = PCURVE('',#83,#169);
#169 = DEFINITIONAL_REPRESENTATION('',(#170),#174);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(0.E+000,157.79999936));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(1.,0.E+000));
#174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#175 = ORIENTED_EDGE('',*,*,#176,.F.);
#176 = EDGE_CURVE('',#177,#154,#179,.T.);
#177 = VERTEX_POINT('',#178);
#178 = CARTESIAN_POINT('',(129.99999908,157.79999936,0.E+000));
#179 = SURFACE_CURVE('',#180,(#184,#191),.PCURVE_S1.);
#180 = LINE('',#181,#182);
#181 = CARTESIAN_POINT('',(129.99999908,157.79999936,0.E+000));
#182 = VECTOR('',#183,1.);
#183 = DIRECTION('',(0.E+000,0.E+000,1.));
#184 = PCURVE('',#111,#185);
#185 = DEFINITIONAL_REPRESENTATION('',(#186),#190);
#186 = LINE('',#187,#188);
#187 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#188 = VECTOR('',#189,1.);
#189 = DIRECTION('',(0.E+000,-1.));
#190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#191 = PCURVE('',#192,#197);
#192 = PLANE('',#193);
#193 = AXIS2_PLACEMENT_3D('',#194,#195,#196);
#194 = CARTESIAN_POINT('',(129.99999908,157.79999936,0.E+000));
#195 = DIRECTION('',(1.,0.E+000,-0.E+000));
#196 = DIRECTION('',(0.E+000,-1.,0.E+000));
#197 = DEFINITIONAL_REPRESENTATION('',(#198),#202);
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(0.E+000,-1.));
#202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#203 = ORIENTED_EDGE('',*,*,#204,.F.);
#204 = EDGE_CURVE('',#96,#177,#205,.T.);
#205 = SURFACE_CURVE('',#206,(#210,#217),.PCURVE_S1.);
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(0.E+000,157.79999936,0.E+000));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(1.,0.E+000,0.E+000));
#210 = PCURVE('',#111,#211);
#211 = DEFINITIONAL_REPRESENTATION('',(#212),#216);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(1.,0.E+000));
#216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#217 = PCURVE('',#137,#218);
#218 = DEFINITIONAL_REPRESENTATION('',(#219),#223);
#219 = LINE('',#220,#221);
#220 = CARTESIAN_POINT('',(0.E+000,157.79999936));
#221 = VECTOR('',#222,1.);
#222 = DIRECTION('',(1.,0.E+000));
#223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#224 = ADVANCED_FACE('',(#225),#192,.T.);
#225 = FACE_BOUND('',#226,.T.);
#226 = EDGE_LOOP('',(#227,#228,#251,#274));
#227 = ORIENTED_EDGE('',*,*,#176,.T.);
#228 = ORIENTED_EDGE('',*,*,#229,.T.);
#229 = EDGE_CURVE('',#154,#230,#232,.T.);
#230 = VERTEX_POINT('',#231);
#231 = CARTESIAN_POINT('',(129.99999908,0.E+000,1.64592));
#232 = SURFACE_CURVE('',#233,(#237,#244),.PCURVE_S1.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(129.99999908,157.79999936,1.64592));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(0.E+000,-1.,0.E+000));
#237 = PCURVE('',#192,#238);
#238 = DEFINITIONAL_REPRESENTATION('',(#239),#243);
#239 = LINE('',#240,#241);
#240 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#241 = VECTOR('',#242,1.);
#242 = DIRECTION('',(1.,0.E+000));
#243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#244 = PCURVE('',#83,#245);
#245 = DEFINITIONAL_REPRESENTATION('',(#246),#250);
#246 = LINE('',#247,#248);
#247 = CARTESIAN_POINT('',(129.99999908,157.79999936));
#248 = VECTOR('',#249,1.);
#249 = DIRECTION('',(0.E+000,-1.));
#250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#251 = ORIENTED_EDGE('',*,*,#252,.F.);
#252 = EDGE_CURVE('',#253,#230,#255,.T.);
#253 = VERTEX_POINT('',#254);
#254 = CARTESIAN_POINT('',(129.99999908,0.E+000,0.E+000));
#255 = SURFACE_CURVE('',#256,(#260,#267),.PCURVE_S1.);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(129.99999908,0.E+000,0.E+000));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(0.E+000,0.E+000,1.));
#260 = PCURVE('',#192,#261);
#261 = DEFINITIONAL_REPRESENTATION('',(#262),#266);
#262 = LINE('',#263,#264);
#263 = CARTESIAN_POINT('',(157.79999936,0.E+000));
#264 = VECTOR('',#265,1.);
#265 = DIRECTION('',(0.E+000,-1.));
#266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#267 = PCURVE('',#55,#268);
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = LINE('',#270,#271);
#270 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#271 = VECTOR('',#272,1.);
#272 = DIRECTION('',(0.E+000,-1.));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = ORIENTED_EDGE('',*,*,#275,.F.);
#275 = EDGE_CURVE('',#177,#253,#276,.T.);
#276 = SURFACE_CURVE('',#277,(#281,#288),.PCURVE_S1.);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(129.99999908,157.79999936,0.E+000));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(0.E+000,-1.,0.E+000));
#281 = PCURVE('',#192,#282);
#282 = DEFINITIONAL_REPRESENTATION('',(#283),#287);
#283 = LINE('',#284,#285);
#284 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#285 = VECTOR('',#286,1.);
#286 = DIRECTION('',(1.,0.E+000));
#287 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#288 = PCURVE('',#137,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#294);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(129.99999908,157.79999936));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(0.E+000,-1.));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = ADVANCED_FACE('',(#296),#55,.T.);
#296 = FACE_BOUND('',#297,.T.);
#297 = EDGE_LOOP('',(#298,#299,#320,#321));
#298 = ORIENTED_EDGE('',*,*,#252,.T.);
#299 = ORIENTED_EDGE('',*,*,#300,.T.);
#300 = EDGE_CURVE('',#230,#35,#301,.T.);
#301 = SURFACE_CURVE('',#302,(#306,#313),.PCURVE_S1.);
#302 = LINE('',#303,#304);
#303 = CARTESIAN_POINT('',(129.99999908,0.E+000,1.64592));
#304 = VECTOR('',#305,1.);
#305 = DIRECTION('',(-1.,0.E+000,0.E+000));
#306 = PCURVE('',#55,#307);
#307 = DEFINITIONAL_REPRESENTATION('',(#308),#312);
#308 = LINE('',#309,#310);
#309 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#310 = VECTOR('',#311,1.);
#311 = DIRECTION('',(1.,0.E+000));
#312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#313 = PCURVE('',#83,#314);
#314 = DEFINITIONAL_REPRESENTATION('',(#315),#319);
#315 = LINE('',#316,#317);
#316 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#317 = VECTOR('',#318,1.);
#318 = DIRECTION('',(-1.,0.E+000));
#319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#320 = ORIENTED_EDGE('',*,*,#32,.F.);
#321 = ORIENTED_EDGE('',*,*,#322,.F.);
#322 = EDGE_CURVE('',#253,#33,#323,.T.);
#323 = SURFACE_CURVE('',#324,(#328,#335),.PCURVE_S1.);
#324 = LINE('',#325,#326);
#325 = CARTESIAN_POINT('',(129.99999908,0.E+000,0.E+000));
#326 = VECTOR('',#327,1.);
#327 = DIRECTION('',(-1.,0.E+000,0.E+000));
#328 = PCURVE('',#55,#329);
#329 = DEFINITIONAL_REPRESENTATION('',(#330),#334);
#330 = LINE('',#331,#332);
#331 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#332 = VECTOR('',#333,1.);
#333 = DIRECTION('',(1.,0.E+000));
#334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#335 = PCURVE('',#137,#336);
#336 = DEFINITIONAL_REPRESENTATION('',(#337),#341);
#337 = LINE('',#338,#339);
#338 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#339 = VECTOR('',#340,1.);
#340 = DIRECTION('',(-1.,0.E+000));
#341 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#342 = ADVANCED_FACE('',(#343),#357,.T.);
#343 = FACE_BOUND('',#344,.F.);
#344 = EDGE_LOOP('',(#345,#375,#397,#398));
#345 = ORIENTED_EDGE('',*,*,#346,.T.);
#346 = EDGE_CURVE('',#347,#349,#351,.T.);
#347 = VERTEX_POINT('',#348);
#348 = CARTESIAN_POINT('',(8.19999884,12.5000004,0.E+000));
#349 = VERTEX_POINT('',#350);
#350 = CARTESIAN_POINT('',(8.19999884,12.5000004,1.64592));
#351 = SEAM_CURVE('',#352,(#356,#368),.PCURVE_S1.);
#352 = LINE('',#353,#354);
#353 = CARTESIAN_POINT('',(8.19999884,12.5000004,0.E+000));
#354 = VECTOR('',#355,1.);
#355 = DIRECTION('',(0.E+000,0.E+000,1.));
#356 = PCURVE('',#357,#362);
#357 = CYLINDRICAL_SURFACE('',#358,1.59999934);
#358 = AXIS2_PLACEMENT_3D('',#359,#360,#361);
#359 = CARTESIAN_POINT('',(6.5999995,12.5000004,0.E+000));
#360 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#361 = DIRECTION('',(1.,0.E+000,-0.E+000));
#362 = DEFINITIONAL_REPRESENTATION('',(#363),#367);
#363 = LINE('',#364,#365);
#364 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#365 = VECTOR('',#366,1.);
#366 = DIRECTION('',(-0.E+000,-1.));
#367 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#368 = PCURVE('',#357,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(-0.E+000,-1.));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = ORIENTED_EDGE('',*,*,#376,.T.);
#376 = EDGE_CURVE('',#349,#349,#377,.T.);
#377 = SURFACE_CURVE('',#378,(#383,#390),.PCURVE_S1.);
#378 = CIRCLE('',#379,1.59999934);
#379 = AXIS2_PLACEMENT_3D('',#380,#381,#382);
#380 = CARTESIAN_POINT('',(6.5999995,12.5000004,1.64592));
#381 = DIRECTION('',(0.E+000,0.E+000,1.));
#382 = DIRECTION('',(1.,0.E+000,-0.E+000));
#383 = PCURVE('',#357,#384);
#384 = DEFINITIONAL_REPRESENTATION('',(#385),#389);
#385 = LINE('',#386,#387);
#386 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#387 = VECTOR('',#388,1.);
#388 = DIRECTION('',(-1.,0.E+000));
#389 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#390 = PCURVE('',#83,#391);
#391 = DEFINITIONAL_REPRESENTATION('',(#392),#396);
#392 = CIRCLE('',#393,1.59999934);
#393 = AXIS2_PLACEMENT_2D('',#394,#395);
#394 = CARTESIAN_POINT('',(6.5999995,12.5000004));
#395 = DIRECTION('',(1.,0.E+000));
#396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#397 = ORIENTED_EDGE('',*,*,#346,.F.);
#398 = ORIENTED_EDGE('',*,*,#399,.F.);
#399 = EDGE_CURVE('',#347,#347,#400,.T.);
#400 = SURFACE_CURVE('',#401,(#406,#413),.PCURVE_S1.);
#401 = CIRCLE('',#402,1.59999934);
#402 = AXIS2_PLACEMENT_3D('',#403,#404,#405);
#403 = CARTESIAN_POINT('',(6.5999995,12.5000004,0.E+000));
#404 = DIRECTION('',(0.E+000,0.E+000,1.));
#405 = DIRECTION('',(1.,0.E+000,-0.E+000));
#406 = PCURVE('',#357,#407);
#407 = DEFINITIONAL_REPRESENTATION('',(#408),#412);
#408 = LINE('',#409,#410);
#409 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#410 = VECTOR('',#411,1.);
#411 = DIRECTION('',(-1.,0.E+000));
#412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#413 = PCURVE('',#137,#414);
#414 = DEFINITIONAL_REPRESENTATION('',(#415),#419);
#415 = CIRCLE('',#416,1.59999934);
#416 = AXIS2_PLACEMENT_2D('',#417,#418);
#417 = CARTESIAN_POINT('',(6.5999995,12.5000004));
#418 = DIRECTION('',(1.,0.E+000));
#419 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#420 = ADVANCED_FACE('',(#421),#435,.T.);
#421 = FACE_BOUND('',#422,.F.);
#422 = EDGE_LOOP('',(#423,#453,#475,#476));
#423 = ORIENTED_EDGE('',*,*,#424,.T.);
#424 = EDGE_CURVE('',#425,#427,#429,.T.);
#425 = VERTEX_POINT('',#426);
#426 = CARTESIAN_POINT('',(8.19999884,56.7000009,0.E+000));
#427 = VERTEX_POINT('',#428);
#428 = CARTESIAN_POINT('',(8.19999884,56.7000009,1.64592));
#429 = SEAM_CURVE('',#430,(#434,#446),.PCURVE_S1.);
#430 = LINE('',#431,#432);
#431 = CARTESIAN_POINT('',(8.19999884,56.7000009,0.E+000));
#432 = VECTOR('',#433,1.);
#433 = DIRECTION('',(0.E+000,0.E+000,1.));
#434 = PCURVE('',#435,#440);
#435 = CYLINDRICAL_SURFACE('',#436,1.59999934);
#436 = AXIS2_PLACEMENT_3D('',#437,#438,#439);
#437 = CARTESIAN_POINT('',(6.5999995,56.7000009,0.E+000));
#438 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#439 = DIRECTION('',(1.,0.E+000,-0.E+000));
#440 = DEFINITIONAL_REPRESENTATION('',(#441),#445);
#441 = LINE('',#442,#443);
#442 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#443 = VECTOR('',#444,1.);
#444 = DIRECTION('',(-0.E+000,-1.));
#445 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#446 = PCURVE('',#435,#447);
#447 = DEFINITIONAL_REPRESENTATION('',(#448),#452);
#448 = LINE('',#449,#450);
#449 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#450 = VECTOR('',#451,1.);
#451 = DIRECTION('',(-0.E+000,-1.));
#452 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#453 = ORIENTED_EDGE('',*,*,#454,.T.);
#454 = EDGE_CURVE('',#427,#427,#455,.T.);
#455 = SURFACE_CURVE('',#456,(#461,#468),.PCURVE_S1.);
#456 = CIRCLE('',#457,1.59999934);
#457 = AXIS2_PLACEMENT_3D('',#458,#459,#460);
#458 = CARTESIAN_POINT('',(6.5999995,56.7000009,1.64592));
#459 = DIRECTION('',(0.E+000,0.E+000,1.));
#460 = DIRECTION('',(1.,0.E+000,-0.E+000));
#461 = PCURVE('',#435,#462);
#462 = DEFINITIONAL_REPRESENTATION('',(#463),#467);
#463 = LINE('',#464,#465);
#464 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#465 = VECTOR('',#466,1.);
#466 = DIRECTION('',(-1.,0.E+000));
#467 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#468 = PCURVE('',#83,#469);
#469 = DEFINITIONAL_REPRESENTATION('',(#470),#474);
#470 = CIRCLE('',#471,1.59999934);
#471 = AXIS2_PLACEMENT_2D('',#472,#473);
#472 = CARTESIAN_POINT('',(6.5999995,56.7000009));
#473 = DIRECTION('',(1.,0.E+000));
#474 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#475 = ORIENTED_EDGE('',*,*,#424,.F.);
#476 = ORIENTED_EDGE('',*,*,#477,.F.);
#477 = EDGE_CURVE('',#425,#425,#478,.T.);
#478 = SURFACE_CURVE('',#479,(#484,#491),.PCURVE_S1.);
#479 = CIRCLE('',#480,1.59999934);
#480 = AXIS2_PLACEMENT_3D('',#481,#482,#483);
#481 = CARTESIAN_POINT('',(6.5999995,56.7000009,0.E+000));
#482 = DIRECTION('',(0.E+000,0.E+000,1.));
#483 = DIRECTION('',(1.,0.E+000,-0.E+000));
#484 = PCURVE('',#435,#485);
#485 = DEFINITIONAL_REPRESENTATION('',(#486),#490);
#486 = LINE('',#487,#488);
#487 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#488 = VECTOR('',#489,1.);
#489 = DIRECTION('',(-1.,0.E+000));
#490 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#491 = PCURVE('',#137,#492);
#492 = DEFINITIONAL_REPRESENTATION('',(#493),#497);
#493 = CIRCLE('',#494,1.59999934);
#494 = AXIS2_PLACEMENT_2D('',#495,#496);
#495 = CARTESIAN_POINT('',(6.5999995,56.7000009));
#496 = DIRECTION('',(1.,0.E+000));
#497 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#498 = ADVANCED_FACE('',(#499),#513,.T.);
#499 = FACE_BOUND('',#500,.F.);
#500 = EDGE_LOOP('',(#501,#531,#553,#554));
#501 = ORIENTED_EDGE('',*,*,#502,.T.);
#502 = EDGE_CURVE('',#503,#505,#507,.T.);
#503 = VERTEX_POINT('',#504);
#504 = CARTESIAN_POINT('',(56.09998686,93.19998886,0.E+000));
#505 = VERTEX_POINT('',#506);
#506 = CARTESIAN_POINT('',(56.09998686,93.19998886,1.64592));
#507 = SEAM_CURVE('',#508,(#512,#524),.PCURVE_S1.);
#508 = LINE('',#509,#510);
#509 = CARTESIAN_POINT('',(56.09998686,93.19998886,0.E+000));
#510 = VECTOR('',#511,1.);
#511 = DIRECTION('',(0.E+000,0.E+000,1.));
#512 = PCURVE('',#513,#518);
#513 = CYLINDRICAL_SURFACE('',#514,1.59999934);
#514 = AXIS2_PLACEMENT_3D('',#515,#516,#517);
#515 = CARTESIAN_POINT('',(54.49998752,93.19998886,0.E+000));
#516 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#517 = DIRECTION('',(1.,0.E+000,-0.E+000));
#518 = DEFINITIONAL_REPRESENTATION('',(#519),#523);
#519 = LINE('',#520,#521);
#520 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#521 = VECTOR('',#522,1.);
#522 = DIRECTION('',(-0.E+000,-1.));
#523 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#524 = PCURVE('',#513,#525);
#525 = DEFINITIONAL_REPRESENTATION('',(#526),#530);
#526 = LINE('',#527,#528);
#527 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#528 = VECTOR('',#529,1.);
#529 = DIRECTION('',(-0.E+000,-1.));
#530 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#531 = ORIENTED_EDGE('',*,*,#532,.T.);
#532 = EDGE_CURVE('',#505,#505,#533,.T.);
#533 = SURFACE_CURVE('',#534,(#539,#546),.PCURVE_S1.);
#534 = CIRCLE('',#535,1.59999934);
#535 = AXIS2_PLACEMENT_3D('',#536,#537,#538);
#536 = CARTESIAN_POINT('',(54.49998752,93.19998886,1.64592));
#537 = DIRECTION('',(0.E+000,0.E+000,1.));
#538 = DIRECTION('',(1.,0.E+000,-0.E+000));
#539 = PCURVE('',#513,#540);
#540 = DEFINITIONAL_REPRESENTATION('',(#541),#545);
#541 = LINE('',#542,#543);
#542 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#543 = VECTOR('',#544,1.);
#544 = DIRECTION('',(-1.,0.E+000));
#545 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#546 = PCURVE('',#83,#547);
#547 = DEFINITIONAL_REPRESENTATION('',(#548),#552);
#548 = CIRCLE('',#549,1.59999934);
#549 = AXIS2_PLACEMENT_2D('',#550,#551);
#550 = CARTESIAN_POINT('',(54.49998752,93.19998886));
#551 = DIRECTION('',(1.,0.E+000));
#552 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#553 = ORIENTED_EDGE('',*,*,#502,.F.);
#554 = ORIENTED_EDGE('',*,*,#555,.F.);
#555 = EDGE_CURVE('',#503,#503,#556,.T.);
#556 = SURFACE_CURVE('',#557,(#562,#569),.PCURVE_S1.);
#557 = CIRCLE('',#558,1.59999934);
#558 = AXIS2_PLACEMENT_3D('',#559,#560,#561);
#559 = CARTESIAN_POINT('',(54.49998752,93.19998886,0.E+000));
#560 = DIRECTION('',(0.E+000,0.E+000,1.));
#561 = DIRECTION('',(1.,0.E+000,-0.E+000));
#562 = PCURVE('',#513,#563);
#563 = DEFINITIONAL_REPRESENTATION('',(#564),#568);
#564 = LINE('',#565,#566);
#565 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#566 = VECTOR('',#567,1.);
#567 = DIRECTION('',(-1.,0.E+000));
#568 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#569 = PCURVE('',#137,#570);
#570 = DEFINITIONAL_REPRESENTATION('',(#571),#575);
#571 = CIRCLE('',#572,1.59999934);
#572 = AXIS2_PLACEMENT_2D('',#573,#574);
#573 = CARTESIAN_POINT('',(54.49998752,93.19998886));
#574 = DIRECTION('',(1.,0.E+000));
#575 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#576 = ADVANCED_FACE('',(#577),#591,.T.);
#577 = FACE_BOUND('',#578,.F.);
#578 = EDGE_LOOP('',(#579,#609,#631,#632));
#579 = ORIENTED_EDGE('',*,*,#580,.T.);
#580 = EDGE_CURVE('',#581,#583,#585,.T.);
#581 = VERTEX_POINT('',#582);
#582 = CARTESIAN_POINT('',(8.19999884,101.100001,0.E+000));
#583 = VERTEX_POINT('',#584);
#584 = CARTESIAN_POINT('',(8.19999884,101.100001,1.64592));
#585 = SEAM_CURVE('',#586,(#590,#602),.PCURVE_S1.);
#586 = LINE('',#587,#588);
#587 = CARTESIAN_POINT('',(8.19999884,101.100001,0.E+000));
#588 = VECTOR('',#589,1.);
#589 = DIRECTION('',(0.E+000,0.E+000,1.));
#590 = PCURVE('',#591,#596);
#591 = CYLINDRICAL_SURFACE('',#592,1.59999934);
#592 = AXIS2_PLACEMENT_3D('',#593,#594,#595);
#593 = CARTESIAN_POINT('',(6.5999995,101.100001,0.E+000));
#594 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#595 = DIRECTION('',(1.,0.E+000,-0.E+000));
#596 = DEFINITIONAL_REPRESENTATION('',(#597),#601);
#597 = LINE('',#598,#599);
#598 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#599 = VECTOR('',#600,1.);
#600 = DIRECTION('',(-0.E+000,-1.));
#601 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#602 = PCURVE('',#591,#603);
#603 = DEFINITIONAL_REPRESENTATION('',(#604),#608);
#604 = LINE('',#605,#606);
#605 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#606 = VECTOR('',#607,1.);
#607 = DIRECTION('',(-0.E+000,-1.));
#608 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#609 = ORIENTED_EDGE('',*,*,#610,.T.);
#610 = EDGE_CURVE('',#583,#583,#611,.T.);
#611 = SURFACE_CURVE('',#612,(#617,#624),.PCURVE_S1.);
#612 = CIRCLE('',#613,1.59999934);
#613 = AXIS2_PLACEMENT_3D('',#614,#615,#616);
#614 = CARTESIAN_POINT('',(6.5999995,101.100001,1.64592));
#615 = DIRECTION('',(0.E+000,0.E+000,1.));
#616 = DIRECTION('',(1.,0.E+000,-0.E+000));
#617 = PCURVE('',#591,#618);
#618 = DEFINITIONAL_REPRESENTATION('',(#619),#623);
#619 = LINE('',#620,#621);
#620 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#621 = VECTOR('',#622,1.);
#622 = DIRECTION('',(-1.,0.E+000));
#623 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#624 = PCURVE('',#83,#625);
#625 = DEFINITIONAL_REPRESENTATION('',(#626),#630);
#626 = CIRCLE('',#627,1.59999934);
#627 = AXIS2_PLACEMENT_2D('',#628,#629);
#628 = CARTESIAN_POINT('',(6.5999995,101.100001));
#629 = DIRECTION('',(1.,0.E+000));
#630 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#631 = ORIENTED_EDGE('',*,*,#580,.F.);
#632 = ORIENTED_EDGE('',*,*,#633,.F.);
#633 = EDGE_CURVE('',#581,#581,#634,.T.);
#634 = SURFACE_CURVE('',#635,(#640,#647),.PCURVE_S1.);
#635 = CIRCLE('',#636,1.59999934);
#636 = AXIS2_PLACEMENT_3D('',#637,#638,#639);
#637 = CARTESIAN_POINT('',(6.5999995,101.100001,0.E+000));
#638 = DIRECTION('',(0.E+000,0.E+000,1.));
#639 = DIRECTION('',(1.,0.E+000,-0.E+000));
#640 = PCURVE('',#591,#641);
#641 = DEFINITIONAL_REPRESENTATION('',(#642),#646);
#642 = LINE('',#643,#644);
#643 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#644 = VECTOR('',#645,1.);
#645 = DIRECTION('',(-1.,0.E+000));
#646 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#647 = PCURVE('',#137,#648);
#648 = DEFINITIONAL_REPRESENTATION('',(#649),#653);
#649 = CIRCLE('',#650,1.59999934);
#650 = AXIS2_PLACEMENT_2D('',#651,#652);
#651 = CARTESIAN_POINT('',(6.5999995,101.100001));
#652 = DIRECTION('',(1.,0.E+000));
#653 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#654 = ADVANCED_FACE('',(#655),#669,.T.);
#655 = FACE_BOUND('',#656,.F.);
#656 = EDGE_LOOP('',(#657,#687,#709,#710));
#657 = ORIENTED_EDGE('',*,*,#658,.T.);
#658 = EDGE_CURVE('',#659,#661,#663,.T.);
#659 = VERTEX_POINT('',#660);
#660 = CARTESIAN_POINT('',(8.19999884,145.29999896,0.E+000));
#661 = VERTEX_POINT('',#662);
#662 = CARTESIAN_POINT('',(8.19999884,145.29999896,1.64592));
#663 = SEAM_CURVE('',#664,(#668,#680),.PCURVE_S1.);
#664 = LINE('',#665,#666);
#665 = CARTESIAN_POINT('',(8.19999884,145.29999896,0.E+000));
#666 = VECTOR('',#667,1.);
#667 = DIRECTION('',(0.E+000,0.E+000,1.));
#668 = PCURVE('',#669,#674);
#669 = CYLINDRICAL_SURFACE('',#670,1.59999934);
#670 = AXIS2_PLACEMENT_3D('',#671,#672,#673);
#671 = CARTESIAN_POINT('',(6.5999995,145.29999896,0.E+000));
#672 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#673 = DIRECTION('',(1.,0.E+000,-0.E+000));
#674 = DEFINITIONAL_REPRESENTATION('',(#675),#679);
#675 = LINE('',#676,#677);
#676 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#677 = VECTOR('',#678,1.);
#678 = DIRECTION('',(-0.E+000,-1.));
#679 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#680 = PCURVE('',#669,#681);
#681 = DEFINITIONAL_REPRESENTATION('',(#682),#686);
#682 = LINE('',#683,#684);
#683 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#684 = VECTOR('',#685,1.);
#685 = DIRECTION('',(-0.E+000,-1.));
#686 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#687 = ORIENTED_EDGE('',*,*,#688,.T.);
#688 = EDGE_CURVE('',#661,#661,#689,.T.);
#689 = SURFACE_CURVE('',#690,(#695,#702),.PCURVE_S1.);
#690 = CIRCLE('',#691,1.59999934);
#691 = AXIS2_PLACEMENT_3D('',#692,#693,#694);
#692 = CARTESIAN_POINT('',(6.5999995,145.29999896,1.64592));
#693 = DIRECTION('',(0.E+000,0.E+000,1.));
#694 = DIRECTION('',(1.,0.E+000,-0.E+000));
#695 = PCURVE('',#669,#696);
#696 = DEFINITIONAL_REPRESENTATION('',(#697),#701);
#697 = LINE('',#698,#699);
#698 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#699 = VECTOR('',#700,1.);
#700 = DIRECTION('',(-1.,0.E+000));
#701 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#702 = PCURVE('',#83,#703);
#703 = DEFINITIONAL_REPRESENTATION('',(#704),#708);
#704 = CIRCLE('',#705,1.59999934);
#705 = AXIS2_PLACEMENT_2D('',#706,#707);
#706 = CARTESIAN_POINT('',(6.5999995,145.29999896));
#707 = DIRECTION('',(1.,0.E+000));
#708 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#709 = ORIENTED_EDGE('',*,*,#658,.F.);
#710 = ORIENTED_EDGE('',*,*,#711,.F.);
#711 = EDGE_CURVE('',#659,#659,#712,.T.);
#712 = SURFACE_CURVE('',#713,(#718,#725),.PCURVE_S1.);
#713 = CIRCLE('',#714,1.59999934);
#714 = AXIS2_PLACEMENT_3D('',#715,#716,#717);
#715 = CARTESIAN_POINT('',(6.5999995,145.29999896,0.E+000));
#716 = DIRECTION('',(0.E+000,0.E+000,1.));
#717 = DIRECTION('',(1.,0.E+000,-0.E+000));
#718 = PCURVE('',#669,#719);
#719 = DEFINITIONAL_REPRESENTATION('',(#720),#724);
#720 = LINE('',#721,#722);
#721 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#722 = VECTOR('',#723,1.);
#723 = DIRECTION('',(-1.,0.E+000));
#724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#725 = PCURVE('',#137,#726);
#726 = DEFINITIONAL_REPRESENTATION('',(#727),#731);
#727 = CIRCLE('',#728,1.59999934);
#728 = AXIS2_PLACEMENT_2D('',#729,#730);
#729 = CARTESIAN_POINT('',(6.5999995,145.29999896));
#730 = DIRECTION('',(1.,0.E+000));
#731 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#732 = ADVANCED_FACE('',(#733),#747,.T.);
#733 = FACE_BOUND('',#734,.F.);
#734 = EDGE_LOOP('',(#735,#765,#787,#788));
#735 = ORIENTED_EDGE('',*,*,#736,.T.);
#736 = EDGE_CURVE('',#737,#739,#741,.T.);
#737 = VERTEX_POINT('',#738);
#738 = CARTESIAN_POINT('',(20.09998774,93.19998886,0.E+000));
#739 = VERTEX_POINT('',#740);
#740 = CARTESIAN_POINT('',(20.09998774,93.19998886,1.64592));
#741 = SEAM_CURVE('',#742,(#746,#758),.PCURVE_S1.);
#742 = LINE('',#743,#744);
#743 = CARTESIAN_POINT('',(20.09998774,93.19998886,0.E+000));
#744 = VECTOR('',#745,1.);
#745 = DIRECTION('',(0.E+000,0.E+000,1.));
#746 = PCURVE('',#747,#752);
#747 = CYLINDRICAL_SURFACE('',#748,1.59999934);
#748 = AXIS2_PLACEMENT_3D('',#749,#750,#751);
#749 = CARTESIAN_POINT('',(18.4999884,93.19998886,0.E+000));
#750 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#751 = DIRECTION('',(1.,0.E+000,-0.E+000));
#752 = DEFINITIONAL_REPRESENTATION('',(#753),#757);
#753 = LINE('',#754,#755);
#754 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#755 = VECTOR('',#756,1.);
#756 = DIRECTION('',(-0.E+000,-1.));
#757 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#758 = PCURVE('',#747,#759);
#759 = DEFINITIONAL_REPRESENTATION('',(#760),#764);
#760 = LINE('',#761,#762);
#761 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#762 = VECTOR('',#763,1.);
#763 = DIRECTION('',(-0.E+000,-1.));
#764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#765 = ORIENTED_EDGE('',*,*,#766,.T.);
#766 = EDGE_CURVE('',#739,#739,#767,.T.);
#767 = SURFACE_CURVE('',#768,(#773,#780),.PCURVE_S1.);
#768 = CIRCLE('',#769,1.59999934);
#769 = AXIS2_PLACEMENT_3D('',#770,#771,#772);
#770 = CARTESIAN_POINT('',(18.4999884,93.19998886,1.64592));
#771 = DIRECTION('',(0.E+000,0.E+000,1.));
#772 = DIRECTION('',(1.,0.E+000,-0.E+000));
#773 = PCURVE('',#747,#774);
#774 = DEFINITIONAL_REPRESENTATION('',(#775),#779);
#775 = LINE('',#776,#777);
#776 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#777 = VECTOR('',#778,1.);
#778 = DIRECTION('',(-1.,0.E+000));
#779 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#780 = PCURVE('',#83,#781);
#781 = DEFINITIONAL_REPRESENTATION('',(#782),#786);
#782 = CIRCLE('',#783,1.59999934);
#783 = AXIS2_PLACEMENT_2D('',#784,#785);
#784 = CARTESIAN_POINT('',(18.4999884,93.19998886));
#785 = DIRECTION('',(1.,0.E+000));
#786 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#787 = ORIENTED_EDGE('',*,*,#736,.F.);
#788 = ORIENTED_EDGE('',*,*,#789,.F.);
#789 = EDGE_CURVE('',#737,#737,#790,.T.);
#790 = SURFACE_CURVE('',#791,(#796,#803),.PCURVE_S1.);
#791 = CIRCLE('',#792,1.59999934);
#792 = AXIS2_PLACEMENT_3D('',#793,#794,#795);
#793 = CARTESIAN_POINT('',(18.4999884,93.19998886,0.E+000));
#794 = DIRECTION('',(0.E+000,0.E+000,1.));
#795 = DIRECTION('',(1.,0.E+000,-0.E+000));
#796 = PCURVE('',#747,#797);
#797 = DEFINITIONAL_REPRESENTATION('',(#798),#802);
#798 = LINE('',#799,#800);
#799 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#800 = VECTOR('',#801,1.);
#801 = DIRECTION('',(-1.,0.E+000));
#802 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#803 = PCURVE('',#137,#804);
#804 = DEFINITIONAL_REPRESENTATION('',(#805),#809);
#805 = CIRCLE('',#806,1.59999934);
#806 = AXIS2_PLACEMENT_2D('',#807,#808);
#807 = CARTESIAN_POINT('',(18.4999884,93.19998886));
#808 = DIRECTION('',(1.,0.E+000));
#809 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#810 = ADVANCED_FACE('',(#811),#825,.T.);
#811 = FACE_BOUND('',#812,.F.);
#812 = EDGE_LOOP('',(#813,#843,#865,#866));
#813 = ORIENTED_EDGE('',*,*,#814,.T.);
#814 = EDGE_CURVE('',#815,#817,#819,.T.);
#815 = VERTEX_POINT('',#816);
#816 = CARTESIAN_POINT('',(20.09998774,143.19998792,0.E+000));
#817 = VERTEX_POINT('',#818);
#818 = CARTESIAN_POINT('',(20.09998774,143.19998792,1.64592));
#819 = SEAM_CURVE('',#820,(#824,#836),.PCURVE_S1.);
#820 = LINE('',#821,#822);
#821 = CARTESIAN_POINT('',(20.09998774,143.19998792,0.E+000));
#822 = VECTOR('',#823,1.);
#823 = DIRECTION('',(0.E+000,0.E+000,1.));
#824 = PCURVE('',#825,#830);
#825 = CYLINDRICAL_SURFACE('',#826,1.59999934);
#826 = AXIS2_PLACEMENT_3D('',#827,#828,#829);
#827 = CARTESIAN_POINT('',(18.4999884,143.19998792,0.E+000));
#828 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#829 = DIRECTION('',(1.,0.E+000,-0.E+000));
#830 = DEFINITIONAL_REPRESENTATION('',(#831),#835);
#831 = LINE('',#832,#833);
#832 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#833 = VECTOR('',#834,1.);
#834 = DIRECTION('',(-0.E+000,-1.));
#835 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#836 = PCURVE('',#825,#837);
#837 = DEFINITIONAL_REPRESENTATION('',(#838),#842);
#838 = LINE('',#839,#840);
#839 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#840 = VECTOR('',#841,1.);
#841 = DIRECTION('',(-0.E+000,-1.));
#842 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#843 = ORIENTED_EDGE('',*,*,#844,.T.);
#844 = EDGE_CURVE('',#817,#817,#845,.T.);
#845 = SURFACE_CURVE('',#846,(#851,#858),.PCURVE_S1.);
#846 = CIRCLE('',#847,1.59999934);
#847 = AXIS2_PLACEMENT_3D('',#848,#849,#850);
#848 = CARTESIAN_POINT('',(18.4999884,143.19998792,1.64592));
#849 = DIRECTION('',(0.E+000,0.E+000,1.));
#850 = DIRECTION('',(1.,0.E+000,-0.E+000));
#851 = PCURVE('',#825,#852);
#852 = DEFINITIONAL_REPRESENTATION('',(#853),#857);
#853 = LINE('',#854,#855);
#854 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#855 = VECTOR('',#856,1.);
#856 = DIRECTION('',(-1.,0.E+000));
#857 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#858 = PCURVE('',#83,#859);
#859 = DEFINITIONAL_REPRESENTATION('',(#860),#864);
#860 = CIRCLE('',#861,1.59999934);
#861 = AXIS2_PLACEMENT_2D('',#862,#863);
#862 = CARTESIAN_POINT('',(18.4999884,143.19998792));
#863 = DIRECTION('',(1.,0.E+000));
#864 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#865 = ORIENTED_EDGE('',*,*,#814,.F.);
#866 = ORIENTED_EDGE('',*,*,#867,.F.);
#867 = EDGE_CURVE('',#815,#815,#868,.T.);
#868 = SURFACE_CURVE('',#869,(#874,#881),.PCURVE_S1.);
#869 = CIRCLE('',#870,1.59999934);
#870 = AXIS2_PLACEMENT_3D('',#871,#872,#873);
#871 = CARTESIAN_POINT('',(18.4999884,143.19998792,0.E+000));
#872 = DIRECTION('',(0.E+000,0.E+000,1.));
#873 = DIRECTION('',(1.,0.E+000,-0.E+000));
#874 = PCURVE('',#825,#875);
#875 = DEFINITIONAL_REPRESENTATION('',(#876),#880);
#876 = LINE('',#877,#878);
#877 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#878 = VECTOR('',#879,1.);
#879 = DIRECTION('',(-1.,0.E+000));
#880 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#881 = PCURVE('',#137,#882);
#882 = DEFINITIONAL_REPRESENTATION('',(#883),#887);
#883 = CIRCLE('',#884,1.59999934);
#884 = AXIS2_PLACEMENT_2D('',#885,#886);
#885 = CARTESIAN_POINT('',(18.4999884,143.19998792));
#886 = DIRECTION('',(1.,0.E+000));
#887 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#888 = ADVANCED_FACE('',(#889),#903,.T.);
#889 = FACE_BOUND('',#890,.F.);
#890 = EDGE_LOOP('',(#891,#921,#943,#944));
#891 = ORIENTED_EDGE('',*,*,#892,.T.);
#892 = EDGE_CURVE('',#893,#895,#897,.T.);
#893 = VERTEX_POINT('',#894);
#894 = CARTESIAN_POINT('',(113.09998716,93.19998886,0.E+000));
#895 = VERTEX_POINT('',#896);
#896 = CARTESIAN_POINT('',(113.09998716,93.19998886,1.64592));
#897 = SEAM_CURVE('',#898,(#902,#914),.PCURVE_S1.);
#898 = LINE('',#899,#900);
#899 = CARTESIAN_POINT('',(113.09998716,93.19998886,0.E+000));
#900 = VECTOR('',#901,1.);
#901 = DIRECTION('',(0.E+000,0.E+000,1.));
#902 = PCURVE('',#903,#908);
#903 = CYLINDRICAL_SURFACE('',#904,1.59999934);
#904 = AXIS2_PLACEMENT_3D('',#905,#906,#907);
#905 = CARTESIAN_POINT('',(111.49998782,93.19998886,0.E+000));
#906 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#907 = DIRECTION('',(1.,0.E+000,-0.E+000));
#908 = DEFINITIONAL_REPRESENTATION('',(#909),#913);
#909 = LINE('',#910,#911);
#910 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#911 = VECTOR('',#912,1.);
#912 = DIRECTION('',(-0.E+000,-1.));
#913 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#914 = PCURVE('',#903,#915);
#915 = DEFINITIONAL_REPRESENTATION('',(#916),#920);
#916 = LINE('',#917,#918);
#917 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#918 = VECTOR('',#919,1.);
#919 = DIRECTION('',(-0.E+000,-1.));
#920 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#921 = ORIENTED_EDGE('',*,*,#922,.T.);
#922 = EDGE_CURVE('',#895,#895,#923,.T.);
#923 = SURFACE_CURVE('',#924,(#929,#936),.PCURVE_S1.);
#924 = CIRCLE('',#925,1.59999934);
#925 = AXIS2_PLACEMENT_3D('',#926,#927,#928);
#926 = CARTESIAN_POINT('',(111.49998782,93.19998886,1.64592));
#927 = DIRECTION('',(0.E+000,0.E+000,1.));
#928 = DIRECTION('',(1.,0.E+000,-0.E+000));
#929 = PCURVE('',#903,#930);
#930 = DEFINITIONAL_REPRESENTATION('',(#931),#935);
#931 = LINE('',#932,#933);
#932 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#933 = VECTOR('',#934,1.);
#934 = DIRECTION('',(-1.,0.E+000));
#935 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#936 = PCURVE('',#83,#937);
#937 = DEFINITIONAL_REPRESENTATION('',(#938),#942);
#938 = CIRCLE('',#939,1.59999934);
#939 = AXIS2_PLACEMENT_2D('',#940,#941);
#940 = CARTESIAN_POINT('',(111.49998782,93.19998886));
#941 = DIRECTION('',(1.,0.E+000));
#942 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#943 = ORIENTED_EDGE('',*,*,#892,.F.);
#944 = ORIENTED_EDGE('',*,*,#945,.F.);
#945 = EDGE_CURVE('',#893,#893,#946,.T.);
#946 = SURFACE_CURVE('',#947,(#952,#959),.PCURVE_S1.);
#947 = CIRCLE('',#948,1.59999934);
#948 = AXIS2_PLACEMENT_3D('',#949,#950,#951);
#949 = CARTESIAN_POINT('',(111.49998782,93.19998886,0.E+000));
#950 = DIRECTION('',(0.E+000,0.E+000,1.));
#951 = DIRECTION('',(1.,0.E+000,-0.E+000));
#952 = PCURVE('',#903,#953);
#953 = DEFINITIONAL_REPRESENTATION('',(#954),#958);
#954 = LINE('',#955,#956);
#955 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#956 = VECTOR('',#957,1.);
#957 = DIRECTION('',(-1.,0.E+000));
#958 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#959 = PCURVE('',#137,#960);
#960 = DEFINITIONAL_REPRESENTATION('',(#961),#965);
#961 = CIRCLE('',#962,1.59999934);
#962 = AXIS2_PLACEMENT_2D('',#963,#964);
#963 = CARTESIAN_POINT('',(111.49998782,93.19998886));
#964 = DIRECTION('',(1.,0.E+000));
#965 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#966 = ADVANCED_FACE('',(#967),#981,.T.);
#967 = FACE_BOUND('',#968,.F.);
#968 = EDGE_LOOP('',(#969,#999,#1021,#1022));
#969 = ORIENTED_EDGE('',*,*,#970,.T.);
#970 = EDGE_CURVE('',#971,#973,#975,.T.);
#971 = VERTEX_POINT('',#972);
#972 = CARTESIAN_POINT('',(124.99999892,12.5000004,0.E+000));
#973 = VERTEX_POINT('',#974);
#974 = CARTESIAN_POINT('',(124.99999892,12.5000004,1.64592));
#975 = SEAM_CURVE('',#976,(#980,#992),.PCURVE_S1.);
#976 = LINE('',#977,#978);
#977 = CARTESIAN_POINT('',(124.99999892,12.5000004,0.E+000));
#978 = VECTOR('',#979,1.);
#979 = DIRECTION('',(0.E+000,0.E+000,1.));
#980 = PCURVE('',#981,#986);
#981 = CYLINDRICAL_SURFACE('',#982,1.59999934);
#982 = AXIS2_PLACEMENT_3D('',#983,#984,#985);
#983 = CARTESIAN_POINT('',(123.39999958,12.5000004,0.E+000));
#984 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#985 = DIRECTION('',(1.,0.E+000,-0.E+000));
#986 = DEFINITIONAL_REPRESENTATION('',(#987),#991);
#987 = LINE('',#988,#989);
#988 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#989 = VECTOR('',#990,1.);
#990 = DIRECTION('',(-0.E+000,-1.));
#991 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#992 = PCURVE('',#981,#993);
#993 = DEFINITIONAL_REPRESENTATION('',(#994),#998);
#994 = LINE('',#995,#996);
#995 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#996 = VECTOR('',#997,1.);
#997 = DIRECTION('',(-0.E+000,-1.));
#998 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#999 = ORIENTED_EDGE('',*,*,#1000,.T.);
#1000 = EDGE_CURVE('',#973,#973,#1001,.T.);
#1001 = SURFACE_CURVE('',#1002,(#1007,#1014),.PCURVE_S1.);
#1002 = CIRCLE('',#1003,1.59999934);
#1003 = AXIS2_PLACEMENT_3D('',#1004,#1005,#1006);
#1004 = CARTESIAN_POINT('',(123.39999958,12.5000004,1.64592));
#1005 = DIRECTION('',(0.E+000,0.E+000,1.));
#1006 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1007 = PCURVE('',#981,#1008);
#1008 = DEFINITIONAL_REPRESENTATION('',(#1009),#1013);
#1009 = LINE('',#1010,#1011);
#1010 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#1011 = VECTOR('',#1012,1.);
#1012 = DIRECTION('',(-1.,0.E+000));
#1013 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1014 = PCURVE('',#83,#1015);
#1015 = DEFINITIONAL_REPRESENTATION('',(#1016),#1020);
#1016 = CIRCLE('',#1017,1.59999934);
#1017 = AXIS2_PLACEMENT_2D('',#1018,#1019);
#1018 = CARTESIAN_POINT('',(123.39999958,12.5000004));
#1019 = DIRECTION('',(1.,0.E+000));
#1020 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1021 = ORIENTED_EDGE('',*,*,#970,.F.);
#1022 = ORIENTED_EDGE('',*,*,#1023,.F.);
#1023 = EDGE_CURVE('',#971,#971,#1024,.T.);
#1024 = SURFACE_CURVE('',#1025,(#1030,#1037),.PCURVE_S1.);
#1025 = CIRCLE('',#1026,1.59999934);
#1026 = AXIS2_PLACEMENT_3D('',#1027,#1028,#1029);
#1027 = CARTESIAN_POINT('',(123.39999958,12.5000004,0.E+000));
#1028 = DIRECTION('',(0.E+000,0.E+000,1.));
#1029 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1030 = PCURVE('',#981,#1031);
#1031 = DEFINITIONAL_REPRESENTATION('',(#1032),#1036);
#1032 = LINE('',#1033,#1034);
#1033 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1034 = VECTOR('',#1035,1.);
#1035 = DIRECTION('',(-1.,0.E+000));
#1036 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1037 = PCURVE('',#137,#1038);
#1038 = DEFINITIONAL_REPRESENTATION('',(#1039),#1043);
#1039 = CIRCLE('',#1040,1.59999934);
#1040 = AXIS2_PLACEMENT_2D('',#1041,#1042);
#1041 = CARTESIAN_POINT('',(123.39999958,12.5000004));
#1042 = DIRECTION('',(1.,0.E+000));
#1043 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1044 = ADVANCED_FACE('',(#1045),#1059,.T.);
#1045 = FACE_BOUND('',#1046,.F.);
#1046 = EDGE_LOOP('',(#1047,#1077,#1099,#1100));
#1047 = ORIENTED_EDGE('',*,*,#1048,.T.);
#1048 = EDGE_CURVE('',#1049,#1051,#1053,.T.);
#1049 = VERTEX_POINT('',#1050);
#1050 = CARTESIAN_POINT('',(124.99999892,56.7000009,0.E+000));
#1051 = VERTEX_POINT('',#1052);
#1052 = CARTESIAN_POINT('',(124.99999892,56.7000009,1.64592));
#1053 = SEAM_CURVE('',#1054,(#1058,#1070),.PCURVE_S1.);
#1054 = LINE('',#1055,#1056);
#1055 = CARTESIAN_POINT('',(124.99999892,56.7000009,0.E+000));
#1056 = VECTOR('',#1057,1.);
#1057 = DIRECTION('',(0.E+000,0.E+000,1.));
#1058 = PCURVE('',#1059,#1064);
#1059 = CYLINDRICAL_SURFACE('',#1060,1.59999934);
#1060 = AXIS2_PLACEMENT_3D('',#1061,#1062,#1063);
#1061 = CARTESIAN_POINT('',(123.39999958,56.7000009,0.E+000));
#1062 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#1063 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1064 = DEFINITIONAL_REPRESENTATION('',(#1065),#1069);
#1065 = LINE('',#1066,#1067);
#1066 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1067 = VECTOR('',#1068,1.);
#1068 = DIRECTION('',(-0.E+000,-1.));
#1069 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1070 = PCURVE('',#1059,#1071);
#1071 = DEFINITIONAL_REPRESENTATION('',(#1072),#1076);
#1072 = LINE('',#1073,#1074);
#1073 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#1074 = VECTOR('',#1075,1.);
#1075 = DIRECTION('',(-0.E+000,-1.));
#1076 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1077 = ORIENTED_EDGE('',*,*,#1078,.T.);
#1078 = EDGE_CURVE('',#1051,#1051,#1079,.T.);
#1079 = SURFACE_CURVE('',#1080,(#1085,#1092),.PCURVE_S1.);
#1080 = CIRCLE('',#1081,1.59999934);
#1081 = AXIS2_PLACEMENT_3D('',#1082,#1083,#1084);
#1082 = CARTESIAN_POINT('',(123.39999958,56.7000009,1.64592));
#1083 = DIRECTION('',(0.E+000,0.E+000,1.));
#1084 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1085 = PCURVE('',#1059,#1086);
#1086 = DEFINITIONAL_REPRESENTATION('',(#1087),#1091);
#1087 = LINE('',#1088,#1089);
#1088 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#1089 = VECTOR('',#1090,1.);
#1090 = DIRECTION('',(-1.,0.E+000));
#1091 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1092 = PCURVE('',#83,#1093);
#1093 = DEFINITIONAL_REPRESENTATION('',(#1094),#1098);
#1094 = CIRCLE('',#1095,1.59999934);
#1095 = AXIS2_PLACEMENT_2D('',#1096,#1097);
#1096 = CARTESIAN_POINT('',(123.39999958,56.7000009));
#1097 = DIRECTION('',(1.,0.E+000));
#1098 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1099 = ORIENTED_EDGE('',*,*,#1048,.F.);
#1100 = ORIENTED_EDGE('',*,*,#1101,.F.);
#1101 = EDGE_CURVE('',#1049,#1049,#1102,.T.);
#1102 = SURFACE_CURVE('',#1103,(#1108,#1115),.PCURVE_S1.);
#1103 = CIRCLE('',#1104,1.59999934);
#1104 = AXIS2_PLACEMENT_3D('',#1105,#1106,#1107);
#1105 = CARTESIAN_POINT('',(123.39999958,56.7000009,0.E+000));
#1106 = DIRECTION('',(0.E+000,0.E+000,1.));
#1107 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1108 = PCURVE('',#1059,#1109);
#1109 = DEFINITIONAL_REPRESENTATION('',(#1110),#1114);
#1110 = LINE('',#1111,#1112);
#1111 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1112 = VECTOR('',#1113,1.);
#1113 = DIRECTION('',(-1.,0.E+000));
#1114 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1115 = PCURVE('',#137,#1116);
#1116 = DEFINITIONAL_REPRESENTATION('',(#1117),#1121);
#1117 = CIRCLE('',#1118,1.59999934);
#1118 = AXIS2_PLACEMENT_2D('',#1119,#1120);
#1119 = CARTESIAN_POINT('',(123.39999958,56.7000009));
#1120 = DIRECTION('',(1.,0.E+000));
#1121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1122 = ADVANCED_FACE('',(#1123),#1137,.T.);
#1123 = FACE_BOUND('',#1124,.F.);
#1124 = EDGE_LOOP('',(#1125,#1155,#1177,#1178));
#1125 = ORIENTED_EDGE('',*,*,#1126,.T.);
#1126 = EDGE_CURVE('',#1127,#1129,#1131,.T.);
#1127 = VERTEX_POINT('',#1128);
#1128 = CARTESIAN_POINT('',(77.0999855,93.19998886,0.E+000));
#1129 = VERTEX_POINT('',#1130);
#1130 = CARTESIAN_POINT('',(77.0999855,93.19998886,1.64592));
#1131 = SEAM_CURVE('',#1132,(#1136,#1148),.PCURVE_S1.);
#1132 = LINE('',#1133,#1134);
#1133 = CARTESIAN_POINT('',(77.0999855,93.19998886,0.E+000));
#1134 = VECTOR('',#1135,1.);
#1135 = DIRECTION('',(0.E+000,0.E+000,1.));
#1136 = PCURVE('',#1137,#1142);
#1137 = CYLINDRICAL_SURFACE('',#1138,1.59999934);
#1138 = AXIS2_PLACEMENT_3D('',#1139,#1140,#1141);
#1139 = CARTESIAN_POINT('',(75.49998616,93.19998886,0.E+000));
#1140 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#1141 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1142 = DEFINITIONAL_REPRESENTATION('',(#1143),#1147);
#1143 = LINE('',#1144,#1145);
#1144 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1145 = VECTOR('',#1146,1.);
#1146 = DIRECTION('',(-0.E+000,-1.));
#1147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1148 = PCURVE('',#1137,#1149);
#1149 = DEFINITIONAL_REPRESENTATION('',(#1150),#1154);
#1150 = LINE('',#1151,#1152);
#1151 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#1152 = VECTOR('',#1153,1.);
#1153 = DIRECTION('',(-0.E+000,-1.));
#1154 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1155 = ORIENTED_EDGE('',*,*,#1156,.T.);
#1156 = EDGE_CURVE('',#1129,#1129,#1157,.T.);
#1157 = SURFACE_CURVE('',#1158,(#1163,#1170),.PCURVE_S1.);
#1158 = CIRCLE('',#1159,1.59999934);
#1159 = AXIS2_PLACEMENT_3D('',#1160,#1161,#1162);
#1160 = CARTESIAN_POINT('',(75.49998616,93.19998886,1.64592));
#1161 = DIRECTION('',(0.E+000,0.E+000,1.));
#1162 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1163 = PCURVE('',#1137,#1164);
#1164 = DEFINITIONAL_REPRESENTATION('',(#1165),#1169);
#1165 = LINE('',#1166,#1167);
#1166 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#1167 = VECTOR('',#1168,1.);
#1168 = DIRECTION('',(-1.,0.E+000));
#1169 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1170 = PCURVE('',#83,#1171);
#1171 = DEFINITIONAL_REPRESENTATION('',(#1172),#1176);
#1172 = CIRCLE('',#1173,1.59999934);
#1173 = AXIS2_PLACEMENT_2D('',#1174,#1175);
#1174 = CARTESIAN_POINT('',(75.49998616,93.19998886));
#1175 = DIRECTION('',(1.,0.E+000));
#1176 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1177 = ORIENTED_EDGE('',*,*,#1126,.F.);
#1178 = ORIENTED_EDGE('',*,*,#1179,.F.);
#1179 = EDGE_CURVE('',#1127,#1127,#1180,.T.);
#1180 = SURFACE_CURVE('',#1181,(#1186,#1193),.PCURVE_S1.);
#1181 = CIRCLE('',#1182,1.59999934);
#1182 = AXIS2_PLACEMENT_3D('',#1183,#1184,#1185);
#1183 = CARTESIAN_POINT('',(75.49998616,93.19998886,0.E+000));
#1184 = DIRECTION('',(0.E+000,0.E+000,1.));
#1185 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1186 = PCURVE('',#1137,#1187);
#1187 = DEFINITIONAL_REPRESENTATION('',(#1188),#1192);
#1188 = LINE('',#1189,#1190);
#1189 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1190 = VECTOR('',#1191,1.);
#1191 = DIRECTION('',(-1.,0.E+000));
#1192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1193 = PCURVE('',#137,#1194);
#1194 = DEFINITIONAL_REPRESENTATION('',(#1195),#1199);
#1195 = CIRCLE('',#1196,1.59999934);
#1196 = AXIS2_PLACEMENT_2D('',#1197,#1198);
#1197 = CARTESIAN_POINT('',(75.49998616,93.19998886));
#1198 = DIRECTION('',(1.,0.E+000));
#1199 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1200 = ADVANCED_FACE('',(#1201),#1215,.T.);
#1201 = FACE_BOUND('',#1202,.F.);
#1202 = EDGE_LOOP('',(#1203,#1233,#1255,#1256));
#1203 = ORIENTED_EDGE('',*,*,#1204,.T.);
#1204 = EDGE_CURVE('',#1205,#1207,#1209,.T.);
#1205 = VERTEX_POINT('',#1206);
#1206 = CARTESIAN_POINT('',(113.09998716,143.19998792,0.E+000));
#1207 = VERTEX_POINT('',#1208);
#1208 = CARTESIAN_POINT('',(113.09998716,143.19998792,1.64592));
#1209 = SEAM_CURVE('',#1210,(#1214,#1226),.PCURVE_S1.);
#1210 = LINE('',#1211,#1212);
#1211 = CARTESIAN_POINT('',(113.09998716,143.19998792,0.E+000));
#1212 = VECTOR('',#1213,1.);
#1213 = DIRECTION('',(0.E+000,0.E+000,1.));
#1214 = PCURVE('',#1215,#1220);
#1215 = CYLINDRICAL_SURFACE('',#1216,1.59999934);
#1216 = AXIS2_PLACEMENT_3D('',#1217,#1218,#1219);
#1217 = CARTESIAN_POINT('',(111.49998782,143.19998792,0.E+000));
#1218 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#1219 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1220 = DEFINITIONAL_REPRESENTATION('',(#1221),#1225);
#1221 = LINE('',#1222,#1223);
#1222 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1223 = VECTOR('',#1224,1.);
#1224 = DIRECTION('',(-0.E+000,-1.));
#1225 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1226 = PCURVE('',#1215,#1227);
#1227 = DEFINITIONAL_REPRESENTATION('',(#1228),#1232);
#1228 = LINE('',#1229,#1230);
#1229 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#1230 = VECTOR('',#1231,1.);
#1231 = DIRECTION('',(-0.E+000,-1.));
#1232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1233 = ORIENTED_EDGE('',*,*,#1234,.T.);
#1234 = EDGE_CURVE('',#1207,#1207,#1235,.T.);
#1235 = SURFACE_CURVE('',#1236,(#1241,#1248),.PCURVE_S1.);
#1236 = CIRCLE('',#1237,1.59999934);
#1237 = AXIS2_PLACEMENT_3D('',#1238,#1239,#1240);
#1238 = CARTESIAN_POINT('',(111.49998782,143.19998792,1.64592));
#1239 = DIRECTION('',(0.E+000,0.E+000,1.));
#1240 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1241 = PCURVE('',#1215,#1242);
#1242 = DEFINITIONAL_REPRESENTATION('',(#1243),#1247);
#1243 = LINE('',#1244,#1245);
#1244 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#1245 = VECTOR('',#1246,1.);
#1246 = DIRECTION('',(-1.,0.E+000));
#1247 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1248 = PCURVE('',#83,#1249);
#1249 = DEFINITIONAL_REPRESENTATION('',(#1250),#1254);
#1250 = CIRCLE('',#1251,1.59999934);
#1251 = AXIS2_PLACEMENT_2D('',#1252,#1253);
#1252 = CARTESIAN_POINT('',(111.49998782,143.19998792));
#1253 = DIRECTION('',(1.,0.E+000));
#1254 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1255 = ORIENTED_EDGE('',*,*,#1204,.F.);
#1256 = ORIENTED_EDGE('',*,*,#1257,.F.);
#1257 = EDGE_CURVE('',#1205,#1205,#1258,.T.);
#1258 = SURFACE_CURVE('',#1259,(#1264,#1271),.PCURVE_S1.);
#1259 = CIRCLE('',#1260,1.59999934);
#1260 = AXIS2_PLACEMENT_3D('',#1261,#1262,#1263);
#1261 = CARTESIAN_POINT('',(111.49998782,143.19998792,0.E+000));
#1262 = DIRECTION('',(0.E+000,0.E+000,1.));
#1263 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1264 = PCURVE('',#1215,#1265);
#1265 = DEFINITIONAL_REPRESENTATION('',(#1266),#1270);
#1266 = LINE('',#1267,#1268);
#1267 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1268 = VECTOR('',#1269,1.);
#1269 = DIRECTION('',(-1.,0.E+000));
#1270 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1271 = PCURVE('',#137,#1272);
#1272 = DEFINITIONAL_REPRESENTATION('',(#1273),#1277);
#1273 = CIRCLE('',#1274,1.59999934);
#1274 = AXIS2_PLACEMENT_2D('',#1275,#1276);
#1275 = CARTESIAN_POINT('',(111.49998782,143.19998792));
#1276 = DIRECTION('',(1.,0.E+000));
#1277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1278 = ADVANCED_FACE('',(#1279),#1293,.T.);
#1279 = FACE_BOUND('',#1280,.F.);
#1280 = EDGE_LOOP('',(#1281,#1311,#1333,#1334));
#1281 = ORIENTED_EDGE('',*,*,#1282,.T.);
#1282 = EDGE_CURVE('',#1283,#1285,#1287,.T.);
#1283 = VERTEX_POINT('',#1284);
#1284 = CARTESIAN_POINT('',(124.99999892,101.100001,0.E+000));
#1285 = VERTEX_POINT('',#1286);
#1286 = CARTESIAN_POINT('',(124.99999892,101.100001,1.64592));
#1287 = SEAM_CURVE('',#1288,(#1292,#1304),.PCURVE_S1.);
#1288 = LINE('',#1289,#1290);
#1289 = CARTESIAN_POINT('',(124.99999892,101.100001,0.E+000));
#1290 = VECTOR('',#1291,1.);
#1291 = DIRECTION('',(0.E+000,0.E+000,1.));
#1292 = PCURVE('',#1293,#1298);
#1293 = CYLINDRICAL_SURFACE('',#1294,1.59999934);
#1294 = AXIS2_PLACEMENT_3D('',#1295,#1296,#1297);
#1295 = CARTESIAN_POINT('',(123.39999958,101.100001,0.E+000));
#1296 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#1297 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1298 = DEFINITIONAL_REPRESENTATION('',(#1299),#1303);
#1299 = LINE('',#1300,#1301);
#1300 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1301 = VECTOR('',#1302,1.);
#1302 = DIRECTION('',(-0.E+000,-1.));
#1303 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1304 = PCURVE('',#1293,#1305);
#1305 = DEFINITIONAL_REPRESENTATION('',(#1306),#1310);
#1306 = LINE('',#1307,#1308);
#1307 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#1308 = VECTOR('',#1309,1.);
#1309 = DIRECTION('',(-0.E+000,-1.));
#1310 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1311 = ORIENTED_EDGE('',*,*,#1312,.T.);
#1312 = EDGE_CURVE('',#1285,#1285,#1313,.T.);
#1313 = SURFACE_CURVE('',#1314,(#1319,#1326),.PCURVE_S1.);
#1314 = CIRCLE('',#1315,1.59999934);
#1315 = AXIS2_PLACEMENT_3D('',#1316,#1317,#1318);
#1316 = CARTESIAN_POINT('',(123.39999958,101.100001,1.64592));
#1317 = DIRECTION('',(0.E+000,0.E+000,1.));
#1318 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1319 = PCURVE('',#1293,#1320);
#1320 = DEFINITIONAL_REPRESENTATION('',(#1321),#1325);
#1321 = LINE('',#1322,#1323);
#1322 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#1323 = VECTOR('',#1324,1.);
#1324 = DIRECTION('',(-1.,0.E+000));
#1325 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1326 = PCURVE('',#83,#1327);
#1327 = DEFINITIONAL_REPRESENTATION('',(#1328),#1332);
#1328 = CIRCLE('',#1329,1.59999934);
#1329 = AXIS2_PLACEMENT_2D('',#1330,#1331);
#1330 = CARTESIAN_POINT('',(123.39999958,101.100001));
#1331 = DIRECTION('',(1.,0.E+000));
#1332 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1333 = ORIENTED_EDGE('',*,*,#1282,.F.);
#1334 = ORIENTED_EDGE('',*,*,#1335,.F.);
#1335 = EDGE_CURVE('',#1283,#1283,#1336,.T.);
#1336 = SURFACE_CURVE('',#1337,(#1342,#1349),.PCURVE_S1.);
#1337 = CIRCLE('',#1338,1.59999934);
#1338 = AXIS2_PLACEMENT_3D('',#1339,#1340,#1341);
#1339 = CARTESIAN_POINT('',(123.39999958,101.100001,0.E+000));
#1340 = DIRECTION('',(0.E+000,0.E+000,1.));
#1341 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1342 = PCURVE('',#1293,#1343);
#1343 = DEFINITIONAL_REPRESENTATION('',(#1344),#1348);
#1344 = LINE('',#1345,#1346);
#1345 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1346 = VECTOR('',#1347,1.);
#1347 = DIRECTION('',(-1.,0.E+000));
#1348 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1349 = PCURVE('',#137,#1350);
#1350 = DEFINITIONAL_REPRESENTATION('',(#1351),#1355);
#1351 = CIRCLE('',#1352,1.59999934);
#1352 = AXIS2_PLACEMENT_2D('',#1353,#1354);
#1353 = CARTESIAN_POINT('',(123.39999958,101.100001));
#1354 = DIRECTION('',(1.,0.E+000));
#1355 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1356 = ADVANCED_FACE('',(#1357),#1371,.T.);
#1357 = FACE_BOUND('',#1358,.F.);
#1358 = EDGE_LOOP('',(#1359,#1389,#1411,#1412));
#1359 = ORIENTED_EDGE('',*,*,#1360,.T.);
#1360 = EDGE_CURVE('',#1361,#1363,#1365,.T.);
#1361 = VERTEX_POINT('',#1362);
#1362 = CARTESIAN_POINT('',(124.99999892,145.29999896,0.E+000));
#1363 = VERTEX_POINT('',#1364);
#1364 = CARTESIAN_POINT('',(124.99999892,145.29999896,1.64592));
#1365 = SEAM_CURVE('',#1366,(#1370,#1382),.PCURVE_S1.);
#1366 = LINE('',#1367,#1368);
#1367 = CARTESIAN_POINT('',(124.99999892,145.29999896,0.E+000));
#1368 = VECTOR('',#1369,1.);
#1369 = DIRECTION('',(0.E+000,0.E+000,1.));
#1370 = PCURVE('',#1371,#1376);
#1371 = CYLINDRICAL_SURFACE('',#1372,1.59999934);
#1372 = AXIS2_PLACEMENT_3D('',#1373,#1374,#1375);
#1373 = CARTESIAN_POINT('',(123.39999958,145.29999896,0.E+000));
#1374 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#1375 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1376 = DEFINITIONAL_REPRESENTATION('',(#1377),#1381);
#1377 = LINE('',#1378,#1379);
#1378 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1379 = VECTOR('',#1380,1.);
#1380 = DIRECTION('',(-0.E+000,-1.));
#1381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1382 = PCURVE('',#1371,#1383);
#1383 = DEFINITIONAL_REPRESENTATION('',(#1384),#1388);
#1384 = LINE('',#1385,#1386);
#1385 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#1386 = VECTOR('',#1387,1.);
#1387 = DIRECTION('',(-0.E+000,-1.));
#1388 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1389 = ORIENTED_EDGE('',*,*,#1390,.T.);
#1390 = EDGE_CURVE('',#1363,#1363,#1391,.T.);
#1391 = SURFACE_CURVE('',#1392,(#1397,#1404),.PCURVE_S1.);
#1392 = CIRCLE('',#1393,1.59999934);
#1393 = AXIS2_PLACEMENT_3D('',#1394,#1395,#1396);
#1394 = CARTESIAN_POINT('',(123.39999958,145.29999896,1.64592));
#1395 = DIRECTION('',(0.E+000,0.E+000,1.));
#1396 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1397 = PCURVE('',#1371,#1398);
#1398 = DEFINITIONAL_REPRESENTATION('',(#1399),#1403);
#1399 = LINE('',#1400,#1401);
#1400 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#1401 = VECTOR('',#1402,1.);
#1402 = DIRECTION('',(-1.,0.E+000));
#1403 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1404 = PCURVE('',#83,#1405);
#1405 = DEFINITIONAL_REPRESENTATION('',(#1406),#1410);
#1406 = CIRCLE('',#1407,1.59999934);
#1407 = AXIS2_PLACEMENT_2D('',#1408,#1409);
#1408 = CARTESIAN_POINT('',(123.39999958,145.29999896));
#1409 = DIRECTION('',(1.,0.E+000));
#1410 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1411 = ORIENTED_EDGE('',*,*,#1360,.F.);
#1412 = ORIENTED_EDGE('',*,*,#1413,.F.);
#1413 = EDGE_CURVE('',#1361,#1361,#1414,.T.);
#1414 = SURFACE_CURVE('',#1415,(#1420,#1427),.PCURVE_S1.);
#1415 = CIRCLE('',#1416,1.59999934);
#1416 = AXIS2_PLACEMENT_3D('',#1417,#1418,#1419);
#1417 = CARTESIAN_POINT('',(123.39999958,145.29999896,0.E+000));
#1418 = DIRECTION('',(0.E+000,0.E+000,1.));
#1419 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1420 = PCURVE('',#1371,#1421);
#1421 = DEFINITIONAL_REPRESENTATION('',(#1422),#1426);
#1422 = LINE('',#1423,#1424);
#1423 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1424 = VECTOR('',#1425,1.);
#1425 = DIRECTION('',(-1.,0.E+000));
#1426 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1427 = PCURVE('',#137,#1428);
#1428 = DEFINITIONAL_REPRESENTATION('',(#1429),#1433);
#1429 = CIRCLE('',#1430,1.59999934);
#1430 = AXIS2_PLACEMENT_2D('',#1431,#1432);
#1431 = CARTESIAN_POINT('',(123.39999958,145.29999896));
#1432 = DIRECTION('',(1.,0.E+000));
#1433 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1434 = ADVANCED_FACE('',(#1435,#1441,#1444,#1447,#1450,#1453,#1456,
    #1459,#1462,#1465,#1468,#1471,#1474,#1477,#1480),#137,.F.);
#1435 = FACE_BOUND('',#1436,.T.);
#1436 = EDGE_LOOP('',(#1437,#1438,#1439,#1440));
#1437 = ORIENTED_EDGE('',*,*,#123,.T.);
#1438 = ORIENTED_EDGE('',*,*,#204,.T.);
#1439 = ORIENTED_EDGE('',*,*,#275,.T.);
#1440 = ORIENTED_EDGE('',*,*,#322,.T.);
#1441 = FACE_BOUND('',#1442,.F.);
#1442 = EDGE_LOOP('',(#1443));
#1443 = ORIENTED_EDGE('',*,*,#399,.T.);
#1444 = FACE_BOUND('',#1445,.F.);
#1445 = EDGE_LOOP('',(#1446));
#1446 = ORIENTED_EDGE('',*,*,#477,.T.);
#1447 = FACE_BOUND('',#1448,.F.);
#1448 = EDGE_LOOP('',(#1449));
#1449 = ORIENTED_EDGE('',*,*,#555,.T.);
#1450 = FACE_BOUND('',#1451,.F.);
#1451 = EDGE_LOOP('',(#1452));
#1452 = ORIENTED_EDGE('',*,*,#633,.T.);
#1453 = FACE_BOUND('',#1454,.F.);
#1454 = EDGE_LOOP('',(#1455));
#1455 = ORIENTED_EDGE('',*,*,#711,.T.);
#1456 = FACE_BOUND('',#1457,.F.);
#1457 = EDGE_LOOP('',(#1458));
#1458 = ORIENTED_EDGE('',*,*,#789,.T.);
#1459 = FACE_BOUND('',#1460,.F.);
#1460 = EDGE_LOOP('',(#1461));
#1461 = ORIENTED_EDGE('',*,*,#867,.T.);
#1462 = FACE_BOUND('',#1463,.F.);
#1463 = EDGE_LOOP('',(#1464));
#1464 = ORIENTED_EDGE('',*,*,#945,.T.);
#1465 = FACE_BOUND('',#1466,.F.);
#1466 = EDGE_LOOP('',(#1467));
#1467 = ORIENTED_EDGE('',*,*,#1023,.T.);
#1468 = FACE_BOUND('',#1469,.F.);
#1469 = EDGE_LOOP('',(#1470));
#1470 = ORIENTED_EDGE('',*,*,#1101,.T.);
#1471 = FACE_BOUND('',#1472,.F.);
#1472 = EDGE_LOOP('',(#1473));
#1473 = ORIENTED_EDGE('',*,*,#1179,.T.);
#1474 = FACE_BOUND('',#1475,.F.);
#1475 = EDGE_LOOP('',(#1476));
#1476 = ORIENTED_EDGE('',*,*,#1257,.T.);
#1477 = FACE_BOUND('',#1478,.F.);
#1478 = EDGE_LOOP('',(#1479));
#1479 = ORIENTED_EDGE('',*,*,#1335,.T.);
#1480 = FACE_BOUND('',#1481,.F.);
#1481 = EDGE_LOOP('',(#1482));
#1482 = ORIENTED_EDGE('',*,*,#1413,.T.);
#1483 = ADVANCED_FACE('',(#1484,#1490,#1493,#1496,#1499,#1502,#1505,
    #1508,#1511,#1514,#1517,#1520,#1523,#1526,#1529),#83,.T.);
#1484 = FACE_BOUND('',#1485,.F.);
#1485 = EDGE_LOOP('',(#1486,#1487,#1488,#1489));
#1486 = ORIENTED_EDGE('',*,*,#67,.T.);
#1487 = ORIENTED_EDGE('',*,*,#153,.T.);
#1488 = ORIENTED_EDGE('',*,*,#229,.T.);
#1489 = ORIENTED_EDGE('',*,*,#300,.T.);
#1490 = FACE_BOUND('',#1491,.T.);
#1491 = EDGE_LOOP('',(#1492));
#1492 = ORIENTED_EDGE('',*,*,#376,.T.);
#1493 = FACE_BOUND('',#1494,.T.);
#1494 = EDGE_LOOP('',(#1495));
#1495 = ORIENTED_EDGE('',*,*,#454,.T.);
#1496 = FACE_BOUND('',#1497,.T.);
#1497 = EDGE_LOOP('',(#1498));
#1498 = ORIENTED_EDGE('',*,*,#532,.T.);
#1499 = FACE_BOUND('',#1500,.T.);
#1500 = EDGE_LOOP('',(#1501));
#1501 = ORIENTED_EDGE('',*,*,#610,.T.);
#1502 = FACE_BOUND('',#1503,.T.);
#1503 = EDGE_LOOP('',(#1504));
#1504 = ORIENTED_EDGE('',*,*,#688,.T.);
#1505 = FACE_BOUND('',#1506,.T.);
#1506 = EDGE_LOOP('',(#1507));
#1507 = ORIENTED_EDGE('',*,*,#766,.T.);
#1508 = FACE_BOUND('',#1509,.T.);
#1509 = EDGE_LOOP('',(#1510));
#1510 = ORIENTED_EDGE('',*,*,#844,.T.);
#1511 = FACE_BOUND('',#1512,.T.);
#1512 = EDGE_LOOP('',(#1513));
#1513 = ORIENTED_EDGE('',*,*,#922,.T.);
#1514 = FACE_BOUND('',#1515,.T.);
#1515 = EDGE_LOOP('',(#1516));
#1516 = ORIENTED_EDGE('',*,*,#1000,.T.);
#1517 = FACE_BOUND('',#1518,.T.);
#1518 = EDGE_LOOP('',(#1519));
#1519 = ORIENTED_EDGE('',*,*,#1078,.T.);
#1520 = FACE_BOUND('',#1521,.T.);
#1521 = EDGE_LOOP('',(#1522));
#1522 = ORIENTED_EDGE('',*,*,#1156,.T.);
#1523 = FACE_BOUND('',#1524,.T.);
#1524 = EDGE_LOOP('',(#1525));
#1525 = ORIENTED_EDGE('',*,*,#1234,.T.);
#1526 = FACE_BOUND('',#1527,.T.);
#1527 = EDGE_LOOP('',(#1528));
#1528 = ORIENTED_EDGE('',*,*,#1312,.T.);
#1529 = FACE_BOUND('',#1530,.T.);
#1530 = EDGE_LOOP('',(#1531));
#1531 = ORIENTED_EDGE('',*,*,#1390,.T.);
#1532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1536)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1533,#1534,#1535)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1533 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1534 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1535 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1536 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#1533,
  'distance_accuracy_value','confusion accuracy');
#1537 = SHAPE_DEFINITION_REPRESENTATION(#1538,#25);
#1538 = PRODUCT_DEFINITION_SHAPE('','',#1539);
#1539 = PRODUCT_DEFINITION('design','',#1540,#1543);
#1540 = PRODUCT_DEFINITION_FORMATION('','',#1541);
#1541 = PRODUCT('Board','Board','',(#1542));
#1542 = PRODUCT_CONTEXT('',#2,'mechanical');
#1543 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1544 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1545,#1547);
#1545 = ( REPRESENTATION_RELATIONSHIP('','',#25,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1546) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1546 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#1547 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1548);
#1548 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('2','=>[0:1:1:2]','',#5,#1539,$);
#1549 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1541));
#1550 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1551),#1532);
#1551 = STYLED_ITEM('color',(#1552),#26);
#1552 = PRESENTATION_STYLE_ASSIGNMENT((#1553,#1559));
#1553 = SURFACE_STYLE_USAGE(.BOTH.,#1554);
#1554 = SURFACE_SIDE_STYLE('',(#1555));
#1555 = SURFACE_STYLE_FILL_AREA(#1556);
#1556 = FILL_AREA_STYLE('',(#1557));
#1557 = FILL_AREA_STYLE_COLOUR('',#1558);
#1558 = COLOUR_RGB('',0.E+000,0.501960813999,0.E+000);
#1559 = CURVE_STYLE('',#1560,POSITIVE_LENGTH_MEASURE(0.1),#1558);
#1560 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
