ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2018-12-14T15:17:03',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 6.5','Open CASCADE 6.5'
  ,'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('PCB','PCB','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15),#19);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#13 = DIRECTION('',(0.E+000,0.E+000,1.));
#14 = DIRECTION('',(1.,0.E+000,-0.E+000));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.E+000,0.E+000,-1.64592));
#17 = DIRECTION('',(0.E+000,0.E+000,1.));
#18 = DIRECTION('',(1.,0.E+000,-0.E+000));
#19 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#23)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#20,#21,#22)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#20 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#21 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#22 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#23 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#20,
  'distance_accuracy_value','confusion accuracy');
#24 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#25 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#26),#3612);
#26 = MANIFOLD_SOLID_BREP('',#27);
#27 = CLOSED_SHELL('',(#28,#148,#224,#295,#342,#420,#530,#606,#682,#758,
    #834,#910,#986,#1062,#1138,#1214,#1290,#1366,#1442,#1518,#1594,#1670
    ,#1746,#1822,#1898,#1974,#2050,#2126,#2202,#2278,#2354,#2430,#2506,
    #2582,#2658,#2734,#2805,#2852,#2930,#3008,#3086,#3164,#3242,#3320,
    #3398,#3476,#3544));
#28 = ADVANCED_FACE('',(#29),#43,.T.);
#29 = FACE_BOUND('',#30,.T.);
#30 = EDGE_LOOP('',(#31,#66,#94,#122));
#31 = ORIENTED_EDGE('',*,*,#32,.T.);
#32 = EDGE_CURVE('',#33,#35,#37,.T.);
#33 = VERTEX_POINT('',#34);
#34 = CARTESIAN_POINT('',(1.27E-005,1.27E-005,0.E+000));
#35 = VERTEX_POINT('',#36);
#36 = CARTESIAN_POINT('',(1.27E-005,1.27E-005,1.64592));
#37 = SURFACE_CURVE('',#38,(#42,#54),.PCURVE_S1.);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(1.27E-005,1.27E-005,0.E+000));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.E+000,0.E+000,1.));
#42 = PCURVE('',#43,#48);
#43 = PLANE('',#44);
#44 = AXIS2_PLACEMENT_3D('',#45,#46,#47);
#45 = CARTESIAN_POINT('',(1.27E-005,1.27E-005,0.E+000));
#46 = DIRECTION('',(-1.,0.E+000,0.E+000));
#47 = DIRECTION('',(0.E+000,1.,0.E+000));
#48 = DEFINITIONAL_REPRESENTATION('',(#49),#53);
#49 = LINE('',#50,#51);
#50 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#51 = VECTOR('',#52,1.);
#52 = DIRECTION('',(0.E+000,-1.));
#53 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#54 = PCURVE('',#55,#60);
#55 = PLANE('',#56);
#56 = AXIS2_PLACEMENT_3D('',#57,#58,#59);
#57 = CARTESIAN_POINT('',(130.00001178,1.27E-005,0.E+000));
#58 = DIRECTION('',(0.E+000,-1.,0.E+000));
#59 = DIRECTION('',(-1.,0.E+000,0.E+000));
#60 = DEFINITIONAL_REPRESENTATION('',(#61),#65);
#61 = LINE('',#62,#63);
#62 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#63 = VECTOR('',#64,1.);
#64 = DIRECTION('',(0.E+000,-1.));
#65 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#66 = ORIENTED_EDGE('',*,*,#67,.T.);
#67 = EDGE_CURVE('',#35,#68,#70,.T.);
#68 = VERTEX_POINT('',#69);
#69 = CARTESIAN_POINT('',(1.27E-005,157.80001206,1.64592));
#70 = SURFACE_CURVE('',#71,(#75,#82),.PCURVE_S1.);
#71 = LINE('',#72,#73);
#72 = CARTESIAN_POINT('',(1.27E-005,1.27E-005,1.64592));
#73 = VECTOR('',#74,1.);
#74 = DIRECTION('',(0.E+000,1.,0.E+000));
#75 = PCURVE('',#43,#76);
#76 = DEFINITIONAL_REPRESENTATION('',(#77),#81);
#77 = LINE('',#78,#79);
#78 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#79 = VECTOR('',#80,1.);
#80 = DIRECTION('',(1.,0.E+000));
#81 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#82 = PCURVE('',#83,#88);
#83 = PLANE('',#84);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(1.27E-005,1.27E-005,1.64592));
#86 = DIRECTION('',(0.E+000,0.E+000,1.));
#87 = DIRECTION('',(1.,0.E+000,-0.E+000));
#88 = DEFINITIONAL_REPRESENTATION('',(#89),#93);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(0.E+000,1.));
#93 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#94 = ORIENTED_EDGE('',*,*,#95,.F.);
#95 = EDGE_CURVE('',#96,#68,#98,.T.);
#96 = VERTEX_POINT('',#97);
#97 = CARTESIAN_POINT('',(1.27E-005,157.80001206,0.E+000));
#98 = SURFACE_CURVE('',#99,(#103,#110),.PCURVE_S1.);
#99 = LINE('',#100,#101);
#100 = CARTESIAN_POINT('',(1.27E-005,157.80001206,0.E+000));
#101 = VECTOR('',#102,1.);
#102 = DIRECTION('',(0.E+000,0.E+000,1.));
#103 = PCURVE('',#43,#104);
#104 = DEFINITIONAL_REPRESENTATION('',(#105),#109);
#105 = LINE('',#106,#107);
#106 = CARTESIAN_POINT('',(157.79999936,0.E+000));
#107 = VECTOR('',#108,1.);
#108 = DIRECTION('',(0.E+000,-1.));
#109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#110 = PCURVE('',#111,#116);
#111 = PLANE('',#112);
#112 = AXIS2_PLACEMENT_3D('',#113,#114,#115);
#113 = CARTESIAN_POINT('',(1.27E-005,157.80001206,0.E+000));
#114 = DIRECTION('',(0.E+000,1.,0.E+000));
#115 = DIRECTION('',(1.,0.E+000,0.E+000));
#116 = DEFINITIONAL_REPRESENTATION('',(#117),#121);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(0.E+000,-1.));
#121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#122 = ORIENTED_EDGE('',*,*,#123,.F.);
#123 = EDGE_CURVE('',#33,#96,#124,.T.);
#124 = SURFACE_CURVE('',#125,(#129,#136),.PCURVE_S1.);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(1.27E-005,1.27E-005,0.E+000));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(0.E+000,1.,0.E+000));
#129 = PCURVE('',#43,#130);
#130 = DEFINITIONAL_REPRESENTATION('',(#131),#135);
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(1.,0.E+000));
#135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#136 = PCURVE('',#137,#142);
#137 = PLANE('',#138);
#138 = AXIS2_PLACEMENT_3D('',#139,#140,#141);
#139 = CARTESIAN_POINT('',(1.27E-005,1.27E-005,0.E+000));
#140 = DIRECTION('',(0.E+000,0.E+000,1.));
#141 = DIRECTION('',(1.,0.E+000,-0.E+000));
#142 = DEFINITIONAL_REPRESENTATION('',(#143),#147);
#143 = LINE('',#144,#145);
#144 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#145 = VECTOR('',#146,1.);
#146 = DIRECTION('',(0.E+000,1.));
#147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#148 = ADVANCED_FACE('',(#149),#111,.T.);
#149 = FACE_BOUND('',#150,.T.);
#150 = EDGE_LOOP('',(#151,#152,#175,#203));
#151 = ORIENTED_EDGE('',*,*,#95,.T.);
#152 = ORIENTED_EDGE('',*,*,#153,.T.);
#153 = EDGE_CURVE('',#68,#154,#156,.T.);
#154 = VERTEX_POINT('',#155);
#155 = CARTESIAN_POINT('',(130.00001178,157.80001206,1.64592));
#156 = SURFACE_CURVE('',#157,(#161,#168),.PCURVE_S1.);
#157 = LINE('',#158,#159);
#158 = CARTESIAN_POINT('',(1.27E-005,157.80001206,1.64592));
#159 = VECTOR('',#160,1.);
#160 = DIRECTION('',(1.,0.E+000,0.E+000));
#161 = PCURVE('',#111,#162);
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(1.,0.E+000));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = PCURVE('',#83,#169);
#169 = DEFINITIONAL_REPRESENTATION('',(#170),#174);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(0.E+000,157.79999936));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(1.,0.E+000));
#174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#175 = ORIENTED_EDGE('',*,*,#176,.F.);
#176 = EDGE_CURVE('',#177,#154,#179,.T.);
#177 = VERTEX_POINT('',#178);
#178 = CARTESIAN_POINT('',(130.00001178,157.80001206,0.E+000));
#179 = SURFACE_CURVE('',#180,(#184,#191),.PCURVE_S1.);
#180 = LINE('',#181,#182);
#181 = CARTESIAN_POINT('',(130.00001178,157.80001206,0.E+000));
#182 = VECTOR('',#183,1.);
#183 = DIRECTION('',(0.E+000,0.E+000,1.));
#184 = PCURVE('',#111,#185);
#185 = DEFINITIONAL_REPRESENTATION('',(#186),#190);
#186 = LINE('',#187,#188);
#187 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#188 = VECTOR('',#189,1.);
#189 = DIRECTION('',(0.E+000,-1.));
#190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#191 = PCURVE('',#192,#197);
#192 = PLANE('',#193);
#193 = AXIS2_PLACEMENT_3D('',#194,#195,#196);
#194 = CARTESIAN_POINT('',(130.00001178,157.80001206,0.E+000));
#195 = DIRECTION('',(1.,0.E+000,-0.E+000));
#196 = DIRECTION('',(0.E+000,-1.,0.E+000));
#197 = DEFINITIONAL_REPRESENTATION('',(#198),#202);
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(0.E+000,-1.));
#202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#203 = ORIENTED_EDGE('',*,*,#204,.F.);
#204 = EDGE_CURVE('',#96,#177,#205,.T.);
#205 = SURFACE_CURVE('',#206,(#210,#217),.PCURVE_S1.);
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(1.27E-005,157.80001206,0.E+000));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(1.,0.E+000,0.E+000));
#210 = PCURVE('',#111,#211);
#211 = DEFINITIONAL_REPRESENTATION('',(#212),#216);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(1.,0.E+000));
#216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#217 = PCURVE('',#137,#218);
#218 = DEFINITIONAL_REPRESENTATION('',(#219),#223);
#219 = LINE('',#220,#221);
#220 = CARTESIAN_POINT('',(0.E+000,157.79999936));
#221 = VECTOR('',#222,1.);
#222 = DIRECTION('',(1.,0.E+000));
#223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#224 = ADVANCED_FACE('',(#225),#192,.T.);
#225 = FACE_BOUND('',#226,.T.);
#226 = EDGE_LOOP('',(#227,#228,#251,#274));
#227 = ORIENTED_EDGE('',*,*,#176,.T.);
#228 = ORIENTED_EDGE('',*,*,#229,.T.);
#229 = EDGE_CURVE('',#154,#230,#232,.T.);
#230 = VERTEX_POINT('',#231);
#231 = CARTESIAN_POINT('',(130.00001178,1.27E-005,1.64592));
#232 = SURFACE_CURVE('',#233,(#237,#244),.PCURVE_S1.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(130.00001178,157.80001206,1.64592));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(0.E+000,-1.,0.E+000));
#237 = PCURVE('',#192,#238);
#238 = DEFINITIONAL_REPRESENTATION('',(#239),#243);
#239 = LINE('',#240,#241);
#240 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#241 = VECTOR('',#242,1.);
#242 = DIRECTION('',(1.,0.E+000));
#243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#244 = PCURVE('',#83,#245);
#245 = DEFINITIONAL_REPRESENTATION('',(#246),#250);
#246 = LINE('',#247,#248);
#247 = CARTESIAN_POINT('',(129.99999908,157.79999936));
#248 = VECTOR('',#249,1.);
#249 = DIRECTION('',(0.E+000,-1.));
#250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#251 = ORIENTED_EDGE('',*,*,#252,.F.);
#252 = EDGE_CURVE('',#253,#230,#255,.T.);
#253 = VERTEX_POINT('',#254);
#254 = CARTESIAN_POINT('',(130.00001178,1.27E-005,0.E+000));
#255 = SURFACE_CURVE('',#256,(#260,#267),.PCURVE_S1.);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(130.00001178,1.27E-005,0.E+000));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(0.E+000,0.E+000,1.));
#260 = PCURVE('',#192,#261);
#261 = DEFINITIONAL_REPRESENTATION('',(#262),#266);
#262 = LINE('',#263,#264);
#263 = CARTESIAN_POINT('',(157.79999936,0.E+000));
#264 = VECTOR('',#265,1.);
#265 = DIRECTION('',(0.E+000,-1.));
#266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#267 = PCURVE('',#55,#268);
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = LINE('',#270,#271);
#270 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#271 = VECTOR('',#272,1.);
#272 = DIRECTION('',(0.E+000,-1.));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = ORIENTED_EDGE('',*,*,#275,.F.);
#275 = EDGE_CURVE('',#177,#253,#276,.T.);
#276 = SURFACE_CURVE('',#277,(#281,#288),.PCURVE_S1.);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(130.00001178,157.80001206,0.E+000));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(0.E+000,-1.,0.E+000));
#281 = PCURVE('',#192,#282);
#282 = DEFINITIONAL_REPRESENTATION('',(#283),#287);
#283 = LINE('',#284,#285);
#284 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#285 = VECTOR('',#286,1.);
#286 = DIRECTION('',(1.,0.E+000));
#287 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#288 = PCURVE('',#137,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#294);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(129.99999908,157.79999936));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(0.E+000,-1.));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = ADVANCED_FACE('',(#296),#55,.T.);
#296 = FACE_BOUND('',#297,.T.);
#297 = EDGE_LOOP('',(#298,#299,#320,#321));
#298 = ORIENTED_EDGE('',*,*,#252,.T.);
#299 = ORIENTED_EDGE('',*,*,#300,.T.);
#300 = EDGE_CURVE('',#230,#35,#301,.T.);
#301 = SURFACE_CURVE('',#302,(#306,#313),.PCURVE_S1.);
#302 = LINE('',#303,#304);
#303 = CARTESIAN_POINT('',(130.00001178,1.27E-005,1.64592));
#304 = VECTOR('',#305,1.);
#305 = DIRECTION('',(-1.,0.E+000,0.E+000));
#306 = PCURVE('',#55,#307);
#307 = DEFINITIONAL_REPRESENTATION('',(#308),#312);
#308 = LINE('',#309,#310);
#309 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#310 = VECTOR('',#311,1.);
#311 = DIRECTION('',(1.,0.E+000));
#312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#313 = PCURVE('',#83,#314);
#314 = DEFINITIONAL_REPRESENTATION('',(#315),#319);
#315 = LINE('',#316,#317);
#316 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#317 = VECTOR('',#318,1.);
#318 = DIRECTION('',(-1.,0.E+000));
#319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#320 = ORIENTED_EDGE('',*,*,#32,.F.);
#321 = ORIENTED_EDGE('',*,*,#322,.F.);
#322 = EDGE_CURVE('',#253,#33,#323,.T.);
#323 = SURFACE_CURVE('',#324,(#328,#335),.PCURVE_S1.);
#324 = LINE('',#325,#326);
#325 = CARTESIAN_POINT('',(130.00001178,1.27E-005,0.E+000));
#326 = VECTOR('',#327,1.);
#327 = DIRECTION('',(-1.,0.E+000,0.E+000));
#328 = PCURVE('',#55,#329);
#329 = DEFINITIONAL_REPRESENTATION('',(#330),#334);
#330 = LINE('',#331,#332);
#331 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#332 = VECTOR('',#333,1.);
#333 = DIRECTION('',(1.,0.E+000));
#334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#335 = PCURVE('',#137,#336);
#336 = DEFINITIONAL_REPRESENTATION('',(#337),#341);
#337 = LINE('',#338,#339);
#338 = CARTESIAN_POINT('',(129.99999908,0.E+000));
#339 = VECTOR('',#340,1.);
#340 = DIRECTION('',(-1.,0.E+000));
#341 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#342 = ADVANCED_FACE('',(#343),#357,.T.);
#343 = FACE_BOUND('',#344,.F.);
#344 = EDGE_LOOP('',(#345,#375,#397,#398));
#345 = ORIENTED_EDGE('',*,*,#346,.T.);
#346 = EDGE_CURVE('',#347,#349,#351,.T.);
#347 = VERTEX_POINT('',#348);
#348 = CARTESIAN_POINT('',(8.20001154,12.5000131,0.E+000));
#349 = VERTEX_POINT('',#350);
#350 = CARTESIAN_POINT('',(8.20001154,12.5000131,1.64592));
#351 = SEAM_CURVE('',#352,(#356,#368),.PCURVE_S1.);
#352 = LINE('',#353,#354);
#353 = CARTESIAN_POINT('',(8.20001154,12.5000131,0.E+000));
#354 = VECTOR('',#355,1.);
#355 = DIRECTION('',(0.E+000,0.E+000,1.));
#356 = PCURVE('',#357,#362);
#357 = CYLINDRICAL_SURFACE('',#358,1.59999934);
#358 = AXIS2_PLACEMENT_3D('',#359,#360,#361);
#359 = CARTESIAN_POINT('',(6.6000122,12.5000131,0.E+000));
#360 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#361 = DIRECTION('',(1.,0.E+000,-0.E+000));
#362 = DEFINITIONAL_REPRESENTATION('',(#363),#367);
#363 = LINE('',#364,#365);
#364 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#365 = VECTOR('',#366,1.);
#366 = DIRECTION('',(-0.E+000,-1.));
#367 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#368 = PCURVE('',#357,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(-0.E+000,-1.));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = ORIENTED_EDGE('',*,*,#376,.T.);
#376 = EDGE_CURVE('',#349,#349,#377,.T.);
#377 = SURFACE_CURVE('',#378,(#383,#390),.PCURVE_S1.);
#378 = CIRCLE('',#379,1.59999934);
#379 = AXIS2_PLACEMENT_3D('',#380,#381,#382);
#380 = CARTESIAN_POINT('',(6.6000122,12.5000131,1.64592));
#381 = DIRECTION('',(0.E+000,0.E+000,1.));
#382 = DIRECTION('',(1.,0.E+000,-0.E+000));
#383 = PCURVE('',#357,#384);
#384 = DEFINITIONAL_REPRESENTATION('',(#385),#389);
#385 = LINE('',#386,#387);
#386 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#387 = VECTOR('',#388,1.);
#388 = DIRECTION('',(-1.,0.E+000));
#389 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#390 = PCURVE('',#83,#391);
#391 = DEFINITIONAL_REPRESENTATION('',(#392),#396);
#392 = CIRCLE('',#393,1.59999934);
#393 = AXIS2_PLACEMENT_2D('',#394,#395);
#394 = CARTESIAN_POINT('',(6.5999995,12.5000004));
#395 = DIRECTION('',(1.,0.E+000));
#396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#397 = ORIENTED_EDGE('',*,*,#346,.F.);
#398 = ORIENTED_EDGE('',*,*,#399,.F.);
#399 = EDGE_CURVE('',#347,#347,#400,.T.);
#400 = SURFACE_CURVE('',#401,(#406,#413),.PCURVE_S1.);
#401 = CIRCLE('',#402,1.59999934);
#402 = AXIS2_PLACEMENT_3D('',#403,#404,#405);
#403 = CARTESIAN_POINT('',(6.6000122,12.5000131,0.E+000));
#404 = DIRECTION('',(0.E+000,0.E+000,1.));
#405 = DIRECTION('',(1.,0.E+000,-0.E+000));
#406 = PCURVE('',#357,#407);
#407 = DEFINITIONAL_REPRESENTATION('',(#408),#412);
#408 = LINE('',#409,#410);
#409 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#410 = VECTOR('',#411,1.);
#411 = DIRECTION('',(-1.,0.E+000));
#412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#413 = PCURVE('',#137,#414);
#414 = DEFINITIONAL_REPRESENTATION('',(#415),#419);
#415 = CIRCLE('',#416,1.59999934);
#416 = AXIS2_PLACEMENT_2D('',#417,#418);
#417 = CARTESIAN_POINT('',(6.5999995,12.5000004));
#418 = DIRECTION('',(1.,0.E+000));
#419 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#420 = ADVANCED_FACE('',(#421),#435,.F.);
#421 = FACE_BOUND('',#422,.F.);
#422 = EDGE_LOOP('',(#423,#458,#481,#509));
#423 = ORIENTED_EDGE('',*,*,#424,.T.);
#424 = EDGE_CURVE('',#425,#427,#429,.T.);
#425 = VERTEX_POINT('',#426);
#426 = CARTESIAN_POINT('',(91.50001258,17.0086401,0.E+000));
#427 = VERTEX_POINT('',#428);
#428 = CARTESIAN_POINT('',(91.50001258,17.0086401,1.64592));
#429 = SURFACE_CURVE('',#430,(#434,#446),.PCURVE_S1.);
#430 = LINE('',#431,#432);
#431 = CARTESIAN_POINT('',(91.50001258,17.0086401,0.E+000));
#432 = VECTOR('',#433,1.);
#433 = DIRECTION('',(0.E+000,0.E+000,1.));
#434 = PCURVE('',#435,#440);
#435 = PLANE('',#436);
#436 = AXIS2_PLACEMENT_3D('',#437,#438,#439);
#437 = CARTESIAN_POINT('',(91.50001258,17.0086401,0.E+000));
#438 = DIRECTION('',(1.627996183953E-004,-0.999999986748,0.E+000));
#439 = DIRECTION('',(-0.999999986748,-1.627996183953E-004,0.E+000));
#440 = DEFINITIONAL_REPRESENTATION('',(#441),#445);
#441 = LINE('',#442,#443);
#442 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#443 = VECTOR('',#444,1.);
#444 = DIRECTION('',(0.E+000,-1.));
#445 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#446 = PCURVE('',#447,#452);
#447 = PLANE('',#448);
#448 = AXIS2_PLACEMENT_3D('',#449,#450,#451);
#449 = CARTESIAN_POINT('',(91.76106616,16.97427136,0.E+000));
#450 = DIRECTION('',(-0.130527626456,-0.991444672552,0.E+000));
#451 = DIRECTION('',(-0.991444672552,0.130527626456,0.E+000));
#452 = DEFINITIONAL_REPRESENTATION('',(#453),#457);
#453 = LINE('',#454,#455);
#454 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#455 = VECTOR('',#456,1.);
#456 = DIRECTION('',(0.E+000,-1.));
#457 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#458 = ORIENTED_EDGE('',*,*,#459,.T.);
#459 = EDGE_CURVE('',#427,#460,#462,.T.);
#460 = VERTEX_POINT('',#461);
#461 = CARTESIAN_POINT('',(38.5000119,17.00001172,1.64592));
#462 = SURFACE_CURVE('',#463,(#467,#474),.PCURVE_S1.);
#463 = LINE('',#464,#465);
#464 = CARTESIAN_POINT('',(91.50001258,17.0086401,1.64592));
#465 = VECTOR('',#466,1.);
#466 = DIRECTION('',(-0.999999986748,-1.627996183953E-004,0.E+000));
#467 = PCURVE('',#435,#468);
#468 = DEFINITIONAL_REPRESENTATION('',(#469),#473);
#469 = LINE('',#470,#471);
#470 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#471 = VECTOR('',#472,1.);
#472 = DIRECTION('',(1.,0.E+000));
#473 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#474 = PCURVE('',#83,#475);
#475 = DEFINITIONAL_REPRESENTATION('',(#476),#480);
#476 = LINE('',#477,#478);
#477 = CARTESIAN_POINT('',(91.49999988,17.0086274));
#478 = VECTOR('',#479,1.);
#479 = DIRECTION('',(-0.999999986748,-1.627996183953E-004));
#480 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#481 = ORIENTED_EDGE('',*,*,#482,.F.);
#482 = EDGE_CURVE('',#483,#460,#485,.T.);
#483 = VERTEX_POINT('',#484);
#484 = CARTESIAN_POINT('',(38.5000119,17.00001172,0.E+000));
#485 = SURFACE_CURVE('',#486,(#490,#497),.PCURVE_S1.);
#486 = LINE('',#487,#488);
#487 = CARTESIAN_POINT('',(38.5000119,17.00001172,0.E+000));
#488 = VECTOR('',#489,1.);
#489 = DIRECTION('',(0.E+000,0.E+000,1.));
#490 = PCURVE('',#435,#491);
#491 = DEFINITIONAL_REPRESENTATION('',(#492),#496);
#492 = LINE('',#493,#494);
#493 = CARTESIAN_POINT('',(53.000001382348,0.E+000));
#494 = VECTOR('',#495,1.);
#495 = DIRECTION('',(0.E+000,-1.));
#496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#497 = PCURVE('',#498,#503);
#498 = PLANE('',#499);
#499 = AXIS2_PLACEMENT_3D('',#500,#501,#502);
#500 = CARTESIAN_POINT('',(38.5000119,17.00001172,0.E+000));
#501 = DIRECTION('',(-1.,0.E+000,0.E+000));
#502 = DIRECTION('',(0.E+000,1.,0.E+000));
#503 = DEFINITIONAL_REPRESENTATION('',(#504),#508);
#504 = LINE('',#505,#506);
#505 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#506 = VECTOR('',#507,1.);
#507 = DIRECTION('',(0.E+000,-1.));
#508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#509 = ORIENTED_EDGE('',*,*,#510,.F.);
#510 = EDGE_CURVE('',#425,#483,#511,.T.);
#511 = SURFACE_CURVE('',#512,(#516,#523),.PCURVE_S1.);
#512 = LINE('',#513,#514);
#513 = CARTESIAN_POINT('',(91.50001258,17.0086401,0.E+000));
#514 = VECTOR('',#515,1.);
#515 = DIRECTION('',(-0.999999986748,-1.627996183953E-004,0.E+000));
#516 = PCURVE('',#435,#517);
#517 = DEFINITIONAL_REPRESENTATION('',(#518),#522);
#518 = LINE('',#519,#520);
#519 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#520 = VECTOR('',#521,1.);
#521 = DIRECTION('',(1.,0.E+000));
#522 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#523 = PCURVE('',#137,#524);
#524 = DEFINITIONAL_REPRESENTATION('',(#525),#529);
#525 = LINE('',#526,#527);
#526 = CARTESIAN_POINT('',(91.49999988,17.0086274));
#527 = VECTOR('',#528,1.);
#528 = DIRECTION('',(-0.999999986748,-1.627996183953E-004));
#529 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#530 = ADVANCED_FACE('',(#531),#498,.F.);
#531 = FACE_BOUND('',#532,.F.);
#532 = EDGE_LOOP('',(#533,#534,#557,#585));
#533 = ORIENTED_EDGE('',*,*,#482,.T.);
#534 = ORIENTED_EDGE('',*,*,#535,.T.);
#535 = EDGE_CURVE('',#460,#536,#538,.T.);
#536 = VERTEX_POINT('',#537);
#537 = CARTESIAN_POINT('',(38.5000119,17.0086401,1.64592));
#538 = SURFACE_CURVE('',#539,(#543,#550),.PCURVE_S1.);
#539 = LINE('',#540,#541);
#540 = CARTESIAN_POINT('',(38.5000119,17.00001172,1.64592));
#541 = VECTOR('',#542,1.);
#542 = DIRECTION('',(0.E+000,1.,0.E+000));
#543 = PCURVE('',#498,#544);
#544 = DEFINITIONAL_REPRESENTATION('',(#545),#549);
#545 = LINE('',#546,#547);
#546 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#547 = VECTOR('',#548,1.);
#548 = DIRECTION('',(1.,0.E+000));
#549 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#550 = PCURVE('',#83,#551);
#551 = DEFINITIONAL_REPRESENTATION('',(#552),#556);
#552 = LINE('',#553,#554);
#553 = CARTESIAN_POINT('',(38.4999992,16.99999902));
#554 = VECTOR('',#555,1.);
#555 = DIRECTION('',(0.E+000,1.));
#556 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#557 = ORIENTED_EDGE('',*,*,#558,.F.);
#558 = EDGE_CURVE('',#559,#536,#561,.T.);
#559 = VERTEX_POINT('',#560);
#560 = CARTESIAN_POINT('',(38.5000119,17.0086401,0.E+000));
#561 = SURFACE_CURVE('',#562,(#566,#573),.PCURVE_S1.);
#562 = LINE('',#563,#564);
#563 = CARTESIAN_POINT('',(38.5000119,17.0086401,0.E+000));
#564 = VECTOR('',#565,1.);
#565 = DIRECTION('',(0.E+000,0.E+000,1.));
#566 = PCURVE('',#498,#567);
#567 = DEFINITIONAL_REPRESENTATION('',(#568),#572);
#568 = LINE('',#569,#570);
#569 = CARTESIAN_POINT('',(8.628379999998E-003,0.E+000));
#570 = VECTOR('',#571,1.);
#571 = DIRECTION('',(0.E+000,-1.));
#572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#573 = PCURVE('',#574,#579);
#574 = PLANE('',#575);
#575 = AXIS2_PLACEMENT_3D('',#576,#577,#578);
#576 = CARTESIAN_POINT('',(38.5000119,17.0086401,0.E+000));
#577 = DIRECTION('',(0.130527626456,-0.991444672552,0.E+000));
#578 = DIRECTION('',(-0.991444672552,-0.130527626456,0.E+000));
#579 = DEFINITIONAL_REPRESENTATION('',(#580),#584);
#580 = LINE('',#581,#582);
#581 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#582 = VECTOR('',#583,1.);
#583 = DIRECTION('',(0.E+000,-1.));
#584 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#585 = ORIENTED_EDGE('',*,*,#586,.F.);
#586 = EDGE_CURVE('',#483,#559,#587,.T.);
#587 = SURFACE_CURVE('',#588,(#592,#599),.PCURVE_S1.);
#588 = LINE('',#589,#590);
#589 = CARTESIAN_POINT('',(38.5000119,17.00001172,0.E+000));
#590 = VECTOR('',#591,1.);
#591 = DIRECTION('',(0.E+000,1.,0.E+000));
#592 = PCURVE('',#498,#593);
#593 = DEFINITIONAL_REPRESENTATION('',(#594),#598);
#594 = LINE('',#595,#596);
#595 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#596 = VECTOR('',#597,1.);
#597 = DIRECTION('',(1.,0.E+000));
#598 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#599 = PCURVE('',#137,#600);
#600 = DEFINITIONAL_REPRESENTATION('',(#601),#605);
#601 = LINE('',#602,#603);
#602 = CARTESIAN_POINT('',(38.4999992,16.99999902));
#603 = VECTOR('',#604,1.);
#604 = DIRECTION('',(0.E+000,1.));
#605 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#606 = ADVANCED_FACE('',(#607),#574,.F.);
#607 = FACE_BOUND('',#608,.F.);
#608 = EDGE_LOOP('',(#609,#610,#633,#661));
#609 = ORIENTED_EDGE('',*,*,#558,.T.);
#610 = ORIENTED_EDGE('',*,*,#611,.T.);
#611 = EDGE_CURVE('',#536,#612,#614,.T.);
#612 = VERTEX_POINT('',#613);
#613 = CARTESIAN_POINT('',(38.23895832,16.97427136,1.64592));
#614 = SURFACE_CURVE('',#615,(#619,#626),.PCURVE_S1.);
#615 = LINE('',#616,#617);
#616 = CARTESIAN_POINT('',(38.5000119,17.0086401,1.64592));
#617 = VECTOR('',#618,1.);
#618 = DIRECTION('',(-0.991444672552,-0.130527626456,0.E+000));
#619 = PCURVE('',#574,#620);
#620 = DEFINITIONAL_REPRESENTATION('',(#621),#625);
#621 = LINE('',#622,#623);
#622 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#623 = VECTOR('',#624,1.);
#624 = DIRECTION('',(1.,0.E+000));
#625 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#626 = PCURVE('',#83,#627);
#627 = DEFINITIONAL_REPRESENTATION('',(#628),#632);
#628 = LINE('',#629,#630);
#629 = CARTESIAN_POINT('',(38.4999992,17.0086274));
#630 = VECTOR('',#631,1.);
#631 = DIRECTION('',(-0.991444672552,-0.130527626456));
#632 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#633 = ORIENTED_EDGE('',*,*,#634,.F.);
#634 = EDGE_CURVE('',#635,#612,#637,.T.);
#635 = VERTEX_POINT('',#636);
#636 = CARTESIAN_POINT('',(38.23895832,16.97427136,0.E+000));
#637 = SURFACE_CURVE('',#638,(#642,#649),.PCURVE_S1.);
#638 = LINE('',#639,#640);
#639 = CARTESIAN_POINT('',(38.23895832,16.97427136,0.E+000));
#640 = VECTOR('',#641,1.);
#641 = DIRECTION('',(0.E+000,0.E+000,1.));
#642 = PCURVE('',#574,#643);
#643 = DEFINITIONAL_REPRESENTATION('',(#644),#648);
#644 = LINE('',#645,#646);
#645 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#646 = VECTOR('',#647,1.);
#647 = DIRECTION('',(0.E+000,-1.));
#648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#649 = PCURVE('',#650,#655);
#650 = PLANE('',#651);
#651 = AXIS2_PLACEMENT_3D('',#652,#653,#654);
#652 = CARTESIAN_POINT('',(38.23895832,16.97427136,0.E+000));
#653 = DIRECTION('',(0.382682927661,-0.923879741566,0.E+000));
#654 = DIRECTION('',(-0.923879741566,-0.382682927661,0.E+000));
#655 = DEFINITIONAL_REPRESENTATION('',(#656),#660);
#656 = LINE('',#657,#658);
#657 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#658 = VECTOR('',#659,1.);
#659 = DIRECTION('',(0.E+000,-1.));
#660 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#661 = ORIENTED_EDGE('',*,*,#662,.F.);
#662 = EDGE_CURVE('',#559,#635,#663,.T.);
#663 = SURFACE_CURVE('',#664,(#668,#675),.PCURVE_S1.);
#664 = LINE('',#665,#666);
#665 = CARTESIAN_POINT('',(38.5000119,17.0086401,0.E+000));
#666 = VECTOR('',#667,1.);
#667 = DIRECTION('',(-0.991444672552,-0.130527626456,0.E+000));
#668 = PCURVE('',#574,#669);
#669 = DEFINITIONAL_REPRESENTATION('',(#670),#674);
#670 = LINE('',#671,#672);
#671 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#672 = VECTOR('',#673,1.);
#673 = DIRECTION('',(1.,0.E+000));
#674 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#675 = PCURVE('',#137,#676);
#676 = DEFINITIONAL_REPRESENTATION('',(#677),#681);
#677 = LINE('',#678,#679);
#678 = CARTESIAN_POINT('',(38.4999992,17.0086274));
#679 = VECTOR('',#680,1.);
#680 = DIRECTION('',(-0.991444672552,-0.130527626456));
#681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#682 = ADVANCED_FACE('',(#683),#650,.F.);
#683 = FACE_BOUND('',#684,.F.);
#684 = EDGE_LOOP('',(#685,#686,#709,#737));
#685 = ORIENTED_EDGE('',*,*,#634,.T.);
#686 = ORIENTED_EDGE('',*,*,#687,.T.);
#687 = EDGE_CURVE('',#612,#688,#690,.T.);
#688 = VERTEX_POINT('',#689);
#689 = CARTESIAN_POINT('',(37.99569744,16.87350956,1.64592));
#690 = SURFACE_CURVE('',#691,(#695,#702),.PCURVE_S1.);
#691 = LINE('',#692,#693);
#692 = CARTESIAN_POINT('',(38.23895832,16.97427136,1.64592));
#693 = VECTOR('',#694,1.);
#694 = DIRECTION('',(-0.923879741566,-0.382682927661,0.E+000));
#695 = PCURVE('',#650,#696);
#696 = DEFINITIONAL_REPRESENTATION('',(#697),#701);
#697 = LINE('',#698,#699);
#698 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#699 = VECTOR('',#700,1.);
#700 = DIRECTION('',(1.,0.E+000));
#701 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#702 = PCURVE('',#83,#703);
#703 = DEFINITIONAL_REPRESENTATION('',(#704),#708);
#704 = LINE('',#705,#706);
#705 = CARTESIAN_POINT('',(38.23894562,16.97425866));
#706 = VECTOR('',#707,1.);
#707 = DIRECTION('',(-0.923879741566,-0.382682927661));
#708 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#709 = ORIENTED_EDGE('',*,*,#710,.F.);
#710 = EDGE_CURVE('',#711,#688,#713,.T.);
#711 = VERTEX_POINT('',#712);
#712 = CARTESIAN_POINT('',(37.99569744,16.87350956,0.E+000));
#713 = SURFACE_CURVE('',#714,(#718,#725),.PCURVE_S1.);
#714 = LINE('',#715,#716);
#715 = CARTESIAN_POINT('',(37.99569744,16.87350956,0.E+000));
#716 = VECTOR('',#717,1.);
#717 = DIRECTION('',(0.E+000,0.E+000,1.));
#718 = PCURVE('',#650,#719);
#719 = DEFINITIONAL_REPRESENTATION('',(#720),#724);
#720 = LINE('',#721,#722);
#721 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#722 = VECTOR('',#723,1.);
#723 = DIRECTION('',(0.E+000,-1.));
#724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#725 = PCURVE('',#726,#731);
#726 = PLANE('',#727);
#727 = AXIS2_PLACEMENT_3D('',#728,#729,#730);
#728 = CARTESIAN_POINT('',(37.99569744,16.87350956,0.E+000));
#729 = DIRECTION('',(0.608758355861,-0.793355698391,0.E+000));
#730 = DIRECTION('',(-0.793355698391,-0.608758355861,0.E+000));
#731 = DEFINITIONAL_REPRESENTATION('',(#732),#736);
#732 = LINE('',#733,#734);
#733 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#734 = VECTOR('',#735,1.);
#735 = DIRECTION('',(0.E+000,-1.));
#736 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#737 = ORIENTED_EDGE('',*,*,#738,.F.);
#738 = EDGE_CURVE('',#635,#711,#739,.T.);
#739 = SURFACE_CURVE('',#740,(#744,#751),.PCURVE_S1.);
#740 = LINE('',#741,#742);
#741 = CARTESIAN_POINT('',(38.23895832,16.97427136,0.E+000));
#742 = VECTOR('',#743,1.);
#743 = DIRECTION('',(-0.923879741566,-0.382682927661,0.E+000));
#744 = PCURVE('',#650,#745);
#745 = DEFINITIONAL_REPRESENTATION('',(#746),#750);
#746 = LINE('',#747,#748);
#747 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#748 = VECTOR('',#749,1.);
#749 = DIRECTION('',(1.,0.E+000));
#750 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#751 = PCURVE('',#137,#752);
#752 = DEFINITIONAL_REPRESENTATION('',(#753),#757);
#753 = LINE('',#754,#755);
#754 = CARTESIAN_POINT('',(38.23894562,16.97425866));
#755 = VECTOR('',#756,1.);
#756 = DIRECTION('',(-0.923879741566,-0.382682927661));
#757 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#758 = ADVANCED_FACE('',(#759),#726,.F.);
#759 = FACE_BOUND('',#760,.F.);
#760 = EDGE_LOOP('',(#761,#762,#785,#813));
#761 = ORIENTED_EDGE('',*,*,#710,.T.);
#762 = ORIENTED_EDGE('',*,*,#763,.T.);
#763 = EDGE_CURVE('',#688,#764,#766,.T.);
#764 = VERTEX_POINT('',#765);
#765 = CARTESIAN_POINT('',(37.78680276,16.71322032,1.64592));
#766 = SURFACE_CURVE('',#767,(#771,#778),.PCURVE_S1.);
#767 = LINE('',#768,#769);
#768 = CARTESIAN_POINT('',(37.99569744,16.87350956,1.64592));
#769 = VECTOR('',#770,1.);
#770 = DIRECTION('',(-0.793355698391,-0.608758355861,0.E+000));
#771 = PCURVE('',#726,#772);
#772 = DEFINITIONAL_REPRESENTATION('',(#773),#777);
#773 = LINE('',#774,#775);
#774 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#775 = VECTOR('',#776,1.);
#776 = DIRECTION('',(1.,0.E+000));
#777 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#778 = PCURVE('',#83,#779);
#779 = DEFINITIONAL_REPRESENTATION('',(#780),#784);
#780 = LINE('',#781,#782);
#781 = CARTESIAN_POINT('',(37.99568474,16.87349686));
#782 = VECTOR('',#783,1.);
#783 = DIRECTION('',(-0.793355698391,-0.608758355861));
#784 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#785 = ORIENTED_EDGE('',*,*,#786,.F.);
#786 = EDGE_CURVE('',#787,#764,#789,.T.);
#787 = VERTEX_POINT('',#788);
#788 = CARTESIAN_POINT('',(37.78680276,16.71322032,0.E+000));
#789 = SURFACE_CURVE('',#790,(#794,#801),.PCURVE_S1.);
#790 = LINE('',#791,#792);
#791 = CARTESIAN_POINT('',(37.78680276,16.71322032,0.E+000));
#792 = VECTOR('',#793,1.);
#793 = DIRECTION('',(0.E+000,0.E+000,1.));
#794 = PCURVE('',#726,#795);
#795 = DEFINITIONAL_REPRESENTATION('',(#796),#800);
#796 = LINE('',#797,#798);
#797 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#798 = VECTOR('',#799,1.);
#799 = DIRECTION('',(0.E+000,-1.));
#800 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#801 = PCURVE('',#802,#807);
#802 = PLANE('',#803);
#803 = AXIS2_PLACEMENT_3D('',#804,#805,#806);
#804 = CARTESIAN_POINT('',(37.78680276,16.71322032,0.E+000));
#805 = DIRECTION('',(0.793355698391,-0.608758355861,0.E+000));
#806 = DIRECTION('',(-0.608758355861,-0.793355698391,0.E+000));
#807 = DEFINITIONAL_REPRESENTATION('',(#808),#812);
#808 = LINE('',#809,#810);
#809 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#810 = VECTOR('',#811,1.);
#811 = DIRECTION('',(0.E+000,-1.));
#812 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#813 = ORIENTED_EDGE('',*,*,#814,.F.);
#814 = EDGE_CURVE('',#711,#787,#815,.T.);
#815 = SURFACE_CURVE('',#816,(#820,#827),.PCURVE_S1.);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(37.99569744,16.87350956,0.E+000));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(-0.793355698391,-0.608758355861,0.E+000));
#820 = PCURVE('',#726,#821);
#821 = DEFINITIONAL_REPRESENTATION('',(#822),#826);
#822 = LINE('',#823,#824);
#823 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#824 = VECTOR('',#825,1.);
#825 = DIRECTION('',(1.,0.E+000));
#826 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#827 = PCURVE('',#137,#828);
#828 = DEFINITIONAL_REPRESENTATION('',(#829),#833);
#829 = LINE('',#830,#831);
#830 = CARTESIAN_POINT('',(37.99568474,16.87349686));
#831 = VECTOR('',#832,1.);
#832 = DIRECTION('',(-0.793355698391,-0.608758355861));
#833 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#834 = ADVANCED_FACE('',(#835),#802,.F.);
#835 = FACE_BOUND('',#836,.F.);
#836 = EDGE_LOOP('',(#837,#838,#861,#889));
#837 = ORIENTED_EDGE('',*,*,#786,.T.);
#838 = ORIENTED_EDGE('',*,*,#839,.T.);
#839 = EDGE_CURVE('',#764,#840,#842,.T.);
#840 = VERTEX_POINT('',#841);
#841 = CARTESIAN_POINT('',(37.62651352,16.50432564,1.64592));
#842 = SURFACE_CURVE('',#843,(#847,#854),.PCURVE_S1.);
#843 = LINE('',#844,#845);
#844 = CARTESIAN_POINT('',(37.78680276,16.71322032,1.64592));
#845 = VECTOR('',#846,1.);
#846 = DIRECTION('',(-0.608758355861,-0.793355698391,0.E+000));
#847 = PCURVE('',#802,#848);
#848 = DEFINITIONAL_REPRESENTATION('',(#849),#853);
#849 = LINE('',#850,#851);
#850 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#851 = VECTOR('',#852,1.);
#852 = DIRECTION('',(1.,0.E+000));
#853 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#854 = PCURVE('',#83,#855);
#855 = DEFINITIONAL_REPRESENTATION('',(#856),#860);
#856 = LINE('',#857,#858);
#857 = CARTESIAN_POINT('',(37.78679006,16.71320762));
#858 = VECTOR('',#859,1.);
#859 = DIRECTION('',(-0.608758355861,-0.793355698391));
#860 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#861 = ORIENTED_EDGE('',*,*,#862,.F.);
#862 = EDGE_CURVE('',#863,#840,#865,.T.);
#863 = VERTEX_POINT('',#864);
#864 = CARTESIAN_POINT('',(37.62651352,16.50432564,0.E+000));
#865 = SURFACE_CURVE('',#866,(#870,#877),.PCURVE_S1.);
#866 = LINE('',#867,#868);
#867 = CARTESIAN_POINT('',(37.62651352,16.50432564,0.E+000));
#868 = VECTOR('',#869,1.);
#869 = DIRECTION('',(0.E+000,0.E+000,1.));
#870 = PCURVE('',#802,#871);
#871 = DEFINITIONAL_REPRESENTATION('',(#872),#876);
#872 = LINE('',#873,#874);
#873 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#874 = VECTOR('',#875,1.);
#875 = DIRECTION('',(0.E+000,-1.));
#876 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#877 = PCURVE('',#878,#883);
#878 = PLANE('',#879);
#879 = AXIS2_PLACEMENT_3D('',#880,#881,#882);
#880 = CARTESIAN_POINT('',(37.62651352,16.50432564,0.E+000));
#881 = DIRECTION('',(0.923879741566,-0.382682927661,0.E+000));
#882 = DIRECTION('',(-0.382682927661,-0.923879741566,0.E+000));
#883 = DEFINITIONAL_REPRESENTATION('',(#884),#888);
#884 = LINE('',#885,#886);
#885 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#886 = VECTOR('',#887,1.);
#887 = DIRECTION('',(0.E+000,-1.));
#888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#889 = ORIENTED_EDGE('',*,*,#890,.F.);
#890 = EDGE_CURVE('',#787,#863,#891,.T.);
#891 = SURFACE_CURVE('',#892,(#896,#903),.PCURVE_S1.);
#892 = LINE('',#893,#894);
#893 = CARTESIAN_POINT('',(37.78680276,16.71322032,0.E+000));
#894 = VECTOR('',#895,1.);
#895 = DIRECTION('',(-0.608758355861,-0.793355698391,0.E+000));
#896 = PCURVE('',#802,#897);
#897 = DEFINITIONAL_REPRESENTATION('',(#898),#902);
#898 = LINE('',#899,#900);
#899 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#900 = VECTOR('',#901,1.);
#901 = DIRECTION('',(1.,0.E+000));
#902 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#903 = PCURVE('',#137,#904);
#904 = DEFINITIONAL_REPRESENTATION('',(#905),#909);
#905 = LINE('',#906,#907);
#906 = CARTESIAN_POINT('',(37.78679006,16.71320762));
#907 = VECTOR('',#908,1.);
#908 = DIRECTION('',(-0.608758355861,-0.793355698391));
#909 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#910 = ADVANCED_FACE('',(#911),#878,.F.);
#911 = FACE_BOUND('',#912,.F.);
#912 = EDGE_LOOP('',(#913,#914,#937,#965));
#913 = ORIENTED_EDGE('',*,*,#862,.T.);
#914 = ORIENTED_EDGE('',*,*,#915,.T.);
#915 = EDGE_CURVE('',#840,#916,#918,.T.);
#916 = VERTEX_POINT('',#917);
#917 = CARTESIAN_POINT('',(37.52575172,16.26106476,1.64592));
#918 = SURFACE_CURVE('',#919,(#923,#930),.PCURVE_S1.);
#919 = LINE('',#920,#921);
#920 = CARTESIAN_POINT('',(37.62651352,16.50432564,1.64592));
#921 = VECTOR('',#922,1.);
#922 = DIRECTION('',(-0.382682927661,-0.923879741566,0.E+000));
#923 = PCURVE('',#878,#924);
#924 = DEFINITIONAL_REPRESENTATION('',(#925),#929);
#925 = LINE('',#926,#927);
#926 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#927 = VECTOR('',#928,1.);
#928 = DIRECTION('',(1.,0.E+000));
#929 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#930 = PCURVE('',#83,#931);
#931 = DEFINITIONAL_REPRESENTATION('',(#932),#936);
#932 = LINE('',#933,#934);
#933 = CARTESIAN_POINT('',(37.62650082,16.50431294));
#934 = VECTOR('',#935,1.);
#935 = DIRECTION('',(-0.382682927661,-0.923879741566));
#936 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#937 = ORIENTED_EDGE('',*,*,#938,.F.);
#938 = EDGE_CURVE('',#939,#916,#941,.T.);
#939 = VERTEX_POINT('',#940);
#940 = CARTESIAN_POINT('',(37.52575172,16.26106476,0.E+000));
#941 = SURFACE_CURVE('',#942,(#946,#953),.PCURVE_S1.);
#942 = LINE('',#943,#944);
#943 = CARTESIAN_POINT('',(37.52575172,16.26106476,0.E+000));
#944 = VECTOR('',#945,1.);
#945 = DIRECTION('',(0.E+000,0.E+000,1.));
#946 = PCURVE('',#878,#947);
#947 = DEFINITIONAL_REPRESENTATION('',(#948),#952);
#948 = LINE('',#949,#950);
#949 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#950 = VECTOR('',#951,1.);
#951 = DIRECTION('',(0.E+000,-1.));
#952 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#953 = PCURVE('',#954,#959);
#954 = PLANE('',#955);
#955 = AXIS2_PLACEMENT_3D('',#956,#957,#958);
#956 = CARTESIAN_POINT('',(37.52575172,16.26106476,0.E+000));
#957 = DIRECTION('',(0.991444672552,-0.130527626456,0.E+000));
#958 = DIRECTION('',(-0.130527626456,-0.991444672552,0.E+000));
#959 = DEFINITIONAL_REPRESENTATION('',(#960),#964);
#960 = LINE('',#961,#962);
#961 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#962 = VECTOR('',#963,1.);
#963 = DIRECTION('',(0.E+000,-1.));
#964 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#965 = ORIENTED_EDGE('',*,*,#966,.F.);
#966 = EDGE_CURVE('',#863,#939,#967,.T.);
#967 = SURFACE_CURVE('',#968,(#972,#979),.PCURVE_S1.);
#968 = LINE('',#969,#970);
#969 = CARTESIAN_POINT('',(37.62651352,16.50432564,0.E+000));
#970 = VECTOR('',#971,1.);
#971 = DIRECTION('',(-0.382682927661,-0.923879741566,0.E+000));
#972 = PCURVE('',#878,#973);
#973 = DEFINITIONAL_REPRESENTATION('',(#974),#978);
#974 = LINE('',#975,#976);
#975 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#976 = VECTOR('',#977,1.);
#977 = DIRECTION('',(1.,0.E+000));
#978 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#979 = PCURVE('',#137,#980);
#980 = DEFINITIONAL_REPRESENTATION('',(#981),#985);
#981 = LINE('',#982,#983);
#982 = CARTESIAN_POINT('',(37.62650082,16.50431294));
#983 = VECTOR('',#984,1.);
#984 = DIRECTION('',(-0.382682927661,-0.923879741566));
#985 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#986 = ADVANCED_FACE('',(#987),#954,.F.);
#987 = FACE_BOUND('',#988,.F.);
#988 = EDGE_LOOP('',(#989,#990,#1013,#1041));
#989 = ORIENTED_EDGE('',*,*,#938,.T.);
#990 = ORIENTED_EDGE('',*,*,#991,.T.);
#991 = EDGE_CURVE('',#916,#992,#994,.T.);
#992 = VERTEX_POINT('',#993);
#993 = CARTESIAN_POINT('',(37.49138298,16.00001118,1.64592));
#994 = SURFACE_CURVE('',#995,(#999,#1006),.PCURVE_S1.);
#995 = LINE('',#996,#997);
#996 = CARTESIAN_POINT('',(37.52575172,16.26106476,1.64592));
#997 = VECTOR('',#998,1.);
#998 = DIRECTION('',(-0.130527626456,-0.991444672552,0.E+000));
#999 = PCURVE('',#954,#1000);
#1000 = DEFINITIONAL_REPRESENTATION('',(#1001),#1005);
#1001 = LINE('',#1002,#1003);
#1002 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1003 = VECTOR('',#1004,1.);
#1004 = DIRECTION('',(1.,0.E+000));
#1005 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1006 = PCURVE('',#83,#1007);
#1007 = DEFINITIONAL_REPRESENTATION('',(#1008),#1012);
#1008 = LINE('',#1009,#1010);
#1009 = CARTESIAN_POINT('',(37.52573902,16.26105206));
#1010 = VECTOR('',#1011,1.);
#1011 = DIRECTION('',(-0.130527626456,-0.991444672552));
#1012 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1013 = ORIENTED_EDGE('',*,*,#1014,.F.);
#1014 = EDGE_CURVE('',#1015,#992,#1017,.T.);
#1015 = VERTEX_POINT('',#1016);
#1016 = CARTESIAN_POINT('',(37.49138298,16.00001118,0.E+000));
#1017 = SURFACE_CURVE('',#1018,(#1022,#1029),.PCURVE_S1.);
#1018 = LINE('',#1019,#1020);
#1019 = CARTESIAN_POINT('',(37.49138298,16.00001118,0.E+000));
#1020 = VECTOR('',#1021,1.);
#1021 = DIRECTION('',(0.E+000,0.E+000,1.));
#1022 = PCURVE('',#954,#1023);
#1023 = DEFINITIONAL_REPRESENTATION('',(#1024),#1028);
#1024 = LINE('',#1025,#1026);
#1025 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#1026 = VECTOR('',#1027,1.);
#1027 = DIRECTION('',(0.E+000,-1.));
#1028 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1029 = PCURVE('',#1030,#1035);
#1030 = PLANE('',#1031);
#1031 = AXIS2_PLACEMENT_3D('',#1032,#1033,#1034);
#1032 = CARTESIAN_POINT('',(37.49138298,16.00001118,0.E+000));
#1033 = DIRECTION('',(0.999990693999,4.314153001476E-003,-0.E+000));
#1034 = DIRECTION('',(4.314153001476E-003,-0.999990693999,0.E+000));
#1035 = DEFINITIONAL_REPRESENTATION('',(#1036),#1040);
#1036 = LINE('',#1037,#1038);
#1037 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1038 = VECTOR('',#1039,1.);
#1039 = DIRECTION('',(0.E+000,-1.));
#1040 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1041 = ORIENTED_EDGE('',*,*,#1042,.F.);
#1042 = EDGE_CURVE('',#939,#1015,#1043,.T.);
#1043 = SURFACE_CURVE('',#1044,(#1048,#1055),.PCURVE_S1.);
#1044 = LINE('',#1045,#1046);
#1045 = CARTESIAN_POINT('',(37.52575172,16.26106476,0.E+000));
#1046 = VECTOR('',#1047,1.);
#1047 = DIRECTION('',(-0.130527626456,-0.991444672552,0.E+000));
#1048 = PCURVE('',#954,#1049);
#1049 = DEFINITIONAL_REPRESENTATION('',(#1050),#1054);
#1050 = LINE('',#1051,#1052);
#1051 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1052 = VECTOR('',#1053,1.);
#1053 = DIRECTION('',(1.,0.E+000));
#1054 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1055 = PCURVE('',#137,#1056);
#1056 = DEFINITIONAL_REPRESENTATION('',(#1057),#1061);
#1057 = LINE('',#1058,#1059);
#1058 = CARTESIAN_POINT('',(37.52573902,16.26105206));
#1059 = VECTOR('',#1060,1.);
#1060 = DIRECTION('',(-0.130527626456,-0.991444672552));
#1061 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1062 = ADVANCED_FACE('',(#1063),#1030,.F.);
#1063 = FACE_BOUND('',#1064,.F.);
#1064 = EDGE_LOOP('',(#1065,#1066,#1089,#1117));
#1065 = ORIENTED_EDGE('',*,*,#1014,.T.);
#1066 = ORIENTED_EDGE('',*,*,#1067,.T.);
#1067 = EDGE_CURVE('',#992,#1068,#1070,.T.);
#1068 = VERTEX_POINT('',#1069);
#1069 = CARTESIAN_POINT('',(37.50001136,14.00001264,1.64592));
#1070 = SURFACE_CURVE('',#1071,(#1075,#1082),.PCURVE_S1.);
#1071 = LINE('',#1072,#1073);
#1072 = CARTESIAN_POINT('',(37.49138298,16.00001118,1.64592));
#1073 = VECTOR('',#1074,1.);
#1074 = DIRECTION('',(4.314153001476E-003,-0.999990693999,0.E+000));
#1075 = PCURVE('',#1030,#1076);
#1076 = DEFINITIONAL_REPRESENTATION('',(#1077),#1081);
#1077 = LINE('',#1078,#1079);
#1078 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1079 = VECTOR('',#1080,1.);
#1080 = DIRECTION('',(1.,0.E+000));
#1081 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1082 = PCURVE('',#83,#1083);
#1083 = DEFINITIONAL_REPRESENTATION('',(#1084),#1088);
#1084 = LINE('',#1085,#1086);
#1085 = CARTESIAN_POINT('',(37.49137028,15.99999848));
#1086 = VECTOR('',#1087,1.);
#1087 = DIRECTION('',(4.314153001476E-003,-0.999990693999));
#1088 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1089 = ORIENTED_EDGE('',*,*,#1090,.F.);
#1090 = EDGE_CURVE('',#1091,#1068,#1093,.T.);
#1091 = VERTEX_POINT('',#1092);
#1092 = CARTESIAN_POINT('',(37.50001136,14.00001264,0.E+000));
#1093 = SURFACE_CURVE('',#1094,(#1098,#1105),.PCURVE_S1.);
#1094 = LINE('',#1095,#1096);
#1095 = CARTESIAN_POINT('',(37.50001136,14.00001264,0.E+000));
#1096 = VECTOR('',#1097,1.);
#1097 = DIRECTION('',(0.E+000,0.E+000,1.));
#1098 = PCURVE('',#1030,#1099);
#1099 = DEFINITIONAL_REPRESENTATION('',(#1100),#1104);
#1100 = LINE('',#1101,#1102);
#1101 = CARTESIAN_POINT('',(2.000017152162,0.E+000));
#1102 = VECTOR('',#1103,1.);
#1103 = DIRECTION('',(0.E+000,-1.));
#1104 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1105 = PCURVE('',#1106,#1111);
#1106 = PLANE('',#1107);
#1107 = AXIS2_PLACEMENT_3D('',#1108,#1109,#1110);
#1108 = CARTESIAN_POINT('',(37.50001136,14.00001264,0.E+000));
#1109 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1110 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1111 = DEFINITIONAL_REPRESENTATION('',(#1112),#1116);
#1112 = LINE('',#1113,#1114);
#1113 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#1114 = VECTOR('',#1115,1.);
#1115 = DIRECTION('',(0.E+000,-1.));
#1116 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1117 = ORIENTED_EDGE('',*,*,#1118,.F.);
#1118 = EDGE_CURVE('',#1015,#1091,#1119,.T.);
#1119 = SURFACE_CURVE('',#1120,(#1124,#1131),.PCURVE_S1.);
#1120 = LINE('',#1121,#1122);
#1121 = CARTESIAN_POINT('',(37.49138298,16.00001118,0.E+000));
#1122 = VECTOR('',#1123,1.);
#1123 = DIRECTION('',(4.314153001476E-003,-0.999990693999,0.E+000));
#1124 = PCURVE('',#1030,#1125);
#1125 = DEFINITIONAL_REPRESENTATION('',(#1126),#1130);
#1126 = LINE('',#1127,#1128);
#1127 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1128 = VECTOR('',#1129,1.);
#1129 = DIRECTION('',(1.,0.E+000));
#1130 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1131 = PCURVE('',#137,#1132);
#1132 = DEFINITIONAL_REPRESENTATION('',(#1133),#1137);
#1133 = LINE('',#1134,#1135);
#1134 = CARTESIAN_POINT('',(37.49137028,15.99999848));
#1135 = VECTOR('',#1136,1.);
#1136 = DIRECTION('',(4.314153001476E-003,-0.999990693999));
#1137 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1138 = ADVANCED_FACE('',(#1139),#1106,.F.);
#1139 = FACE_BOUND('',#1140,.F.);
#1140 = EDGE_LOOP('',(#1141,#1142,#1165,#1193));
#1141 = ORIENTED_EDGE('',*,*,#1090,.T.);
#1142 = ORIENTED_EDGE('',*,*,#1143,.T.);
#1143 = EDGE_CURVE('',#1068,#1144,#1146,.T.);
#1144 = VERTEX_POINT('',#1145);
#1145 = CARTESIAN_POINT('',(37.49138298,14.00001264,1.64592));
#1146 = SURFACE_CURVE('',#1147,(#1151,#1158),.PCURVE_S1.);
#1147 = LINE('',#1148,#1149);
#1148 = CARTESIAN_POINT('',(37.50001136,14.00001264,1.64592));
#1149 = VECTOR('',#1150,1.);
#1150 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1151 = PCURVE('',#1106,#1152);
#1152 = DEFINITIONAL_REPRESENTATION('',(#1153),#1157);
#1153 = LINE('',#1154,#1155);
#1154 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1155 = VECTOR('',#1156,1.);
#1156 = DIRECTION('',(1.,0.E+000));
#1157 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1158 = PCURVE('',#83,#1159);
#1159 = DEFINITIONAL_REPRESENTATION('',(#1160),#1164);
#1160 = LINE('',#1161,#1162);
#1161 = CARTESIAN_POINT('',(37.49999866,13.99999994));
#1162 = VECTOR('',#1163,1.);
#1163 = DIRECTION('',(-1.,0.E+000));
#1164 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1165 = ORIENTED_EDGE('',*,*,#1166,.F.);
#1166 = EDGE_CURVE('',#1167,#1144,#1169,.T.);
#1167 = VERTEX_POINT('',#1168);
#1168 = CARTESIAN_POINT('',(37.49138298,14.00001264,0.E+000));
#1169 = SURFACE_CURVE('',#1170,(#1174,#1181),.PCURVE_S1.);
#1170 = LINE('',#1171,#1172);
#1171 = CARTESIAN_POINT('',(37.49138298,14.00001264,0.E+000));
#1172 = VECTOR('',#1173,1.);
#1173 = DIRECTION('',(0.E+000,0.E+000,1.));
#1174 = PCURVE('',#1106,#1175);
#1175 = DEFINITIONAL_REPRESENTATION('',(#1176),#1180);
#1176 = LINE('',#1177,#1178);
#1177 = CARTESIAN_POINT('',(8.628380000005E-003,0.E+000));
#1178 = VECTOR('',#1179,1.);
#1179 = DIRECTION('',(0.E+000,-1.));
#1180 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1181 = PCURVE('',#1182,#1187);
#1182 = PLANE('',#1183);
#1183 = AXIS2_PLACEMENT_3D('',#1184,#1185,#1186);
#1184 = CARTESIAN_POINT('',(37.49138298,14.00001264,0.E+000));
#1185 = DIRECTION('',(0.991444672552,0.130527626456,-0.E+000));
#1186 = DIRECTION('',(0.130527626456,-0.991444672552,0.E+000));
#1187 = DEFINITIONAL_REPRESENTATION('',(#1188),#1192);
#1188 = LINE('',#1189,#1190);
#1189 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1190 = VECTOR('',#1191,1.);
#1191 = DIRECTION('',(0.E+000,-1.));
#1192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1193 = ORIENTED_EDGE('',*,*,#1194,.F.);
#1194 = EDGE_CURVE('',#1091,#1167,#1195,.T.);
#1195 = SURFACE_CURVE('',#1196,(#1200,#1207),.PCURVE_S1.);
#1196 = LINE('',#1197,#1198);
#1197 = CARTESIAN_POINT('',(37.50001136,14.00001264,0.E+000));
#1198 = VECTOR('',#1199,1.);
#1199 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1200 = PCURVE('',#1106,#1201);
#1201 = DEFINITIONAL_REPRESENTATION('',(#1202),#1206);
#1202 = LINE('',#1203,#1204);
#1203 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#1204 = VECTOR('',#1205,1.);
#1205 = DIRECTION('',(1.,0.E+000));
#1206 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1207 = PCURVE('',#137,#1208);
#1208 = DEFINITIONAL_REPRESENTATION('',(#1209),#1213);
#1209 = LINE('',#1210,#1211);
#1210 = CARTESIAN_POINT('',(37.49999866,13.99999994));
#1211 = VECTOR('',#1212,1.);
#1212 = DIRECTION('',(-1.,0.E+000));
#1213 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1214 = ADVANCED_FACE('',(#1215),#1182,.F.);
#1215 = FACE_BOUND('',#1216,.F.);
#1216 = EDGE_LOOP('',(#1217,#1218,#1241,#1269));
#1217 = ORIENTED_EDGE('',*,*,#1166,.T.);
#1218 = ORIENTED_EDGE('',*,*,#1219,.T.);
#1219 = EDGE_CURVE('',#1144,#1220,#1222,.T.);
#1220 = VERTEX_POINT('',#1221);
#1221 = CARTESIAN_POINT('',(37.52575172,13.73895906,1.64592));
#1222 = SURFACE_CURVE('',#1223,(#1227,#1234),.PCURVE_S1.);
#1223 = LINE('',#1224,#1225);
#1224 = CARTESIAN_POINT('',(37.49138298,14.00001264,1.64592));
#1225 = VECTOR('',#1226,1.);
#1226 = DIRECTION('',(0.130527626456,-0.991444672552,0.E+000));
#1227 = PCURVE('',#1182,#1228);
#1228 = DEFINITIONAL_REPRESENTATION('',(#1229),#1233);
#1229 = LINE('',#1230,#1231);
#1230 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1231 = VECTOR('',#1232,1.);
#1232 = DIRECTION('',(1.,0.E+000));
#1233 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1234 = PCURVE('',#83,#1235);
#1235 = DEFINITIONAL_REPRESENTATION('',(#1236),#1240);
#1236 = LINE('',#1237,#1238);
#1237 = CARTESIAN_POINT('',(37.49137028,13.99999994));
#1238 = VECTOR('',#1239,1.);
#1239 = DIRECTION('',(0.130527626456,-0.991444672552));
#1240 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1241 = ORIENTED_EDGE('',*,*,#1242,.F.);
#1242 = EDGE_CURVE('',#1243,#1220,#1245,.T.);
#1243 = VERTEX_POINT('',#1244);
#1244 = CARTESIAN_POINT('',(37.52575172,13.73895906,0.E+000));
#1245 = SURFACE_CURVE('',#1246,(#1250,#1257),.PCURVE_S1.);
#1246 = LINE('',#1247,#1248);
#1247 = CARTESIAN_POINT('',(37.52575172,13.73895906,0.E+000));
#1248 = VECTOR('',#1249,1.);
#1249 = DIRECTION('',(0.E+000,0.E+000,1.));
#1250 = PCURVE('',#1182,#1251);
#1251 = DEFINITIONAL_REPRESENTATION('',(#1252),#1256);
#1252 = LINE('',#1253,#1254);
#1253 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#1254 = VECTOR('',#1255,1.);
#1255 = DIRECTION('',(0.E+000,-1.));
#1256 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1257 = PCURVE('',#1258,#1263);
#1258 = PLANE('',#1259);
#1259 = AXIS2_PLACEMENT_3D('',#1260,#1261,#1262);
#1260 = CARTESIAN_POINT('',(37.52575172,13.73895906,0.E+000));
#1261 = DIRECTION('',(0.923879741566,0.382682927661,-0.E+000));
#1262 = DIRECTION('',(0.382682927661,-0.923879741566,0.E+000));
#1263 = DEFINITIONAL_REPRESENTATION('',(#1264),#1268);
#1264 = LINE('',#1265,#1266);
#1265 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1266 = VECTOR('',#1267,1.);
#1267 = DIRECTION('',(0.E+000,-1.));
#1268 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1269 = ORIENTED_EDGE('',*,*,#1270,.F.);
#1270 = EDGE_CURVE('',#1167,#1243,#1271,.T.);
#1271 = SURFACE_CURVE('',#1272,(#1276,#1283),.PCURVE_S1.);
#1272 = LINE('',#1273,#1274);
#1273 = CARTESIAN_POINT('',(37.49138298,14.00001264,0.E+000));
#1274 = VECTOR('',#1275,1.);
#1275 = DIRECTION('',(0.130527626456,-0.991444672552,0.E+000));
#1276 = PCURVE('',#1182,#1277);
#1277 = DEFINITIONAL_REPRESENTATION('',(#1278),#1282);
#1278 = LINE('',#1279,#1280);
#1279 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1280 = VECTOR('',#1281,1.);
#1281 = DIRECTION('',(1.,0.E+000));
#1282 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1283 = PCURVE('',#137,#1284);
#1284 = DEFINITIONAL_REPRESENTATION('',(#1285),#1289);
#1285 = LINE('',#1286,#1287);
#1286 = CARTESIAN_POINT('',(37.49137028,13.99999994));
#1287 = VECTOR('',#1288,1.);
#1288 = DIRECTION('',(0.130527626456,-0.991444672552));
#1289 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1290 = ADVANCED_FACE('',(#1291),#1258,.F.);
#1291 = FACE_BOUND('',#1292,.F.);
#1292 = EDGE_LOOP('',(#1293,#1294,#1317,#1345));
#1293 = ORIENTED_EDGE('',*,*,#1242,.T.);
#1294 = ORIENTED_EDGE('',*,*,#1295,.T.);
#1295 = EDGE_CURVE('',#1220,#1296,#1298,.T.);
#1296 = VERTEX_POINT('',#1297);
#1297 = CARTESIAN_POINT('',(37.62651352,13.49569818,1.64592));
#1298 = SURFACE_CURVE('',#1299,(#1303,#1310),.PCURVE_S1.);
#1299 = LINE('',#1300,#1301);
#1300 = CARTESIAN_POINT('',(37.52575172,13.73895906,1.64592));
#1301 = VECTOR('',#1302,1.);
#1302 = DIRECTION('',(0.382682927661,-0.923879741566,0.E+000));
#1303 = PCURVE('',#1258,#1304);
#1304 = DEFINITIONAL_REPRESENTATION('',(#1305),#1309);
#1305 = LINE('',#1306,#1307);
#1306 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1307 = VECTOR('',#1308,1.);
#1308 = DIRECTION('',(1.,0.E+000));
#1309 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1310 = PCURVE('',#83,#1311);
#1311 = DEFINITIONAL_REPRESENTATION('',(#1312),#1316);
#1312 = LINE('',#1313,#1314);
#1313 = CARTESIAN_POINT('',(37.52573902,13.73894636));
#1314 = VECTOR('',#1315,1.);
#1315 = DIRECTION('',(0.382682927661,-0.923879741566));
#1316 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1317 = ORIENTED_EDGE('',*,*,#1318,.F.);
#1318 = EDGE_CURVE('',#1319,#1296,#1321,.T.);
#1319 = VERTEX_POINT('',#1320);
#1320 = CARTESIAN_POINT('',(37.62651352,13.49569818,0.E+000));
#1321 = SURFACE_CURVE('',#1322,(#1326,#1333),.PCURVE_S1.);
#1322 = LINE('',#1323,#1324);
#1323 = CARTESIAN_POINT('',(37.62651352,13.49569818,0.E+000));
#1324 = VECTOR('',#1325,1.);
#1325 = DIRECTION('',(0.E+000,0.E+000,1.));
#1326 = PCURVE('',#1258,#1327);
#1327 = DEFINITIONAL_REPRESENTATION('',(#1328),#1332);
#1328 = LINE('',#1329,#1330);
#1329 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#1330 = VECTOR('',#1331,1.);
#1331 = DIRECTION('',(0.E+000,-1.));
#1332 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1333 = PCURVE('',#1334,#1339);
#1334 = PLANE('',#1335);
#1335 = AXIS2_PLACEMENT_3D('',#1336,#1337,#1338);
#1336 = CARTESIAN_POINT('',(37.62651352,13.49569818,0.E+000));
#1337 = DIRECTION('',(0.793355698391,0.608758355861,-0.E+000));
#1338 = DIRECTION('',(0.608758355861,-0.793355698391,0.E+000));
#1339 = DEFINITIONAL_REPRESENTATION('',(#1340),#1344);
#1340 = LINE('',#1341,#1342);
#1341 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1342 = VECTOR('',#1343,1.);
#1343 = DIRECTION('',(0.E+000,-1.));
#1344 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1345 = ORIENTED_EDGE('',*,*,#1346,.F.);
#1346 = EDGE_CURVE('',#1243,#1319,#1347,.T.);
#1347 = SURFACE_CURVE('',#1348,(#1352,#1359),.PCURVE_S1.);
#1348 = LINE('',#1349,#1350);
#1349 = CARTESIAN_POINT('',(37.52575172,13.73895906,0.E+000));
#1350 = VECTOR('',#1351,1.);
#1351 = DIRECTION('',(0.382682927661,-0.923879741566,0.E+000));
#1352 = PCURVE('',#1258,#1353);
#1353 = DEFINITIONAL_REPRESENTATION('',(#1354),#1358);
#1354 = LINE('',#1355,#1356);
#1355 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1356 = VECTOR('',#1357,1.);
#1357 = DIRECTION('',(1.,0.E+000));
#1358 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1359 = PCURVE('',#137,#1360);
#1360 = DEFINITIONAL_REPRESENTATION('',(#1361),#1365);
#1361 = LINE('',#1362,#1363);
#1362 = CARTESIAN_POINT('',(37.52573902,13.73894636));
#1363 = VECTOR('',#1364,1.);
#1364 = DIRECTION('',(0.382682927661,-0.923879741566));
#1365 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1366 = ADVANCED_FACE('',(#1367),#1334,.F.);
#1367 = FACE_BOUND('',#1368,.F.);
#1368 = EDGE_LOOP('',(#1369,#1370,#1393,#1421));
#1369 = ORIENTED_EDGE('',*,*,#1318,.T.);
#1370 = ORIENTED_EDGE('',*,*,#1371,.T.);
#1371 = EDGE_CURVE('',#1296,#1372,#1374,.T.);
#1372 = VERTEX_POINT('',#1373);
#1373 = CARTESIAN_POINT('',(37.78680276,13.2868035,1.64592));
#1374 = SURFACE_CURVE('',#1375,(#1379,#1386),.PCURVE_S1.);
#1375 = LINE('',#1376,#1377);
#1376 = CARTESIAN_POINT('',(37.62651352,13.49569818,1.64592));
#1377 = VECTOR('',#1378,1.);
#1378 = DIRECTION('',(0.608758355861,-0.793355698391,0.E+000));
#1379 = PCURVE('',#1334,#1380);
#1380 = DEFINITIONAL_REPRESENTATION('',(#1381),#1385);
#1381 = LINE('',#1382,#1383);
#1382 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1383 = VECTOR('',#1384,1.);
#1384 = DIRECTION('',(1.,0.E+000));
#1385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1386 = PCURVE('',#83,#1387);
#1387 = DEFINITIONAL_REPRESENTATION('',(#1388),#1392);
#1388 = LINE('',#1389,#1390);
#1389 = CARTESIAN_POINT('',(37.62650082,13.49568548));
#1390 = VECTOR('',#1391,1.);
#1391 = DIRECTION('',(0.608758355861,-0.793355698391));
#1392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1393 = ORIENTED_EDGE('',*,*,#1394,.F.);
#1394 = EDGE_CURVE('',#1395,#1372,#1397,.T.);
#1395 = VERTEX_POINT('',#1396);
#1396 = CARTESIAN_POINT('',(37.78680276,13.2868035,0.E+000));
#1397 = SURFACE_CURVE('',#1398,(#1402,#1409),.PCURVE_S1.);
#1398 = LINE('',#1399,#1400);
#1399 = CARTESIAN_POINT('',(37.78680276,13.2868035,0.E+000));
#1400 = VECTOR('',#1401,1.);
#1401 = DIRECTION('',(0.E+000,0.E+000,1.));
#1402 = PCURVE('',#1334,#1403);
#1403 = DEFINITIONAL_REPRESENTATION('',(#1404),#1408);
#1404 = LINE('',#1405,#1406);
#1405 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#1406 = VECTOR('',#1407,1.);
#1407 = DIRECTION('',(0.E+000,-1.));
#1408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1409 = PCURVE('',#1410,#1415);
#1410 = PLANE('',#1411);
#1411 = AXIS2_PLACEMENT_3D('',#1412,#1413,#1414);
#1412 = CARTESIAN_POINT('',(37.78680276,13.2868035,0.E+000));
#1413 = DIRECTION('',(0.608758355861,0.793355698391,-0.E+000));
#1414 = DIRECTION('',(0.793355698391,-0.608758355861,0.E+000));
#1415 = DEFINITIONAL_REPRESENTATION('',(#1416),#1420);
#1416 = LINE('',#1417,#1418);
#1417 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1418 = VECTOR('',#1419,1.);
#1419 = DIRECTION('',(0.E+000,-1.));
#1420 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1421 = ORIENTED_EDGE('',*,*,#1422,.F.);
#1422 = EDGE_CURVE('',#1319,#1395,#1423,.T.);
#1423 = SURFACE_CURVE('',#1424,(#1428,#1435),.PCURVE_S1.);
#1424 = LINE('',#1425,#1426);
#1425 = CARTESIAN_POINT('',(37.62651352,13.49569818,0.E+000));
#1426 = VECTOR('',#1427,1.);
#1427 = DIRECTION('',(0.608758355861,-0.793355698391,0.E+000));
#1428 = PCURVE('',#1334,#1429);
#1429 = DEFINITIONAL_REPRESENTATION('',(#1430),#1434);
#1430 = LINE('',#1431,#1432);
#1431 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1432 = VECTOR('',#1433,1.);
#1433 = DIRECTION('',(1.,0.E+000));
#1434 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1435 = PCURVE('',#137,#1436);
#1436 = DEFINITIONAL_REPRESENTATION('',(#1437),#1441);
#1437 = LINE('',#1438,#1439);
#1438 = CARTESIAN_POINT('',(37.62650082,13.49568548));
#1439 = VECTOR('',#1440,1.);
#1440 = DIRECTION('',(0.608758355861,-0.793355698391));
#1441 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1442 = ADVANCED_FACE('',(#1443),#1410,.F.);
#1443 = FACE_BOUND('',#1444,.F.);
#1444 = EDGE_LOOP('',(#1445,#1446,#1469,#1497));
#1445 = ORIENTED_EDGE('',*,*,#1394,.T.);
#1446 = ORIENTED_EDGE('',*,*,#1447,.T.);
#1447 = EDGE_CURVE('',#1372,#1448,#1450,.T.);
#1448 = VERTEX_POINT('',#1449);
#1449 = CARTESIAN_POINT('',(37.99569744,13.12651426,1.64592));
#1450 = SURFACE_CURVE('',#1451,(#1455,#1462),.PCURVE_S1.);
#1451 = LINE('',#1452,#1453);
#1452 = CARTESIAN_POINT('',(37.78680276,13.2868035,1.64592));
#1453 = VECTOR('',#1454,1.);
#1454 = DIRECTION('',(0.793355698391,-0.608758355861,0.E+000));
#1455 = PCURVE('',#1410,#1456);
#1456 = DEFINITIONAL_REPRESENTATION('',(#1457),#1461);
#1457 = LINE('',#1458,#1459);
#1458 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1459 = VECTOR('',#1460,1.);
#1460 = DIRECTION('',(1.,0.E+000));
#1461 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1462 = PCURVE('',#83,#1463);
#1463 = DEFINITIONAL_REPRESENTATION('',(#1464),#1468);
#1464 = LINE('',#1465,#1466);
#1465 = CARTESIAN_POINT('',(37.78679006,13.2867908));
#1466 = VECTOR('',#1467,1.);
#1467 = DIRECTION('',(0.793355698391,-0.608758355861));
#1468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1469 = ORIENTED_EDGE('',*,*,#1470,.F.);
#1470 = EDGE_CURVE('',#1471,#1448,#1473,.T.);
#1471 = VERTEX_POINT('',#1472);
#1472 = CARTESIAN_POINT('',(37.99569744,13.12651426,0.E+000));
#1473 = SURFACE_CURVE('',#1474,(#1478,#1485),.PCURVE_S1.);
#1474 = LINE('',#1475,#1476);
#1475 = CARTESIAN_POINT('',(37.99569744,13.12651426,0.E+000));
#1476 = VECTOR('',#1477,1.);
#1477 = DIRECTION('',(0.E+000,0.E+000,1.));
#1478 = PCURVE('',#1410,#1479);
#1479 = DEFINITIONAL_REPRESENTATION('',(#1480),#1484);
#1480 = LINE('',#1481,#1482);
#1481 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#1482 = VECTOR('',#1483,1.);
#1483 = DIRECTION('',(0.E+000,-1.));
#1484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1485 = PCURVE('',#1486,#1491);
#1486 = PLANE('',#1487);
#1487 = AXIS2_PLACEMENT_3D('',#1488,#1489,#1490);
#1488 = CARTESIAN_POINT('',(37.99569744,13.12651426,0.E+000));
#1489 = DIRECTION('',(0.382682927661,0.923879741566,-0.E+000));
#1490 = DIRECTION('',(0.923879741566,-0.382682927661,0.E+000));
#1491 = DEFINITIONAL_REPRESENTATION('',(#1492),#1496);
#1492 = LINE('',#1493,#1494);
#1493 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1494 = VECTOR('',#1495,1.);
#1495 = DIRECTION('',(0.E+000,-1.));
#1496 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1497 = ORIENTED_EDGE('',*,*,#1498,.F.);
#1498 = EDGE_CURVE('',#1395,#1471,#1499,.T.);
#1499 = SURFACE_CURVE('',#1500,(#1504,#1511),.PCURVE_S1.);
#1500 = LINE('',#1501,#1502);
#1501 = CARTESIAN_POINT('',(37.78680276,13.2868035,0.E+000));
#1502 = VECTOR('',#1503,1.);
#1503 = DIRECTION('',(0.793355698391,-0.608758355861,0.E+000));
#1504 = PCURVE('',#1410,#1505);
#1505 = DEFINITIONAL_REPRESENTATION('',(#1506),#1510);
#1506 = LINE('',#1507,#1508);
#1507 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1508 = VECTOR('',#1509,1.);
#1509 = DIRECTION('',(1.,0.E+000));
#1510 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1511 = PCURVE('',#137,#1512);
#1512 = DEFINITIONAL_REPRESENTATION('',(#1513),#1517);
#1513 = LINE('',#1514,#1515);
#1514 = CARTESIAN_POINT('',(37.78679006,13.2867908));
#1515 = VECTOR('',#1516,1.);
#1516 = DIRECTION('',(0.793355698391,-0.608758355861));
#1517 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1518 = ADVANCED_FACE('',(#1519),#1486,.F.);
#1519 = FACE_BOUND('',#1520,.F.);
#1520 = EDGE_LOOP('',(#1521,#1522,#1545,#1573));
#1521 = ORIENTED_EDGE('',*,*,#1470,.T.);
#1522 = ORIENTED_EDGE('',*,*,#1523,.T.);
#1523 = EDGE_CURVE('',#1448,#1524,#1526,.T.);
#1524 = VERTEX_POINT('',#1525);
#1525 = CARTESIAN_POINT('',(38.23895832,13.02575246,1.64592));
#1526 = SURFACE_CURVE('',#1527,(#1531,#1538),.PCURVE_S1.);
#1527 = LINE('',#1528,#1529);
#1528 = CARTESIAN_POINT('',(37.99569744,13.12651426,1.64592));
#1529 = VECTOR('',#1530,1.);
#1530 = DIRECTION('',(0.923879741566,-0.382682927661,0.E+000));
#1531 = PCURVE('',#1486,#1532);
#1532 = DEFINITIONAL_REPRESENTATION('',(#1533),#1537);
#1533 = LINE('',#1534,#1535);
#1534 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1535 = VECTOR('',#1536,1.);
#1536 = DIRECTION('',(1.,0.E+000));
#1537 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1538 = PCURVE('',#83,#1539);
#1539 = DEFINITIONAL_REPRESENTATION('',(#1540),#1544);
#1540 = LINE('',#1541,#1542);
#1541 = CARTESIAN_POINT('',(37.99568474,13.12650156));
#1542 = VECTOR('',#1543,1.);
#1543 = DIRECTION('',(0.923879741566,-0.382682927661));
#1544 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1545 = ORIENTED_EDGE('',*,*,#1546,.F.);
#1546 = EDGE_CURVE('',#1547,#1524,#1549,.T.);
#1547 = VERTEX_POINT('',#1548);
#1548 = CARTESIAN_POINT('',(38.23895832,13.02575246,0.E+000));
#1549 = SURFACE_CURVE('',#1550,(#1554,#1561),.PCURVE_S1.);
#1550 = LINE('',#1551,#1552);
#1551 = CARTESIAN_POINT('',(38.23895832,13.02575246,0.E+000));
#1552 = VECTOR('',#1553,1.);
#1553 = DIRECTION('',(0.E+000,0.E+000,1.));
#1554 = PCURVE('',#1486,#1555);
#1555 = DEFINITIONAL_REPRESENTATION('',(#1556),#1560);
#1556 = LINE('',#1557,#1558);
#1557 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#1558 = VECTOR('',#1559,1.);
#1559 = DIRECTION('',(0.E+000,-1.));
#1560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1561 = PCURVE('',#1562,#1567);
#1562 = PLANE('',#1563);
#1563 = AXIS2_PLACEMENT_3D('',#1564,#1565,#1566);
#1564 = CARTESIAN_POINT('',(38.23895832,13.02575246,0.E+000));
#1565 = DIRECTION('',(0.130527626456,0.991444672552,-0.E+000));
#1566 = DIRECTION('',(0.991444672552,-0.130527626456,0.E+000));
#1567 = DEFINITIONAL_REPRESENTATION('',(#1568),#1572);
#1568 = LINE('',#1569,#1570);
#1569 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1570 = VECTOR('',#1571,1.);
#1571 = DIRECTION('',(0.E+000,-1.));
#1572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1573 = ORIENTED_EDGE('',*,*,#1574,.F.);
#1574 = EDGE_CURVE('',#1471,#1547,#1575,.T.);
#1575 = SURFACE_CURVE('',#1576,(#1580,#1587),.PCURVE_S1.);
#1576 = LINE('',#1577,#1578);
#1577 = CARTESIAN_POINT('',(37.99569744,13.12651426,0.E+000));
#1578 = VECTOR('',#1579,1.);
#1579 = DIRECTION('',(0.923879741566,-0.382682927661,0.E+000));
#1580 = PCURVE('',#1486,#1581);
#1581 = DEFINITIONAL_REPRESENTATION('',(#1582),#1586);
#1582 = LINE('',#1583,#1584);
#1583 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1584 = VECTOR('',#1585,1.);
#1585 = DIRECTION('',(1.,0.E+000));
#1586 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1587 = PCURVE('',#137,#1588);
#1588 = DEFINITIONAL_REPRESENTATION('',(#1589),#1593);
#1589 = LINE('',#1590,#1591);
#1590 = CARTESIAN_POINT('',(37.99568474,13.12650156));
#1591 = VECTOR('',#1592,1.);
#1592 = DIRECTION('',(0.923879741566,-0.382682927661));
#1593 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1594 = ADVANCED_FACE('',(#1595),#1562,.F.);
#1595 = FACE_BOUND('',#1596,.F.);
#1596 = EDGE_LOOP('',(#1597,#1598,#1621,#1649));
#1597 = ORIENTED_EDGE('',*,*,#1546,.T.);
#1598 = ORIENTED_EDGE('',*,*,#1599,.T.);
#1599 = EDGE_CURVE('',#1524,#1600,#1602,.T.);
#1600 = VERTEX_POINT('',#1601);
#1601 = CARTESIAN_POINT('',(38.5000119,12.99138372,1.64592));
#1602 = SURFACE_CURVE('',#1603,(#1607,#1614),.PCURVE_S1.);
#1603 = LINE('',#1604,#1605);
#1604 = CARTESIAN_POINT('',(38.23895832,13.02575246,1.64592));
#1605 = VECTOR('',#1606,1.);
#1606 = DIRECTION('',(0.991444672552,-0.130527626456,0.E+000));
#1607 = PCURVE('',#1562,#1608);
#1608 = DEFINITIONAL_REPRESENTATION('',(#1609),#1613);
#1609 = LINE('',#1610,#1611);
#1610 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1611 = VECTOR('',#1612,1.);
#1612 = DIRECTION('',(1.,0.E+000));
#1613 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1614 = PCURVE('',#83,#1615);
#1615 = DEFINITIONAL_REPRESENTATION('',(#1616),#1620);
#1616 = LINE('',#1617,#1618);
#1617 = CARTESIAN_POINT('',(38.23894562,13.02573976));
#1618 = VECTOR('',#1619,1.);
#1619 = DIRECTION('',(0.991444672552,-0.130527626456));
#1620 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1621 = ORIENTED_EDGE('',*,*,#1622,.F.);
#1622 = EDGE_CURVE('',#1623,#1600,#1625,.T.);
#1623 = VERTEX_POINT('',#1624);
#1624 = CARTESIAN_POINT('',(38.5000119,12.99138372,0.E+000));
#1625 = SURFACE_CURVE('',#1626,(#1630,#1637),.PCURVE_S1.);
#1626 = LINE('',#1627,#1628);
#1627 = CARTESIAN_POINT('',(38.5000119,12.99138372,0.E+000));
#1628 = VECTOR('',#1629,1.);
#1629 = DIRECTION('',(0.E+000,0.E+000,1.));
#1630 = PCURVE('',#1562,#1631);
#1631 = DEFINITIONAL_REPRESENTATION('',(#1632),#1636);
#1632 = LINE('',#1633,#1634);
#1633 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#1634 = VECTOR('',#1635,1.);
#1635 = DIRECTION('',(0.E+000,-1.));
#1636 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1637 = PCURVE('',#1638,#1643);
#1638 = PLANE('',#1639);
#1639 = AXIS2_PLACEMENT_3D('',#1640,#1641,#1642);
#1640 = CARTESIAN_POINT('',(38.5000119,12.99138372,0.E+000));
#1641 = DIRECTION('',(-1.627996183954E-004,0.999999986748,0.E+000));
#1642 = DIRECTION('',(0.999999986748,1.627996183954E-004,0.E+000));
#1643 = DEFINITIONAL_REPRESENTATION('',(#1644),#1648);
#1644 = LINE('',#1645,#1646);
#1645 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1646 = VECTOR('',#1647,1.);
#1647 = DIRECTION('',(0.E+000,-1.));
#1648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1649 = ORIENTED_EDGE('',*,*,#1650,.F.);
#1650 = EDGE_CURVE('',#1547,#1623,#1651,.T.);
#1651 = SURFACE_CURVE('',#1652,(#1656,#1663),.PCURVE_S1.);
#1652 = LINE('',#1653,#1654);
#1653 = CARTESIAN_POINT('',(38.23895832,13.02575246,0.E+000));
#1654 = VECTOR('',#1655,1.);
#1655 = DIRECTION('',(0.991444672552,-0.130527626456,0.E+000));
#1656 = PCURVE('',#1562,#1657);
#1657 = DEFINITIONAL_REPRESENTATION('',(#1658),#1662);
#1658 = LINE('',#1659,#1660);
#1659 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1660 = VECTOR('',#1661,1.);
#1661 = DIRECTION('',(1.,0.E+000));
#1662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1663 = PCURVE('',#137,#1664);
#1664 = DEFINITIONAL_REPRESENTATION('',(#1665),#1669);
#1665 = LINE('',#1666,#1667);
#1666 = CARTESIAN_POINT('',(38.23894562,13.02573976));
#1667 = VECTOR('',#1668,1.);
#1668 = DIRECTION('',(0.991444672552,-0.130527626456));
#1669 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1670 = ADVANCED_FACE('',(#1671),#1638,.F.);
#1671 = FACE_BOUND('',#1672,.F.);
#1672 = EDGE_LOOP('',(#1673,#1674,#1697,#1725));
#1673 = ORIENTED_EDGE('',*,*,#1622,.T.);
#1674 = ORIENTED_EDGE('',*,*,#1675,.T.);
#1675 = EDGE_CURVE('',#1600,#1676,#1678,.T.);
#1676 = VERTEX_POINT('',#1677);
#1677 = CARTESIAN_POINT('',(91.50001258,13.0000121,1.64592));
#1678 = SURFACE_CURVE('',#1679,(#1683,#1690),.PCURVE_S1.);
#1679 = LINE('',#1680,#1681);
#1680 = CARTESIAN_POINT('',(38.5000119,12.99138372,1.64592));
#1681 = VECTOR('',#1682,1.);
#1682 = DIRECTION('',(0.999999986748,1.627996183954E-004,0.E+000));
#1683 = PCURVE('',#1638,#1684);
#1684 = DEFINITIONAL_REPRESENTATION('',(#1685),#1689);
#1685 = LINE('',#1686,#1687);
#1686 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1687 = VECTOR('',#1688,1.);
#1688 = DIRECTION('',(1.,0.E+000));
#1689 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1690 = PCURVE('',#83,#1691);
#1691 = DEFINITIONAL_REPRESENTATION('',(#1692),#1696);
#1692 = LINE('',#1693,#1694);
#1693 = CARTESIAN_POINT('',(38.4999992,12.99137102));
#1694 = VECTOR('',#1695,1.);
#1695 = DIRECTION('',(0.999999986748,1.627996183954E-004));
#1696 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1697 = ORIENTED_EDGE('',*,*,#1698,.F.);
#1698 = EDGE_CURVE('',#1699,#1676,#1701,.T.);
#1699 = VERTEX_POINT('',#1700);
#1700 = CARTESIAN_POINT('',(91.50001258,13.0000121,0.E+000));
#1701 = SURFACE_CURVE('',#1702,(#1706,#1713),.PCURVE_S1.);
#1702 = LINE('',#1703,#1704);
#1703 = CARTESIAN_POINT('',(91.50001258,13.0000121,0.E+000));
#1704 = VECTOR('',#1705,1.);
#1705 = DIRECTION('',(0.E+000,0.E+000,1.));
#1706 = PCURVE('',#1638,#1707);
#1707 = DEFINITIONAL_REPRESENTATION('',(#1708),#1712);
#1708 = LINE('',#1709,#1710);
#1709 = CARTESIAN_POINT('',(53.000001382348,0.E+000));
#1710 = VECTOR('',#1711,1.);
#1711 = DIRECTION('',(0.E+000,-1.));
#1712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1713 = PCURVE('',#1714,#1719);
#1714 = PLANE('',#1715);
#1715 = AXIS2_PLACEMENT_3D('',#1716,#1717,#1718);
#1716 = CARTESIAN_POINT('',(91.50001258,13.0000121,0.E+000));
#1717 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1718 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1719 = DEFINITIONAL_REPRESENTATION('',(#1720),#1724);
#1720 = LINE('',#1721,#1722);
#1721 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1722 = VECTOR('',#1723,1.);
#1723 = DIRECTION('',(0.E+000,-1.));
#1724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1725 = ORIENTED_EDGE('',*,*,#1726,.F.);
#1726 = EDGE_CURVE('',#1623,#1699,#1727,.T.);
#1727 = SURFACE_CURVE('',#1728,(#1732,#1739),.PCURVE_S1.);
#1728 = LINE('',#1729,#1730);
#1729 = CARTESIAN_POINT('',(38.5000119,12.99138372,0.E+000));
#1730 = VECTOR('',#1731,1.);
#1731 = DIRECTION('',(0.999999986748,1.627996183954E-004,0.E+000));
#1732 = PCURVE('',#1638,#1733);
#1733 = DEFINITIONAL_REPRESENTATION('',(#1734),#1738);
#1734 = LINE('',#1735,#1736);
#1735 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1736 = VECTOR('',#1737,1.);
#1737 = DIRECTION('',(1.,0.E+000));
#1738 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1739 = PCURVE('',#137,#1740);
#1740 = DEFINITIONAL_REPRESENTATION('',(#1741),#1745);
#1741 = LINE('',#1742,#1743);
#1742 = CARTESIAN_POINT('',(38.4999992,12.99137102));
#1743 = VECTOR('',#1744,1.);
#1744 = DIRECTION('',(0.999999986748,1.627996183954E-004));
#1745 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1746 = ADVANCED_FACE('',(#1747),#1714,.F.);
#1747 = FACE_BOUND('',#1748,.F.);
#1748 = EDGE_LOOP('',(#1749,#1750,#1773,#1801));
#1749 = ORIENTED_EDGE('',*,*,#1698,.T.);
#1750 = ORIENTED_EDGE('',*,*,#1751,.T.);
#1751 = EDGE_CURVE('',#1676,#1752,#1754,.T.);
#1752 = VERTEX_POINT('',#1753);
#1753 = CARTESIAN_POINT('',(91.50001258,12.99138372,1.64592));
#1754 = SURFACE_CURVE('',#1755,(#1759,#1766),.PCURVE_S1.);
#1755 = LINE('',#1756,#1757);
#1756 = CARTESIAN_POINT('',(91.50001258,13.0000121,1.64592));
#1757 = VECTOR('',#1758,1.);
#1758 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1759 = PCURVE('',#1714,#1760);
#1760 = DEFINITIONAL_REPRESENTATION('',(#1761),#1765);
#1761 = LINE('',#1762,#1763);
#1762 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1763 = VECTOR('',#1764,1.);
#1764 = DIRECTION('',(1.,0.E+000));
#1765 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1766 = PCURVE('',#83,#1767);
#1767 = DEFINITIONAL_REPRESENTATION('',(#1768),#1772);
#1768 = LINE('',#1769,#1770);
#1769 = CARTESIAN_POINT('',(91.49999988,12.9999994));
#1770 = VECTOR('',#1771,1.);
#1771 = DIRECTION('',(0.E+000,-1.));
#1772 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1773 = ORIENTED_EDGE('',*,*,#1774,.F.);
#1774 = EDGE_CURVE('',#1775,#1752,#1777,.T.);
#1775 = VERTEX_POINT('',#1776);
#1776 = CARTESIAN_POINT('',(91.50001258,12.99138372,0.E+000));
#1777 = SURFACE_CURVE('',#1778,(#1782,#1789),.PCURVE_S1.);
#1778 = LINE('',#1779,#1780);
#1779 = CARTESIAN_POINT('',(91.50001258,12.99138372,0.E+000));
#1780 = VECTOR('',#1781,1.);
#1781 = DIRECTION('',(0.E+000,0.E+000,1.));
#1782 = PCURVE('',#1714,#1783);
#1783 = DEFINITIONAL_REPRESENTATION('',(#1784),#1788);
#1784 = LINE('',#1785,#1786);
#1785 = CARTESIAN_POINT('',(8.628380000005E-003,0.E+000));
#1786 = VECTOR('',#1787,1.);
#1787 = DIRECTION('',(0.E+000,-1.));
#1788 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1789 = PCURVE('',#1790,#1795);
#1790 = PLANE('',#1791);
#1791 = AXIS2_PLACEMENT_3D('',#1792,#1793,#1794);
#1792 = CARTESIAN_POINT('',(91.50001258,12.99138372,0.E+000));
#1793 = DIRECTION('',(-0.130527626456,0.991444672552,0.E+000));
#1794 = DIRECTION('',(0.991444672552,0.130527626456,0.E+000));
#1795 = DEFINITIONAL_REPRESENTATION('',(#1796),#1800);
#1796 = LINE('',#1797,#1798);
#1797 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1798 = VECTOR('',#1799,1.);
#1799 = DIRECTION('',(0.E+000,-1.));
#1800 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1801 = ORIENTED_EDGE('',*,*,#1802,.F.);
#1802 = EDGE_CURVE('',#1699,#1775,#1803,.T.);
#1803 = SURFACE_CURVE('',#1804,(#1808,#1815),.PCURVE_S1.);
#1804 = LINE('',#1805,#1806);
#1805 = CARTESIAN_POINT('',(91.50001258,13.0000121,0.E+000));
#1806 = VECTOR('',#1807,1.);
#1807 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1808 = PCURVE('',#1714,#1809);
#1809 = DEFINITIONAL_REPRESENTATION('',(#1810),#1814);
#1810 = LINE('',#1811,#1812);
#1811 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1812 = VECTOR('',#1813,1.);
#1813 = DIRECTION('',(1.,0.E+000));
#1814 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1815 = PCURVE('',#137,#1816);
#1816 = DEFINITIONAL_REPRESENTATION('',(#1817),#1821);
#1817 = LINE('',#1818,#1819);
#1818 = CARTESIAN_POINT('',(91.49999988,12.9999994));
#1819 = VECTOR('',#1820,1.);
#1820 = DIRECTION('',(0.E+000,-1.));
#1821 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1822 = ADVANCED_FACE('',(#1823),#1790,.F.);
#1823 = FACE_BOUND('',#1824,.F.);
#1824 = EDGE_LOOP('',(#1825,#1826,#1849,#1877));
#1825 = ORIENTED_EDGE('',*,*,#1774,.T.);
#1826 = ORIENTED_EDGE('',*,*,#1827,.T.);
#1827 = EDGE_CURVE('',#1752,#1828,#1830,.T.);
#1828 = VERTEX_POINT('',#1829);
#1829 = CARTESIAN_POINT('',(91.76106616,13.02575246,1.64592));
#1830 = SURFACE_CURVE('',#1831,(#1835,#1842),.PCURVE_S1.);
#1831 = LINE('',#1832,#1833);
#1832 = CARTESIAN_POINT('',(91.50001258,12.99138372,1.64592));
#1833 = VECTOR('',#1834,1.);
#1834 = DIRECTION('',(0.991444672552,0.130527626456,0.E+000));
#1835 = PCURVE('',#1790,#1836);
#1836 = DEFINITIONAL_REPRESENTATION('',(#1837),#1841);
#1837 = LINE('',#1838,#1839);
#1838 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1839 = VECTOR('',#1840,1.);
#1840 = DIRECTION('',(1.,0.E+000));
#1841 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1842 = PCURVE('',#83,#1843);
#1843 = DEFINITIONAL_REPRESENTATION('',(#1844),#1848);
#1844 = LINE('',#1845,#1846);
#1845 = CARTESIAN_POINT('',(91.49999988,12.99137102));
#1846 = VECTOR('',#1847,1.);
#1847 = DIRECTION('',(0.991444672552,0.130527626456));
#1848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1849 = ORIENTED_EDGE('',*,*,#1850,.F.);
#1850 = EDGE_CURVE('',#1851,#1828,#1853,.T.);
#1851 = VERTEX_POINT('',#1852);
#1852 = CARTESIAN_POINT('',(91.76106616,13.02575246,0.E+000));
#1853 = SURFACE_CURVE('',#1854,(#1858,#1865),.PCURVE_S1.);
#1854 = LINE('',#1855,#1856);
#1855 = CARTESIAN_POINT('',(91.76106616,13.02575246,0.E+000));
#1856 = VECTOR('',#1857,1.);
#1857 = DIRECTION('',(0.E+000,0.E+000,1.));
#1858 = PCURVE('',#1790,#1859);
#1859 = DEFINITIONAL_REPRESENTATION('',(#1860),#1864);
#1860 = LINE('',#1861,#1862);
#1861 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#1862 = VECTOR('',#1863,1.);
#1863 = DIRECTION('',(0.E+000,-1.));
#1864 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1865 = PCURVE('',#1866,#1871);
#1866 = PLANE('',#1867);
#1867 = AXIS2_PLACEMENT_3D('',#1868,#1869,#1870);
#1868 = CARTESIAN_POINT('',(91.76106616,13.02575246,0.E+000));
#1869 = DIRECTION('',(-0.382682927661,0.923879741566,0.E+000));
#1870 = DIRECTION('',(0.923879741566,0.382682927661,0.E+000));
#1871 = DEFINITIONAL_REPRESENTATION('',(#1872),#1876);
#1872 = LINE('',#1873,#1874);
#1873 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1874 = VECTOR('',#1875,1.);
#1875 = DIRECTION('',(0.E+000,-1.));
#1876 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1877 = ORIENTED_EDGE('',*,*,#1878,.F.);
#1878 = EDGE_CURVE('',#1775,#1851,#1879,.T.);
#1879 = SURFACE_CURVE('',#1880,(#1884,#1891),.PCURVE_S1.);
#1880 = LINE('',#1881,#1882);
#1881 = CARTESIAN_POINT('',(91.50001258,12.99138372,0.E+000));
#1882 = VECTOR('',#1883,1.);
#1883 = DIRECTION('',(0.991444672552,0.130527626456,0.E+000));
#1884 = PCURVE('',#1790,#1885);
#1885 = DEFINITIONAL_REPRESENTATION('',(#1886),#1890);
#1886 = LINE('',#1887,#1888);
#1887 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1888 = VECTOR('',#1889,1.);
#1889 = DIRECTION('',(1.,0.E+000));
#1890 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1891 = PCURVE('',#137,#1892);
#1892 = DEFINITIONAL_REPRESENTATION('',(#1893),#1897);
#1893 = LINE('',#1894,#1895);
#1894 = CARTESIAN_POINT('',(91.49999988,12.99137102));
#1895 = VECTOR('',#1896,1.);
#1896 = DIRECTION('',(0.991444672552,0.130527626456));
#1897 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1898 = ADVANCED_FACE('',(#1899),#1866,.F.);
#1899 = FACE_BOUND('',#1900,.F.);
#1900 = EDGE_LOOP('',(#1901,#1902,#1925,#1953));
#1901 = ORIENTED_EDGE('',*,*,#1850,.T.);
#1902 = ORIENTED_EDGE('',*,*,#1903,.T.);
#1903 = EDGE_CURVE('',#1828,#1904,#1906,.T.);
#1904 = VERTEX_POINT('',#1905);
#1905 = CARTESIAN_POINT('',(92.00432704,13.12651426,1.64592));
#1906 = SURFACE_CURVE('',#1907,(#1911,#1918),.PCURVE_S1.);
#1907 = LINE('',#1908,#1909);
#1908 = CARTESIAN_POINT('',(91.76106616,13.02575246,1.64592));
#1909 = VECTOR('',#1910,1.);
#1910 = DIRECTION('',(0.923879741566,0.382682927661,0.E+000));
#1911 = PCURVE('',#1866,#1912);
#1912 = DEFINITIONAL_REPRESENTATION('',(#1913),#1917);
#1913 = LINE('',#1914,#1915);
#1914 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1915 = VECTOR('',#1916,1.);
#1916 = DIRECTION('',(1.,0.E+000));
#1917 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1918 = PCURVE('',#83,#1919);
#1919 = DEFINITIONAL_REPRESENTATION('',(#1920),#1924);
#1920 = LINE('',#1921,#1922);
#1921 = CARTESIAN_POINT('',(91.76105346,13.02573976));
#1922 = VECTOR('',#1923,1.);
#1923 = DIRECTION('',(0.923879741566,0.382682927661));
#1924 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1925 = ORIENTED_EDGE('',*,*,#1926,.F.);
#1926 = EDGE_CURVE('',#1927,#1904,#1929,.T.);
#1927 = VERTEX_POINT('',#1928);
#1928 = CARTESIAN_POINT('',(92.00432704,13.12651426,0.E+000));
#1929 = SURFACE_CURVE('',#1930,(#1934,#1941),.PCURVE_S1.);
#1930 = LINE('',#1931,#1932);
#1931 = CARTESIAN_POINT('',(92.00432704,13.12651426,0.E+000));
#1932 = VECTOR('',#1933,1.);
#1933 = DIRECTION('',(0.E+000,0.E+000,1.));
#1934 = PCURVE('',#1866,#1935);
#1935 = DEFINITIONAL_REPRESENTATION('',(#1936),#1940);
#1936 = LINE('',#1937,#1938);
#1937 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#1938 = VECTOR('',#1939,1.);
#1939 = DIRECTION('',(0.E+000,-1.));
#1940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1941 = PCURVE('',#1942,#1947);
#1942 = PLANE('',#1943);
#1943 = AXIS2_PLACEMENT_3D('',#1944,#1945,#1946);
#1944 = CARTESIAN_POINT('',(92.00432704,13.12651426,0.E+000));
#1945 = DIRECTION('',(-0.608758355861,0.793355698391,0.E+000));
#1946 = DIRECTION('',(0.793355698391,0.608758355861,0.E+000));
#1947 = DEFINITIONAL_REPRESENTATION('',(#1948),#1952);
#1948 = LINE('',#1949,#1950);
#1949 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1950 = VECTOR('',#1951,1.);
#1951 = DIRECTION('',(0.E+000,-1.));
#1952 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1953 = ORIENTED_EDGE('',*,*,#1954,.F.);
#1954 = EDGE_CURVE('',#1851,#1927,#1955,.T.);
#1955 = SURFACE_CURVE('',#1956,(#1960,#1967),.PCURVE_S1.);
#1956 = LINE('',#1957,#1958);
#1957 = CARTESIAN_POINT('',(91.76106616,13.02575246,0.E+000));
#1958 = VECTOR('',#1959,1.);
#1959 = DIRECTION('',(0.923879741566,0.382682927661,0.E+000));
#1960 = PCURVE('',#1866,#1961);
#1961 = DEFINITIONAL_REPRESENTATION('',(#1962),#1966);
#1962 = LINE('',#1963,#1964);
#1963 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1964 = VECTOR('',#1965,1.);
#1965 = DIRECTION('',(1.,0.E+000));
#1966 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1967 = PCURVE('',#137,#1968);
#1968 = DEFINITIONAL_REPRESENTATION('',(#1969),#1973);
#1969 = LINE('',#1970,#1971);
#1970 = CARTESIAN_POINT('',(91.76105346,13.02573976));
#1971 = VECTOR('',#1972,1.);
#1972 = DIRECTION('',(0.923879741566,0.382682927661));
#1973 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1974 = ADVANCED_FACE('',(#1975),#1942,.F.);
#1975 = FACE_BOUND('',#1976,.F.);
#1976 = EDGE_LOOP('',(#1977,#1978,#2001,#2029));
#1977 = ORIENTED_EDGE('',*,*,#1926,.T.);
#1978 = ORIENTED_EDGE('',*,*,#1979,.T.);
#1979 = EDGE_CURVE('',#1904,#1980,#1982,.T.);
#1980 = VERTEX_POINT('',#1981);
#1981 = CARTESIAN_POINT('',(92.21322172,13.2868035,1.64592));
#1982 = SURFACE_CURVE('',#1983,(#1987,#1994),.PCURVE_S1.);
#1983 = LINE('',#1984,#1985);
#1984 = CARTESIAN_POINT('',(92.00432704,13.12651426,1.64592));
#1985 = VECTOR('',#1986,1.);
#1986 = DIRECTION('',(0.793355698391,0.608758355861,0.E+000));
#1987 = PCURVE('',#1942,#1988);
#1988 = DEFINITIONAL_REPRESENTATION('',(#1989),#1993);
#1989 = LINE('',#1990,#1991);
#1990 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1991 = VECTOR('',#1992,1.);
#1992 = DIRECTION('',(1.,0.E+000));
#1993 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1994 = PCURVE('',#83,#1995);
#1995 = DEFINITIONAL_REPRESENTATION('',(#1996),#2000);
#1996 = LINE('',#1997,#1998);
#1997 = CARTESIAN_POINT('',(92.00431434,13.12650156));
#1998 = VECTOR('',#1999,1.);
#1999 = DIRECTION('',(0.793355698391,0.608758355861));
#2000 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2001 = ORIENTED_EDGE('',*,*,#2002,.F.);
#2002 = EDGE_CURVE('',#2003,#1980,#2005,.T.);
#2003 = VERTEX_POINT('',#2004);
#2004 = CARTESIAN_POINT('',(92.21322172,13.2868035,0.E+000));
#2005 = SURFACE_CURVE('',#2006,(#2010,#2017),.PCURVE_S1.);
#2006 = LINE('',#2007,#2008);
#2007 = CARTESIAN_POINT('',(92.21322172,13.2868035,0.E+000));
#2008 = VECTOR('',#2009,1.);
#2009 = DIRECTION('',(0.E+000,0.E+000,1.));
#2010 = PCURVE('',#1942,#2011);
#2011 = DEFINITIONAL_REPRESENTATION('',(#2012),#2016);
#2012 = LINE('',#2013,#2014);
#2013 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#2014 = VECTOR('',#2015,1.);
#2015 = DIRECTION('',(0.E+000,-1.));
#2016 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2017 = PCURVE('',#2018,#2023);
#2018 = PLANE('',#2019);
#2019 = AXIS2_PLACEMENT_3D('',#2020,#2021,#2022);
#2020 = CARTESIAN_POINT('',(92.21322172,13.2868035,0.E+000));
#2021 = DIRECTION('',(-0.793355698391,0.608758355861,0.E+000));
#2022 = DIRECTION('',(0.608758355861,0.793355698391,0.E+000));
#2023 = DEFINITIONAL_REPRESENTATION('',(#2024),#2028);
#2024 = LINE('',#2025,#2026);
#2025 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2026 = VECTOR('',#2027,1.);
#2027 = DIRECTION('',(0.E+000,-1.));
#2028 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2029 = ORIENTED_EDGE('',*,*,#2030,.F.);
#2030 = EDGE_CURVE('',#1927,#2003,#2031,.T.);
#2031 = SURFACE_CURVE('',#2032,(#2036,#2043),.PCURVE_S1.);
#2032 = LINE('',#2033,#2034);
#2033 = CARTESIAN_POINT('',(92.00432704,13.12651426,0.E+000));
#2034 = VECTOR('',#2035,1.);
#2035 = DIRECTION('',(0.793355698391,0.608758355861,0.E+000));
#2036 = PCURVE('',#1942,#2037);
#2037 = DEFINITIONAL_REPRESENTATION('',(#2038),#2042);
#2038 = LINE('',#2039,#2040);
#2039 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2040 = VECTOR('',#2041,1.);
#2041 = DIRECTION('',(1.,0.E+000));
#2042 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2043 = PCURVE('',#137,#2044);
#2044 = DEFINITIONAL_REPRESENTATION('',(#2045),#2049);
#2045 = LINE('',#2046,#2047);
#2046 = CARTESIAN_POINT('',(92.00431434,13.12650156));
#2047 = VECTOR('',#2048,1.);
#2048 = DIRECTION('',(0.793355698391,0.608758355861));
#2049 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2050 = ADVANCED_FACE('',(#2051),#2018,.F.);
#2051 = FACE_BOUND('',#2052,.F.);
#2052 = EDGE_LOOP('',(#2053,#2054,#2077,#2105));
#2053 = ORIENTED_EDGE('',*,*,#2002,.T.);
#2054 = ORIENTED_EDGE('',*,*,#2055,.T.);
#2055 = EDGE_CURVE('',#1980,#2056,#2058,.T.);
#2056 = VERTEX_POINT('',#2057);
#2057 = CARTESIAN_POINT('',(92.37351096,13.49569818,1.64592));
#2058 = SURFACE_CURVE('',#2059,(#2063,#2070),.PCURVE_S1.);
#2059 = LINE('',#2060,#2061);
#2060 = CARTESIAN_POINT('',(92.21322172,13.2868035,1.64592));
#2061 = VECTOR('',#2062,1.);
#2062 = DIRECTION('',(0.608758355861,0.793355698391,0.E+000));
#2063 = PCURVE('',#2018,#2064);
#2064 = DEFINITIONAL_REPRESENTATION('',(#2065),#2069);
#2065 = LINE('',#2066,#2067);
#2066 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2067 = VECTOR('',#2068,1.);
#2068 = DIRECTION('',(1.,0.E+000));
#2069 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2070 = PCURVE('',#83,#2071);
#2071 = DEFINITIONAL_REPRESENTATION('',(#2072),#2076);
#2072 = LINE('',#2073,#2074);
#2073 = CARTESIAN_POINT('',(92.21320902,13.2867908));
#2074 = VECTOR('',#2075,1.);
#2075 = DIRECTION('',(0.608758355861,0.793355698391));
#2076 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2077 = ORIENTED_EDGE('',*,*,#2078,.F.);
#2078 = EDGE_CURVE('',#2079,#2056,#2081,.T.);
#2079 = VERTEX_POINT('',#2080);
#2080 = CARTESIAN_POINT('',(92.37351096,13.49569818,0.E+000));
#2081 = SURFACE_CURVE('',#2082,(#2086,#2093),.PCURVE_S1.);
#2082 = LINE('',#2083,#2084);
#2083 = CARTESIAN_POINT('',(92.37351096,13.49569818,0.E+000));
#2084 = VECTOR('',#2085,1.);
#2085 = DIRECTION('',(0.E+000,0.E+000,1.));
#2086 = PCURVE('',#2018,#2087);
#2087 = DEFINITIONAL_REPRESENTATION('',(#2088),#2092);
#2088 = LINE('',#2089,#2090);
#2089 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#2090 = VECTOR('',#2091,1.);
#2091 = DIRECTION('',(0.E+000,-1.));
#2092 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2093 = PCURVE('',#2094,#2099);
#2094 = PLANE('',#2095);
#2095 = AXIS2_PLACEMENT_3D('',#2096,#2097,#2098);
#2096 = CARTESIAN_POINT('',(92.37351096,13.49569818,0.E+000));
#2097 = DIRECTION('',(-0.923879741566,0.382682927661,0.E+000));
#2098 = DIRECTION('',(0.382682927661,0.923879741566,0.E+000));
#2099 = DEFINITIONAL_REPRESENTATION('',(#2100),#2104);
#2100 = LINE('',#2101,#2102);
#2101 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2102 = VECTOR('',#2103,1.);
#2103 = DIRECTION('',(0.E+000,-1.));
#2104 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2105 = ORIENTED_EDGE('',*,*,#2106,.F.);
#2106 = EDGE_CURVE('',#2003,#2079,#2107,.T.);
#2107 = SURFACE_CURVE('',#2108,(#2112,#2119),.PCURVE_S1.);
#2108 = LINE('',#2109,#2110);
#2109 = CARTESIAN_POINT('',(92.21322172,13.2868035,0.E+000));
#2110 = VECTOR('',#2111,1.);
#2111 = DIRECTION('',(0.608758355861,0.793355698391,0.E+000));
#2112 = PCURVE('',#2018,#2113);
#2113 = DEFINITIONAL_REPRESENTATION('',(#2114),#2118);
#2114 = LINE('',#2115,#2116);
#2115 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2116 = VECTOR('',#2117,1.);
#2117 = DIRECTION('',(1.,0.E+000));
#2118 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2119 = PCURVE('',#137,#2120);
#2120 = DEFINITIONAL_REPRESENTATION('',(#2121),#2125);
#2121 = LINE('',#2122,#2123);
#2122 = CARTESIAN_POINT('',(92.21320902,13.2867908));
#2123 = VECTOR('',#2124,1.);
#2124 = DIRECTION('',(0.608758355861,0.793355698391));
#2125 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2126 = ADVANCED_FACE('',(#2127),#2094,.F.);
#2127 = FACE_BOUND('',#2128,.F.);
#2128 = EDGE_LOOP('',(#2129,#2130,#2153,#2181));
#2129 = ORIENTED_EDGE('',*,*,#2078,.T.);
#2130 = ORIENTED_EDGE('',*,*,#2131,.T.);
#2131 = EDGE_CURVE('',#2056,#2132,#2134,.T.);
#2132 = VERTEX_POINT('',#2133);
#2133 = CARTESIAN_POINT('',(92.47427276,13.73895906,1.64592));
#2134 = SURFACE_CURVE('',#2135,(#2139,#2146),.PCURVE_S1.);
#2135 = LINE('',#2136,#2137);
#2136 = CARTESIAN_POINT('',(92.37351096,13.49569818,1.64592));
#2137 = VECTOR('',#2138,1.);
#2138 = DIRECTION('',(0.382682927661,0.923879741566,0.E+000));
#2139 = PCURVE('',#2094,#2140);
#2140 = DEFINITIONAL_REPRESENTATION('',(#2141),#2145);
#2141 = LINE('',#2142,#2143);
#2142 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2143 = VECTOR('',#2144,1.);
#2144 = DIRECTION('',(1.,0.E+000));
#2145 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2146 = PCURVE('',#83,#2147);
#2147 = DEFINITIONAL_REPRESENTATION('',(#2148),#2152);
#2148 = LINE('',#2149,#2150);
#2149 = CARTESIAN_POINT('',(92.37349826,13.49568548));
#2150 = VECTOR('',#2151,1.);
#2151 = DIRECTION('',(0.382682927661,0.923879741566));
#2152 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2153 = ORIENTED_EDGE('',*,*,#2154,.F.);
#2154 = EDGE_CURVE('',#2155,#2132,#2157,.T.);
#2155 = VERTEX_POINT('',#2156);
#2156 = CARTESIAN_POINT('',(92.47427276,13.73895906,0.E+000));
#2157 = SURFACE_CURVE('',#2158,(#2162,#2169),.PCURVE_S1.);
#2158 = LINE('',#2159,#2160);
#2159 = CARTESIAN_POINT('',(92.47427276,13.73895906,0.E+000));
#2160 = VECTOR('',#2161,1.);
#2161 = DIRECTION('',(0.E+000,0.E+000,1.));
#2162 = PCURVE('',#2094,#2163);
#2163 = DEFINITIONAL_REPRESENTATION('',(#2164),#2168);
#2164 = LINE('',#2165,#2166);
#2165 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#2166 = VECTOR('',#2167,1.);
#2167 = DIRECTION('',(0.E+000,-1.));
#2168 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2169 = PCURVE('',#2170,#2175);
#2170 = PLANE('',#2171);
#2171 = AXIS2_PLACEMENT_3D('',#2172,#2173,#2174);
#2172 = CARTESIAN_POINT('',(92.47427276,13.73895906,0.E+000));
#2173 = DIRECTION('',(-0.991444672552,0.130527626456,0.E+000));
#2174 = DIRECTION('',(0.130527626456,0.991444672552,0.E+000));
#2175 = DEFINITIONAL_REPRESENTATION('',(#2176),#2180);
#2176 = LINE('',#2177,#2178);
#2177 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2178 = VECTOR('',#2179,1.);
#2179 = DIRECTION('',(0.E+000,-1.));
#2180 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2181 = ORIENTED_EDGE('',*,*,#2182,.F.);
#2182 = EDGE_CURVE('',#2079,#2155,#2183,.T.);
#2183 = SURFACE_CURVE('',#2184,(#2188,#2195),.PCURVE_S1.);
#2184 = LINE('',#2185,#2186);
#2185 = CARTESIAN_POINT('',(92.37351096,13.49569818,0.E+000));
#2186 = VECTOR('',#2187,1.);
#2187 = DIRECTION('',(0.382682927661,0.923879741566,0.E+000));
#2188 = PCURVE('',#2094,#2189);
#2189 = DEFINITIONAL_REPRESENTATION('',(#2190),#2194);
#2190 = LINE('',#2191,#2192);
#2191 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2192 = VECTOR('',#2193,1.);
#2193 = DIRECTION('',(1.,0.E+000));
#2194 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2195 = PCURVE('',#137,#2196);
#2196 = DEFINITIONAL_REPRESENTATION('',(#2197),#2201);
#2197 = LINE('',#2198,#2199);
#2198 = CARTESIAN_POINT('',(92.37349826,13.49568548));
#2199 = VECTOR('',#2200,1.);
#2200 = DIRECTION('',(0.382682927661,0.923879741566));
#2201 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2202 = ADVANCED_FACE('',(#2203),#2170,.F.);
#2203 = FACE_BOUND('',#2204,.F.);
#2204 = EDGE_LOOP('',(#2205,#2206,#2229,#2257));
#2205 = ORIENTED_EDGE('',*,*,#2154,.T.);
#2206 = ORIENTED_EDGE('',*,*,#2207,.T.);
#2207 = EDGE_CURVE('',#2132,#2208,#2210,.T.);
#2208 = VERTEX_POINT('',#2209);
#2209 = CARTESIAN_POINT('',(92.5086415,14.00001264,1.64592));
#2210 = SURFACE_CURVE('',#2211,(#2215,#2222),.PCURVE_S1.);
#2211 = LINE('',#2212,#2213);
#2212 = CARTESIAN_POINT('',(92.47427276,13.73895906,1.64592));
#2213 = VECTOR('',#2214,1.);
#2214 = DIRECTION('',(0.130527626456,0.991444672552,0.E+000));
#2215 = PCURVE('',#2170,#2216);
#2216 = DEFINITIONAL_REPRESENTATION('',(#2217),#2221);
#2217 = LINE('',#2218,#2219);
#2218 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2219 = VECTOR('',#2220,1.);
#2220 = DIRECTION('',(1.,0.E+000));
#2221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2222 = PCURVE('',#83,#2223);
#2223 = DEFINITIONAL_REPRESENTATION('',(#2224),#2228);
#2224 = LINE('',#2225,#2226);
#2225 = CARTESIAN_POINT('',(92.47426006,13.73894636));
#2226 = VECTOR('',#2227,1.);
#2227 = DIRECTION('',(0.130527626456,0.991444672552));
#2228 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2229 = ORIENTED_EDGE('',*,*,#2230,.F.);
#2230 = EDGE_CURVE('',#2231,#2208,#2233,.T.);
#2231 = VERTEX_POINT('',#2232);
#2232 = CARTESIAN_POINT('',(92.5086415,14.00001264,0.E+000));
#2233 = SURFACE_CURVE('',#2234,(#2238,#2245),.PCURVE_S1.);
#2234 = LINE('',#2235,#2236);
#2235 = CARTESIAN_POINT('',(92.5086415,14.00001264,0.E+000));
#2236 = VECTOR('',#2237,1.);
#2237 = DIRECTION('',(0.E+000,0.E+000,1.));
#2238 = PCURVE('',#2170,#2239);
#2239 = DEFINITIONAL_REPRESENTATION('',(#2240),#2244);
#2240 = LINE('',#2241,#2242);
#2241 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#2242 = VECTOR('',#2243,1.);
#2243 = DIRECTION('',(0.E+000,-1.));
#2244 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2245 = PCURVE('',#2246,#2251);
#2246 = PLANE('',#2247);
#2247 = AXIS2_PLACEMENT_3D('',#2248,#2249,#2250);
#2248 = CARTESIAN_POINT('',(92.5086415,14.00001264,0.E+000));
#2249 = DIRECTION('',(-0.999990693999,-4.314153001476E-003,0.E+000));
#2250 = DIRECTION('',(-4.314153001476E-003,0.999990693999,0.E+000));
#2251 = DEFINITIONAL_REPRESENTATION('',(#2252),#2256);
#2252 = LINE('',#2253,#2254);
#2253 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2254 = VECTOR('',#2255,1.);
#2255 = DIRECTION('',(0.E+000,-1.));
#2256 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2257 = ORIENTED_EDGE('',*,*,#2258,.F.);
#2258 = EDGE_CURVE('',#2155,#2231,#2259,.T.);
#2259 = SURFACE_CURVE('',#2260,(#2264,#2271),.PCURVE_S1.);
#2260 = LINE('',#2261,#2262);
#2261 = CARTESIAN_POINT('',(92.47427276,13.73895906,0.E+000));
#2262 = VECTOR('',#2263,1.);
#2263 = DIRECTION('',(0.130527626456,0.991444672552,0.E+000));
#2264 = PCURVE('',#2170,#2265);
#2265 = DEFINITIONAL_REPRESENTATION('',(#2266),#2270);
#2266 = LINE('',#2267,#2268);
#2267 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2268 = VECTOR('',#2269,1.);
#2269 = DIRECTION('',(1.,0.E+000));
#2270 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2271 = PCURVE('',#137,#2272);
#2272 = DEFINITIONAL_REPRESENTATION('',(#2273),#2277);
#2273 = LINE('',#2274,#2275);
#2274 = CARTESIAN_POINT('',(92.47426006,13.73894636));
#2275 = VECTOR('',#2276,1.);
#2276 = DIRECTION('',(0.130527626456,0.991444672552));
#2277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2278 = ADVANCED_FACE('',(#2279),#2246,.F.);
#2279 = FACE_BOUND('',#2280,.F.);
#2280 = EDGE_LOOP('',(#2281,#2282,#2305,#2333));
#2281 = ORIENTED_EDGE('',*,*,#2230,.T.);
#2282 = ORIENTED_EDGE('',*,*,#2283,.T.);
#2283 = EDGE_CURVE('',#2208,#2284,#2286,.T.);
#2284 = VERTEX_POINT('',#2285);
#2285 = CARTESIAN_POINT('',(92.50001312,16.00001118,1.64592));
#2286 = SURFACE_CURVE('',#2287,(#2291,#2298),.PCURVE_S1.);
#2287 = LINE('',#2288,#2289);
#2288 = CARTESIAN_POINT('',(92.5086415,14.00001264,1.64592));
#2289 = VECTOR('',#2290,1.);
#2290 = DIRECTION('',(-4.314153001476E-003,0.999990693999,0.E+000));
#2291 = PCURVE('',#2246,#2292);
#2292 = DEFINITIONAL_REPRESENTATION('',(#2293),#2297);
#2293 = LINE('',#2294,#2295);
#2294 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2295 = VECTOR('',#2296,1.);
#2296 = DIRECTION('',(1.,0.E+000));
#2297 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2298 = PCURVE('',#83,#2299);
#2299 = DEFINITIONAL_REPRESENTATION('',(#2300),#2304);
#2300 = LINE('',#2301,#2302);
#2301 = CARTESIAN_POINT('',(92.5086288,13.99999994));
#2302 = VECTOR('',#2303,1.);
#2303 = DIRECTION('',(-4.314153001476E-003,0.999990693999));
#2304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2305 = ORIENTED_EDGE('',*,*,#2306,.F.);
#2306 = EDGE_CURVE('',#2307,#2284,#2309,.T.);
#2307 = VERTEX_POINT('',#2308);
#2308 = CARTESIAN_POINT('',(92.50001312,16.00001118,0.E+000));
#2309 = SURFACE_CURVE('',#2310,(#2314,#2321),.PCURVE_S1.);
#2310 = LINE('',#2311,#2312);
#2311 = CARTESIAN_POINT('',(92.50001312,16.00001118,0.E+000));
#2312 = VECTOR('',#2313,1.);
#2313 = DIRECTION('',(0.E+000,0.E+000,1.));
#2314 = PCURVE('',#2246,#2315);
#2315 = DEFINITIONAL_REPRESENTATION('',(#2316),#2320);
#2316 = LINE('',#2317,#2318);
#2317 = CARTESIAN_POINT('',(2.000017152162,0.E+000));
#2318 = VECTOR('',#2319,1.);
#2319 = DIRECTION('',(0.E+000,-1.));
#2320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2321 = PCURVE('',#2322,#2327);
#2322 = PLANE('',#2323);
#2323 = AXIS2_PLACEMENT_3D('',#2324,#2325,#2326);
#2324 = CARTESIAN_POINT('',(92.50001312,16.00001118,0.E+000));
#2325 = DIRECTION('',(0.E+000,1.,0.E+000));
#2326 = DIRECTION('',(1.,0.E+000,0.E+000));
#2327 = DEFINITIONAL_REPRESENTATION('',(#2328),#2332);
#2328 = LINE('',#2329,#2330);
#2329 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2330 = VECTOR('',#2331,1.);
#2331 = DIRECTION('',(0.E+000,-1.));
#2332 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2333 = ORIENTED_EDGE('',*,*,#2334,.F.);
#2334 = EDGE_CURVE('',#2231,#2307,#2335,.T.);
#2335 = SURFACE_CURVE('',#2336,(#2340,#2347),.PCURVE_S1.);
#2336 = LINE('',#2337,#2338);
#2337 = CARTESIAN_POINT('',(92.5086415,14.00001264,0.E+000));
#2338 = VECTOR('',#2339,1.);
#2339 = DIRECTION('',(-4.314153001476E-003,0.999990693999,0.E+000));
#2340 = PCURVE('',#2246,#2341);
#2341 = DEFINITIONAL_REPRESENTATION('',(#2342),#2346);
#2342 = LINE('',#2343,#2344);
#2343 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2344 = VECTOR('',#2345,1.);
#2345 = DIRECTION('',(1.,0.E+000));
#2346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2347 = PCURVE('',#137,#2348);
#2348 = DEFINITIONAL_REPRESENTATION('',(#2349),#2353);
#2349 = LINE('',#2350,#2351);
#2350 = CARTESIAN_POINT('',(92.5086288,13.99999994));
#2351 = VECTOR('',#2352,1.);
#2352 = DIRECTION('',(-4.314153001476E-003,0.999990693999));
#2353 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2354 = ADVANCED_FACE('',(#2355),#2322,.F.);
#2355 = FACE_BOUND('',#2356,.F.);
#2356 = EDGE_LOOP('',(#2357,#2358,#2381,#2409));
#2357 = ORIENTED_EDGE('',*,*,#2306,.T.);
#2358 = ORIENTED_EDGE('',*,*,#2359,.T.);
#2359 = EDGE_CURVE('',#2284,#2360,#2362,.T.);
#2360 = VERTEX_POINT('',#2361);
#2361 = CARTESIAN_POINT('',(92.5086415,16.00001118,1.64592));
#2362 = SURFACE_CURVE('',#2363,(#2367,#2374),.PCURVE_S1.);
#2363 = LINE('',#2364,#2365);
#2364 = CARTESIAN_POINT('',(92.50001312,16.00001118,1.64592));
#2365 = VECTOR('',#2366,1.);
#2366 = DIRECTION('',(1.,0.E+000,0.E+000));
#2367 = PCURVE('',#2322,#2368);
#2368 = DEFINITIONAL_REPRESENTATION('',(#2369),#2373);
#2369 = LINE('',#2370,#2371);
#2370 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2371 = VECTOR('',#2372,1.);
#2372 = DIRECTION('',(1.,0.E+000));
#2373 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2374 = PCURVE('',#83,#2375);
#2375 = DEFINITIONAL_REPRESENTATION('',(#2376),#2380);
#2376 = LINE('',#2377,#2378);
#2377 = CARTESIAN_POINT('',(92.50000042,15.99999848));
#2378 = VECTOR('',#2379,1.);
#2379 = DIRECTION('',(1.,0.E+000));
#2380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2381 = ORIENTED_EDGE('',*,*,#2382,.F.);
#2382 = EDGE_CURVE('',#2383,#2360,#2385,.T.);
#2383 = VERTEX_POINT('',#2384);
#2384 = CARTESIAN_POINT('',(92.5086415,16.00001118,0.E+000));
#2385 = SURFACE_CURVE('',#2386,(#2390,#2397),.PCURVE_S1.);
#2386 = LINE('',#2387,#2388);
#2387 = CARTESIAN_POINT('',(92.5086415,16.00001118,0.E+000));
#2388 = VECTOR('',#2389,1.);
#2389 = DIRECTION('',(0.E+000,0.E+000,1.));
#2390 = PCURVE('',#2322,#2391);
#2391 = DEFINITIONAL_REPRESENTATION('',(#2392),#2396);
#2392 = LINE('',#2393,#2394);
#2393 = CARTESIAN_POINT('',(8.628380000005E-003,0.E+000));
#2394 = VECTOR('',#2395,1.);
#2395 = DIRECTION('',(0.E+000,-1.));
#2396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2397 = PCURVE('',#2398,#2403);
#2398 = PLANE('',#2399);
#2399 = AXIS2_PLACEMENT_3D('',#2400,#2401,#2402);
#2400 = CARTESIAN_POINT('',(92.5086415,16.00001118,0.E+000));
#2401 = DIRECTION('',(-0.991444672552,-0.130527626456,0.E+000));
#2402 = DIRECTION('',(-0.130527626456,0.991444672552,0.E+000));
#2403 = DEFINITIONAL_REPRESENTATION('',(#2404),#2408);
#2404 = LINE('',#2405,#2406);
#2405 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2406 = VECTOR('',#2407,1.);
#2407 = DIRECTION('',(0.E+000,-1.));
#2408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2409 = ORIENTED_EDGE('',*,*,#2410,.F.);
#2410 = EDGE_CURVE('',#2307,#2383,#2411,.T.);
#2411 = SURFACE_CURVE('',#2412,(#2416,#2423),.PCURVE_S1.);
#2412 = LINE('',#2413,#2414);
#2413 = CARTESIAN_POINT('',(92.50001312,16.00001118,0.E+000));
#2414 = VECTOR('',#2415,1.);
#2415 = DIRECTION('',(1.,0.E+000,0.E+000));
#2416 = PCURVE('',#2322,#2417);
#2417 = DEFINITIONAL_REPRESENTATION('',(#2418),#2422);
#2418 = LINE('',#2419,#2420);
#2419 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2420 = VECTOR('',#2421,1.);
#2421 = DIRECTION('',(1.,0.E+000));
#2422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2423 = PCURVE('',#137,#2424);
#2424 = DEFINITIONAL_REPRESENTATION('',(#2425),#2429);
#2425 = LINE('',#2426,#2427);
#2426 = CARTESIAN_POINT('',(92.50000042,15.99999848));
#2427 = VECTOR('',#2428,1.);
#2428 = DIRECTION('',(1.,0.E+000));
#2429 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2430 = ADVANCED_FACE('',(#2431),#2398,.F.);
#2431 = FACE_BOUND('',#2432,.F.);
#2432 = EDGE_LOOP('',(#2433,#2434,#2457,#2485));
#2433 = ORIENTED_EDGE('',*,*,#2382,.T.);
#2434 = ORIENTED_EDGE('',*,*,#2435,.T.);
#2435 = EDGE_CURVE('',#2360,#2436,#2438,.T.);
#2436 = VERTEX_POINT('',#2437);
#2437 = CARTESIAN_POINT('',(92.47427276,16.26106476,1.64592));
#2438 = SURFACE_CURVE('',#2439,(#2443,#2450),.PCURVE_S1.);
#2439 = LINE('',#2440,#2441);
#2440 = CARTESIAN_POINT('',(92.5086415,16.00001118,1.64592));
#2441 = VECTOR('',#2442,1.);
#2442 = DIRECTION('',(-0.130527626456,0.991444672552,0.E+000));
#2443 = PCURVE('',#2398,#2444);
#2444 = DEFINITIONAL_REPRESENTATION('',(#2445),#2449);
#2445 = LINE('',#2446,#2447);
#2446 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2447 = VECTOR('',#2448,1.);
#2448 = DIRECTION('',(1.,0.E+000));
#2449 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2450 = PCURVE('',#83,#2451);
#2451 = DEFINITIONAL_REPRESENTATION('',(#2452),#2456);
#2452 = LINE('',#2453,#2454);
#2453 = CARTESIAN_POINT('',(92.5086288,15.99999848));
#2454 = VECTOR('',#2455,1.);
#2455 = DIRECTION('',(-0.130527626456,0.991444672552));
#2456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2457 = ORIENTED_EDGE('',*,*,#2458,.F.);
#2458 = EDGE_CURVE('',#2459,#2436,#2461,.T.);
#2459 = VERTEX_POINT('',#2460);
#2460 = CARTESIAN_POINT('',(92.47427276,16.26106476,0.E+000));
#2461 = SURFACE_CURVE('',#2462,(#2466,#2473),.PCURVE_S1.);
#2462 = LINE('',#2463,#2464);
#2463 = CARTESIAN_POINT('',(92.47427276,16.26106476,0.E+000));
#2464 = VECTOR('',#2465,1.);
#2465 = DIRECTION('',(0.E+000,0.E+000,1.));
#2466 = PCURVE('',#2398,#2467);
#2467 = DEFINITIONAL_REPRESENTATION('',(#2468),#2472);
#2468 = LINE('',#2469,#2470);
#2469 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#2470 = VECTOR('',#2471,1.);
#2471 = DIRECTION('',(0.E+000,-1.));
#2472 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2473 = PCURVE('',#2474,#2479);
#2474 = PLANE('',#2475);
#2475 = AXIS2_PLACEMENT_3D('',#2476,#2477,#2478);
#2476 = CARTESIAN_POINT('',(92.47427276,16.26106476,0.E+000));
#2477 = DIRECTION('',(-0.923879741566,-0.382682927661,0.E+000));
#2478 = DIRECTION('',(-0.382682927661,0.923879741566,0.E+000));
#2479 = DEFINITIONAL_REPRESENTATION('',(#2480),#2484);
#2480 = LINE('',#2481,#2482);
#2481 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2482 = VECTOR('',#2483,1.);
#2483 = DIRECTION('',(0.E+000,-1.));
#2484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2485 = ORIENTED_EDGE('',*,*,#2486,.F.);
#2486 = EDGE_CURVE('',#2383,#2459,#2487,.T.);
#2487 = SURFACE_CURVE('',#2488,(#2492,#2499),.PCURVE_S1.);
#2488 = LINE('',#2489,#2490);
#2489 = CARTESIAN_POINT('',(92.5086415,16.00001118,0.E+000));
#2490 = VECTOR('',#2491,1.);
#2491 = DIRECTION('',(-0.130527626456,0.991444672552,0.E+000));
#2492 = PCURVE('',#2398,#2493);
#2493 = DEFINITIONAL_REPRESENTATION('',(#2494),#2498);
#2494 = LINE('',#2495,#2496);
#2495 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2496 = VECTOR('',#2497,1.);
#2497 = DIRECTION('',(1.,0.E+000));
#2498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2499 = PCURVE('',#137,#2500);
#2500 = DEFINITIONAL_REPRESENTATION('',(#2501),#2505);
#2501 = LINE('',#2502,#2503);
#2502 = CARTESIAN_POINT('',(92.5086288,15.99999848));
#2503 = VECTOR('',#2504,1.);
#2504 = DIRECTION('',(-0.130527626456,0.991444672552));
#2505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2506 = ADVANCED_FACE('',(#2507),#2474,.F.);
#2507 = FACE_BOUND('',#2508,.F.);
#2508 = EDGE_LOOP('',(#2509,#2510,#2533,#2561));
#2509 = ORIENTED_EDGE('',*,*,#2458,.T.);
#2510 = ORIENTED_EDGE('',*,*,#2511,.T.);
#2511 = EDGE_CURVE('',#2436,#2512,#2514,.T.);
#2512 = VERTEX_POINT('',#2513);
#2513 = CARTESIAN_POINT('',(92.37351096,16.50432564,1.64592));
#2514 = SURFACE_CURVE('',#2515,(#2519,#2526),.PCURVE_S1.);
#2515 = LINE('',#2516,#2517);
#2516 = CARTESIAN_POINT('',(92.47427276,16.26106476,1.64592));
#2517 = VECTOR('',#2518,1.);
#2518 = DIRECTION('',(-0.382682927661,0.923879741566,0.E+000));
#2519 = PCURVE('',#2474,#2520);
#2520 = DEFINITIONAL_REPRESENTATION('',(#2521),#2525);
#2521 = LINE('',#2522,#2523);
#2522 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2523 = VECTOR('',#2524,1.);
#2524 = DIRECTION('',(1.,0.E+000));
#2525 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2526 = PCURVE('',#83,#2527);
#2527 = DEFINITIONAL_REPRESENTATION('',(#2528),#2532);
#2528 = LINE('',#2529,#2530);
#2529 = CARTESIAN_POINT('',(92.47426006,16.26105206));
#2530 = VECTOR('',#2531,1.);
#2531 = DIRECTION('',(-0.382682927661,0.923879741566));
#2532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2533 = ORIENTED_EDGE('',*,*,#2534,.F.);
#2534 = EDGE_CURVE('',#2535,#2512,#2537,.T.);
#2535 = VERTEX_POINT('',#2536);
#2536 = CARTESIAN_POINT('',(92.37351096,16.50432564,0.E+000));
#2537 = SURFACE_CURVE('',#2538,(#2542,#2549),.PCURVE_S1.);
#2538 = LINE('',#2539,#2540);
#2539 = CARTESIAN_POINT('',(92.37351096,16.50432564,0.E+000));
#2540 = VECTOR('',#2541,1.);
#2541 = DIRECTION('',(0.E+000,0.E+000,1.));
#2542 = PCURVE('',#2474,#2543);
#2543 = DEFINITIONAL_REPRESENTATION('',(#2544),#2548);
#2544 = LINE('',#2545,#2546);
#2545 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#2546 = VECTOR('',#2547,1.);
#2547 = DIRECTION('',(0.E+000,-1.));
#2548 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2549 = PCURVE('',#2550,#2555);
#2550 = PLANE('',#2551);
#2551 = AXIS2_PLACEMENT_3D('',#2552,#2553,#2554);
#2552 = CARTESIAN_POINT('',(92.37351096,16.50432564,0.E+000));
#2553 = DIRECTION('',(-0.793355698391,-0.608758355861,0.E+000));
#2554 = DIRECTION('',(-0.608758355861,0.793355698391,0.E+000));
#2555 = DEFINITIONAL_REPRESENTATION('',(#2556),#2560);
#2556 = LINE('',#2557,#2558);
#2557 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2558 = VECTOR('',#2559,1.);
#2559 = DIRECTION('',(0.E+000,-1.));
#2560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2561 = ORIENTED_EDGE('',*,*,#2562,.F.);
#2562 = EDGE_CURVE('',#2459,#2535,#2563,.T.);
#2563 = SURFACE_CURVE('',#2564,(#2568,#2575),.PCURVE_S1.);
#2564 = LINE('',#2565,#2566);
#2565 = CARTESIAN_POINT('',(92.47427276,16.26106476,0.E+000));
#2566 = VECTOR('',#2567,1.);
#2567 = DIRECTION('',(-0.382682927661,0.923879741566,0.E+000));
#2568 = PCURVE('',#2474,#2569);
#2569 = DEFINITIONAL_REPRESENTATION('',(#2570),#2574);
#2570 = LINE('',#2571,#2572);
#2571 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2572 = VECTOR('',#2573,1.);
#2573 = DIRECTION('',(1.,0.E+000));
#2574 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2575 = PCURVE('',#137,#2576);
#2576 = DEFINITIONAL_REPRESENTATION('',(#2577),#2581);
#2577 = LINE('',#2578,#2579);
#2578 = CARTESIAN_POINT('',(92.47426006,16.26105206));
#2579 = VECTOR('',#2580,1.);
#2580 = DIRECTION('',(-0.382682927661,0.923879741566));
#2581 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2582 = ADVANCED_FACE('',(#2583),#2550,.F.);
#2583 = FACE_BOUND('',#2584,.F.);
#2584 = EDGE_LOOP('',(#2585,#2586,#2609,#2637));
#2585 = ORIENTED_EDGE('',*,*,#2534,.T.);
#2586 = ORIENTED_EDGE('',*,*,#2587,.T.);
#2587 = EDGE_CURVE('',#2512,#2588,#2590,.T.);
#2588 = VERTEX_POINT('',#2589);
#2589 = CARTESIAN_POINT('',(92.21322172,16.71322032,1.64592));
#2590 = SURFACE_CURVE('',#2591,(#2595,#2602),.PCURVE_S1.);
#2591 = LINE('',#2592,#2593);
#2592 = CARTESIAN_POINT('',(92.37351096,16.50432564,1.64592));
#2593 = VECTOR('',#2594,1.);
#2594 = DIRECTION('',(-0.608758355861,0.793355698391,0.E+000));
#2595 = PCURVE('',#2550,#2596);
#2596 = DEFINITIONAL_REPRESENTATION('',(#2597),#2601);
#2597 = LINE('',#2598,#2599);
#2598 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2599 = VECTOR('',#2600,1.);
#2600 = DIRECTION('',(1.,0.E+000));
#2601 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2602 = PCURVE('',#83,#2603);
#2603 = DEFINITIONAL_REPRESENTATION('',(#2604),#2608);
#2604 = LINE('',#2605,#2606);
#2605 = CARTESIAN_POINT('',(92.37349826,16.50431294));
#2606 = VECTOR('',#2607,1.);
#2607 = DIRECTION('',(-0.608758355861,0.793355698391));
#2608 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2609 = ORIENTED_EDGE('',*,*,#2610,.F.);
#2610 = EDGE_CURVE('',#2611,#2588,#2613,.T.);
#2611 = VERTEX_POINT('',#2612);
#2612 = CARTESIAN_POINT('',(92.21322172,16.71322032,0.E+000));
#2613 = SURFACE_CURVE('',#2614,(#2618,#2625),.PCURVE_S1.);
#2614 = LINE('',#2615,#2616);
#2615 = CARTESIAN_POINT('',(92.21322172,16.71322032,0.E+000));
#2616 = VECTOR('',#2617,1.);
#2617 = DIRECTION('',(0.E+000,0.E+000,1.));
#2618 = PCURVE('',#2550,#2619);
#2619 = DEFINITIONAL_REPRESENTATION('',(#2620),#2624);
#2620 = LINE('',#2621,#2622);
#2621 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#2622 = VECTOR('',#2623,1.);
#2623 = DIRECTION('',(0.E+000,-1.));
#2624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2625 = PCURVE('',#2626,#2631);
#2626 = PLANE('',#2627);
#2627 = AXIS2_PLACEMENT_3D('',#2628,#2629,#2630);
#2628 = CARTESIAN_POINT('',(92.21322172,16.71322032,0.E+000));
#2629 = DIRECTION('',(-0.608758355861,-0.793355698391,0.E+000));
#2630 = DIRECTION('',(-0.793355698391,0.608758355861,0.E+000));
#2631 = DEFINITIONAL_REPRESENTATION('',(#2632),#2636);
#2632 = LINE('',#2633,#2634);
#2633 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2634 = VECTOR('',#2635,1.);
#2635 = DIRECTION('',(0.E+000,-1.));
#2636 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2637 = ORIENTED_EDGE('',*,*,#2638,.F.);
#2638 = EDGE_CURVE('',#2535,#2611,#2639,.T.);
#2639 = SURFACE_CURVE('',#2640,(#2644,#2651),.PCURVE_S1.);
#2640 = LINE('',#2641,#2642);
#2641 = CARTESIAN_POINT('',(92.37351096,16.50432564,0.E+000));
#2642 = VECTOR('',#2643,1.);
#2643 = DIRECTION('',(-0.608758355861,0.793355698391,0.E+000));
#2644 = PCURVE('',#2550,#2645);
#2645 = DEFINITIONAL_REPRESENTATION('',(#2646),#2650);
#2646 = LINE('',#2647,#2648);
#2647 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2648 = VECTOR('',#2649,1.);
#2649 = DIRECTION('',(1.,0.E+000));
#2650 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2651 = PCURVE('',#137,#2652);
#2652 = DEFINITIONAL_REPRESENTATION('',(#2653),#2657);
#2653 = LINE('',#2654,#2655);
#2654 = CARTESIAN_POINT('',(92.37349826,16.50431294));
#2655 = VECTOR('',#2656,1.);
#2656 = DIRECTION('',(-0.608758355861,0.793355698391));
#2657 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2658 = ADVANCED_FACE('',(#2659),#2626,.F.);
#2659 = FACE_BOUND('',#2660,.F.);
#2660 = EDGE_LOOP('',(#2661,#2662,#2685,#2713));
#2661 = ORIENTED_EDGE('',*,*,#2610,.T.);
#2662 = ORIENTED_EDGE('',*,*,#2663,.T.);
#2663 = EDGE_CURVE('',#2588,#2664,#2666,.T.);
#2664 = VERTEX_POINT('',#2665);
#2665 = CARTESIAN_POINT('',(92.00432704,16.87350956,1.64592));
#2666 = SURFACE_CURVE('',#2667,(#2671,#2678),.PCURVE_S1.);
#2667 = LINE('',#2668,#2669);
#2668 = CARTESIAN_POINT('',(92.21322172,16.71322032,1.64592));
#2669 = VECTOR('',#2670,1.);
#2670 = DIRECTION('',(-0.793355698391,0.608758355861,0.E+000));
#2671 = PCURVE('',#2626,#2672);
#2672 = DEFINITIONAL_REPRESENTATION('',(#2673),#2677);
#2673 = LINE('',#2674,#2675);
#2674 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2675 = VECTOR('',#2676,1.);
#2676 = DIRECTION('',(1.,0.E+000));
#2677 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2678 = PCURVE('',#83,#2679);
#2679 = DEFINITIONAL_REPRESENTATION('',(#2680),#2684);
#2680 = LINE('',#2681,#2682);
#2681 = CARTESIAN_POINT('',(92.21320902,16.71320762));
#2682 = VECTOR('',#2683,1.);
#2683 = DIRECTION('',(-0.793355698391,0.608758355861));
#2684 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2685 = ORIENTED_EDGE('',*,*,#2686,.F.);
#2686 = EDGE_CURVE('',#2687,#2664,#2689,.T.);
#2687 = VERTEX_POINT('',#2688);
#2688 = CARTESIAN_POINT('',(92.00432704,16.87350956,0.E+000));
#2689 = SURFACE_CURVE('',#2690,(#2694,#2701),.PCURVE_S1.);
#2690 = LINE('',#2691,#2692);
#2691 = CARTESIAN_POINT('',(92.00432704,16.87350956,0.E+000));
#2692 = VECTOR('',#2693,1.);
#2693 = DIRECTION('',(0.E+000,0.E+000,1.));
#2694 = PCURVE('',#2626,#2695);
#2695 = DEFINITIONAL_REPRESENTATION('',(#2696),#2700);
#2696 = LINE('',#2697,#2698);
#2697 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#2698 = VECTOR('',#2699,1.);
#2699 = DIRECTION('',(0.E+000,-1.));
#2700 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2701 = PCURVE('',#2702,#2707);
#2702 = PLANE('',#2703);
#2703 = AXIS2_PLACEMENT_3D('',#2704,#2705,#2706);
#2704 = CARTESIAN_POINT('',(92.00432704,16.87350956,0.E+000));
#2705 = DIRECTION('',(-0.382682927661,-0.923879741566,0.E+000));
#2706 = DIRECTION('',(-0.923879741566,0.382682927661,0.E+000));
#2707 = DEFINITIONAL_REPRESENTATION('',(#2708),#2712);
#2708 = LINE('',#2709,#2710);
#2709 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2710 = VECTOR('',#2711,1.);
#2711 = DIRECTION('',(0.E+000,-1.));
#2712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2713 = ORIENTED_EDGE('',*,*,#2714,.F.);
#2714 = EDGE_CURVE('',#2611,#2687,#2715,.T.);
#2715 = SURFACE_CURVE('',#2716,(#2720,#2727),.PCURVE_S1.);
#2716 = LINE('',#2717,#2718);
#2717 = CARTESIAN_POINT('',(92.21322172,16.71322032,0.E+000));
#2718 = VECTOR('',#2719,1.);
#2719 = DIRECTION('',(-0.793355698391,0.608758355861,0.E+000));
#2720 = PCURVE('',#2626,#2721);
#2721 = DEFINITIONAL_REPRESENTATION('',(#2722),#2726);
#2722 = LINE('',#2723,#2724);
#2723 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2724 = VECTOR('',#2725,1.);
#2725 = DIRECTION('',(1.,0.E+000));
#2726 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2727 = PCURVE('',#137,#2728);
#2728 = DEFINITIONAL_REPRESENTATION('',(#2729),#2733);
#2729 = LINE('',#2730,#2731);
#2730 = CARTESIAN_POINT('',(92.21320902,16.71320762));
#2731 = VECTOR('',#2732,1.);
#2732 = DIRECTION('',(-0.793355698391,0.608758355861));
#2733 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2734 = ADVANCED_FACE('',(#2735),#2702,.F.);
#2735 = FACE_BOUND('',#2736,.F.);
#2736 = EDGE_LOOP('',(#2737,#2738,#2761,#2784));
#2737 = ORIENTED_EDGE('',*,*,#2686,.T.);
#2738 = ORIENTED_EDGE('',*,*,#2739,.T.);
#2739 = EDGE_CURVE('',#2664,#2740,#2742,.T.);
#2740 = VERTEX_POINT('',#2741);
#2741 = CARTESIAN_POINT('',(91.76106616,16.97427136,1.64592));
#2742 = SURFACE_CURVE('',#2743,(#2747,#2754),.PCURVE_S1.);
#2743 = LINE('',#2744,#2745);
#2744 = CARTESIAN_POINT('',(92.00432704,16.87350956,1.64592));
#2745 = VECTOR('',#2746,1.);
#2746 = DIRECTION('',(-0.923879741566,0.382682927661,0.E+000));
#2747 = PCURVE('',#2702,#2748);
#2748 = DEFINITIONAL_REPRESENTATION('',(#2749),#2753);
#2749 = LINE('',#2750,#2751);
#2750 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2751 = VECTOR('',#2752,1.);
#2752 = DIRECTION('',(1.,0.E+000));
#2753 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2754 = PCURVE('',#83,#2755);
#2755 = DEFINITIONAL_REPRESENTATION('',(#2756),#2760);
#2756 = LINE('',#2757,#2758);
#2757 = CARTESIAN_POINT('',(92.00431434,16.87349686));
#2758 = VECTOR('',#2759,1.);
#2759 = DIRECTION('',(-0.923879741566,0.382682927661));
#2760 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2761 = ORIENTED_EDGE('',*,*,#2762,.F.);
#2762 = EDGE_CURVE('',#2763,#2740,#2765,.T.);
#2763 = VERTEX_POINT('',#2764);
#2764 = CARTESIAN_POINT('',(91.76106616,16.97427136,0.E+000));
#2765 = SURFACE_CURVE('',#2766,(#2770,#2777),.PCURVE_S1.);
#2766 = LINE('',#2767,#2768);
#2767 = CARTESIAN_POINT('',(91.76106616,16.97427136,0.E+000));
#2768 = VECTOR('',#2769,1.);
#2769 = DIRECTION('',(0.E+000,0.E+000,1.));
#2770 = PCURVE('',#2702,#2771);
#2771 = DEFINITIONAL_REPRESENTATION('',(#2772),#2776);
#2772 = LINE('',#2773,#2774);
#2773 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#2774 = VECTOR('',#2775,1.);
#2775 = DIRECTION('',(0.E+000,-1.));
#2776 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2777 = PCURVE('',#447,#2778);
#2778 = DEFINITIONAL_REPRESENTATION('',(#2779),#2783);
#2779 = LINE('',#2780,#2781);
#2780 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2781 = VECTOR('',#2782,1.);
#2782 = DIRECTION('',(0.E+000,-1.));
#2783 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2784 = ORIENTED_EDGE('',*,*,#2785,.F.);
#2785 = EDGE_CURVE('',#2687,#2763,#2786,.T.);
#2786 = SURFACE_CURVE('',#2787,(#2791,#2798),.PCURVE_S1.);
#2787 = LINE('',#2788,#2789);
#2788 = CARTESIAN_POINT('',(92.00432704,16.87350956,0.E+000));
#2789 = VECTOR('',#2790,1.);
#2790 = DIRECTION('',(-0.923879741566,0.382682927661,0.E+000));
#2791 = PCURVE('',#2702,#2792);
#2792 = DEFINITIONAL_REPRESENTATION('',(#2793),#2797);
#2793 = LINE('',#2794,#2795);
#2794 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2795 = VECTOR('',#2796,1.);
#2796 = DIRECTION('',(1.,0.E+000));
#2797 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2798 = PCURVE('',#137,#2799);
#2799 = DEFINITIONAL_REPRESENTATION('',(#2800),#2804);
#2800 = LINE('',#2801,#2802);
#2801 = CARTESIAN_POINT('',(92.00431434,16.87349686));
#2802 = VECTOR('',#2803,1.);
#2803 = DIRECTION('',(-0.923879741566,0.382682927661));
#2804 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2805 = ADVANCED_FACE('',(#2806),#447,.F.);
#2806 = FACE_BOUND('',#2807,.F.);
#2807 = EDGE_LOOP('',(#2808,#2809,#2830,#2831));
#2808 = ORIENTED_EDGE('',*,*,#2762,.T.);
#2809 = ORIENTED_EDGE('',*,*,#2810,.T.);
#2810 = EDGE_CURVE('',#2740,#427,#2811,.T.);
#2811 = SURFACE_CURVE('',#2812,(#2816,#2823),.PCURVE_S1.);
#2812 = LINE('',#2813,#2814);
#2813 = CARTESIAN_POINT('',(91.76106616,16.97427136,1.64592));
#2814 = VECTOR('',#2815,1.);
#2815 = DIRECTION('',(-0.991444672552,0.130527626456,0.E+000));
#2816 = PCURVE('',#447,#2817);
#2817 = DEFINITIONAL_REPRESENTATION('',(#2818),#2822);
#2818 = LINE('',#2819,#2820);
#2819 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2820 = VECTOR('',#2821,1.);
#2821 = DIRECTION('',(1.,0.E+000));
#2822 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2823 = PCURVE('',#83,#2824);
#2824 = DEFINITIONAL_REPRESENTATION('',(#2825),#2829);
#2825 = LINE('',#2826,#2827);
#2826 = CARTESIAN_POINT('',(91.76105346,16.97425866));
#2827 = VECTOR('',#2828,1.);
#2828 = DIRECTION('',(-0.991444672552,0.130527626456));
#2829 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2830 = ORIENTED_EDGE('',*,*,#424,.F.);
#2831 = ORIENTED_EDGE('',*,*,#2832,.F.);
#2832 = EDGE_CURVE('',#2763,#425,#2833,.T.);
#2833 = SURFACE_CURVE('',#2834,(#2838,#2845),.PCURVE_S1.);
#2834 = LINE('',#2835,#2836);
#2835 = CARTESIAN_POINT('',(91.76106616,16.97427136,0.E+000));
#2836 = VECTOR('',#2837,1.);
#2837 = DIRECTION('',(-0.991444672552,0.130527626456,0.E+000));
#2838 = PCURVE('',#447,#2839);
#2839 = DEFINITIONAL_REPRESENTATION('',(#2840),#2844);
#2840 = LINE('',#2841,#2842);
#2841 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2842 = VECTOR('',#2843,1.);
#2843 = DIRECTION('',(1.,0.E+000));
#2844 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2845 = PCURVE('',#137,#2846);
#2846 = DEFINITIONAL_REPRESENTATION('',(#2847),#2851);
#2847 = LINE('',#2848,#2849);
#2848 = CARTESIAN_POINT('',(91.76105346,16.97425866));
#2849 = VECTOR('',#2850,1.);
#2850 = DIRECTION('',(-0.991444672552,0.130527626456));
#2851 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2852 = ADVANCED_FACE('',(#2853),#2867,.T.);
#2853 = FACE_BOUND('',#2854,.F.);
#2854 = EDGE_LOOP('',(#2855,#2885,#2907,#2908));
#2855 = ORIENTED_EDGE('',*,*,#2856,.T.);
#2856 = EDGE_CURVE('',#2857,#2859,#2861,.T.);
#2857 = VERTEX_POINT('',#2858);
#2858 = CARTESIAN_POINT('',(8.20001154,145.30001166,0.E+000));
#2859 = VERTEX_POINT('',#2860);
#2860 = CARTESIAN_POINT('',(8.20001154,145.30001166,1.64592));
#2861 = SEAM_CURVE('',#2862,(#2866,#2878),.PCURVE_S1.);
#2862 = LINE('',#2863,#2864);
#2863 = CARTESIAN_POINT('',(8.20001154,145.30001166,0.E+000));
#2864 = VECTOR('',#2865,1.);
#2865 = DIRECTION('',(0.E+000,0.E+000,1.));
#2866 = PCURVE('',#2867,#2872);
#2867 = CYLINDRICAL_SURFACE('',#2868,1.59999934);
#2868 = AXIS2_PLACEMENT_3D('',#2869,#2870,#2871);
#2869 = CARTESIAN_POINT('',(6.6000122,145.30001166,0.E+000));
#2870 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#2871 = DIRECTION('',(1.,0.E+000,-0.E+000));
#2872 = DEFINITIONAL_REPRESENTATION('',(#2873),#2877);
#2873 = LINE('',#2874,#2875);
#2874 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#2875 = VECTOR('',#2876,1.);
#2876 = DIRECTION('',(-0.E+000,-1.));
#2877 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2878 = PCURVE('',#2867,#2879);
#2879 = DEFINITIONAL_REPRESENTATION('',(#2880),#2884);
#2880 = LINE('',#2881,#2882);
#2881 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#2882 = VECTOR('',#2883,1.);
#2883 = DIRECTION('',(-0.E+000,-1.));
#2884 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2885 = ORIENTED_EDGE('',*,*,#2886,.T.);
#2886 = EDGE_CURVE('',#2859,#2859,#2887,.T.);
#2887 = SURFACE_CURVE('',#2888,(#2893,#2900),.PCURVE_S1.);
#2888 = CIRCLE('',#2889,1.59999934);
#2889 = AXIS2_PLACEMENT_3D('',#2890,#2891,#2892);
#2890 = CARTESIAN_POINT('',(6.6000122,145.30001166,1.64592));
#2891 = DIRECTION('',(0.E+000,0.E+000,1.));
#2892 = DIRECTION('',(1.,0.E+000,-0.E+000));
#2893 = PCURVE('',#2867,#2894);
#2894 = DEFINITIONAL_REPRESENTATION('',(#2895),#2899);
#2895 = LINE('',#2896,#2897);
#2896 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#2897 = VECTOR('',#2898,1.);
#2898 = DIRECTION('',(-1.,0.E+000));
#2899 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2900 = PCURVE('',#83,#2901);
#2901 = DEFINITIONAL_REPRESENTATION('',(#2902),#2906);
#2902 = CIRCLE('',#2903,1.59999934);
#2903 = AXIS2_PLACEMENT_2D('',#2904,#2905);
#2904 = CARTESIAN_POINT('',(6.5999995,145.29999896));
#2905 = DIRECTION('',(1.,0.E+000));
#2906 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2907 = ORIENTED_EDGE('',*,*,#2856,.F.);
#2908 = ORIENTED_EDGE('',*,*,#2909,.F.);
#2909 = EDGE_CURVE('',#2857,#2857,#2910,.T.);
#2910 = SURFACE_CURVE('',#2911,(#2916,#2923),.PCURVE_S1.);
#2911 = CIRCLE('',#2912,1.59999934);
#2912 = AXIS2_PLACEMENT_3D('',#2913,#2914,#2915);
#2913 = CARTESIAN_POINT('',(6.6000122,145.30001166,0.E+000));
#2914 = DIRECTION('',(0.E+000,0.E+000,1.));
#2915 = DIRECTION('',(1.,0.E+000,-0.E+000));
#2916 = PCURVE('',#2867,#2917);
#2917 = DEFINITIONAL_REPRESENTATION('',(#2918),#2922);
#2918 = LINE('',#2919,#2920);
#2919 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#2920 = VECTOR('',#2921,1.);
#2921 = DIRECTION('',(-1.,0.E+000));
#2922 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2923 = PCURVE('',#137,#2924);
#2924 = DEFINITIONAL_REPRESENTATION('',(#2925),#2929);
#2925 = CIRCLE('',#2926,1.59999934);
#2926 = AXIS2_PLACEMENT_2D('',#2927,#2928);
#2927 = CARTESIAN_POINT('',(6.5999995,145.29999896));
#2928 = DIRECTION('',(1.,0.E+000));
#2929 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2930 = ADVANCED_FACE('',(#2931),#2945,.T.);
#2931 = FACE_BOUND('',#2932,.F.);
#2932 = EDGE_LOOP('',(#2933,#2963,#2985,#2986));
#2933 = ORIENTED_EDGE('',*,*,#2934,.T.);
#2934 = EDGE_CURVE('',#2935,#2937,#2939,.T.);
#2935 = VERTEX_POINT('',#2936);
#2936 = CARTESIAN_POINT('',(8.20001154,56.7000136,0.E+000));
#2937 = VERTEX_POINT('',#2938);
#2938 = CARTESIAN_POINT('',(8.20001154,56.7000136,1.64592));
#2939 = SEAM_CURVE('',#2940,(#2944,#2956),.PCURVE_S1.);
#2940 = LINE('',#2941,#2942);
#2941 = CARTESIAN_POINT('',(8.20001154,56.7000136,0.E+000));
#2942 = VECTOR('',#2943,1.);
#2943 = DIRECTION('',(0.E+000,0.E+000,1.));
#2944 = PCURVE('',#2945,#2950);
#2945 = CYLINDRICAL_SURFACE('',#2946,1.59999934);
#2946 = AXIS2_PLACEMENT_3D('',#2947,#2948,#2949);
#2947 = CARTESIAN_POINT('',(6.6000122,56.7000136,0.E+000));
#2948 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#2949 = DIRECTION('',(1.,0.E+000,-0.E+000));
#2950 = DEFINITIONAL_REPRESENTATION('',(#2951),#2955);
#2951 = LINE('',#2952,#2953);
#2952 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#2953 = VECTOR('',#2954,1.);
#2954 = DIRECTION('',(-0.E+000,-1.));
#2955 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2956 = PCURVE('',#2945,#2957);
#2957 = DEFINITIONAL_REPRESENTATION('',(#2958),#2962);
#2958 = LINE('',#2959,#2960);
#2959 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#2960 = VECTOR('',#2961,1.);
#2961 = DIRECTION('',(-0.E+000,-1.));
#2962 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2963 = ORIENTED_EDGE('',*,*,#2964,.T.);
#2964 = EDGE_CURVE('',#2937,#2937,#2965,.T.);
#2965 = SURFACE_CURVE('',#2966,(#2971,#2978),.PCURVE_S1.);
#2966 = CIRCLE('',#2967,1.59999934);
#2967 = AXIS2_PLACEMENT_3D('',#2968,#2969,#2970);
#2968 = CARTESIAN_POINT('',(6.6000122,56.7000136,1.64592));
#2969 = DIRECTION('',(0.E+000,0.E+000,1.));
#2970 = DIRECTION('',(1.,0.E+000,-0.E+000));
#2971 = PCURVE('',#2945,#2972);
#2972 = DEFINITIONAL_REPRESENTATION('',(#2973),#2977);
#2973 = LINE('',#2974,#2975);
#2974 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#2975 = VECTOR('',#2976,1.);
#2976 = DIRECTION('',(-1.,0.E+000));
#2977 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2978 = PCURVE('',#83,#2979);
#2979 = DEFINITIONAL_REPRESENTATION('',(#2980),#2984);
#2980 = CIRCLE('',#2981,1.59999934);
#2981 = AXIS2_PLACEMENT_2D('',#2982,#2983);
#2982 = CARTESIAN_POINT('',(6.5999995,56.7000009));
#2983 = DIRECTION('',(1.,0.E+000));
#2984 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2985 = ORIENTED_EDGE('',*,*,#2934,.F.);
#2986 = ORIENTED_EDGE('',*,*,#2987,.F.);
#2987 = EDGE_CURVE('',#2935,#2935,#2988,.T.);
#2988 = SURFACE_CURVE('',#2989,(#2994,#3001),.PCURVE_S1.);
#2989 = CIRCLE('',#2990,1.59999934);
#2990 = AXIS2_PLACEMENT_3D('',#2991,#2992,#2993);
#2991 = CARTESIAN_POINT('',(6.6000122,56.7000136,0.E+000));
#2992 = DIRECTION('',(0.E+000,0.E+000,1.));
#2993 = DIRECTION('',(1.,0.E+000,-0.E+000));
#2994 = PCURVE('',#2945,#2995);
#2995 = DEFINITIONAL_REPRESENTATION('',(#2996),#3000);
#2996 = LINE('',#2997,#2998);
#2997 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#2998 = VECTOR('',#2999,1.);
#2999 = DIRECTION('',(-1.,0.E+000));
#3000 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3001 = PCURVE('',#137,#3002);
#3002 = DEFINITIONAL_REPRESENTATION('',(#3003),#3007);
#3003 = CIRCLE('',#3004,1.59999934);
#3004 = AXIS2_PLACEMENT_2D('',#3005,#3006);
#3005 = CARTESIAN_POINT('',(6.5999995,56.7000009));
#3006 = DIRECTION('',(1.,0.E+000));
#3007 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3008 = ADVANCED_FACE('',(#3009),#3023,.T.);
#3009 = FACE_BOUND('',#3010,.F.);
#3010 = EDGE_LOOP('',(#3011,#3041,#3063,#3064));
#3011 = ORIENTED_EDGE('',*,*,#3012,.T.);
#3012 = EDGE_CURVE('',#3013,#3015,#3017,.T.);
#3013 = VERTEX_POINT('',#3014);
#3014 = CARTESIAN_POINT('',(8.20001154,101.1000137,0.E+000));
#3015 = VERTEX_POINT('',#3016);
#3016 = CARTESIAN_POINT('',(8.20001154,101.1000137,1.64592));
#3017 = SEAM_CURVE('',#3018,(#3022,#3034),.PCURVE_S1.);
#3018 = LINE('',#3019,#3020);
#3019 = CARTESIAN_POINT('',(8.20001154,101.1000137,0.E+000));
#3020 = VECTOR('',#3021,1.);
#3021 = DIRECTION('',(0.E+000,0.E+000,1.));
#3022 = PCURVE('',#3023,#3028);
#3023 = CYLINDRICAL_SURFACE('',#3024,1.59999934);
#3024 = AXIS2_PLACEMENT_3D('',#3025,#3026,#3027);
#3025 = CARTESIAN_POINT('',(6.6000122,101.1000137,0.E+000));
#3026 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3027 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3028 = DEFINITIONAL_REPRESENTATION('',(#3029),#3033);
#3029 = LINE('',#3030,#3031);
#3030 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3031 = VECTOR('',#3032,1.);
#3032 = DIRECTION('',(-0.E+000,-1.));
#3033 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3034 = PCURVE('',#3023,#3035);
#3035 = DEFINITIONAL_REPRESENTATION('',(#3036),#3040);
#3036 = LINE('',#3037,#3038);
#3037 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3038 = VECTOR('',#3039,1.);
#3039 = DIRECTION('',(-0.E+000,-1.));
#3040 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3041 = ORIENTED_EDGE('',*,*,#3042,.T.);
#3042 = EDGE_CURVE('',#3015,#3015,#3043,.T.);
#3043 = SURFACE_CURVE('',#3044,(#3049,#3056),.PCURVE_S1.);
#3044 = CIRCLE('',#3045,1.59999934);
#3045 = AXIS2_PLACEMENT_3D('',#3046,#3047,#3048);
#3046 = CARTESIAN_POINT('',(6.6000122,101.1000137,1.64592));
#3047 = DIRECTION('',(0.E+000,0.E+000,1.));
#3048 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3049 = PCURVE('',#3023,#3050);
#3050 = DEFINITIONAL_REPRESENTATION('',(#3051),#3055);
#3051 = LINE('',#3052,#3053);
#3052 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3053 = VECTOR('',#3054,1.);
#3054 = DIRECTION('',(-1.,0.E+000));
#3055 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3056 = PCURVE('',#83,#3057);
#3057 = DEFINITIONAL_REPRESENTATION('',(#3058),#3062);
#3058 = CIRCLE('',#3059,1.59999934);
#3059 = AXIS2_PLACEMENT_2D('',#3060,#3061);
#3060 = CARTESIAN_POINT('',(6.5999995,101.100001));
#3061 = DIRECTION('',(1.,0.E+000));
#3062 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3063 = ORIENTED_EDGE('',*,*,#3012,.F.);
#3064 = ORIENTED_EDGE('',*,*,#3065,.F.);
#3065 = EDGE_CURVE('',#3013,#3013,#3066,.T.);
#3066 = SURFACE_CURVE('',#3067,(#3072,#3079),.PCURVE_S1.);
#3067 = CIRCLE('',#3068,1.59999934);
#3068 = AXIS2_PLACEMENT_3D('',#3069,#3070,#3071);
#3069 = CARTESIAN_POINT('',(6.6000122,101.1000137,0.E+000));
#3070 = DIRECTION('',(0.E+000,0.E+000,1.));
#3071 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3072 = PCURVE('',#3023,#3073);
#3073 = DEFINITIONAL_REPRESENTATION('',(#3074),#3078);
#3074 = LINE('',#3075,#3076);
#3075 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3076 = VECTOR('',#3077,1.);
#3077 = DIRECTION('',(-1.,0.E+000));
#3078 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3079 = PCURVE('',#137,#3080);
#3080 = DEFINITIONAL_REPRESENTATION('',(#3081),#3085);
#3081 = CIRCLE('',#3082,1.59999934);
#3082 = AXIS2_PLACEMENT_2D('',#3083,#3084);
#3083 = CARTESIAN_POINT('',(6.5999995,101.100001));
#3084 = DIRECTION('',(1.,0.E+000));
#3085 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3086 = ADVANCED_FACE('',(#3087),#3101,.T.);
#3087 = FACE_BOUND('',#3088,.F.);
#3088 = EDGE_LOOP('',(#3089,#3119,#3141,#3142));
#3089 = ORIENTED_EDGE('',*,*,#3090,.T.);
#3090 = EDGE_CURVE('',#3091,#3093,#3095,.T.);
#3091 = VERTEX_POINT('',#3092);
#3092 = CARTESIAN_POINT('',(125.00001162,56.7000136,0.E+000));
#3093 = VERTEX_POINT('',#3094);
#3094 = CARTESIAN_POINT('',(125.00001162,56.7000136,1.64592));
#3095 = SEAM_CURVE('',#3096,(#3100,#3112),.PCURVE_S1.);
#3096 = LINE('',#3097,#3098);
#3097 = CARTESIAN_POINT('',(125.00001162,56.7000136,0.E+000));
#3098 = VECTOR('',#3099,1.);
#3099 = DIRECTION('',(0.E+000,0.E+000,1.));
#3100 = PCURVE('',#3101,#3106);
#3101 = CYLINDRICAL_SURFACE('',#3102,1.59999934);
#3102 = AXIS2_PLACEMENT_3D('',#3103,#3104,#3105);
#3103 = CARTESIAN_POINT('',(123.40001228,56.7000136,0.E+000));
#3104 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3105 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3106 = DEFINITIONAL_REPRESENTATION('',(#3107),#3111);
#3107 = LINE('',#3108,#3109);
#3108 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3109 = VECTOR('',#3110,1.);
#3110 = DIRECTION('',(-0.E+000,-1.));
#3111 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3112 = PCURVE('',#3101,#3113);
#3113 = DEFINITIONAL_REPRESENTATION('',(#3114),#3118);
#3114 = LINE('',#3115,#3116);
#3115 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3116 = VECTOR('',#3117,1.);
#3117 = DIRECTION('',(-0.E+000,-1.));
#3118 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3119 = ORIENTED_EDGE('',*,*,#3120,.T.);
#3120 = EDGE_CURVE('',#3093,#3093,#3121,.T.);
#3121 = SURFACE_CURVE('',#3122,(#3127,#3134),.PCURVE_S1.);
#3122 = CIRCLE('',#3123,1.59999934);
#3123 = AXIS2_PLACEMENT_3D('',#3124,#3125,#3126);
#3124 = CARTESIAN_POINT('',(123.40001228,56.7000136,1.64592));
#3125 = DIRECTION('',(0.E+000,0.E+000,1.));
#3126 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3127 = PCURVE('',#3101,#3128);
#3128 = DEFINITIONAL_REPRESENTATION('',(#3129),#3133);
#3129 = LINE('',#3130,#3131);
#3130 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3131 = VECTOR('',#3132,1.);
#3132 = DIRECTION('',(-1.,0.E+000));
#3133 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3134 = PCURVE('',#83,#3135);
#3135 = DEFINITIONAL_REPRESENTATION('',(#3136),#3140);
#3136 = CIRCLE('',#3137,1.59999934);
#3137 = AXIS2_PLACEMENT_2D('',#3138,#3139);
#3138 = CARTESIAN_POINT('',(123.39999958,56.7000009));
#3139 = DIRECTION('',(1.,0.E+000));
#3140 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3141 = ORIENTED_EDGE('',*,*,#3090,.F.);
#3142 = ORIENTED_EDGE('',*,*,#3143,.F.);
#3143 = EDGE_CURVE('',#3091,#3091,#3144,.T.);
#3144 = SURFACE_CURVE('',#3145,(#3150,#3157),.PCURVE_S1.);
#3145 = CIRCLE('',#3146,1.59999934);
#3146 = AXIS2_PLACEMENT_3D('',#3147,#3148,#3149);
#3147 = CARTESIAN_POINT('',(123.40001228,56.7000136,0.E+000));
#3148 = DIRECTION('',(0.E+000,0.E+000,1.));
#3149 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3150 = PCURVE('',#3101,#3151);
#3151 = DEFINITIONAL_REPRESENTATION('',(#3152),#3156);
#3152 = LINE('',#3153,#3154);
#3153 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3154 = VECTOR('',#3155,1.);
#3155 = DIRECTION('',(-1.,0.E+000));
#3156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3157 = PCURVE('',#137,#3158);
#3158 = DEFINITIONAL_REPRESENTATION('',(#3159),#3163);
#3159 = CIRCLE('',#3160,1.59999934);
#3160 = AXIS2_PLACEMENT_2D('',#3161,#3162);
#3161 = CARTESIAN_POINT('',(123.39999958,56.7000009));
#3162 = DIRECTION('',(1.,0.E+000));
#3163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3164 = ADVANCED_FACE('',(#3165),#3179,.T.);
#3165 = FACE_BOUND('',#3166,.F.);
#3166 = EDGE_LOOP('',(#3167,#3197,#3219,#3220));
#3167 = ORIENTED_EDGE('',*,*,#3168,.T.);
#3168 = EDGE_CURVE('',#3169,#3171,#3173,.T.);
#3169 = VERTEX_POINT('',#3170);
#3170 = CARTESIAN_POINT('',(125.00001162,12.5000131,0.E+000));
#3171 = VERTEX_POINT('',#3172);
#3172 = CARTESIAN_POINT('',(125.00001162,12.5000131,1.64592));
#3173 = SEAM_CURVE('',#3174,(#3178,#3190),.PCURVE_S1.);
#3174 = LINE('',#3175,#3176);
#3175 = CARTESIAN_POINT('',(125.00001162,12.5000131,0.E+000));
#3176 = VECTOR('',#3177,1.);
#3177 = DIRECTION('',(0.E+000,0.E+000,1.));
#3178 = PCURVE('',#3179,#3184);
#3179 = CYLINDRICAL_SURFACE('',#3180,1.59999934);
#3180 = AXIS2_PLACEMENT_3D('',#3181,#3182,#3183);
#3181 = CARTESIAN_POINT('',(123.40001228,12.5000131,0.E+000));
#3182 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3183 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3184 = DEFINITIONAL_REPRESENTATION('',(#3185),#3189);
#3185 = LINE('',#3186,#3187);
#3186 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3187 = VECTOR('',#3188,1.);
#3188 = DIRECTION('',(-0.E+000,-1.));
#3189 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3190 = PCURVE('',#3179,#3191);
#3191 = DEFINITIONAL_REPRESENTATION('',(#3192),#3196);
#3192 = LINE('',#3193,#3194);
#3193 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3194 = VECTOR('',#3195,1.);
#3195 = DIRECTION('',(-0.E+000,-1.));
#3196 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3197 = ORIENTED_EDGE('',*,*,#3198,.T.);
#3198 = EDGE_CURVE('',#3171,#3171,#3199,.T.);
#3199 = SURFACE_CURVE('',#3200,(#3205,#3212),.PCURVE_S1.);
#3200 = CIRCLE('',#3201,1.59999934);
#3201 = AXIS2_PLACEMENT_3D('',#3202,#3203,#3204);
#3202 = CARTESIAN_POINT('',(123.40001228,12.5000131,1.64592));
#3203 = DIRECTION('',(0.E+000,0.E+000,1.));
#3204 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3205 = PCURVE('',#3179,#3206);
#3206 = DEFINITIONAL_REPRESENTATION('',(#3207),#3211);
#3207 = LINE('',#3208,#3209);
#3208 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3209 = VECTOR('',#3210,1.);
#3210 = DIRECTION('',(-1.,0.E+000));
#3211 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3212 = PCURVE('',#83,#3213);
#3213 = DEFINITIONAL_REPRESENTATION('',(#3214),#3218);
#3214 = CIRCLE('',#3215,1.59999934);
#3215 = AXIS2_PLACEMENT_2D('',#3216,#3217);
#3216 = CARTESIAN_POINT('',(123.39999958,12.5000004));
#3217 = DIRECTION('',(1.,0.E+000));
#3218 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3219 = ORIENTED_EDGE('',*,*,#3168,.F.);
#3220 = ORIENTED_EDGE('',*,*,#3221,.F.);
#3221 = EDGE_CURVE('',#3169,#3169,#3222,.T.);
#3222 = SURFACE_CURVE('',#3223,(#3228,#3235),.PCURVE_S1.);
#3223 = CIRCLE('',#3224,1.59999934);
#3224 = AXIS2_PLACEMENT_3D('',#3225,#3226,#3227);
#3225 = CARTESIAN_POINT('',(123.40001228,12.5000131,0.E+000));
#3226 = DIRECTION('',(0.E+000,0.E+000,1.));
#3227 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3228 = PCURVE('',#3179,#3229);
#3229 = DEFINITIONAL_REPRESENTATION('',(#3230),#3234);
#3230 = LINE('',#3231,#3232);
#3231 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3232 = VECTOR('',#3233,1.);
#3233 = DIRECTION('',(-1.,0.E+000));
#3234 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3235 = PCURVE('',#137,#3236);
#3236 = DEFINITIONAL_REPRESENTATION('',(#3237),#3241);
#3237 = CIRCLE('',#3238,1.59999934);
#3238 = AXIS2_PLACEMENT_2D('',#3239,#3240);
#3239 = CARTESIAN_POINT('',(123.39999958,12.5000004));
#3240 = DIRECTION('',(1.,0.E+000));
#3241 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3242 = ADVANCED_FACE('',(#3243),#3257,.T.);
#3243 = FACE_BOUND('',#3244,.F.);
#3244 = EDGE_LOOP('',(#3245,#3275,#3297,#3298));
#3245 = ORIENTED_EDGE('',*,*,#3246,.T.);
#3246 = EDGE_CURVE('',#3247,#3249,#3251,.T.);
#3247 = VERTEX_POINT('',#3248);
#3248 = CARTESIAN_POINT('',(76.00001056,93.80001306,0.E+000));
#3249 = VERTEX_POINT('',#3250);
#3250 = CARTESIAN_POINT('',(76.00001056,93.80001306,1.64592));
#3251 = SEAM_CURVE('',#3252,(#3256,#3268),.PCURVE_S1.);
#3252 = LINE('',#3253,#3254);
#3253 = CARTESIAN_POINT('',(76.00001056,93.80001306,0.E+000));
#3254 = VECTOR('',#3255,1.);
#3255 = DIRECTION('',(0.E+000,0.E+000,1.));
#3256 = PCURVE('',#3257,#3262);
#3257 = CYLINDRICAL_SURFACE('',#3258,10.99999832);
#3258 = AXIS2_PLACEMENT_3D('',#3259,#3260,#3261);
#3259 = CARTESIAN_POINT('',(65.00001224,93.80001306,0.E+000));
#3260 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3261 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3262 = DEFINITIONAL_REPRESENTATION('',(#3263),#3267);
#3263 = LINE('',#3264,#3265);
#3264 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3265 = VECTOR('',#3266,1.);
#3266 = DIRECTION('',(-0.E+000,-1.));
#3267 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3268 = PCURVE('',#3257,#3269);
#3269 = DEFINITIONAL_REPRESENTATION('',(#3270),#3274);
#3270 = LINE('',#3271,#3272);
#3271 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3272 = VECTOR('',#3273,1.);
#3273 = DIRECTION('',(-0.E+000,-1.));
#3274 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3275 = ORIENTED_EDGE('',*,*,#3276,.T.);
#3276 = EDGE_CURVE('',#3249,#3249,#3277,.T.);
#3277 = SURFACE_CURVE('',#3278,(#3283,#3290),.PCURVE_S1.);
#3278 = CIRCLE('',#3279,10.99999832);
#3279 = AXIS2_PLACEMENT_3D('',#3280,#3281,#3282);
#3280 = CARTESIAN_POINT('',(65.00001224,93.80001306,1.64592));
#3281 = DIRECTION('',(0.E+000,0.E+000,1.));
#3282 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3283 = PCURVE('',#3257,#3284);
#3284 = DEFINITIONAL_REPRESENTATION('',(#3285),#3289);
#3285 = LINE('',#3286,#3287);
#3286 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3287 = VECTOR('',#3288,1.);
#3288 = DIRECTION('',(-1.,0.E+000));
#3289 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3290 = PCURVE('',#83,#3291);
#3291 = DEFINITIONAL_REPRESENTATION('',(#3292),#3296);
#3292 = CIRCLE('',#3293,10.99999832);
#3293 = AXIS2_PLACEMENT_2D('',#3294,#3295);
#3294 = CARTESIAN_POINT('',(64.99999954,93.80000036));
#3295 = DIRECTION('',(1.,0.E+000));
#3296 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3297 = ORIENTED_EDGE('',*,*,#3246,.F.);
#3298 = ORIENTED_EDGE('',*,*,#3299,.F.);
#3299 = EDGE_CURVE('',#3247,#3247,#3300,.T.);
#3300 = SURFACE_CURVE('',#3301,(#3306,#3313),.PCURVE_S1.);
#3301 = CIRCLE('',#3302,10.99999832);
#3302 = AXIS2_PLACEMENT_3D('',#3303,#3304,#3305);
#3303 = CARTESIAN_POINT('',(65.00001224,93.80001306,0.E+000));
#3304 = DIRECTION('',(0.E+000,0.E+000,1.));
#3305 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3306 = PCURVE('',#3257,#3307);
#3307 = DEFINITIONAL_REPRESENTATION('',(#3308),#3312);
#3308 = LINE('',#3309,#3310);
#3309 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3310 = VECTOR('',#3311,1.);
#3311 = DIRECTION('',(-1.,0.E+000));
#3312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3313 = PCURVE('',#137,#3314);
#3314 = DEFINITIONAL_REPRESENTATION('',(#3315),#3319);
#3315 = CIRCLE('',#3316,10.99999832);
#3316 = AXIS2_PLACEMENT_2D('',#3317,#3318);
#3317 = CARTESIAN_POINT('',(64.99999954,93.80000036));
#3318 = DIRECTION('',(1.,0.E+000));
#3319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3320 = ADVANCED_FACE('',(#3321),#3335,.T.);
#3321 = FACE_BOUND('',#3322,.F.);
#3322 = EDGE_LOOP('',(#3323,#3353,#3375,#3376));
#3323 = ORIENTED_EDGE('',*,*,#3324,.T.);
#3324 = EDGE_CURVE('',#3325,#3327,#3329,.T.);
#3325 = VERTEX_POINT('',#3326);
#3326 = CARTESIAN_POINT('',(125.00001162,101.1000137,0.E+000));
#3327 = VERTEX_POINT('',#3328);
#3328 = CARTESIAN_POINT('',(125.00001162,101.1000137,1.64592));
#3329 = SEAM_CURVE('',#3330,(#3334,#3346),.PCURVE_S1.);
#3330 = LINE('',#3331,#3332);
#3331 = CARTESIAN_POINT('',(125.00001162,101.1000137,0.E+000));
#3332 = VECTOR('',#3333,1.);
#3333 = DIRECTION('',(0.E+000,0.E+000,1.));
#3334 = PCURVE('',#3335,#3340);
#3335 = CYLINDRICAL_SURFACE('',#3336,1.59999934);
#3336 = AXIS2_PLACEMENT_3D('',#3337,#3338,#3339);
#3337 = CARTESIAN_POINT('',(123.40001228,101.1000137,0.E+000));
#3338 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3339 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3340 = DEFINITIONAL_REPRESENTATION('',(#3341),#3345);
#3341 = LINE('',#3342,#3343);
#3342 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3343 = VECTOR('',#3344,1.);
#3344 = DIRECTION('',(-0.E+000,-1.));
#3345 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3346 = PCURVE('',#3335,#3347);
#3347 = DEFINITIONAL_REPRESENTATION('',(#3348),#3352);
#3348 = LINE('',#3349,#3350);
#3349 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3350 = VECTOR('',#3351,1.);
#3351 = DIRECTION('',(-0.E+000,-1.));
#3352 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3353 = ORIENTED_EDGE('',*,*,#3354,.T.);
#3354 = EDGE_CURVE('',#3327,#3327,#3355,.T.);
#3355 = SURFACE_CURVE('',#3356,(#3361,#3368),.PCURVE_S1.);
#3356 = CIRCLE('',#3357,1.59999934);
#3357 = AXIS2_PLACEMENT_3D('',#3358,#3359,#3360);
#3358 = CARTESIAN_POINT('',(123.40001228,101.1000137,1.64592));
#3359 = DIRECTION('',(0.E+000,0.E+000,1.));
#3360 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3361 = PCURVE('',#3335,#3362);
#3362 = DEFINITIONAL_REPRESENTATION('',(#3363),#3367);
#3363 = LINE('',#3364,#3365);
#3364 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3365 = VECTOR('',#3366,1.);
#3366 = DIRECTION('',(-1.,0.E+000));
#3367 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3368 = PCURVE('',#83,#3369);
#3369 = DEFINITIONAL_REPRESENTATION('',(#3370),#3374);
#3370 = CIRCLE('',#3371,1.59999934);
#3371 = AXIS2_PLACEMENT_2D('',#3372,#3373);
#3372 = CARTESIAN_POINT('',(123.39999958,101.100001));
#3373 = DIRECTION('',(1.,0.E+000));
#3374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3375 = ORIENTED_EDGE('',*,*,#3324,.F.);
#3376 = ORIENTED_EDGE('',*,*,#3377,.F.);
#3377 = EDGE_CURVE('',#3325,#3325,#3378,.T.);
#3378 = SURFACE_CURVE('',#3379,(#3384,#3391),.PCURVE_S1.);
#3379 = CIRCLE('',#3380,1.59999934);
#3380 = AXIS2_PLACEMENT_3D('',#3381,#3382,#3383);
#3381 = CARTESIAN_POINT('',(123.40001228,101.1000137,0.E+000));
#3382 = DIRECTION('',(0.E+000,0.E+000,1.));
#3383 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3384 = PCURVE('',#3335,#3385);
#3385 = DEFINITIONAL_REPRESENTATION('',(#3386),#3390);
#3386 = LINE('',#3387,#3388);
#3387 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3388 = VECTOR('',#3389,1.);
#3389 = DIRECTION('',(-1.,0.E+000));
#3390 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3391 = PCURVE('',#137,#3392);
#3392 = DEFINITIONAL_REPRESENTATION('',(#3393),#3397);
#3393 = CIRCLE('',#3394,1.59999934);
#3394 = AXIS2_PLACEMENT_2D('',#3395,#3396);
#3395 = CARTESIAN_POINT('',(123.39999958,101.100001));
#3396 = DIRECTION('',(1.,0.E+000));
#3397 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3398 = ADVANCED_FACE('',(#3399),#3413,.T.);
#3399 = FACE_BOUND('',#3400,.F.);
#3400 = EDGE_LOOP('',(#3401,#3431,#3453,#3454));
#3401 = ORIENTED_EDGE('',*,*,#3402,.T.);
#3402 = EDGE_CURVE('',#3403,#3405,#3407,.T.);
#3403 = VERTEX_POINT('',#3404);
#3404 = CARTESIAN_POINT('',(125.00001162,145.30001166,0.E+000));
#3405 = VERTEX_POINT('',#3406);
#3406 = CARTESIAN_POINT('',(125.00001162,145.30001166,1.64592));
#3407 = SEAM_CURVE('',#3408,(#3412,#3424),.PCURVE_S1.);
#3408 = LINE('',#3409,#3410);
#3409 = CARTESIAN_POINT('',(125.00001162,145.30001166,0.E+000));
#3410 = VECTOR('',#3411,1.);
#3411 = DIRECTION('',(0.E+000,0.E+000,1.));
#3412 = PCURVE('',#3413,#3418);
#3413 = CYLINDRICAL_SURFACE('',#3414,1.59999934);
#3414 = AXIS2_PLACEMENT_3D('',#3415,#3416,#3417);
#3415 = CARTESIAN_POINT('',(123.40001228,145.30001166,0.E+000));
#3416 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3417 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3418 = DEFINITIONAL_REPRESENTATION('',(#3419),#3423);
#3419 = LINE('',#3420,#3421);
#3420 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3421 = VECTOR('',#3422,1.);
#3422 = DIRECTION('',(-0.E+000,-1.));
#3423 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3424 = PCURVE('',#3413,#3425);
#3425 = DEFINITIONAL_REPRESENTATION('',(#3426),#3430);
#3426 = LINE('',#3427,#3428);
#3427 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3428 = VECTOR('',#3429,1.);
#3429 = DIRECTION('',(-0.E+000,-1.));
#3430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3431 = ORIENTED_EDGE('',*,*,#3432,.T.);
#3432 = EDGE_CURVE('',#3405,#3405,#3433,.T.);
#3433 = SURFACE_CURVE('',#3434,(#3439,#3446),.PCURVE_S1.);
#3434 = CIRCLE('',#3435,1.59999934);
#3435 = AXIS2_PLACEMENT_3D('',#3436,#3437,#3438);
#3436 = CARTESIAN_POINT('',(123.40001228,145.30001166,1.64592));
#3437 = DIRECTION('',(0.E+000,0.E+000,1.));
#3438 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3439 = PCURVE('',#3413,#3440);
#3440 = DEFINITIONAL_REPRESENTATION('',(#3441),#3445);
#3441 = LINE('',#3442,#3443);
#3442 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3443 = VECTOR('',#3444,1.);
#3444 = DIRECTION('',(-1.,0.E+000));
#3445 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3446 = PCURVE('',#83,#3447);
#3447 = DEFINITIONAL_REPRESENTATION('',(#3448),#3452);
#3448 = CIRCLE('',#3449,1.59999934);
#3449 = AXIS2_PLACEMENT_2D('',#3450,#3451);
#3450 = CARTESIAN_POINT('',(123.39999958,145.29999896));
#3451 = DIRECTION('',(1.,0.E+000));
#3452 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3453 = ORIENTED_EDGE('',*,*,#3402,.F.);
#3454 = ORIENTED_EDGE('',*,*,#3455,.F.);
#3455 = EDGE_CURVE('',#3403,#3403,#3456,.T.);
#3456 = SURFACE_CURVE('',#3457,(#3462,#3469),.PCURVE_S1.);
#3457 = CIRCLE('',#3458,1.59999934);
#3458 = AXIS2_PLACEMENT_3D('',#3459,#3460,#3461);
#3459 = CARTESIAN_POINT('',(123.40001228,145.30001166,0.E+000));
#3460 = DIRECTION('',(0.E+000,0.E+000,1.));
#3461 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3462 = PCURVE('',#3413,#3463);
#3463 = DEFINITIONAL_REPRESENTATION('',(#3464),#3468);
#3464 = LINE('',#3465,#3466);
#3465 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3466 = VECTOR('',#3467,1.);
#3467 = DIRECTION('',(-1.,0.E+000));
#3468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3469 = PCURVE('',#137,#3470);
#3470 = DEFINITIONAL_REPRESENTATION('',(#3471),#3475);
#3471 = CIRCLE('',#3472,1.59999934);
#3472 = AXIS2_PLACEMENT_2D('',#3473,#3474);
#3473 = CARTESIAN_POINT('',(123.39999958,145.29999896));
#3474 = DIRECTION('',(1.,0.E+000));
#3475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3476 = ADVANCED_FACE('',(#3477,#3483,#3486,#3520,#3523,#3526,#3529,
    #3532,#3535,#3538,#3541),#137,.F.);
#3477 = FACE_BOUND('',#3478,.T.);
#3478 = EDGE_LOOP('',(#3479,#3480,#3481,#3482));
#3479 = ORIENTED_EDGE('',*,*,#123,.T.);
#3480 = ORIENTED_EDGE('',*,*,#204,.T.);
#3481 = ORIENTED_EDGE('',*,*,#275,.T.);
#3482 = ORIENTED_EDGE('',*,*,#322,.T.);
#3483 = FACE_BOUND('',#3484,.F.);
#3484 = EDGE_LOOP('',(#3485));
#3485 = ORIENTED_EDGE('',*,*,#399,.T.);
#3486 = FACE_BOUND('',#3487,.F.);
#3487 = EDGE_LOOP('',(#3488,#3489,#3490,#3491,#3492,#3493,#3494,#3495,
    #3496,#3497,#3498,#3499,#3500,#3501,#3502,#3503,#3504,#3505,#3506,
    #3507,#3508,#3509,#3510,#3511,#3512,#3513,#3514,#3515,#3516,#3517,
    #3518,#3519));
#3488 = ORIENTED_EDGE('',*,*,#510,.T.);
#3489 = ORIENTED_EDGE('',*,*,#586,.T.);
#3490 = ORIENTED_EDGE('',*,*,#662,.T.);
#3491 = ORIENTED_EDGE('',*,*,#738,.T.);
#3492 = ORIENTED_EDGE('',*,*,#814,.T.);
#3493 = ORIENTED_EDGE('',*,*,#890,.T.);
#3494 = ORIENTED_EDGE('',*,*,#966,.T.);
#3495 = ORIENTED_EDGE('',*,*,#1042,.T.);
#3496 = ORIENTED_EDGE('',*,*,#1118,.T.);
#3497 = ORIENTED_EDGE('',*,*,#1194,.T.);
#3498 = ORIENTED_EDGE('',*,*,#1270,.T.);
#3499 = ORIENTED_EDGE('',*,*,#1346,.T.);
#3500 = ORIENTED_EDGE('',*,*,#1422,.T.);
#3501 = ORIENTED_EDGE('',*,*,#1498,.T.);
#3502 = ORIENTED_EDGE('',*,*,#1574,.T.);
#3503 = ORIENTED_EDGE('',*,*,#1650,.T.);
#3504 = ORIENTED_EDGE('',*,*,#1726,.T.);
#3505 = ORIENTED_EDGE('',*,*,#1802,.T.);
#3506 = ORIENTED_EDGE('',*,*,#1878,.T.);
#3507 = ORIENTED_EDGE('',*,*,#1954,.T.);
#3508 = ORIENTED_EDGE('',*,*,#2030,.T.);
#3509 = ORIENTED_EDGE('',*,*,#2106,.T.);
#3510 = ORIENTED_EDGE('',*,*,#2182,.T.);
#3511 = ORIENTED_EDGE('',*,*,#2258,.T.);
#3512 = ORIENTED_EDGE('',*,*,#2334,.T.);
#3513 = ORIENTED_EDGE('',*,*,#2410,.T.);
#3514 = ORIENTED_EDGE('',*,*,#2486,.T.);
#3515 = ORIENTED_EDGE('',*,*,#2562,.T.);
#3516 = ORIENTED_EDGE('',*,*,#2638,.T.);
#3517 = ORIENTED_EDGE('',*,*,#2714,.T.);
#3518 = ORIENTED_EDGE('',*,*,#2785,.T.);
#3519 = ORIENTED_EDGE('',*,*,#2832,.T.);
#3520 = FACE_BOUND('',#3521,.F.);
#3521 = EDGE_LOOP('',(#3522));
#3522 = ORIENTED_EDGE('',*,*,#2909,.T.);
#3523 = FACE_BOUND('',#3524,.F.);
#3524 = EDGE_LOOP('',(#3525));
#3525 = ORIENTED_EDGE('',*,*,#2987,.T.);
#3526 = FACE_BOUND('',#3527,.F.);
#3527 = EDGE_LOOP('',(#3528));
#3528 = ORIENTED_EDGE('',*,*,#3065,.T.);
#3529 = FACE_BOUND('',#3530,.F.);
#3530 = EDGE_LOOP('',(#3531));
#3531 = ORIENTED_EDGE('',*,*,#3143,.T.);
#3532 = FACE_BOUND('',#3533,.F.);
#3533 = EDGE_LOOP('',(#3534));
#3534 = ORIENTED_EDGE('',*,*,#3221,.T.);
#3535 = FACE_BOUND('',#3536,.F.);
#3536 = EDGE_LOOP('',(#3537));
#3537 = ORIENTED_EDGE('',*,*,#3299,.T.);
#3538 = FACE_BOUND('',#3539,.F.);
#3539 = EDGE_LOOP('',(#3540));
#3540 = ORIENTED_EDGE('',*,*,#3377,.T.);
#3541 = FACE_BOUND('',#3542,.F.);
#3542 = EDGE_LOOP('',(#3543));
#3543 = ORIENTED_EDGE('',*,*,#3455,.T.);
#3544 = ADVANCED_FACE('',(#3545,#3551,#3554,#3588,#3591,#3594,#3597,
    #3600,#3603,#3606,#3609),#83,.T.);
#3545 = FACE_BOUND('',#3546,.F.);
#3546 = EDGE_LOOP('',(#3547,#3548,#3549,#3550));
#3547 = ORIENTED_EDGE('',*,*,#67,.T.);
#3548 = ORIENTED_EDGE('',*,*,#153,.T.);
#3549 = ORIENTED_EDGE('',*,*,#229,.T.);
#3550 = ORIENTED_EDGE('',*,*,#300,.T.);
#3551 = FACE_BOUND('',#3552,.T.);
#3552 = EDGE_LOOP('',(#3553));
#3553 = ORIENTED_EDGE('',*,*,#376,.T.);
#3554 = FACE_BOUND('',#3555,.T.);
#3555 = EDGE_LOOP('',(#3556,#3557,#3558,#3559,#3560,#3561,#3562,#3563,
    #3564,#3565,#3566,#3567,#3568,#3569,#3570,#3571,#3572,#3573,#3574,
    #3575,#3576,#3577,#3578,#3579,#3580,#3581,#3582,#3583,#3584,#3585,
    #3586,#3587));
#3556 = ORIENTED_EDGE('',*,*,#459,.T.);
#3557 = ORIENTED_EDGE('',*,*,#535,.T.);
#3558 = ORIENTED_EDGE('',*,*,#611,.T.);
#3559 = ORIENTED_EDGE('',*,*,#687,.T.);
#3560 = ORIENTED_EDGE('',*,*,#763,.T.);
#3561 = ORIENTED_EDGE('',*,*,#839,.T.);
#3562 = ORIENTED_EDGE('',*,*,#915,.T.);
#3563 = ORIENTED_EDGE('',*,*,#991,.T.);
#3564 = ORIENTED_EDGE('',*,*,#1067,.T.);
#3565 = ORIENTED_EDGE('',*,*,#1143,.T.);
#3566 = ORIENTED_EDGE('',*,*,#1219,.T.);
#3567 = ORIENTED_EDGE('',*,*,#1295,.T.);
#3568 = ORIENTED_EDGE('',*,*,#1371,.T.);
#3569 = ORIENTED_EDGE('',*,*,#1447,.T.);
#3570 = ORIENTED_EDGE('',*,*,#1523,.T.);
#3571 = ORIENTED_EDGE('',*,*,#1599,.T.);
#3572 = ORIENTED_EDGE('',*,*,#1675,.T.);
#3573 = ORIENTED_EDGE('',*,*,#1751,.T.);
#3574 = ORIENTED_EDGE('',*,*,#1827,.T.);
#3575 = ORIENTED_EDGE('',*,*,#1903,.T.);
#3576 = ORIENTED_EDGE('',*,*,#1979,.T.);
#3577 = ORIENTED_EDGE('',*,*,#2055,.T.);
#3578 = ORIENTED_EDGE('',*,*,#2131,.T.);
#3579 = ORIENTED_EDGE('',*,*,#2207,.T.);
#3580 = ORIENTED_EDGE('',*,*,#2283,.T.);
#3581 = ORIENTED_EDGE('',*,*,#2359,.T.);
#3582 = ORIENTED_EDGE('',*,*,#2435,.T.);
#3583 = ORIENTED_EDGE('',*,*,#2511,.T.);
#3584 = ORIENTED_EDGE('',*,*,#2587,.T.);
#3585 = ORIENTED_EDGE('',*,*,#2663,.T.);
#3586 = ORIENTED_EDGE('',*,*,#2739,.T.);
#3587 = ORIENTED_EDGE('',*,*,#2810,.T.);
#3588 = FACE_BOUND('',#3589,.T.);
#3589 = EDGE_LOOP('',(#3590));
#3590 = ORIENTED_EDGE('',*,*,#2886,.T.);
#3591 = FACE_BOUND('',#3592,.T.);
#3592 = EDGE_LOOP('',(#3593));
#3593 = ORIENTED_EDGE('',*,*,#2964,.T.);
#3594 = FACE_BOUND('',#3595,.T.);
#3595 = EDGE_LOOP('',(#3596));
#3596 = ORIENTED_EDGE('',*,*,#3042,.T.);
#3597 = FACE_BOUND('',#3598,.T.);
#3598 = EDGE_LOOP('',(#3599));
#3599 = ORIENTED_EDGE('',*,*,#3120,.T.);
#3600 = FACE_BOUND('',#3601,.T.);
#3601 = EDGE_LOOP('',(#3602));
#3602 = ORIENTED_EDGE('',*,*,#3198,.T.);
#3603 = FACE_BOUND('',#3604,.T.);
#3604 = EDGE_LOOP('',(#3605));
#3605 = ORIENTED_EDGE('',*,*,#3276,.T.);
#3606 = FACE_BOUND('',#3607,.T.);
#3607 = EDGE_LOOP('',(#3608));
#3608 = ORIENTED_EDGE('',*,*,#3354,.T.);
#3609 = FACE_BOUND('',#3610,.T.);
#3610 = EDGE_LOOP('',(#3611));
#3611 = ORIENTED_EDGE('',*,*,#3432,.T.);
#3612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3616)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3613,#3614,#3615)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#3613 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#3614 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#3615 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#3616 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#3613,
  'distance_accuracy_value','confusion accuracy');
#3617 = SHAPE_DEFINITION_REPRESENTATION(#3618,#25);
#3618 = PRODUCT_DEFINITION_SHAPE('','',#3619);
#3619 = PRODUCT_DEFINITION('design','',#3620,#3623);
#3620 = PRODUCT_DEFINITION_FORMATION('','',#3621);
#3621 = PRODUCT('Board','Board','',(#3622));
#3622 = PRODUCT_CONTEXT('',#2,'mechanical');
#3623 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#3624 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3625,#3627);
#3625 = ( REPRESENTATION_RELATIONSHIP('','',#25,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3626) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#3626 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#3627 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #3628);
#3628 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('5','=>[0:1:1:2]','',#5,#3619,$);
#3629 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#3621));
#3630 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #3631),#3612);
#3631 = STYLED_ITEM('color',(#3632),#26);
#3632 = PRESENTATION_STYLE_ASSIGNMENT((#3633,#3639));
#3633 = SURFACE_STYLE_USAGE(.BOTH.,#3634);
#3634 = SURFACE_SIDE_STYLE('',(#3635));
#3635 = SURFACE_STYLE_FILL_AREA(#3636);
#3636 = FILL_AREA_STYLE('',(#3637));
#3637 = FILL_AREA_STYLE_COLOUR('',#3638);
#3638 = COLOUR_RGB('',0.E+000,0.501960813999,0.E+000);
#3639 = CURVE_STYLE('',#3640,POSITIVE_LENGTH_MEASURE(0.1),#3638);
#3640 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
