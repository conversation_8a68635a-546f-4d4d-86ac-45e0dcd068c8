ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2018-12-14T18:33:38',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 6.5','Open CASCADE 6.5'
  ,'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('PCB','PCB','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15),#19);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#13 = DIRECTION('',(0.E+000,0.E+000,1.));
#14 = DIRECTION('',(1.,0.E+000,-0.E+000));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.E+000,0.E+000,-1.0459212));
#17 = DIRECTION('',(0.E+000,0.E+000,1.));
#18 = DIRECTION('',(1.,0.E+000,-0.E+000));
#19 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#23)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#20,#21,#22)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#20 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#21 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#22 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#23 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#20,
  'distance_accuracy_value','confusion accuracy');
#24 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#25 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#26),#724);
#26 = MANIFOLD_SOLID_BREP('',#27);
#27 = CLOSED_SHELL('',(#28,#148,#224,#295,#342,#428,#514,#600,#686,#705)
  );
#28 = ADVANCED_FACE('',(#29),#43,.F.);
#29 = FACE_BOUND('',#30,.F.);
#30 = EDGE_LOOP('',(#31,#66,#94,#122));
#31 = ORIENTED_EDGE('',*,*,#32,.T.);
#32 = EDGE_CURVE('',#33,#35,#37,.T.);
#33 = VERTEX_POINT('',#34);
#34 = CARTESIAN_POINT('',(0.E+000,-1.27E-005,0.E+000));
#35 = VERTEX_POINT('',#36);
#36 = CARTESIAN_POINT('',(0.E+000,-1.27E-005,1.0459212));
#37 = SURFACE_CURVE('',#38,(#42,#54),.PCURVE_S1.);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(0.E+000,-1.27E-005,0.E+000));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.E+000,0.E+000,1.));
#42 = PCURVE('',#43,#48);
#43 = PLANE('',#44);
#44 = AXIS2_PLACEMENT_3D('',#45,#46,#47);
#45 = CARTESIAN_POINT('',(0.E+000,-1.27E-005,0.E+000));
#46 = DIRECTION('',(-1.001577183591E-007,1.,0.E+000));
#47 = DIRECTION('',(1.,1.001577183591E-007,0.E+000));
#48 = DEFINITIONAL_REPRESENTATION('',(#49),#53);
#49 = LINE('',#50,#51);
#50 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#51 = VECTOR('',#52,1.);
#52 = DIRECTION('',(0.E+000,-1.));
#53 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#54 = PCURVE('',#55,#60);
#55 = PLANE('',#56);
#56 = AXIS2_PLACEMENT_3D('',#57,#58,#59);
#57 = CARTESIAN_POINT('',(0.E+000,20.00000064,0.E+000));
#58 = DIRECTION('',(1.,0.E+000,-0.E+000));
#59 = DIRECTION('',(0.E+000,-1.,0.E+000));
#60 = DEFINITIONAL_REPRESENTATION('',(#61),#65);
#61 = LINE('',#62,#63);
#62 = CARTESIAN_POINT('',(20.00001334,0.E+000));
#63 = VECTOR('',#64,1.);
#64 = DIRECTION('',(0.E+000,-1.));
#65 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#66 = ORIENTED_EDGE('',*,*,#67,.T.);
#67 = EDGE_CURVE('',#35,#68,#70,.T.);
#68 = VERTEX_POINT('',#69);
#69 = CARTESIAN_POINT('',(126.8000131,0.E+000,1.0459212));
#70 = SURFACE_CURVE('',#71,(#75,#82),.PCURVE_S1.);
#71 = LINE('',#72,#73);
#72 = CARTESIAN_POINT('',(0.E+000,-1.27E-005,1.0459212));
#73 = VECTOR('',#74,1.);
#74 = DIRECTION('',(1.,1.001577183591E-007,0.E+000));
#75 = PCURVE('',#43,#76);
#76 = DEFINITIONAL_REPRESENTATION('',(#77),#81);
#77 = LINE('',#78,#79);
#78 = CARTESIAN_POINT('',(0.E+000,-1.0459212));
#79 = VECTOR('',#80,1.);
#80 = DIRECTION('',(1.,0.E+000));
#81 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#82 = PCURVE('',#83,#88);
#83 = PLANE('',#84);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(0.E+000,-1.27E-005,1.0459212));
#86 = DIRECTION('',(0.E+000,0.E+000,-1.));
#87 = DIRECTION('',(-1.,0.E+000,-0.E+000));
#88 = DEFINITIONAL_REPRESENTATION('',(#89),#93);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(-1.,1.001577183591E-007));
#93 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#94 = ORIENTED_EDGE('',*,*,#95,.F.);
#95 = EDGE_CURVE('',#96,#68,#98,.T.);
#96 = VERTEX_POINT('',#97);
#97 = CARTESIAN_POINT('',(126.8000131,0.E+000,0.E+000));
#98 = SURFACE_CURVE('',#99,(#103,#110),.PCURVE_S1.);
#99 = LINE('',#100,#101);
#100 = CARTESIAN_POINT('',(126.8000131,0.E+000,0.E+000));
#101 = VECTOR('',#102,1.);
#102 = DIRECTION('',(0.E+000,0.E+000,1.));
#103 = PCURVE('',#43,#104);
#104 = DEFINITIONAL_REPRESENTATION('',(#105),#109);
#105 = LINE('',#106,#107);
#106 = CARTESIAN_POINT('',(126.8000131,0.E+000));
#107 = VECTOR('',#108,1.);
#108 = DIRECTION('',(0.E+000,-1.));
#109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#110 = PCURVE('',#111,#116);
#111 = PLANE('',#112);
#112 = AXIS2_PLACEMENT_3D('',#113,#114,#115);
#113 = CARTESIAN_POINT('',(126.8000131,0.E+000,0.E+000));
#114 = DIRECTION('',(-1.,0.E+000,0.E+000));
#115 = DIRECTION('',(0.E+000,1.,0.E+000));
#116 = DEFINITIONAL_REPRESENTATION('',(#117),#121);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(0.E+000,-1.));
#121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#122 = ORIENTED_EDGE('',*,*,#123,.F.);
#123 = EDGE_CURVE('',#33,#96,#124,.T.);
#124 = SURFACE_CURVE('',#125,(#129,#136),.PCURVE_S1.);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(0.E+000,-1.27E-005,0.E+000));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(1.,1.001577183591E-007,0.E+000));
#129 = PCURVE('',#43,#130);
#130 = DEFINITIONAL_REPRESENTATION('',(#131),#135);
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(1.,0.E+000));
#135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#136 = PCURVE('',#137,#142);
#137 = PLANE('',#138);
#138 = AXIS2_PLACEMENT_3D('',#139,#140,#141);
#139 = CARTESIAN_POINT('',(0.E+000,-1.27E-005,0.E+000));
#140 = DIRECTION('',(0.E+000,0.E+000,-1.));
#141 = DIRECTION('',(-1.,0.E+000,-0.E+000));
#142 = DEFINITIONAL_REPRESENTATION('',(#143),#147);
#143 = LINE('',#144,#145);
#144 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#145 = VECTOR('',#146,1.);
#146 = DIRECTION('',(-1.,1.001577183591E-007));
#147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#148 = ADVANCED_FACE('',(#149),#111,.F.);
#149 = FACE_BOUND('',#150,.F.);
#150 = EDGE_LOOP('',(#151,#152,#175,#203));
#151 = ORIENTED_EDGE('',*,*,#95,.T.);
#152 = ORIENTED_EDGE('',*,*,#153,.T.);
#153 = EDGE_CURVE('',#68,#154,#156,.T.);
#154 = VERTEX_POINT('',#155);
#155 = CARTESIAN_POINT('',(126.8000131,20.00000064,1.0459212));
#156 = SURFACE_CURVE('',#157,(#161,#168),.PCURVE_S1.);
#157 = LINE('',#158,#159);
#158 = CARTESIAN_POINT('',(126.8000131,0.E+000,1.0459212));
#159 = VECTOR('',#160,1.);
#160 = DIRECTION('',(0.E+000,1.,0.E+000));
#161 = PCURVE('',#111,#162);
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(0.E+000,-1.0459212));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(1.,0.E+000));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = PCURVE('',#83,#169);
#169 = DEFINITIONAL_REPRESENTATION('',(#170),#174);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(-126.8000131,1.27E-005));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(0.E+000,1.));
#174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#175 = ORIENTED_EDGE('',*,*,#176,.F.);
#176 = EDGE_CURVE('',#177,#154,#179,.T.);
#177 = VERTEX_POINT('',#178);
#178 = CARTESIAN_POINT('',(126.8000131,20.00000064,0.E+000));
#179 = SURFACE_CURVE('',#180,(#184,#191),.PCURVE_S1.);
#180 = LINE('',#181,#182);
#181 = CARTESIAN_POINT('',(126.8000131,20.00000064,0.E+000));
#182 = VECTOR('',#183,1.);
#183 = DIRECTION('',(0.E+000,0.E+000,1.));
#184 = PCURVE('',#111,#185);
#185 = DEFINITIONAL_REPRESENTATION('',(#186),#190);
#186 = LINE('',#187,#188);
#187 = CARTESIAN_POINT('',(20.00000064,0.E+000));
#188 = VECTOR('',#189,1.);
#189 = DIRECTION('',(0.E+000,-1.));
#190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#191 = PCURVE('',#192,#197);
#192 = PLANE('',#193);
#193 = AXIS2_PLACEMENT_3D('',#194,#195,#196);
#194 = CARTESIAN_POINT('',(126.8000131,20.00000064,0.E+000));
#195 = DIRECTION('',(0.E+000,-1.,0.E+000));
#196 = DIRECTION('',(-1.,0.E+000,0.E+000));
#197 = DEFINITIONAL_REPRESENTATION('',(#198),#202);
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(0.E+000,-1.));
#202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#203 = ORIENTED_EDGE('',*,*,#204,.F.);
#204 = EDGE_CURVE('',#96,#177,#205,.T.);
#205 = SURFACE_CURVE('',#206,(#210,#217),.PCURVE_S1.);
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(126.8000131,0.E+000,0.E+000));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(0.E+000,1.,0.E+000));
#210 = PCURVE('',#111,#211);
#211 = DEFINITIONAL_REPRESENTATION('',(#212),#216);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(1.,0.E+000));
#216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#217 = PCURVE('',#137,#218);
#218 = DEFINITIONAL_REPRESENTATION('',(#219),#223);
#219 = LINE('',#220,#221);
#220 = CARTESIAN_POINT('',(-126.8000131,1.27E-005));
#221 = VECTOR('',#222,1.);
#222 = DIRECTION('',(0.E+000,1.));
#223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#224 = ADVANCED_FACE('',(#225),#192,.F.);
#225 = FACE_BOUND('',#226,.F.);
#226 = EDGE_LOOP('',(#227,#228,#251,#274));
#227 = ORIENTED_EDGE('',*,*,#176,.T.);
#228 = ORIENTED_EDGE('',*,*,#229,.T.);
#229 = EDGE_CURVE('',#154,#230,#232,.T.);
#230 = VERTEX_POINT('',#231);
#231 = CARTESIAN_POINT('',(0.E+000,20.00000064,1.0459212));
#232 = SURFACE_CURVE('',#233,(#237,#244),.PCURVE_S1.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(126.8000131,20.00000064,1.0459212));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(-1.,0.E+000,0.E+000));
#237 = PCURVE('',#192,#238);
#238 = DEFINITIONAL_REPRESENTATION('',(#239),#243);
#239 = LINE('',#240,#241);
#240 = CARTESIAN_POINT('',(0.E+000,-1.0459212));
#241 = VECTOR('',#242,1.);
#242 = DIRECTION('',(1.,0.E+000));
#243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#244 = PCURVE('',#83,#245);
#245 = DEFINITIONAL_REPRESENTATION('',(#246),#250);
#246 = LINE('',#247,#248);
#247 = CARTESIAN_POINT('',(-126.8000131,20.00001334));
#248 = VECTOR('',#249,1.);
#249 = DIRECTION('',(1.,0.E+000));
#250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#251 = ORIENTED_EDGE('',*,*,#252,.F.);
#252 = EDGE_CURVE('',#253,#230,#255,.T.);
#253 = VERTEX_POINT('',#254);
#254 = CARTESIAN_POINT('',(0.E+000,20.00000064,0.E+000));
#255 = SURFACE_CURVE('',#256,(#260,#267),.PCURVE_S1.);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(0.E+000,20.00000064,0.E+000));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(0.E+000,0.E+000,1.));
#260 = PCURVE('',#192,#261);
#261 = DEFINITIONAL_REPRESENTATION('',(#262),#266);
#262 = LINE('',#263,#264);
#263 = CARTESIAN_POINT('',(126.8000131,0.E+000));
#264 = VECTOR('',#265,1.);
#265 = DIRECTION('',(0.E+000,-1.));
#266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#267 = PCURVE('',#55,#268);
#268 = DEFINITIONAL_REPRESENTATION('',(#269),#273);
#269 = LINE('',#270,#271);
#270 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#271 = VECTOR('',#272,1.);
#272 = DIRECTION('',(0.E+000,-1.));
#273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#274 = ORIENTED_EDGE('',*,*,#275,.F.);
#275 = EDGE_CURVE('',#177,#253,#276,.T.);
#276 = SURFACE_CURVE('',#277,(#281,#288),.PCURVE_S1.);
#277 = LINE('',#278,#279);
#278 = CARTESIAN_POINT('',(126.8000131,20.00000064,0.E+000));
#279 = VECTOR('',#280,1.);
#280 = DIRECTION('',(-1.,0.E+000,0.E+000));
#281 = PCURVE('',#192,#282);
#282 = DEFINITIONAL_REPRESENTATION('',(#283),#287);
#283 = LINE('',#284,#285);
#284 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#285 = VECTOR('',#286,1.);
#286 = DIRECTION('',(1.,0.E+000));
#287 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#288 = PCURVE('',#137,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#294);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(-126.8000131,20.00001334));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(1.,0.E+000));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = ADVANCED_FACE('',(#296),#55,.F.);
#296 = FACE_BOUND('',#297,.F.);
#297 = EDGE_LOOP('',(#298,#299,#320,#321));
#298 = ORIENTED_EDGE('',*,*,#252,.T.);
#299 = ORIENTED_EDGE('',*,*,#300,.T.);
#300 = EDGE_CURVE('',#230,#35,#301,.T.);
#301 = SURFACE_CURVE('',#302,(#306,#313),.PCURVE_S1.);
#302 = LINE('',#303,#304);
#303 = CARTESIAN_POINT('',(0.E+000,20.00000064,1.0459212));
#304 = VECTOR('',#305,1.);
#305 = DIRECTION('',(0.E+000,-1.,0.E+000));
#306 = PCURVE('',#55,#307);
#307 = DEFINITIONAL_REPRESENTATION('',(#308),#312);
#308 = LINE('',#309,#310);
#309 = CARTESIAN_POINT('',(0.E+000,-1.0459212));
#310 = VECTOR('',#311,1.);
#311 = DIRECTION('',(1.,0.E+000));
#312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#313 = PCURVE('',#83,#314);
#314 = DEFINITIONAL_REPRESENTATION('',(#315),#319);
#315 = LINE('',#316,#317);
#316 = CARTESIAN_POINT('',(0.E+000,20.00001334));
#317 = VECTOR('',#318,1.);
#318 = DIRECTION('',(-0.E+000,-1.));
#319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#320 = ORIENTED_EDGE('',*,*,#32,.F.);
#321 = ORIENTED_EDGE('',*,*,#322,.F.);
#322 = EDGE_CURVE('',#253,#33,#323,.T.);
#323 = SURFACE_CURVE('',#324,(#328,#335),.PCURVE_S1.);
#324 = LINE('',#325,#326);
#325 = CARTESIAN_POINT('',(0.E+000,20.00000064,0.E+000));
#326 = VECTOR('',#327,1.);
#327 = DIRECTION('',(0.E+000,-1.,0.E+000));
#328 = PCURVE('',#55,#329);
#329 = DEFINITIONAL_REPRESENTATION('',(#330),#334);
#330 = LINE('',#331,#332);
#331 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#332 = VECTOR('',#333,1.);
#333 = DIRECTION('',(1.,0.E+000));
#334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#335 = PCURVE('',#137,#336);
#336 = DEFINITIONAL_REPRESENTATION('',(#337),#341);
#337 = LINE('',#338,#339);
#338 = CARTESIAN_POINT('',(0.E+000,20.00001334));
#339 = VECTOR('',#340,1.);
#340 = DIRECTION('',(-0.E+000,-1.));
#341 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#342 = ADVANCED_FACE('',(#343),#357,.F.);
#343 = FACE_BOUND('',#344,.T.);
#344 = EDGE_LOOP('',(#345,#375,#401,#402));
#345 = ORIENTED_EDGE('',*,*,#346,.T.);
#346 = EDGE_CURVE('',#347,#349,#351,.T.);
#347 = VERTEX_POINT('',#348);
#348 = CARTESIAN_POINT('',(6.5999995,3.39998812,0.E+000));
#349 = VERTEX_POINT('',#350);
#350 = CARTESIAN_POINT('',(6.5999995,3.39998812,1.0459212));
#351 = SEAM_CURVE('',#352,(#356,#368),.PCURVE_S1.);
#352 = LINE('',#353,#354);
#353 = CARTESIAN_POINT('',(6.5999995,3.39998812,0.E+000));
#354 = VECTOR('',#355,1.);
#355 = DIRECTION('',(0.E+000,0.E+000,1.));
#356 = PCURVE('',#357,#362);
#357 = CYLINDRICAL_SURFACE('',#358,1.59999934);
#358 = AXIS2_PLACEMENT_3D('',#359,#360,#361);
#359 = CARTESIAN_POINT('',(5.00000016,3.39998812,0.E+000));
#360 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#361 = DIRECTION('',(1.,0.E+000,-0.E+000));
#362 = DEFINITIONAL_REPRESENTATION('',(#363),#367);
#363 = LINE('',#364,#365);
#364 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#365 = VECTOR('',#366,1.);
#366 = DIRECTION('',(-0.E+000,-1.));
#367 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#368 = PCURVE('',#357,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(-0.E+000,-1.));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = ORIENTED_EDGE('',*,*,#376,.T.);
#376 = EDGE_CURVE('',#349,#349,#377,.T.);
#377 = SURFACE_CURVE('',#378,(#383,#390),.PCURVE_S1.);
#378 = CIRCLE('',#379,1.59999934);
#379 = AXIS2_PLACEMENT_3D('',#380,#381,#382);
#380 = CARTESIAN_POINT('',(5.00000016,3.39998812,1.0459212));
#381 = DIRECTION('',(0.E+000,0.E+000,1.));
#382 = DIRECTION('',(1.,0.E+000,-0.E+000));
#383 = PCURVE('',#357,#384);
#384 = DEFINITIONAL_REPRESENTATION('',(#385),#389);
#385 = LINE('',#386,#387);
#386 = CARTESIAN_POINT('',(-0.E+000,-1.0459212));
#387 = VECTOR('',#388,1.);
#388 = DIRECTION('',(-1.,0.E+000));
#389 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#390 = PCURVE('',#83,#391);
#391 = DEFINITIONAL_REPRESENTATION('',(#392),#400);
#392 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#393,#394,#395,#396,#397,#398
,#399),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#393 = CARTESIAN_POINT('',(-6.5999995,3.40000082));
#394 = CARTESIAN_POINT('',(-6.5999995,6.171280968957));
#395 = CARTESIAN_POINT('',(-4.20000049,4.785640894478));
#396 = CARTESIAN_POINT('',(-1.80000148,3.40000082));
#397 = CARTESIAN_POINT('',(-4.20000049,2.014360745522));
#398 = CARTESIAN_POINT('',(-6.5999995,0.628720671043));
#399 = CARTESIAN_POINT('',(-6.5999995,3.40000082));
#400 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#401 = ORIENTED_EDGE('',*,*,#346,.F.);
#402 = ORIENTED_EDGE('',*,*,#403,.F.);
#403 = EDGE_CURVE('',#347,#347,#404,.T.);
#404 = SURFACE_CURVE('',#405,(#410,#417),.PCURVE_S1.);
#405 = CIRCLE('',#406,1.59999934);
#406 = AXIS2_PLACEMENT_3D('',#407,#408,#409);
#407 = CARTESIAN_POINT('',(5.00000016,3.39998812,0.E+000));
#408 = DIRECTION('',(0.E+000,0.E+000,1.));
#409 = DIRECTION('',(1.,0.E+000,-0.E+000));
#410 = PCURVE('',#357,#411);
#411 = DEFINITIONAL_REPRESENTATION('',(#412),#416);
#412 = LINE('',#413,#414);
#413 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#414 = VECTOR('',#415,1.);
#415 = DIRECTION('',(-1.,0.E+000));
#416 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#417 = PCURVE('',#137,#418);
#418 = DEFINITIONAL_REPRESENTATION('',(#419),#427);
#419 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#420,#421,#422,#423,#424,#425
,#426),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#420 = CARTESIAN_POINT('',(-6.5999995,3.40000082));
#421 = CARTESIAN_POINT('',(-6.5999995,6.171280968957));
#422 = CARTESIAN_POINT('',(-4.20000049,4.785640894478));
#423 = CARTESIAN_POINT('',(-1.80000148,3.40000082));
#424 = CARTESIAN_POINT('',(-4.20000049,2.014360745522));
#425 = CARTESIAN_POINT('',(-6.5999995,0.628720671043));
#426 = CARTESIAN_POINT('',(-6.5999995,3.40000082));
#427 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#428 = ADVANCED_FACE('',(#429),#443,.F.);
#429 = FACE_BOUND('',#430,.T.);
#430 = EDGE_LOOP('',(#431,#461,#487,#488));
#431 = ORIENTED_EDGE('',*,*,#432,.T.);
#432 = EDGE_CURVE('',#433,#435,#437,.T.);
#433 = VERTEX_POINT('',#434);
#434 = CARTESIAN_POINT('',(6.5999995,16.59998712,0.E+000));
#435 = VERTEX_POINT('',#436);
#436 = CARTESIAN_POINT('',(6.5999995,16.59998712,1.0459212));
#437 = SEAM_CURVE('',#438,(#442,#454),.PCURVE_S1.);
#438 = LINE('',#439,#440);
#439 = CARTESIAN_POINT('',(6.5999995,16.59998712,0.E+000));
#440 = VECTOR('',#441,1.);
#441 = DIRECTION('',(0.E+000,0.E+000,1.));
#442 = PCURVE('',#443,#448);
#443 = CYLINDRICAL_SURFACE('',#444,1.59999934);
#444 = AXIS2_PLACEMENT_3D('',#445,#446,#447);
#445 = CARTESIAN_POINT('',(5.00000016,16.59998712,0.E+000));
#446 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#447 = DIRECTION('',(1.,0.E+000,-0.E+000));
#448 = DEFINITIONAL_REPRESENTATION('',(#449),#453);
#449 = LINE('',#450,#451);
#450 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#451 = VECTOR('',#452,1.);
#452 = DIRECTION('',(-0.E+000,-1.));
#453 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#454 = PCURVE('',#443,#455);
#455 = DEFINITIONAL_REPRESENTATION('',(#456),#460);
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(-0.E+000,-1.));
#460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#461 = ORIENTED_EDGE('',*,*,#462,.T.);
#462 = EDGE_CURVE('',#435,#435,#463,.T.);
#463 = SURFACE_CURVE('',#464,(#469,#476),.PCURVE_S1.);
#464 = CIRCLE('',#465,1.59999934);
#465 = AXIS2_PLACEMENT_3D('',#466,#467,#468);
#466 = CARTESIAN_POINT('',(5.00000016,16.59998712,1.0459212));
#467 = DIRECTION('',(0.E+000,0.E+000,1.));
#468 = DIRECTION('',(1.,0.E+000,-0.E+000));
#469 = PCURVE('',#443,#470);
#470 = DEFINITIONAL_REPRESENTATION('',(#471),#475);
#471 = LINE('',#472,#473);
#472 = CARTESIAN_POINT('',(-0.E+000,-1.0459212));
#473 = VECTOR('',#474,1.);
#474 = DIRECTION('',(-1.,0.E+000));
#475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#476 = PCURVE('',#83,#477);
#477 = DEFINITIONAL_REPRESENTATION('',(#478),#486);
#478 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#479,#480,#481,#482,#483,#484
,#485),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#479 = CARTESIAN_POINT('',(-6.5999995,16.59999982));
#480 = CARTESIAN_POINT('',(-6.5999995,19.371279968957));
#481 = CARTESIAN_POINT('',(-4.20000049,17.985639894478));
#482 = CARTESIAN_POINT('',(-1.80000148,16.59999982));
#483 = CARTESIAN_POINT('',(-4.20000049,15.214359745522));
#484 = CARTESIAN_POINT('',(-6.5999995,13.828719671043));
#485 = CARTESIAN_POINT('',(-6.5999995,16.59999982));
#486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#487 = ORIENTED_EDGE('',*,*,#432,.F.);
#488 = ORIENTED_EDGE('',*,*,#489,.F.);
#489 = EDGE_CURVE('',#433,#433,#490,.T.);
#490 = SURFACE_CURVE('',#491,(#496,#503),.PCURVE_S1.);
#491 = CIRCLE('',#492,1.59999934);
#492 = AXIS2_PLACEMENT_3D('',#493,#494,#495);
#493 = CARTESIAN_POINT('',(5.00000016,16.59998712,0.E+000));
#494 = DIRECTION('',(0.E+000,0.E+000,1.));
#495 = DIRECTION('',(1.,0.E+000,-0.E+000));
#496 = PCURVE('',#443,#497);
#497 = DEFINITIONAL_REPRESENTATION('',(#498),#502);
#498 = LINE('',#499,#500);
#499 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#500 = VECTOR('',#501,1.);
#501 = DIRECTION('',(-1.,0.E+000));
#502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#503 = PCURVE('',#137,#504);
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#513);
#505 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#506,#507,#508,#509,#510,#511
,#512),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#506 = CARTESIAN_POINT('',(-6.5999995,16.59999982));
#507 = CARTESIAN_POINT('',(-6.5999995,19.371279968957));
#508 = CARTESIAN_POINT('',(-4.20000049,17.985639894478));
#509 = CARTESIAN_POINT('',(-1.80000148,16.59999982));
#510 = CARTESIAN_POINT('',(-4.20000049,15.214359745522));
#511 = CARTESIAN_POINT('',(-6.5999995,13.828719671043));
#512 = CARTESIAN_POINT('',(-6.5999995,16.59999982));
#513 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#514 = ADVANCED_FACE('',(#515),#529,.F.);
#515 = FACE_BOUND('',#516,.T.);
#516 = EDGE_LOOP('',(#517,#547,#573,#574));
#517 = ORIENTED_EDGE('',*,*,#518,.T.);
#518 = EDGE_CURVE('',#519,#521,#523,.T.);
#519 = VERTEX_POINT('',#520);
#520 = CARTESIAN_POINT('',(123.39999958,3.39998812,0.E+000));
#521 = VERTEX_POINT('',#522);
#522 = CARTESIAN_POINT('',(123.39999958,3.39998812,1.0459212));
#523 = SEAM_CURVE('',#524,(#528,#540),.PCURVE_S1.);
#524 = LINE('',#525,#526);
#525 = CARTESIAN_POINT('',(123.39999958,3.39998812,0.E+000));
#526 = VECTOR('',#527,1.);
#527 = DIRECTION('',(0.E+000,0.E+000,1.));
#528 = PCURVE('',#529,#534);
#529 = CYLINDRICAL_SURFACE('',#530,1.59999934);
#530 = AXIS2_PLACEMENT_3D('',#531,#532,#533);
#531 = CARTESIAN_POINT('',(121.80000024,3.39998812,0.E+000));
#532 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#533 = DIRECTION('',(1.,0.E+000,-0.E+000));
#534 = DEFINITIONAL_REPRESENTATION('',(#535),#539);
#535 = LINE('',#536,#537);
#536 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#537 = VECTOR('',#538,1.);
#538 = DIRECTION('',(-0.E+000,-1.));
#539 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#540 = PCURVE('',#529,#541);
#541 = DEFINITIONAL_REPRESENTATION('',(#542),#546);
#542 = LINE('',#543,#544);
#543 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#544 = VECTOR('',#545,1.);
#545 = DIRECTION('',(-0.E+000,-1.));
#546 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#547 = ORIENTED_EDGE('',*,*,#548,.T.);
#548 = EDGE_CURVE('',#521,#521,#549,.T.);
#549 = SURFACE_CURVE('',#550,(#555,#562),.PCURVE_S1.);
#550 = CIRCLE('',#551,1.59999934);
#551 = AXIS2_PLACEMENT_3D('',#552,#553,#554);
#552 = CARTESIAN_POINT('',(121.80000024,3.39998812,1.0459212));
#553 = DIRECTION('',(0.E+000,0.E+000,1.));
#554 = DIRECTION('',(1.,0.E+000,-0.E+000));
#555 = PCURVE('',#529,#556);
#556 = DEFINITIONAL_REPRESENTATION('',(#557),#561);
#557 = LINE('',#558,#559);
#558 = CARTESIAN_POINT('',(-0.E+000,-1.0459212));
#559 = VECTOR('',#560,1.);
#560 = DIRECTION('',(-1.,0.E+000));
#561 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#562 = PCURVE('',#83,#563);
#563 = DEFINITIONAL_REPRESENTATION('',(#564),#572);
#564 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#565,#566,#567,#568,#569,#570
,#571),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#565 = CARTESIAN_POINT('',(-123.39999958,3.40000082));
#566 = CARTESIAN_POINT('',(-123.39999958,6.171280968957));
#567 = CARTESIAN_POINT('',(-121.00000057,4.785640894478));
#568 = CARTESIAN_POINT('',(-118.60000156,3.40000082));
#569 = CARTESIAN_POINT('',(-121.00000057,2.014360745522));
#570 = CARTESIAN_POINT('',(-123.39999958,0.628720671043));
#571 = CARTESIAN_POINT('',(-123.39999958,3.40000082));
#572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#573 = ORIENTED_EDGE('',*,*,#518,.F.);
#574 = ORIENTED_EDGE('',*,*,#575,.F.);
#575 = EDGE_CURVE('',#519,#519,#576,.T.);
#576 = SURFACE_CURVE('',#577,(#582,#589),.PCURVE_S1.);
#577 = CIRCLE('',#578,1.59999934);
#578 = AXIS2_PLACEMENT_3D('',#579,#580,#581);
#579 = CARTESIAN_POINT('',(121.80000024,3.39998812,0.E+000));
#580 = DIRECTION('',(0.E+000,0.E+000,1.));
#581 = DIRECTION('',(1.,0.E+000,-0.E+000));
#582 = PCURVE('',#529,#583);
#583 = DEFINITIONAL_REPRESENTATION('',(#584),#588);
#584 = LINE('',#585,#586);
#585 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#586 = VECTOR('',#587,1.);
#587 = DIRECTION('',(-1.,0.E+000));
#588 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#589 = PCURVE('',#137,#590);
#590 = DEFINITIONAL_REPRESENTATION('',(#591),#599);
#591 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#592,#593,#594,#595,#596,#597
,#598),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#592 = CARTESIAN_POINT('',(-123.39999958,3.40000082));
#593 = CARTESIAN_POINT('',(-123.39999958,6.171280968957));
#594 = CARTESIAN_POINT('',(-121.00000057,4.785640894478));
#595 = CARTESIAN_POINT('',(-118.60000156,3.40000082));
#596 = CARTESIAN_POINT('',(-121.00000057,2.014360745522));
#597 = CARTESIAN_POINT('',(-123.39999958,0.628720671043));
#598 = CARTESIAN_POINT('',(-123.39999958,3.40000082));
#599 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#600 = ADVANCED_FACE('',(#601),#615,.F.);
#601 = FACE_BOUND('',#602,.T.);
#602 = EDGE_LOOP('',(#603,#633,#659,#660));
#603 = ORIENTED_EDGE('',*,*,#604,.T.);
#604 = EDGE_CURVE('',#605,#607,#609,.T.);
#605 = VERTEX_POINT('',#606);
#606 = CARTESIAN_POINT('',(123.39999958,16.59998712,0.E+000));
#607 = VERTEX_POINT('',#608);
#608 = CARTESIAN_POINT('',(123.39999958,16.59998712,1.0459212));
#609 = SEAM_CURVE('',#610,(#614,#626),.PCURVE_S1.);
#610 = LINE('',#611,#612);
#611 = CARTESIAN_POINT('',(123.39999958,16.59998712,0.E+000));
#612 = VECTOR('',#613,1.);
#613 = DIRECTION('',(0.E+000,0.E+000,1.));
#614 = PCURVE('',#615,#620);
#615 = CYLINDRICAL_SURFACE('',#616,1.59999934);
#616 = AXIS2_PLACEMENT_3D('',#617,#618,#619);
#617 = CARTESIAN_POINT('',(121.80000024,16.59998712,0.E+000));
#618 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#619 = DIRECTION('',(1.,0.E+000,-0.E+000));
#620 = DEFINITIONAL_REPRESENTATION('',(#621),#625);
#621 = LINE('',#622,#623);
#622 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#623 = VECTOR('',#624,1.);
#624 = DIRECTION('',(-0.E+000,-1.));
#625 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#626 = PCURVE('',#615,#627);
#627 = DEFINITIONAL_REPRESENTATION('',(#628),#632);
#628 = LINE('',#629,#630);
#629 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#630 = VECTOR('',#631,1.);
#631 = DIRECTION('',(-0.E+000,-1.));
#632 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#633 = ORIENTED_EDGE('',*,*,#634,.T.);
#634 = EDGE_CURVE('',#607,#607,#635,.T.);
#635 = SURFACE_CURVE('',#636,(#641,#648),.PCURVE_S1.);
#636 = CIRCLE('',#637,1.59999934);
#637 = AXIS2_PLACEMENT_3D('',#638,#639,#640);
#638 = CARTESIAN_POINT('',(121.80000024,16.59998712,1.0459212));
#639 = DIRECTION('',(0.E+000,0.E+000,1.));
#640 = DIRECTION('',(1.,0.E+000,-0.E+000));
#641 = PCURVE('',#615,#642);
#642 = DEFINITIONAL_REPRESENTATION('',(#643),#647);
#643 = LINE('',#644,#645);
#644 = CARTESIAN_POINT('',(-0.E+000,-1.0459212));
#645 = VECTOR('',#646,1.);
#646 = DIRECTION('',(-1.,0.E+000));
#647 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#648 = PCURVE('',#83,#649);
#649 = DEFINITIONAL_REPRESENTATION('',(#650),#658);
#650 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#651,#652,#653,#654,#655,#656
,#657),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#651 = CARTESIAN_POINT('',(-123.39999958,16.59999982));
#652 = CARTESIAN_POINT('',(-123.39999958,19.371279968957));
#653 = CARTESIAN_POINT('',(-121.00000057,17.985639894478));
#654 = CARTESIAN_POINT('',(-118.60000156,16.59999982));
#655 = CARTESIAN_POINT('',(-121.00000057,15.214359745522));
#656 = CARTESIAN_POINT('',(-123.39999958,13.828719671043));
#657 = CARTESIAN_POINT('',(-123.39999958,16.59999982));
#658 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#659 = ORIENTED_EDGE('',*,*,#604,.F.);
#660 = ORIENTED_EDGE('',*,*,#661,.F.);
#661 = EDGE_CURVE('',#605,#605,#662,.T.);
#662 = SURFACE_CURVE('',#663,(#668,#675),.PCURVE_S1.);
#663 = CIRCLE('',#664,1.59999934);
#664 = AXIS2_PLACEMENT_3D('',#665,#666,#667);
#665 = CARTESIAN_POINT('',(121.80000024,16.59998712,0.E+000));
#666 = DIRECTION('',(0.E+000,0.E+000,1.));
#667 = DIRECTION('',(1.,0.E+000,-0.E+000));
#668 = PCURVE('',#615,#669);
#669 = DEFINITIONAL_REPRESENTATION('',(#670),#674);
#670 = LINE('',#671,#672);
#671 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#672 = VECTOR('',#673,1.);
#673 = DIRECTION('',(-1.,0.E+000));
#674 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#675 = PCURVE('',#137,#676);
#676 = DEFINITIONAL_REPRESENTATION('',(#677),#685);
#677 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#678,#679,#680,#681,#682,#683
,#684),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#678 = CARTESIAN_POINT('',(-123.39999958,16.59999982));
#679 = CARTESIAN_POINT('',(-123.39999958,19.371279968957));
#680 = CARTESIAN_POINT('',(-121.00000057,17.985639894478));
#681 = CARTESIAN_POINT('',(-118.60000156,16.59999982));
#682 = CARTESIAN_POINT('',(-121.00000057,15.214359745522));
#683 = CARTESIAN_POINT('',(-123.39999958,13.828719671043));
#684 = CARTESIAN_POINT('',(-123.39999958,16.59999982));
#685 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#686 = ADVANCED_FACE('',(#687,#693,#696,#699,#702),#137,.T.);
#687 = FACE_BOUND('',#688,.F.);
#688 = EDGE_LOOP('',(#689,#690,#691,#692));
#689 = ORIENTED_EDGE('',*,*,#123,.T.);
#690 = ORIENTED_EDGE('',*,*,#204,.T.);
#691 = ORIENTED_EDGE('',*,*,#275,.T.);
#692 = ORIENTED_EDGE('',*,*,#322,.T.);
#693 = FACE_BOUND('',#694,.T.);
#694 = EDGE_LOOP('',(#695));
#695 = ORIENTED_EDGE('',*,*,#403,.T.);
#696 = FACE_BOUND('',#697,.T.);
#697 = EDGE_LOOP('',(#698));
#698 = ORIENTED_EDGE('',*,*,#489,.T.);
#699 = FACE_BOUND('',#700,.T.);
#700 = EDGE_LOOP('',(#701));
#701 = ORIENTED_EDGE('',*,*,#575,.T.);
#702 = FACE_BOUND('',#703,.T.);
#703 = EDGE_LOOP('',(#704));
#704 = ORIENTED_EDGE('',*,*,#661,.T.);
#705 = ADVANCED_FACE('',(#706,#712,#715,#718,#721),#83,.F.);
#706 = FACE_BOUND('',#707,.T.);
#707 = EDGE_LOOP('',(#708,#709,#710,#711));
#708 = ORIENTED_EDGE('',*,*,#67,.T.);
#709 = ORIENTED_EDGE('',*,*,#153,.T.);
#710 = ORIENTED_EDGE('',*,*,#229,.T.);
#711 = ORIENTED_EDGE('',*,*,#300,.T.);
#712 = FACE_BOUND('',#713,.F.);
#713 = EDGE_LOOP('',(#714));
#714 = ORIENTED_EDGE('',*,*,#376,.T.);
#715 = FACE_BOUND('',#716,.F.);
#716 = EDGE_LOOP('',(#717));
#717 = ORIENTED_EDGE('',*,*,#462,.T.);
#718 = FACE_BOUND('',#719,.F.);
#719 = EDGE_LOOP('',(#720));
#720 = ORIENTED_EDGE('',*,*,#548,.T.);
#721 = FACE_BOUND('',#722,.F.);
#722 = EDGE_LOOP('',(#723));
#723 = ORIENTED_EDGE('',*,*,#634,.T.);
#724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#728)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#725,#726,#727)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#725 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#726 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#727 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#728 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#725,
  'distance_accuracy_value','confusion accuracy');
#729 = SHAPE_DEFINITION_REPRESENTATION(#730,#25);
#730 = PRODUCT_DEFINITION_SHAPE('','',#731);
#731 = PRODUCT_DEFINITION('design','',#732,#735);
#732 = PRODUCT_DEFINITION_FORMATION('','',#733);
#733 = PRODUCT('Board','Board','',(#734));
#734 = PRODUCT_CONTEXT('',#2,'mechanical');
#735 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#736 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#737,#739);
#737 = ( REPRESENTATION_RELATIONSHIP('','',#25,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#738) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#738 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#739 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#740
  );
#740 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('146','=>[0:1:1:2]','',#5,#731,$);
#741 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#733));
#742 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#743)
  ,#724);
#743 = STYLED_ITEM('color',(#744),#26);
#744 = PRESENTATION_STYLE_ASSIGNMENT((#745,#751));
#745 = SURFACE_STYLE_USAGE(.BOTH.,#746);
#746 = SURFACE_SIDE_STYLE('',(#747));
#747 = SURFACE_STYLE_FILL_AREA(#748);
#748 = FILL_AREA_STYLE('',(#749));
#749 = FILL_AREA_STYLE_COLOUR('',#750);
#750 = COLOUR_RGB('',0.E+000,0.501960813999,0.E+000);
#751 = CURVE_STYLE('',#752,POSITIVE_LENGTH_MEASURE(0.1),#750);
#752 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
