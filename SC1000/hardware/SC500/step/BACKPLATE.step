ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2020-08-20T13:19:09',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 6.5','Open CASCADE 6.5'
  ,'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('PCB','PCB','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15),#19);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#13 = DIRECTION('',(0.E+000,0.E+000,1.));
#14 = DIRECTION('',(1.,0.E+000,-0.E+000));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.E+000,0.E+000,-1.72191934));
#17 = DIRECTION('',(0.E+000,0.E+000,1.));
#18 = DIRECTION('',(1.,0.E+000,-0.E+000));
#19 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#23)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#20,#21,#22)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#20 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#21 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#22 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#23 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#20,
  'distance_accuracy_value','confusion accuracy');
#24 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#25 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#26),#4416);
#26 = MANIFOLD_SOLID_BREP('',#27);
#27 = CLOSED_SHELL('',(#28,#148,#224,#300,#386,#457,#514,#592,#670,#748,
    #826,#904,#1014,#1090,#1161,#1208,#1286,#1396,#1472,#1548,#1624,
    #1700,#1776,#1852,#1928,#2004,#2080,#2156,#2232,#2308,#2384,#2460,
    #2536,#2612,#2688,#2764,#2840,#2916,#2992,#3068,#3144,#3220,#3296,
    #3372,#3448,#3524,#3600,#3676,#3752,#3828,#3904,#3975,#4022,#4100,
    #4178,#4256,#4336));
#28 = ADVANCED_FACE('',(#29),#43,.T.);
#29 = FACE_BOUND('',#30,.T.);
#30 = EDGE_LOOP('',(#31,#66,#94,#122));
#31 = ORIENTED_EDGE('',*,*,#32,.T.);
#32 = EDGE_CURVE('',#33,#35,#37,.T.);
#33 = VERTEX_POINT('',#34);
#34 = CARTESIAN_POINT('',(-1.225148454909E-017,1.99998838,0.E+000));
#35 = VERTEX_POINT('',#36);
#36 = CARTESIAN_POINT('',(-1.225148454909E-017,1.99998838,1.72191934));
#37 = SURFACE_CURVE('',#38,(#42,#54),.PCURVE_S1.);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(-1.225148454909E-017,1.99998838,0.E+000));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.E+000,0.E+000,1.));
#42 = PCURVE('',#43,#48);
#43 = PLANE('',#44);
#44 = AXIS2_PLACEMENT_3D('',#45,#46,#47);
#45 = CARTESIAN_POINT('',(0.E+000,1.99998838,0.E+000));
#46 = DIRECTION('',(-1.,0.E+000,0.E+000));
#47 = DIRECTION('',(0.E+000,1.,0.E+000));
#48 = DEFINITIONAL_REPRESENTATION('',(#49),#53);
#49 = LINE('',#50,#51);
#50 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#51 = VECTOR('',#52,1.);
#52 = DIRECTION('',(0.E+000,-1.));
#53 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#54 = PCURVE('',#55,#60);
#55 = CYLINDRICAL_SURFACE('',#56,1.999986282967);
#56 = AXIS2_PLACEMENT_3D('',#57,#58,#59);
#57 = CARTESIAN_POINT('',(1.999986282966,1.999986282966,0.E+000));
#58 = DIRECTION('',(0.E+000,0.E+000,-1.));
#59 = DIRECTION('',(1.048523997587E-006,-0.999999999999,0.E+000));
#60 = DEFINITIONAL_REPRESENTATION('',(#61),#65);
#61 = LINE('',#62,#63);
#62 = CARTESIAN_POINT('',(1.570798423843,0.E+000));
#63 = VECTOR('',#64,1.);
#64 = DIRECTION('',(0.E+000,-1.));
#65 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#66 = ORIENTED_EDGE('',*,*,#67,.T.);
#67 = EDGE_CURVE('',#35,#68,#70,.T.);
#68 = VERTEX_POINT('',#69);
#69 = CARTESIAN_POINT('',(0.E+000,18.5000011,1.72191934));
#70 = SURFACE_CURVE('',#71,(#75,#82),.PCURVE_S1.);
#71 = LINE('',#72,#73);
#72 = CARTESIAN_POINT('',(0.E+000,1.99998838,1.72191934));
#73 = VECTOR('',#74,1.);
#74 = DIRECTION('',(0.E+000,1.,0.E+000));
#75 = PCURVE('',#43,#76);
#76 = DEFINITIONAL_REPRESENTATION('',(#77),#81);
#77 = LINE('',#78,#79);
#78 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#79 = VECTOR('',#80,1.);
#80 = DIRECTION('',(1.,0.E+000));
#81 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#82 = PCURVE('',#83,#88);
#83 = PLANE('',#84);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(0.E+000,1.99998838,1.72191934));
#86 = DIRECTION('',(0.E+000,0.E+000,1.));
#87 = DIRECTION('',(1.,0.E+000,-0.E+000));
#88 = DEFINITIONAL_REPRESENTATION('',(#89),#93);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(0.E+000,1.));
#93 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#94 = ORIENTED_EDGE('',*,*,#95,.F.);
#95 = EDGE_CURVE('',#96,#68,#98,.T.);
#96 = VERTEX_POINT('',#97);
#97 = CARTESIAN_POINT('',(0.E+000,18.5000011,0.E+000));
#98 = SURFACE_CURVE('',#99,(#103,#110),.PCURVE_S1.);
#99 = LINE('',#100,#101);
#100 = CARTESIAN_POINT('',(0.E+000,18.5000011,0.E+000));
#101 = VECTOR('',#102,1.);
#102 = DIRECTION('',(0.E+000,0.E+000,1.));
#103 = PCURVE('',#43,#104);
#104 = DEFINITIONAL_REPRESENTATION('',(#105),#109);
#105 = LINE('',#106,#107);
#106 = CARTESIAN_POINT('',(16.50001272,0.E+000));
#107 = VECTOR('',#108,1.);
#108 = DIRECTION('',(0.E+000,-1.));
#109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#110 = PCURVE('',#111,#116);
#111 = PLANE('',#112);
#112 = AXIS2_PLACEMENT_3D('',#113,#114,#115);
#113 = CARTESIAN_POINT('',(0.E+000,18.5000011,0.E+000));
#114 = DIRECTION('',(2.853932668759E-008,1.,-0.E+000));
#115 = DIRECTION('',(1.,-2.853932668759E-008,0.E+000));
#116 = DEFINITIONAL_REPRESENTATION('',(#117),#121);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(0.E+000,-1.));
#121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#122 = ORIENTED_EDGE('',*,*,#123,.F.);
#123 = EDGE_CURVE('',#33,#96,#124,.T.);
#124 = SURFACE_CURVE('',#125,(#129,#136),.PCURVE_S1.);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(0.E+000,1.99998838,0.E+000));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(0.E+000,1.,0.E+000));
#129 = PCURVE('',#43,#130);
#130 = DEFINITIONAL_REPRESENTATION('',(#131),#135);
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(1.,0.E+000));
#135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#136 = PCURVE('',#137,#142);
#137 = PLANE('',#138);
#138 = AXIS2_PLACEMENT_3D('',#139,#140,#141);
#139 = CARTESIAN_POINT('',(0.E+000,1.99998838,0.E+000));
#140 = DIRECTION('',(0.E+000,0.E+000,1.));
#141 = DIRECTION('',(1.,0.E+000,-0.E+000));
#142 = DEFINITIONAL_REPRESENTATION('',(#143),#147);
#143 = LINE('',#144,#145);
#144 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#145 = VECTOR('',#146,1.);
#146 = DIRECTION('',(0.E+000,1.));
#147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#148 = ADVANCED_FACE('',(#149),#111,.T.);
#149 = FACE_BOUND('',#150,.T.);
#150 = EDGE_LOOP('',(#151,#152,#175,#203));
#151 = ORIENTED_EDGE('',*,*,#95,.T.);
#152 = ORIENTED_EDGE('',*,*,#153,.T.);
#153 = EDGE_CURVE('',#68,#154,#156,.T.);
#154 = VERTEX_POINT('',#155);
#155 = CARTESIAN_POINT('',(88.99999726,18.49999856,1.72191934));
#156 = SURFACE_CURVE('',#157,(#161,#168),.PCURVE_S1.);
#157 = LINE('',#158,#159);
#158 = CARTESIAN_POINT('',(0.E+000,18.5000011,1.72191934));
#159 = VECTOR('',#160,1.);
#160 = DIRECTION('',(1.,-2.853932668759E-008,0.E+000));
#161 = PCURVE('',#111,#162);
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(1.,0.E+000));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = PCURVE('',#83,#169);
#169 = DEFINITIONAL_REPRESENTATION('',(#170),#174);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(0.E+000,16.50001272));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(1.,-2.853932668759E-008));
#174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#175 = ORIENTED_EDGE('',*,*,#176,.F.);
#176 = EDGE_CURVE('',#177,#154,#179,.T.);
#177 = VERTEX_POINT('',#178);
#178 = CARTESIAN_POINT('',(88.99999726,18.49999856,0.E+000));
#179 = SURFACE_CURVE('',#180,(#184,#191),.PCURVE_S1.);
#180 = LINE('',#181,#182);
#181 = CARTESIAN_POINT('',(88.99999726,18.49999856,0.E+000));
#182 = VECTOR('',#183,1.);
#183 = DIRECTION('',(0.E+000,0.E+000,1.));
#184 = PCURVE('',#111,#185);
#185 = DEFINITIONAL_REPRESENTATION('',(#186),#190);
#186 = LINE('',#187,#188);
#187 = CARTESIAN_POINT('',(88.99999726,0.E+000));
#188 = VECTOR('',#189,1.);
#189 = DIRECTION('',(0.E+000,-1.));
#190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#191 = PCURVE('',#192,#197);
#192 = PLANE('',#193);
#193 = AXIS2_PLACEMENT_3D('',#194,#195,#196);
#194 = CARTESIAN_POINT('',(88.99999726,18.49999856,0.E+000));
#195 = DIRECTION('',(1.,-4.618178972059E-007,0.E+000));
#196 = DIRECTION('',(-4.618178972059E-007,-1.,0.E+000));
#197 = DEFINITIONAL_REPRESENTATION('',(#198),#202);
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(0.E+000,-1.));
#202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#203 = ORIENTED_EDGE('',*,*,#204,.F.);
#204 = EDGE_CURVE('',#96,#177,#205,.T.);
#205 = SURFACE_CURVE('',#206,(#210,#217),.PCURVE_S1.);
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(0.E+000,18.5000011,0.E+000));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(1.,-2.853932668759E-008,0.E+000));
#210 = PCURVE('',#111,#211);
#211 = DEFINITIONAL_REPRESENTATION('',(#212),#216);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(1.,0.E+000));
#216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#217 = PCURVE('',#137,#218);
#218 = DEFINITIONAL_REPRESENTATION('',(#219),#223);
#219 = LINE('',#220,#221);
#220 = CARTESIAN_POINT('',(0.E+000,16.50001272));
#221 = VECTOR('',#222,1.);
#222 = DIRECTION('',(1.,-2.853932668759E-008));
#223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#224 = ADVANCED_FACE('',(#225),#192,.T.);
#225 = FACE_BOUND('',#226,.T.);
#226 = EDGE_LOOP('',(#227,#228,#251,#279));
#227 = ORIENTED_EDGE('',*,*,#176,.T.);
#228 = ORIENTED_EDGE('',*,*,#229,.T.);
#229 = EDGE_CURVE('',#154,#230,#232,.T.);
#230 = VERTEX_POINT('',#231);
#231 = CARTESIAN_POINT('',(88.99998964,1.99998838,1.72191934));
#232 = SURFACE_CURVE('',#233,(#237,#244),.PCURVE_S1.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(88.99999726,18.49999856,1.72191934));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(-4.618178972059E-007,-1.,0.E+000));
#237 = PCURVE('',#192,#238);
#238 = DEFINITIONAL_REPRESENTATION('',(#239),#243);
#239 = LINE('',#240,#241);
#240 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#241 = VECTOR('',#242,1.);
#242 = DIRECTION('',(1.,0.E+000));
#243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#244 = PCURVE('',#83,#245);
#245 = DEFINITIONAL_REPRESENTATION('',(#246),#250);
#246 = LINE('',#247,#248);
#247 = CARTESIAN_POINT('',(88.99999726,16.50001018));
#248 = VECTOR('',#249,1.);
#249 = DIRECTION('',(-4.618178972059E-007,-1.));
#250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#251 = ORIENTED_EDGE('',*,*,#252,.F.);
#252 = EDGE_CURVE('',#253,#230,#255,.T.);
#253 = VERTEX_POINT('',#254);
#254 = CARTESIAN_POINT('',(88.99998964,1.99998838,0.E+000));
#255 = SURFACE_CURVE('',#256,(#260,#267),.PCURVE_S1.);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(88.99998964,1.99998838,0.E+000));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(0.E+000,0.E+000,1.));
#260 = PCURVE('',#192,#261);
#261 = DEFINITIONAL_REPRESENTATION('',(#262),#266);
#262 = LINE('',#263,#264);
#263 = CARTESIAN_POINT('',(16.500010180002,0.E+000));
#264 = VECTOR('',#265,1.);
#265 = DIRECTION('',(0.E+000,-1.));
#266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#267 = PCURVE('',#268,#273);
#268 = CYLINDRICAL_SURFACE('',#269,1.999986282944);
#269 = AXIS2_PLACEMENT_3D('',#270,#271,#272);
#270 = CARTESIAN_POINT('',(87.000003357062,1.999983742943,0.E+000));
#271 = DIRECTION('',(0.E+000,0.E+000,-1.));
#272 = DIRECTION('',(0.999999999997,2.318544638804E-006,0.E+000));
#273 = DEFINITIONAL_REPRESENTATION('',(#274),#278);
#274 = LINE('',#275,#276);
#275 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#276 = VECTOR('',#277,1.);
#277 = DIRECTION('',(0.E+000,-1.));
#278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#279 = ORIENTED_EDGE('',*,*,#280,.F.);
#280 = EDGE_CURVE('',#177,#253,#281,.T.);
#281 = SURFACE_CURVE('',#282,(#286,#293),.PCURVE_S1.);
#282 = LINE('',#283,#284);
#283 = CARTESIAN_POINT('',(88.99999726,18.49999856,0.E+000));
#284 = VECTOR('',#285,1.);
#285 = DIRECTION('',(-4.618178972059E-007,-1.,0.E+000));
#286 = PCURVE('',#192,#287);
#287 = DEFINITIONAL_REPRESENTATION('',(#288),#292);
#288 = LINE('',#289,#290);
#289 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#290 = VECTOR('',#291,1.);
#291 = DIRECTION('',(1.,0.E+000));
#292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#293 = PCURVE('',#137,#294);
#294 = DEFINITIONAL_REPRESENTATION('',(#295),#299);
#295 = LINE('',#296,#297);
#296 = CARTESIAN_POINT('',(88.99999726,16.50001018));
#297 = VECTOR('',#298,1.);
#298 = DIRECTION('',(-4.618178972059E-007,-1.));
#299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#300 = ADVANCED_FACE('',(#301),#268,.T.);
#301 = FACE_BOUND('',#302,.T.);
#302 = EDGE_LOOP('',(#303,#304,#332,#360));
#303 = ORIENTED_EDGE('',*,*,#252,.T.);
#304 = ORIENTED_EDGE('',*,*,#305,.T.);
#305 = EDGE_CURVE('',#230,#306,#308,.T.);
#306 = VERTEX_POINT('',#307);
#307 = CARTESIAN_POINT('',(87.00000126,-2.539999996592E-006,1.72191934)
  );
#308 = SURFACE_CURVE('',#309,(#314,#321),.PCURVE_S1.);
#309 = CIRCLE('',#310,1.999986282944);
#310 = AXIS2_PLACEMENT_3D('',#311,#312,#313);
#311 = CARTESIAN_POINT('',(87.000003357062,1.999983742943,1.72191934));
#312 = DIRECTION('',(0.E+000,0.E+000,-1.));
#313 = DIRECTION('',(0.999999999997,2.318544638804E-006,0.E+000));
#314 = PCURVE('',#268,#315);
#315 = DEFINITIONAL_REPRESENTATION('',(#316),#320);
#316 = LINE('',#317,#318);
#317 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#318 = VECTOR('',#319,1.);
#319 = DIRECTION('',(1.,0.E+000));
#320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#321 = PCURVE('',#83,#322);
#322 = DEFINITIONAL_REPRESENTATION('',(#323),#331);
#323 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#324,#325,#326,#327,#328,#329
,#330),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#324 = CARTESIAN_POINT('',(88.99998964,0.E+000));
#325 = CARTESIAN_POINT('',(88.999997671619,-3.46407785649));
#326 = CARTESIAN_POINT('',(86.000014231402,-1.732045883831));
#327 = CARTESIAN_POINT('',(83.000030791185,-1.391117242249E-005));
#328 = CARTESIAN_POINT('',(86.000006199783,1.732031972659));
#329 = CARTESIAN_POINT('',(88.999981608381,3.46407785649));
#330 = CARTESIAN_POINT('',(88.99998964,0.E+000));
#331 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#332 = ORIENTED_EDGE('',*,*,#333,.F.);
#333 = EDGE_CURVE('',#334,#306,#336,.T.);
#334 = VERTEX_POINT('',#335);
#335 = CARTESIAN_POINT('',(87.00000126,-2.539999996592E-006,0.E+000));
#336 = SURFACE_CURVE('',#337,(#341,#348),.PCURVE_S1.);
#337 = LINE('',#338,#339);
#338 = CARTESIAN_POINT('',(87.00000126,-2.539999996592E-006,0.E+000));
#339 = VECTOR('',#340,1.);
#340 = DIRECTION('',(0.E+000,0.E+000,1.));
#341 = PCURVE('',#268,#342);
#342 = DEFINITIONAL_REPRESENTATION('',(#343),#347);
#343 = LINE('',#344,#345);
#344 = CARTESIAN_POINT('',(1.570799693878,0.E+000));
#345 = VECTOR('',#346,1.);
#346 = DIRECTION('',(0.E+000,-1.));
#347 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#348 = PCURVE('',#349,#354);
#349 = PLANE('',#350);
#350 = AXIS2_PLACEMENT_3D('',#351,#352,#353);
#351 = CARTESIAN_POINT('',(87.00000126,-2.54E-006,0.E+000));
#352 = DIRECTION('',(-2.988234841312E-008,-1.,0.E+000));
#353 = DIRECTION('',(-1.,2.988234841312E-008,0.E+000));
#354 = DEFINITIONAL_REPRESENTATION('',(#355),#359);
#355 = LINE('',#356,#357);
#356 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#357 = VECTOR('',#358,1.);
#358 = DIRECTION('',(0.E+000,-1.));
#359 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#360 = ORIENTED_EDGE('',*,*,#361,.F.);
#361 = EDGE_CURVE('',#253,#334,#362,.T.);
#362 = SURFACE_CURVE('',#363,(#368,#375),.PCURVE_S1.);
#363 = CIRCLE('',#364,1.999986282944);
#364 = AXIS2_PLACEMENT_3D('',#365,#366,#367);
#365 = CARTESIAN_POINT('',(87.000003357062,1.999983742943,0.E+000));
#366 = DIRECTION('',(0.E+000,0.E+000,-1.));
#367 = DIRECTION('',(0.999999999997,2.318544638804E-006,0.E+000));
#368 = PCURVE('',#268,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(1.,0.E+000));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = PCURVE('',#137,#376);
#376 = DEFINITIONAL_REPRESENTATION('',(#377),#385);
#377 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#378,#379,#380,#381,#382,#383
,#384),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#378 = CARTESIAN_POINT('',(88.99998964,0.E+000));
#379 = CARTESIAN_POINT('',(88.999997671619,-3.46407785649));
#380 = CARTESIAN_POINT('',(86.000014231402,-1.732045883831));
#381 = CARTESIAN_POINT('',(83.000030791185,-1.391117242249E-005));
#382 = CARTESIAN_POINT('',(86.000006199783,1.732031972659));
#383 = CARTESIAN_POINT('',(88.999981608381,3.46407785649));
#384 = CARTESIAN_POINT('',(88.99998964,0.E+000));
#385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#386 = ADVANCED_FACE('',(#387),#349,.T.);
#387 = FACE_BOUND('',#388,.T.);
#388 = EDGE_LOOP('',(#389,#390,#413,#436));
#389 = ORIENTED_EDGE('',*,*,#333,.T.);
#390 = ORIENTED_EDGE('',*,*,#391,.T.);
#391 = EDGE_CURVE('',#306,#392,#394,.T.);
#392 = VERTEX_POINT('',#393);
#393 = CARTESIAN_POINT('',(1.99998838,-3.794707603699E-018,1.72191934));
#394 = SURFACE_CURVE('',#395,(#399,#406),.PCURVE_S1.);
#395 = LINE('',#396,#397);
#396 = CARTESIAN_POINT('',(87.00000126,-2.54E-006,1.72191934));
#397 = VECTOR('',#398,1.);
#398 = DIRECTION('',(-1.,2.988234841312E-008,0.E+000));
#399 = PCURVE('',#349,#400);
#400 = DEFINITIONAL_REPRESENTATION('',(#401),#405);
#401 = LINE('',#402,#403);
#402 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#403 = VECTOR('',#404,1.);
#404 = DIRECTION('',(1.,0.E+000));
#405 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#406 = PCURVE('',#83,#407);
#407 = DEFINITIONAL_REPRESENTATION('',(#408),#412);
#408 = LINE('',#409,#410);
#409 = CARTESIAN_POINT('',(87.00000126,-1.99999092));
#410 = VECTOR('',#411,1.);
#411 = DIRECTION('',(-1.,2.988234841312E-008));
#412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#413 = ORIENTED_EDGE('',*,*,#414,.F.);
#414 = EDGE_CURVE('',#415,#392,#417,.T.);
#415 = VERTEX_POINT('',#416);
#416 = CARTESIAN_POINT('',(1.99998838,-3.794707603699E-018,0.E+000));
#417 = SURFACE_CURVE('',#418,(#422,#429),.PCURVE_S1.);
#418 = LINE('',#419,#420);
#419 = CARTESIAN_POINT('',(1.99998838,-3.794707603699E-018,0.E+000));
#420 = VECTOR('',#421,1.);
#421 = DIRECTION('',(0.E+000,0.E+000,1.));
#422 = PCURVE('',#349,#423);
#423 = DEFINITIONAL_REPRESENTATION('',(#424),#428);
#424 = LINE('',#425,#426);
#425 = CARTESIAN_POINT('',(85.00001288,0.E+000));
#426 = VECTOR('',#427,1.);
#427 = DIRECTION('',(0.E+000,-1.));
#428 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#429 = PCURVE('',#55,#430);
#430 = DEFINITIONAL_REPRESENTATION('',(#431),#435);
#431 = LINE('',#432,#433);
#432 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#433 = VECTOR('',#434,1.);
#434 = DIRECTION('',(0.E+000,-1.));
#435 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#436 = ORIENTED_EDGE('',*,*,#437,.F.);
#437 = EDGE_CURVE('',#334,#415,#438,.T.);
#438 = SURFACE_CURVE('',#439,(#443,#450),.PCURVE_S1.);
#439 = LINE('',#440,#441);
#440 = CARTESIAN_POINT('',(87.00000126,-2.54E-006,0.E+000));
#441 = VECTOR('',#442,1.);
#442 = DIRECTION('',(-1.,2.988234841312E-008,0.E+000));
#443 = PCURVE('',#349,#444);
#444 = DEFINITIONAL_REPRESENTATION('',(#445),#449);
#445 = LINE('',#446,#447);
#446 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#447 = VECTOR('',#448,1.);
#448 = DIRECTION('',(1.,0.E+000));
#449 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#450 = PCURVE('',#137,#451);
#451 = DEFINITIONAL_REPRESENTATION('',(#452),#456);
#452 = LINE('',#453,#454);
#453 = CARTESIAN_POINT('',(87.00000126,-1.99999092));
#454 = VECTOR('',#455,1.);
#455 = DIRECTION('',(-1.,2.988234841312E-008));
#456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#457 = ADVANCED_FACE('',(#458),#55,.T.);
#458 = FACE_BOUND('',#459,.T.);
#459 = EDGE_LOOP('',(#460,#461,#487,#488));
#460 = ORIENTED_EDGE('',*,*,#414,.T.);
#461 = ORIENTED_EDGE('',*,*,#462,.T.);
#462 = EDGE_CURVE('',#392,#35,#463,.T.);
#463 = SURFACE_CURVE('',#464,(#469,#476),.PCURVE_S1.);
#464 = CIRCLE('',#465,1.999986282967);
#465 = AXIS2_PLACEMENT_3D('',#466,#467,#468);
#466 = CARTESIAN_POINT('',(1.999986282966,1.999986282966,1.72191934));
#467 = DIRECTION('',(0.E+000,0.E+000,-1.));
#468 = DIRECTION('',(1.048523997587E-006,-0.999999999999,0.E+000));
#469 = PCURVE('',#55,#470);
#470 = DEFINITIONAL_REPRESENTATION('',(#471),#475);
#471 = LINE('',#472,#473);
#472 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#473 = VECTOR('',#474,1.);
#474 = DIRECTION('',(1.,0.E+000));
#475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#476 = PCURVE('',#83,#477);
#477 = DEFINITIONAL_REPRESENTATION('',(#478),#486);
#478 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#479,#480,#481,#482,#483,#484
,#485),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#479 = CARTESIAN_POINT('',(1.99998838,-1.99998838));
#480 = CARTESIAN_POINT('',(-1.464089476539,-1.999992012169));
#481 = CARTESIAN_POINT('',(0.26794630618,0.999989228365));
#482 = CARTESIAN_POINT('',(1.999982088899,3.999970468899));
#483 = CARTESIAN_POINT('',(3.732024162719,0.999992860534));
#484 = CARTESIAN_POINT('',(5.464066236539,-1.999984747831));
#485 = CARTESIAN_POINT('',(1.99998838,-1.99998838));
#486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#487 = ORIENTED_EDGE('',*,*,#32,.F.);
#488 = ORIENTED_EDGE('',*,*,#489,.F.);
#489 = EDGE_CURVE('',#415,#33,#490,.T.);
#490 = SURFACE_CURVE('',#491,(#496,#503),.PCURVE_S1.);
#491 = CIRCLE('',#492,1.999986282967);
#492 = AXIS2_PLACEMENT_3D('',#493,#494,#495);
#493 = CARTESIAN_POINT('',(1.999986282966,1.999986282966,0.E+000));
#494 = DIRECTION('',(0.E+000,0.E+000,-1.));
#495 = DIRECTION('',(1.048523997587E-006,-0.999999999999,0.E+000));
#496 = PCURVE('',#55,#497);
#497 = DEFINITIONAL_REPRESENTATION('',(#498),#502);
#498 = LINE('',#499,#500);
#499 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#500 = VECTOR('',#501,1.);
#501 = DIRECTION('',(1.,0.E+000));
#502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#503 = PCURVE('',#137,#504);
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#513);
#505 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#506,#507,#508,#509,#510,#511
,#512),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#506 = CARTESIAN_POINT('',(1.99998838,-1.99998838));
#507 = CARTESIAN_POINT('',(-1.464089476539,-1.999992012169));
#508 = CARTESIAN_POINT('',(0.26794630618,0.999989228365));
#509 = CARTESIAN_POINT('',(1.999982088899,3.999970468899));
#510 = CARTESIAN_POINT('',(3.732024162719,0.999992860534));
#511 = CARTESIAN_POINT('',(5.464066236539,-1.999984747831));
#512 = CARTESIAN_POINT('',(1.99998838,-1.99998838));
#513 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#514 = ADVANCED_FACE('',(#515),#529,.T.);
#515 = FACE_BOUND('',#516,.F.);
#516 = EDGE_LOOP('',(#517,#547,#569,#570));
#517 = ORIENTED_EDGE('',*,*,#518,.T.);
#518 = EDGE_CURVE('',#519,#521,#523,.T.);
#519 = VERTEX_POINT('',#520);
#520 = CARTESIAN_POINT('',(6.1000005,3.50000062,0.E+000));
#521 = VERTEX_POINT('',#522);
#522 = CARTESIAN_POINT('',(6.1000005,3.50000062,1.72191934));
#523 = SEAM_CURVE('',#524,(#528,#540),.PCURVE_S1.);
#524 = LINE('',#525,#526);
#525 = CARTESIAN_POINT('',(6.1000005,3.50000062,0.E+000));
#526 = VECTOR('',#527,1.);
#527 = DIRECTION('',(0.E+000,0.E+000,1.));
#528 = PCURVE('',#529,#534);
#529 = CYLINDRICAL_SURFACE('',#530,1.59999934);
#530 = AXIS2_PLACEMENT_3D('',#531,#532,#533);
#531 = CARTESIAN_POINT('',(4.50000116,3.50000062,0.E+000));
#532 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#533 = DIRECTION('',(1.,0.E+000,-0.E+000));
#534 = DEFINITIONAL_REPRESENTATION('',(#535),#539);
#535 = LINE('',#536,#537);
#536 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#537 = VECTOR('',#538,1.);
#538 = DIRECTION('',(-0.E+000,-1.));
#539 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#540 = PCURVE('',#529,#541);
#541 = DEFINITIONAL_REPRESENTATION('',(#542),#546);
#542 = LINE('',#543,#544);
#543 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#544 = VECTOR('',#545,1.);
#545 = DIRECTION('',(-0.E+000,-1.));
#546 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#547 = ORIENTED_EDGE('',*,*,#548,.T.);
#548 = EDGE_CURVE('',#521,#521,#549,.T.);
#549 = SURFACE_CURVE('',#550,(#555,#562),.PCURVE_S1.);
#550 = CIRCLE('',#551,1.59999934);
#551 = AXIS2_PLACEMENT_3D('',#552,#553,#554);
#552 = CARTESIAN_POINT('',(4.50000116,3.50000062,1.72191934));
#553 = DIRECTION('',(0.E+000,0.E+000,1.));
#554 = DIRECTION('',(1.,0.E+000,-0.E+000));
#555 = PCURVE('',#529,#556);
#556 = DEFINITIONAL_REPRESENTATION('',(#557),#561);
#557 = LINE('',#558,#559);
#558 = CARTESIAN_POINT('',(-0.E+000,-1.72191934));
#559 = VECTOR('',#560,1.);
#560 = DIRECTION('',(-1.,0.E+000));
#561 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#562 = PCURVE('',#83,#563);
#563 = DEFINITIONAL_REPRESENTATION('',(#564),#568);
#564 = CIRCLE('',#565,1.59999934);
#565 = AXIS2_PLACEMENT_2D('',#566,#567);
#566 = CARTESIAN_POINT('',(4.50000116,1.50001224));
#567 = DIRECTION('',(1.,0.E+000));
#568 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#569 = ORIENTED_EDGE('',*,*,#518,.F.);
#570 = ORIENTED_EDGE('',*,*,#571,.F.);
#571 = EDGE_CURVE('',#519,#519,#572,.T.);
#572 = SURFACE_CURVE('',#573,(#578,#585),.PCURVE_S1.);
#573 = CIRCLE('',#574,1.59999934);
#574 = AXIS2_PLACEMENT_3D('',#575,#576,#577);
#575 = CARTESIAN_POINT('',(4.50000116,3.50000062,0.E+000));
#576 = DIRECTION('',(0.E+000,0.E+000,1.));
#577 = DIRECTION('',(1.,0.E+000,-0.E+000));
#578 = PCURVE('',#529,#579);
#579 = DEFINITIONAL_REPRESENTATION('',(#580),#584);
#580 = LINE('',#581,#582);
#581 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#582 = VECTOR('',#583,1.);
#583 = DIRECTION('',(-1.,0.E+000));
#584 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#585 = PCURVE('',#137,#586);
#586 = DEFINITIONAL_REPRESENTATION('',(#587),#591);
#587 = CIRCLE('',#588,1.59999934);
#588 = AXIS2_PLACEMENT_2D('',#589,#590);
#589 = CARTESIAN_POINT('',(4.50000116,1.50001224));
#590 = DIRECTION('',(1.,0.E+000));
#591 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#592 = ADVANCED_FACE('',(#593),#607,.T.);
#593 = FACE_BOUND('',#594,.F.);
#594 = EDGE_LOOP('',(#595,#625,#647,#648));
#595 = ORIENTED_EDGE('',*,*,#596,.T.);
#596 = EDGE_CURVE('',#597,#599,#601,.T.);
#597 = VERTEX_POINT('',#598);
#598 = CARTESIAN_POINT('',(26.84999964,11.14999802,0.E+000));
#599 = VERTEX_POINT('',#600);
#600 = CARTESIAN_POINT('',(26.84999964,11.14999802,1.72191934));
#601 = SEAM_CURVE('',#602,(#606,#618),.PCURVE_S1.);
#602 = LINE('',#603,#604);
#603 = CARTESIAN_POINT('',(26.84999964,11.14999802,0.E+000));
#604 = VECTOR('',#605,1.);
#605 = DIRECTION('',(0.E+000,0.E+000,1.));
#606 = PCURVE('',#607,#612);
#607 = CYLINDRICAL_SURFACE('',#608,3.34999838);
#608 = AXIS2_PLACEMENT_3D('',#609,#610,#611);
#609 = CARTESIAN_POINT('',(23.50000126,11.14999802,0.E+000));
#610 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#611 = DIRECTION('',(1.,0.E+000,-0.E+000));
#612 = DEFINITIONAL_REPRESENTATION('',(#613),#617);
#613 = LINE('',#614,#615);
#614 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#615 = VECTOR('',#616,1.);
#616 = DIRECTION('',(-0.E+000,-1.));
#617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#618 = PCURVE('',#607,#619);
#619 = DEFINITIONAL_REPRESENTATION('',(#620),#624);
#620 = LINE('',#621,#622);
#621 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#622 = VECTOR('',#623,1.);
#623 = DIRECTION('',(-0.E+000,-1.));
#624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#625 = ORIENTED_EDGE('',*,*,#626,.T.);
#626 = EDGE_CURVE('',#599,#599,#627,.T.);
#627 = SURFACE_CURVE('',#628,(#633,#640),.PCURVE_S1.);
#628 = CIRCLE('',#629,3.34999838);
#629 = AXIS2_PLACEMENT_3D('',#630,#631,#632);
#630 = CARTESIAN_POINT('',(23.50000126,11.14999802,1.72191934));
#631 = DIRECTION('',(0.E+000,0.E+000,1.));
#632 = DIRECTION('',(1.,0.E+000,-0.E+000));
#633 = PCURVE('',#607,#634);
#634 = DEFINITIONAL_REPRESENTATION('',(#635),#639);
#635 = LINE('',#636,#637);
#636 = CARTESIAN_POINT('',(-0.E+000,-1.72191934));
#637 = VECTOR('',#638,1.);
#638 = DIRECTION('',(-1.,0.E+000));
#639 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#640 = PCURVE('',#83,#641);
#641 = DEFINITIONAL_REPRESENTATION('',(#642),#646);
#642 = CIRCLE('',#643,3.34999838);
#643 = AXIS2_PLACEMENT_2D('',#644,#645);
#644 = CARTESIAN_POINT('',(23.50000126,9.15000964));
#645 = DIRECTION('',(1.,0.E+000));
#646 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#647 = ORIENTED_EDGE('',*,*,#596,.F.);
#648 = ORIENTED_EDGE('',*,*,#649,.F.);
#649 = EDGE_CURVE('',#597,#597,#650,.T.);
#650 = SURFACE_CURVE('',#651,(#656,#663),.PCURVE_S1.);
#651 = CIRCLE('',#652,3.34999838);
#652 = AXIS2_PLACEMENT_3D('',#653,#654,#655);
#653 = CARTESIAN_POINT('',(23.50000126,11.14999802,0.E+000));
#654 = DIRECTION('',(0.E+000,0.E+000,1.));
#655 = DIRECTION('',(1.,0.E+000,-0.E+000));
#656 = PCURVE('',#607,#657);
#657 = DEFINITIONAL_REPRESENTATION('',(#658),#662);
#658 = LINE('',#659,#660);
#659 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#660 = VECTOR('',#661,1.);
#661 = DIRECTION('',(-1.,0.E+000));
#662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#663 = PCURVE('',#137,#664);
#664 = DEFINITIONAL_REPRESENTATION('',(#665),#669);
#665 = CIRCLE('',#666,3.34999838);
#666 = AXIS2_PLACEMENT_2D('',#667,#668);
#667 = CARTESIAN_POINT('',(23.50000126,9.15000964));
#668 = DIRECTION('',(1.,0.E+000));
#669 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#670 = ADVANCED_FACE('',(#671),#685,.T.);
#671 = FACE_BOUND('',#672,.F.);
#672 = EDGE_LOOP('',(#673,#703,#725,#726));
#673 = ORIENTED_EDGE('',*,*,#674,.T.);
#674 = EDGE_CURVE('',#675,#677,#679,.T.);
#675 = VERTEX_POINT('',#676);
#676 = CARTESIAN_POINT('',(6.1000005,16.00000102,0.E+000));
#677 = VERTEX_POINT('',#678);
#678 = CARTESIAN_POINT('',(6.1000005,16.00000102,1.72191934));
#679 = SEAM_CURVE('',#680,(#684,#696),.PCURVE_S1.);
#680 = LINE('',#681,#682);
#681 = CARTESIAN_POINT('',(6.1000005,16.00000102,0.E+000));
#682 = VECTOR('',#683,1.);
#683 = DIRECTION('',(0.E+000,0.E+000,1.));
#684 = PCURVE('',#685,#690);
#685 = CYLINDRICAL_SURFACE('',#686,1.59999934);
#686 = AXIS2_PLACEMENT_3D('',#687,#688,#689);
#687 = CARTESIAN_POINT('',(4.50000116,16.00000102,0.E+000));
#688 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#689 = DIRECTION('',(1.,0.E+000,-0.E+000));
#690 = DEFINITIONAL_REPRESENTATION('',(#691),#695);
#691 = LINE('',#692,#693);
#692 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#693 = VECTOR('',#694,1.);
#694 = DIRECTION('',(-0.E+000,-1.));
#695 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#696 = PCURVE('',#685,#697);
#697 = DEFINITIONAL_REPRESENTATION('',(#698),#702);
#698 = LINE('',#699,#700);
#699 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#700 = VECTOR('',#701,1.);
#701 = DIRECTION('',(-0.E+000,-1.));
#702 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#703 = ORIENTED_EDGE('',*,*,#704,.T.);
#704 = EDGE_CURVE('',#677,#677,#705,.T.);
#705 = SURFACE_CURVE('',#706,(#711,#718),.PCURVE_S1.);
#706 = CIRCLE('',#707,1.59999934);
#707 = AXIS2_PLACEMENT_3D('',#708,#709,#710);
#708 = CARTESIAN_POINT('',(4.50000116,16.00000102,1.72191934));
#709 = DIRECTION('',(0.E+000,0.E+000,1.));
#710 = DIRECTION('',(1.,0.E+000,-0.E+000));
#711 = PCURVE('',#685,#712);
#712 = DEFINITIONAL_REPRESENTATION('',(#713),#717);
#713 = LINE('',#714,#715);
#714 = CARTESIAN_POINT('',(-0.E+000,-1.72191934));
#715 = VECTOR('',#716,1.);
#716 = DIRECTION('',(-1.,0.E+000));
#717 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#718 = PCURVE('',#83,#719);
#719 = DEFINITIONAL_REPRESENTATION('',(#720),#724);
#720 = CIRCLE('',#721,1.59999934);
#721 = AXIS2_PLACEMENT_2D('',#722,#723);
#722 = CARTESIAN_POINT('',(4.50000116,14.00001264));
#723 = DIRECTION('',(1.,0.E+000));
#724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#725 = ORIENTED_EDGE('',*,*,#674,.F.);
#726 = ORIENTED_EDGE('',*,*,#727,.F.);
#727 = EDGE_CURVE('',#675,#675,#728,.T.);
#728 = SURFACE_CURVE('',#729,(#734,#741),.PCURVE_S1.);
#729 = CIRCLE('',#730,1.59999934);
#730 = AXIS2_PLACEMENT_3D('',#731,#732,#733);
#731 = CARTESIAN_POINT('',(4.50000116,16.00000102,0.E+000));
#732 = DIRECTION('',(0.E+000,0.E+000,1.));
#733 = DIRECTION('',(1.,0.E+000,-0.E+000));
#734 = PCURVE('',#685,#735);
#735 = DEFINITIONAL_REPRESENTATION('',(#736),#740);
#736 = LINE('',#737,#738);
#737 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#738 = VECTOR('',#739,1.);
#739 = DIRECTION('',(-1.,0.E+000));
#740 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#741 = PCURVE('',#137,#742);
#742 = DEFINITIONAL_REPRESENTATION('',(#743),#747);
#743 = CIRCLE('',#744,1.59999934);
#744 = AXIS2_PLACEMENT_2D('',#745,#746);
#745 = CARTESIAN_POINT('',(4.50000116,14.00001264));
#746 = DIRECTION('',(1.,0.E+000));
#747 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#748 = ADVANCED_FACE('',(#749),#763,.T.);
#749 = FACE_BOUND('',#750,.F.);
#750 = EDGE_LOOP('',(#751,#781,#803,#804));
#751 = ORIENTED_EDGE('',*,*,#752,.T.);
#752 = EDGE_CURVE('',#753,#755,#757,.T.);
#753 = VERTEX_POINT('',#754);
#754 = CARTESIAN_POINT('',(14.74999844,11.24999782,0.E+000));
#755 = VERTEX_POINT('',#756);
#756 = CARTESIAN_POINT('',(14.74999844,11.24999782,1.72191934));
#757 = SEAM_CURVE('',#758,(#762,#774),.PCURVE_S1.);
#758 = LINE('',#759,#760);
#759 = CARTESIAN_POINT('',(14.74999844,11.24999782,0.E+000));
#760 = VECTOR('',#761,1.);
#761 = DIRECTION('',(0.E+000,0.E+000,1.));
#762 = PCURVE('',#763,#768);
#763 = CYLINDRICAL_SURFACE('',#764,1.74999904);
#764 = AXIS2_PLACEMENT_3D('',#765,#766,#767);
#765 = CARTESIAN_POINT('',(12.9999994,11.24999782,0.E+000));
#766 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#767 = DIRECTION('',(1.,0.E+000,-0.E+000));
#768 = DEFINITIONAL_REPRESENTATION('',(#769),#773);
#769 = LINE('',#770,#771);
#770 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#771 = VECTOR('',#772,1.);
#772 = DIRECTION('',(-0.E+000,-1.));
#773 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#774 = PCURVE('',#763,#775);
#775 = DEFINITIONAL_REPRESENTATION('',(#776),#780);
#776 = LINE('',#777,#778);
#777 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#778 = VECTOR('',#779,1.);
#779 = DIRECTION('',(-0.E+000,-1.));
#780 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#781 = ORIENTED_EDGE('',*,*,#782,.T.);
#782 = EDGE_CURVE('',#755,#755,#783,.T.);
#783 = SURFACE_CURVE('',#784,(#789,#796),.PCURVE_S1.);
#784 = CIRCLE('',#785,1.74999904);
#785 = AXIS2_PLACEMENT_3D('',#786,#787,#788);
#786 = CARTESIAN_POINT('',(12.9999994,11.24999782,1.72191934));
#787 = DIRECTION('',(0.E+000,0.E+000,1.));
#788 = DIRECTION('',(1.,0.E+000,-0.E+000));
#789 = PCURVE('',#763,#790);
#790 = DEFINITIONAL_REPRESENTATION('',(#791),#795);
#791 = LINE('',#792,#793);
#792 = CARTESIAN_POINT('',(-0.E+000,-1.72191934));
#793 = VECTOR('',#794,1.);
#794 = DIRECTION('',(-1.,0.E+000));
#795 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#796 = PCURVE('',#83,#797);
#797 = DEFINITIONAL_REPRESENTATION('',(#798),#802);
#798 = CIRCLE('',#799,1.74999904);
#799 = AXIS2_PLACEMENT_2D('',#800,#801);
#800 = CARTESIAN_POINT('',(12.9999994,9.25000944));
#801 = DIRECTION('',(1.,0.E+000));
#802 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#803 = ORIENTED_EDGE('',*,*,#752,.F.);
#804 = ORIENTED_EDGE('',*,*,#805,.F.);
#805 = EDGE_CURVE('',#753,#753,#806,.T.);
#806 = SURFACE_CURVE('',#807,(#812,#819),.PCURVE_S1.);
#807 = CIRCLE('',#808,1.74999904);
#808 = AXIS2_PLACEMENT_3D('',#809,#810,#811);
#809 = CARTESIAN_POINT('',(12.9999994,11.24999782,0.E+000));
#810 = DIRECTION('',(0.E+000,0.E+000,1.));
#811 = DIRECTION('',(1.,0.E+000,-0.E+000));
#812 = PCURVE('',#763,#813);
#813 = DEFINITIONAL_REPRESENTATION('',(#814),#818);
#814 = LINE('',#815,#816);
#815 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#816 = VECTOR('',#817,1.);
#817 = DIRECTION('',(-1.,0.E+000));
#818 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#819 = PCURVE('',#137,#820);
#820 = DEFINITIONAL_REPRESENTATION('',(#821),#825);
#821 = CIRCLE('',#822,1.74999904);
#822 = AXIS2_PLACEMENT_2D('',#823,#824);
#823 = CARTESIAN_POINT('',(12.9999994,9.25000944));
#824 = DIRECTION('',(1.,0.E+000));
#825 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#826 = ADVANCED_FACE('',(#827),#841,.T.);
#827 = FACE_BOUND('',#828,.F.);
#828 = EDGE_LOOP('',(#829,#859,#881,#882));
#829 = ORIENTED_EDGE('',*,*,#830,.T.);
#830 = EDGE_CURVE('',#831,#833,#835,.T.);
#831 = VERTEX_POINT('',#832);
#832 = CARTESIAN_POINT('',(14.74999844,15.30999732,0.E+000));
#833 = VERTEX_POINT('',#834);
#834 = CARTESIAN_POINT('',(14.74999844,15.30999732,1.72191934));
#835 = SEAM_CURVE('',#836,(#840,#852),.PCURVE_S1.);
#836 = LINE('',#837,#838);
#837 = CARTESIAN_POINT('',(14.74999844,15.30999732,0.E+000));
#838 = VECTOR('',#839,1.);
#839 = DIRECTION('',(0.E+000,0.E+000,1.));
#840 = PCURVE('',#841,#846);
#841 = CYLINDRICAL_SURFACE('',#842,1.74999904);
#842 = AXIS2_PLACEMENT_3D('',#843,#844,#845);
#843 = CARTESIAN_POINT('',(12.9999994,15.30999732,0.E+000));
#844 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#845 = DIRECTION('',(1.,0.E+000,-0.E+000));
#846 = DEFINITIONAL_REPRESENTATION('',(#847),#851);
#847 = LINE('',#848,#849);
#848 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#849 = VECTOR('',#850,1.);
#850 = DIRECTION('',(-0.E+000,-1.));
#851 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#852 = PCURVE('',#841,#853);
#853 = DEFINITIONAL_REPRESENTATION('',(#854),#858);
#854 = LINE('',#855,#856);
#855 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#856 = VECTOR('',#857,1.);
#857 = DIRECTION('',(-0.E+000,-1.));
#858 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#859 = ORIENTED_EDGE('',*,*,#860,.T.);
#860 = EDGE_CURVE('',#833,#833,#861,.T.);
#861 = SURFACE_CURVE('',#862,(#867,#874),.PCURVE_S1.);
#862 = CIRCLE('',#863,1.74999904);
#863 = AXIS2_PLACEMENT_3D('',#864,#865,#866);
#864 = CARTESIAN_POINT('',(12.9999994,15.30999732,1.72191934));
#865 = DIRECTION('',(0.E+000,0.E+000,1.));
#866 = DIRECTION('',(1.,0.E+000,-0.E+000));
#867 = PCURVE('',#841,#868);
#868 = DEFINITIONAL_REPRESENTATION('',(#869),#873);
#869 = LINE('',#870,#871);
#870 = CARTESIAN_POINT('',(-0.E+000,-1.72191934));
#871 = VECTOR('',#872,1.);
#872 = DIRECTION('',(-1.,0.E+000));
#873 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#874 = PCURVE('',#83,#875);
#875 = DEFINITIONAL_REPRESENTATION('',(#876),#880);
#876 = CIRCLE('',#877,1.74999904);
#877 = AXIS2_PLACEMENT_2D('',#878,#879);
#878 = CARTESIAN_POINT('',(12.9999994,13.31000894));
#879 = DIRECTION('',(1.,0.E+000));
#880 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#881 = ORIENTED_EDGE('',*,*,#830,.F.);
#882 = ORIENTED_EDGE('',*,*,#883,.F.);
#883 = EDGE_CURVE('',#831,#831,#884,.T.);
#884 = SURFACE_CURVE('',#885,(#890,#897),.PCURVE_S1.);
#885 = CIRCLE('',#886,1.74999904);
#886 = AXIS2_PLACEMENT_3D('',#887,#888,#889);
#887 = CARTESIAN_POINT('',(12.9999994,15.30999732,0.E+000));
#888 = DIRECTION('',(0.E+000,0.E+000,1.));
#889 = DIRECTION('',(1.,0.E+000,-0.E+000));
#890 = PCURVE('',#841,#891);
#891 = DEFINITIONAL_REPRESENTATION('',(#892),#896);
#892 = LINE('',#893,#894);
#893 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#894 = VECTOR('',#895,1.);
#895 = DIRECTION('',(-1.,0.E+000));
#896 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#897 = PCURVE('',#137,#898);
#898 = DEFINITIONAL_REPRESENTATION('',(#899),#903);
#899 = CIRCLE('',#900,1.74999904);
#900 = AXIS2_PLACEMENT_2D('',#901,#902);
#901 = CARTESIAN_POINT('',(12.9999994,13.31000894));
#902 = DIRECTION('',(1.,0.E+000));
#903 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#904 = ADVANCED_FACE('',(#905),#919,.F.);
#905 = FACE_BOUND('',#906,.F.);
#906 = EDGE_LOOP('',(#907,#942,#965,#993));
#907 = ORIENTED_EDGE('',*,*,#908,.T.);
#908 = EDGE_CURVE('',#909,#911,#913,.T.);
#909 = VERTEX_POINT('',#910);
#910 = CARTESIAN_POINT('',(49.9999889,16.24998528,0.E+000));
#911 = VERTEX_POINT('',#912);
#912 = CARTESIAN_POINT('',(49.9999889,16.24998528,1.72191934));
#913 = SURFACE_CURVE('',#914,(#918,#930),.PCURVE_S1.);
#914 = LINE('',#915,#916);
#915 = CARTESIAN_POINT('',(49.9999889,16.24998528,0.E+000));
#916 = VECTOR('',#917,1.);
#917 = DIRECTION('',(0.E+000,0.E+000,1.));
#918 = PCURVE('',#919,#924);
#919 = PLANE('',#920);
#920 = AXIS2_PLACEMENT_3D('',#921,#922,#923);
#921 = CARTESIAN_POINT('',(49.9999889,16.24998528,0.E+000));
#922 = DIRECTION('',(0.E+000,-1.,0.E+000));
#923 = DIRECTION('',(-1.,0.E+000,0.E+000));
#924 = DEFINITIONAL_REPRESENTATION('',(#925),#929);
#925 = LINE('',#926,#927);
#926 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#927 = VECTOR('',#928,1.);
#928 = DIRECTION('',(0.E+000,-1.));
#929 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#930 = PCURVE('',#931,#936);
#931 = PLANE('',#932);
#932 = AXIS2_PLACEMENT_3D('',#933,#934,#935);
#933 = CARTESIAN_POINT('',(49.9999889,8.24998604,0.E+000));
#934 = DIRECTION('',(-1.,0.E+000,0.E+000));
#935 = DIRECTION('',(0.E+000,1.,0.E+000));
#936 = DEFINITIONAL_REPRESENTATION('',(#937),#941);
#937 = LINE('',#938,#939);
#938 = CARTESIAN_POINT('',(7.99999924,0.E+000));
#939 = VECTOR('',#940,1.);
#940 = DIRECTION('',(0.E+000,-1.));
#941 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#942 = ORIENTED_EDGE('',*,*,#943,.T.);
#943 = EDGE_CURVE('',#911,#944,#946,.T.);
#944 = VERTEX_POINT('',#945);
#945 = CARTESIAN_POINT('',(34.49998688,16.24998528,1.72191934));
#946 = SURFACE_CURVE('',#947,(#951,#958),.PCURVE_S1.);
#947 = LINE('',#948,#949);
#948 = CARTESIAN_POINT('',(49.9999889,16.24998528,1.72191934));
#949 = VECTOR('',#950,1.);
#950 = DIRECTION('',(-1.,0.E+000,0.E+000));
#951 = PCURVE('',#919,#952);
#952 = DEFINITIONAL_REPRESENTATION('',(#953),#957);
#953 = LINE('',#954,#955);
#954 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#955 = VECTOR('',#956,1.);
#956 = DIRECTION('',(1.,0.E+000));
#957 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#958 = PCURVE('',#83,#959);
#959 = DEFINITIONAL_REPRESENTATION('',(#960),#964);
#960 = LINE('',#961,#962);
#961 = CARTESIAN_POINT('',(49.9999889,14.2499969));
#962 = VECTOR('',#963,1.);
#963 = DIRECTION('',(-1.,0.E+000));
#964 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#965 = ORIENTED_EDGE('',*,*,#966,.F.);
#966 = EDGE_CURVE('',#967,#944,#969,.T.);
#967 = VERTEX_POINT('',#968);
#968 = CARTESIAN_POINT('',(34.49998688,16.24998528,0.E+000));
#969 = SURFACE_CURVE('',#970,(#974,#981),.PCURVE_S1.);
#970 = LINE('',#971,#972);
#971 = CARTESIAN_POINT('',(34.49998688,16.24998528,0.E+000));
#972 = VECTOR('',#973,1.);
#973 = DIRECTION('',(0.E+000,0.E+000,1.));
#974 = PCURVE('',#919,#975);
#975 = DEFINITIONAL_REPRESENTATION('',(#976),#980);
#976 = LINE('',#977,#978);
#977 = CARTESIAN_POINT('',(15.50000202,0.E+000));
#978 = VECTOR('',#979,1.);
#979 = DIRECTION('',(0.E+000,-1.));
#980 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#981 = PCURVE('',#982,#987);
#982 = PLANE('',#983);
#983 = AXIS2_PLACEMENT_3D('',#984,#985,#986);
#984 = CARTESIAN_POINT('',(34.49998688,16.24998528,0.E+000));
#985 = DIRECTION('',(1.,0.E+000,-0.E+000));
#986 = DIRECTION('',(0.E+000,-1.,0.E+000));
#987 = DEFINITIONAL_REPRESENTATION('',(#988),#992);
#988 = LINE('',#989,#990);
#989 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#990 = VECTOR('',#991,1.);
#991 = DIRECTION('',(0.E+000,-1.));
#992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#993 = ORIENTED_EDGE('',*,*,#994,.F.);
#994 = EDGE_CURVE('',#909,#967,#995,.T.);
#995 = SURFACE_CURVE('',#996,(#1000,#1007),.PCURVE_S1.);
#996 = LINE('',#997,#998);
#997 = CARTESIAN_POINT('',(49.9999889,16.24998528,0.E+000));
#998 = VECTOR('',#999,1.);
#999 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1000 = PCURVE('',#919,#1001);
#1001 = DEFINITIONAL_REPRESENTATION('',(#1002),#1006);
#1002 = LINE('',#1003,#1004);
#1003 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#1004 = VECTOR('',#1005,1.);
#1005 = DIRECTION('',(1.,0.E+000));
#1006 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1007 = PCURVE('',#137,#1008);
#1008 = DEFINITIONAL_REPRESENTATION('',(#1009),#1013);
#1009 = LINE('',#1010,#1011);
#1010 = CARTESIAN_POINT('',(49.9999889,14.2499969));
#1011 = VECTOR('',#1012,1.);
#1012 = DIRECTION('',(-1.,0.E+000));
#1013 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1014 = ADVANCED_FACE('',(#1015),#982,.F.);
#1015 = FACE_BOUND('',#1016,.F.);
#1016 = EDGE_LOOP('',(#1017,#1018,#1041,#1069));
#1017 = ORIENTED_EDGE('',*,*,#966,.T.);
#1018 = ORIENTED_EDGE('',*,*,#1019,.T.);
#1019 = EDGE_CURVE('',#944,#1020,#1022,.T.);
#1020 = VERTEX_POINT('',#1021);
#1021 = CARTESIAN_POINT('',(34.49998688,8.24998604,1.72191934));
#1022 = SURFACE_CURVE('',#1023,(#1027,#1034),.PCURVE_S1.);
#1023 = LINE('',#1024,#1025);
#1024 = CARTESIAN_POINT('',(34.49998688,16.24998528,1.72191934));
#1025 = VECTOR('',#1026,1.);
#1026 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1027 = PCURVE('',#982,#1028);
#1028 = DEFINITIONAL_REPRESENTATION('',(#1029),#1033);
#1029 = LINE('',#1030,#1031);
#1030 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1031 = VECTOR('',#1032,1.);
#1032 = DIRECTION('',(1.,0.E+000));
#1033 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1034 = PCURVE('',#83,#1035);
#1035 = DEFINITIONAL_REPRESENTATION('',(#1036),#1040);
#1036 = LINE('',#1037,#1038);
#1037 = CARTESIAN_POINT('',(34.49998688,14.2499969));
#1038 = VECTOR('',#1039,1.);
#1039 = DIRECTION('',(0.E+000,-1.));
#1040 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1041 = ORIENTED_EDGE('',*,*,#1042,.F.);
#1042 = EDGE_CURVE('',#1043,#1020,#1045,.T.);
#1043 = VERTEX_POINT('',#1044);
#1044 = CARTESIAN_POINT('',(34.49998688,8.24998604,0.E+000));
#1045 = SURFACE_CURVE('',#1046,(#1050,#1057),.PCURVE_S1.);
#1046 = LINE('',#1047,#1048);
#1047 = CARTESIAN_POINT('',(34.49998688,8.24998604,0.E+000));
#1048 = VECTOR('',#1049,1.);
#1049 = DIRECTION('',(0.E+000,0.E+000,1.));
#1050 = PCURVE('',#982,#1051);
#1051 = DEFINITIONAL_REPRESENTATION('',(#1052),#1056);
#1052 = LINE('',#1053,#1054);
#1053 = CARTESIAN_POINT('',(7.99999924,0.E+000));
#1054 = VECTOR('',#1055,1.);
#1055 = DIRECTION('',(0.E+000,-1.));
#1056 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1057 = PCURVE('',#1058,#1063);
#1058 = PLANE('',#1059);
#1059 = AXIS2_PLACEMENT_3D('',#1060,#1061,#1062);
#1060 = CARTESIAN_POINT('',(34.49998688,8.24998604,0.E+000));
#1061 = DIRECTION('',(0.E+000,1.,0.E+000));
#1062 = DIRECTION('',(1.,0.E+000,0.E+000));
#1063 = DEFINITIONAL_REPRESENTATION('',(#1064),#1068);
#1064 = LINE('',#1065,#1066);
#1065 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1066 = VECTOR('',#1067,1.);
#1067 = DIRECTION('',(0.E+000,-1.));
#1068 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1069 = ORIENTED_EDGE('',*,*,#1070,.F.);
#1070 = EDGE_CURVE('',#967,#1043,#1071,.T.);
#1071 = SURFACE_CURVE('',#1072,(#1076,#1083),.PCURVE_S1.);
#1072 = LINE('',#1073,#1074);
#1073 = CARTESIAN_POINT('',(34.49998688,16.24998528,0.E+000));
#1074 = VECTOR('',#1075,1.);
#1075 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1076 = PCURVE('',#982,#1077);
#1077 = DEFINITIONAL_REPRESENTATION('',(#1078),#1082);
#1078 = LINE('',#1079,#1080);
#1079 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1080 = VECTOR('',#1081,1.);
#1081 = DIRECTION('',(1.,0.E+000));
#1082 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1083 = PCURVE('',#137,#1084);
#1084 = DEFINITIONAL_REPRESENTATION('',(#1085),#1089);
#1085 = LINE('',#1086,#1087);
#1086 = CARTESIAN_POINT('',(34.49998688,14.2499969));
#1087 = VECTOR('',#1088,1.);
#1088 = DIRECTION('',(0.E+000,-1.));
#1089 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1090 = ADVANCED_FACE('',(#1091),#1058,.F.);
#1091 = FACE_BOUND('',#1092,.F.);
#1092 = EDGE_LOOP('',(#1093,#1094,#1117,#1140));
#1093 = ORIENTED_EDGE('',*,*,#1042,.T.);
#1094 = ORIENTED_EDGE('',*,*,#1095,.T.);
#1095 = EDGE_CURVE('',#1020,#1096,#1098,.T.);
#1096 = VERTEX_POINT('',#1097);
#1097 = CARTESIAN_POINT('',(49.9999889,8.24998604,1.72191934));
#1098 = SURFACE_CURVE('',#1099,(#1103,#1110),.PCURVE_S1.);
#1099 = LINE('',#1100,#1101);
#1100 = CARTESIAN_POINT('',(34.49998688,8.24998604,1.72191934));
#1101 = VECTOR('',#1102,1.);
#1102 = DIRECTION('',(1.,0.E+000,0.E+000));
#1103 = PCURVE('',#1058,#1104);
#1104 = DEFINITIONAL_REPRESENTATION('',(#1105),#1109);
#1105 = LINE('',#1106,#1107);
#1106 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1107 = VECTOR('',#1108,1.);
#1108 = DIRECTION('',(1.,0.E+000));
#1109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1110 = PCURVE('',#83,#1111);
#1111 = DEFINITIONAL_REPRESENTATION('',(#1112),#1116);
#1112 = LINE('',#1113,#1114);
#1113 = CARTESIAN_POINT('',(34.49998688,6.24999766));
#1114 = VECTOR('',#1115,1.);
#1115 = DIRECTION('',(1.,0.E+000));
#1116 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1117 = ORIENTED_EDGE('',*,*,#1118,.F.);
#1118 = EDGE_CURVE('',#1119,#1096,#1121,.T.);
#1119 = VERTEX_POINT('',#1120);
#1120 = CARTESIAN_POINT('',(49.9999889,8.24998604,0.E+000));
#1121 = SURFACE_CURVE('',#1122,(#1126,#1133),.PCURVE_S1.);
#1122 = LINE('',#1123,#1124);
#1123 = CARTESIAN_POINT('',(49.9999889,8.24998604,0.E+000));
#1124 = VECTOR('',#1125,1.);
#1125 = DIRECTION('',(0.E+000,0.E+000,1.));
#1126 = PCURVE('',#1058,#1127);
#1127 = DEFINITIONAL_REPRESENTATION('',(#1128),#1132);
#1128 = LINE('',#1129,#1130);
#1129 = CARTESIAN_POINT('',(15.50000202,0.E+000));
#1130 = VECTOR('',#1131,1.);
#1131 = DIRECTION('',(0.E+000,-1.));
#1132 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1133 = PCURVE('',#931,#1134);
#1134 = DEFINITIONAL_REPRESENTATION('',(#1135),#1139);
#1135 = LINE('',#1136,#1137);
#1136 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1137 = VECTOR('',#1138,1.);
#1138 = DIRECTION('',(0.E+000,-1.));
#1139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1140 = ORIENTED_EDGE('',*,*,#1141,.F.);
#1141 = EDGE_CURVE('',#1043,#1119,#1142,.T.);
#1142 = SURFACE_CURVE('',#1143,(#1147,#1154),.PCURVE_S1.);
#1143 = LINE('',#1144,#1145);
#1144 = CARTESIAN_POINT('',(34.49998688,8.24998604,0.E+000));
#1145 = VECTOR('',#1146,1.);
#1146 = DIRECTION('',(1.,0.E+000,0.E+000));
#1147 = PCURVE('',#1058,#1148);
#1148 = DEFINITIONAL_REPRESENTATION('',(#1149),#1153);
#1149 = LINE('',#1150,#1151);
#1150 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1151 = VECTOR('',#1152,1.);
#1152 = DIRECTION('',(1.,0.E+000));
#1153 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1154 = PCURVE('',#137,#1155);
#1155 = DEFINITIONAL_REPRESENTATION('',(#1156),#1160);
#1156 = LINE('',#1157,#1158);
#1157 = CARTESIAN_POINT('',(34.49998688,6.24999766));
#1158 = VECTOR('',#1159,1.);
#1159 = DIRECTION('',(1.,0.E+000));
#1160 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1161 = ADVANCED_FACE('',(#1162),#931,.F.);
#1162 = FACE_BOUND('',#1163,.F.);
#1163 = EDGE_LOOP('',(#1164,#1165,#1186,#1187));
#1164 = ORIENTED_EDGE('',*,*,#1118,.T.);
#1165 = ORIENTED_EDGE('',*,*,#1166,.T.);
#1166 = EDGE_CURVE('',#1096,#911,#1167,.T.);
#1167 = SURFACE_CURVE('',#1168,(#1172,#1179),.PCURVE_S1.);
#1168 = LINE('',#1169,#1170);
#1169 = CARTESIAN_POINT('',(49.9999889,8.24998604,1.72191934));
#1170 = VECTOR('',#1171,1.);
#1171 = DIRECTION('',(0.E+000,1.,0.E+000));
#1172 = PCURVE('',#931,#1173);
#1173 = DEFINITIONAL_REPRESENTATION('',(#1174),#1178);
#1174 = LINE('',#1175,#1176);
#1175 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1176 = VECTOR('',#1177,1.);
#1177 = DIRECTION('',(1.,0.E+000));
#1178 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1179 = PCURVE('',#83,#1180);
#1180 = DEFINITIONAL_REPRESENTATION('',(#1181),#1185);
#1181 = LINE('',#1182,#1183);
#1182 = CARTESIAN_POINT('',(49.9999889,6.24999766));
#1183 = VECTOR('',#1184,1.);
#1184 = DIRECTION('',(0.E+000,1.));
#1185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1186 = ORIENTED_EDGE('',*,*,#908,.F.);
#1187 = ORIENTED_EDGE('',*,*,#1188,.F.);
#1188 = EDGE_CURVE('',#1119,#909,#1189,.T.);
#1189 = SURFACE_CURVE('',#1190,(#1194,#1201),.PCURVE_S1.);
#1190 = LINE('',#1191,#1192);
#1191 = CARTESIAN_POINT('',(49.9999889,8.24998604,0.E+000));
#1192 = VECTOR('',#1193,1.);
#1193 = DIRECTION('',(0.E+000,1.,0.E+000));
#1194 = PCURVE('',#931,#1195);
#1195 = DEFINITIONAL_REPRESENTATION('',(#1196),#1200);
#1196 = LINE('',#1197,#1198);
#1197 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1198 = VECTOR('',#1199,1.);
#1199 = DIRECTION('',(1.,0.E+000));
#1200 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1201 = PCURVE('',#137,#1202);
#1202 = DEFINITIONAL_REPRESENTATION('',(#1203),#1207);
#1203 = LINE('',#1204,#1205);
#1204 = CARTESIAN_POINT('',(49.9999889,6.24999766));
#1205 = VECTOR('',#1206,1.);
#1206 = DIRECTION('',(0.E+000,1.));
#1207 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1208 = ADVANCED_FACE('',(#1209),#1223,.T.);
#1209 = FACE_BOUND('',#1210,.F.);
#1210 = EDGE_LOOP('',(#1211,#1241,#1263,#1264));
#1211 = ORIENTED_EDGE('',*,*,#1212,.T.);
#1212 = EDGE_CURVE('',#1213,#1215,#1217,.T.);
#1213 = VERTEX_POINT('',#1214);
#1214 = CARTESIAN_POINT('',(86.10001068,3.49998538,0.E+000));
#1215 = VERTEX_POINT('',#1216);
#1216 = CARTESIAN_POINT('',(86.10001068,3.49998538,1.72191934));
#1217 = SEAM_CURVE('',#1218,(#1222,#1234),.PCURVE_S1.);
#1218 = LINE('',#1219,#1220);
#1219 = CARTESIAN_POINT('',(86.10001068,3.49998538,0.E+000));
#1220 = VECTOR('',#1221,1.);
#1221 = DIRECTION('',(0.E+000,0.E+000,1.));
#1222 = PCURVE('',#1223,#1228);
#1223 = CYLINDRICAL_SURFACE('',#1224,1.59999934);
#1224 = AXIS2_PLACEMENT_3D('',#1225,#1226,#1227);
#1225 = CARTESIAN_POINT('',(84.50001134,3.49998538,0.E+000));
#1226 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#1227 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1228 = DEFINITIONAL_REPRESENTATION('',(#1229),#1233);
#1229 = LINE('',#1230,#1231);
#1230 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1231 = VECTOR('',#1232,1.);
#1232 = DIRECTION('',(-0.E+000,-1.));
#1233 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1234 = PCURVE('',#1223,#1235);
#1235 = DEFINITIONAL_REPRESENTATION('',(#1236),#1240);
#1236 = LINE('',#1237,#1238);
#1237 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#1238 = VECTOR('',#1239,1.);
#1239 = DIRECTION('',(-0.E+000,-1.));
#1240 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1241 = ORIENTED_EDGE('',*,*,#1242,.T.);
#1242 = EDGE_CURVE('',#1215,#1215,#1243,.T.);
#1243 = SURFACE_CURVE('',#1244,(#1249,#1256),.PCURVE_S1.);
#1244 = CIRCLE('',#1245,1.59999934);
#1245 = AXIS2_PLACEMENT_3D('',#1246,#1247,#1248);
#1246 = CARTESIAN_POINT('',(84.50001134,3.49998538,1.72191934));
#1247 = DIRECTION('',(0.E+000,0.E+000,1.));
#1248 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1249 = PCURVE('',#1223,#1250);
#1250 = DEFINITIONAL_REPRESENTATION('',(#1251),#1255);
#1251 = LINE('',#1252,#1253);
#1252 = CARTESIAN_POINT('',(-0.E+000,-1.72191934));
#1253 = VECTOR('',#1254,1.);
#1254 = DIRECTION('',(-1.,0.E+000));
#1255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1256 = PCURVE('',#83,#1257);
#1257 = DEFINITIONAL_REPRESENTATION('',(#1258),#1262);
#1258 = CIRCLE('',#1259,1.59999934);
#1259 = AXIS2_PLACEMENT_2D('',#1260,#1261);
#1260 = CARTESIAN_POINT('',(84.50001134,1.499997));
#1261 = DIRECTION('',(1.,0.E+000));
#1262 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1263 = ORIENTED_EDGE('',*,*,#1212,.F.);
#1264 = ORIENTED_EDGE('',*,*,#1265,.F.);
#1265 = EDGE_CURVE('',#1213,#1213,#1266,.T.);
#1266 = SURFACE_CURVE('',#1267,(#1272,#1279),.PCURVE_S1.);
#1267 = CIRCLE('',#1268,1.59999934);
#1268 = AXIS2_PLACEMENT_3D('',#1269,#1270,#1271);
#1269 = CARTESIAN_POINT('',(84.50001134,3.49998538,0.E+000));
#1270 = DIRECTION('',(0.E+000,0.E+000,1.));
#1271 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1272 = PCURVE('',#1223,#1273);
#1273 = DEFINITIONAL_REPRESENTATION('',(#1274),#1278);
#1274 = LINE('',#1275,#1276);
#1275 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#1276 = VECTOR('',#1277,1.);
#1277 = DIRECTION('',(-1.,0.E+000));
#1278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1279 = PCURVE('',#137,#1280);
#1280 = DEFINITIONAL_REPRESENTATION('',(#1281),#1285);
#1281 = CIRCLE('',#1282,1.59999934);
#1282 = AXIS2_PLACEMENT_2D('',#1283,#1284);
#1283 = CARTESIAN_POINT('',(84.50001134,1.499997));
#1284 = DIRECTION('',(1.,0.E+000));
#1285 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1286 = ADVANCED_FACE('',(#1287),#1301,.F.);
#1287 = FACE_BOUND('',#1288,.F.);
#1288 = EDGE_LOOP('',(#1289,#1324,#1347,#1375));
#1289 = ORIENTED_EDGE('',*,*,#1290,.T.);
#1290 = EDGE_CURVE('',#1291,#1293,#1295,.T.);
#1291 = VERTEX_POINT('',#1292);
#1292 = CARTESIAN_POINT('',(60.49998314,11.50482586,0.E+000));
#1293 = VERTEX_POINT('',#1294);
#1294 = CARTESIAN_POINT('',(60.49998314,11.50482586,1.72191934));
#1295 = SURFACE_CURVE('',#1296,(#1300,#1312),.PCURVE_S1.);
#1296 = LINE('',#1297,#1298);
#1297 = CARTESIAN_POINT('',(60.49998314,11.50482586,0.E+000));
#1298 = VECTOR('',#1299,1.);
#1299 = DIRECTION('',(0.E+000,0.E+000,1.));
#1300 = PCURVE('',#1301,#1306);
#1301 = PLANE('',#1302);
#1302 = AXIS2_PLACEMENT_3D('',#1303,#1304,#1305);
#1303 = CARTESIAN_POINT('',(60.49998314,11.50482586,0.E+000));
#1304 = DIRECTION('',(9.801754873774E-002,-0.995184686447,0.E+000));
#1305 = DIRECTION('',(-0.995184686447,-9.801754873774E-002,0.E+000));
#1306 = DEFINITIONAL_REPRESENTATION('',(#1307),#1311);
#1307 = LINE('',#1308,#1309);
#1308 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1309 = VECTOR('',#1310,1.);
#1310 = DIRECTION('',(0.E+000,-1.));
#1311 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1312 = PCURVE('',#1313,#1318);
#1313 = PLANE('',#1314);
#1314 = AXIS2_PLACEMENT_3D('',#1315,#1316,#1317);
#1315 = CARTESIAN_POINT('',(65.499996,11.49514846,0.E+000));
#1316 = DIRECTION('',(-1.935471396762E-003,-0.999998126973,0.E+000));
#1317 = DIRECTION('',(-0.999998126973,1.935471396762E-003,0.E+000));
#1318 = DEFINITIONAL_REPRESENTATION('',(#1319),#1323);
#1319 = LINE('',#1320,#1321);
#1320 = CARTESIAN_POINT('',(5.000022225174,0.E+000));
#1321 = VECTOR('',#1322,1.);
#1322 = DIRECTION('',(0.E+000,-1.));
#1323 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1324 = ORIENTED_EDGE('',*,*,#1325,.T.);
#1325 = EDGE_CURVE('',#1293,#1326,#1328,.T.);
#1326 = VERTEX_POINT('',#1327);
#1327 = CARTESIAN_POINT('',(60.10791382,11.46621024,1.72191934));
#1328 = SURFACE_CURVE('',#1329,(#1333,#1340),.PCURVE_S1.);
#1329 = LINE('',#1330,#1331);
#1330 = CARTESIAN_POINT('',(60.49998314,11.50482586,1.72191934));
#1331 = VECTOR('',#1332,1.);
#1332 = DIRECTION('',(-0.995184686447,-9.801754873774E-002,0.E+000));
#1333 = PCURVE('',#1301,#1334);
#1334 = DEFINITIONAL_REPRESENTATION('',(#1335),#1339);
#1335 = LINE('',#1336,#1337);
#1336 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1337 = VECTOR('',#1338,1.);
#1338 = DIRECTION('',(1.,0.E+000));
#1339 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1340 = PCURVE('',#83,#1341);
#1341 = DEFINITIONAL_REPRESENTATION('',(#1342),#1346);
#1342 = LINE('',#1343,#1344);
#1343 = CARTESIAN_POINT('',(60.49998314,9.50483748));
#1344 = VECTOR('',#1345,1.);
#1345 = DIRECTION('',(-0.995184686447,-9.801754873774E-002));
#1346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1347 = ORIENTED_EDGE('',*,*,#1348,.F.);
#1348 = EDGE_CURVE('',#1349,#1326,#1351,.T.);
#1349 = VERTEX_POINT('',#1350);
#1350 = CARTESIAN_POINT('',(60.10791382,11.46621024,0.E+000));
#1351 = SURFACE_CURVE('',#1352,(#1356,#1363),.PCURVE_S1.);
#1352 = LINE('',#1353,#1354);
#1353 = CARTESIAN_POINT('',(60.10791382,11.46621024,0.E+000));
#1354 = VECTOR('',#1355,1.);
#1355 = DIRECTION('',(0.E+000,0.E+000,1.));
#1356 = PCURVE('',#1301,#1357);
#1357 = DEFINITIONAL_REPRESENTATION('',(#1358),#1362);
#1358 = LINE('',#1359,#1360);
#1359 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#1360 = VECTOR('',#1361,1.);
#1361 = DIRECTION('',(0.E+000,-1.));
#1362 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1363 = PCURVE('',#1364,#1369);
#1364 = PLANE('',#1365);
#1365 = AXIS2_PLACEMENT_3D('',#1366,#1367,#1368);
#1366 = CARTESIAN_POINT('',(60.10791382,11.46621024,0.E+000));
#1367 = DIRECTION('',(0.290281519442,-0.956941293638,0.E+000));
#1368 = DIRECTION('',(-0.956941293638,-0.290281519442,0.E+000));
#1369 = DEFINITIONAL_REPRESENTATION('',(#1370),#1374);
#1370 = LINE('',#1371,#1372);
#1371 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1372 = VECTOR('',#1373,1.);
#1373 = DIRECTION('',(0.E+000,-1.));
#1374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1375 = ORIENTED_EDGE('',*,*,#1376,.F.);
#1376 = EDGE_CURVE('',#1291,#1349,#1377,.T.);
#1377 = SURFACE_CURVE('',#1378,(#1382,#1389),.PCURVE_S1.);
#1378 = LINE('',#1379,#1380);
#1379 = CARTESIAN_POINT('',(60.49998314,11.50482586,0.E+000));
#1380 = VECTOR('',#1381,1.);
#1381 = DIRECTION('',(-0.995184686447,-9.801754873774E-002,0.E+000));
#1382 = PCURVE('',#1301,#1383);
#1383 = DEFINITIONAL_REPRESENTATION('',(#1384),#1388);
#1384 = LINE('',#1385,#1386);
#1385 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1386 = VECTOR('',#1387,1.);
#1387 = DIRECTION('',(1.,0.E+000));
#1388 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1389 = PCURVE('',#137,#1390);
#1390 = DEFINITIONAL_REPRESENTATION('',(#1391),#1395);
#1391 = LINE('',#1392,#1393);
#1392 = CARTESIAN_POINT('',(60.49998314,9.50483748));
#1393 = VECTOR('',#1394,1.);
#1394 = DIRECTION('',(-0.995184686447,-9.801754873774E-002));
#1395 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1396 = ADVANCED_FACE('',(#1397),#1364,.F.);
#1397 = FACE_BOUND('',#1398,.F.);
#1398 = EDGE_LOOP('',(#1399,#1400,#1423,#1451));
#1399 = ORIENTED_EDGE('',*,*,#1348,.T.);
#1400 = ORIENTED_EDGE('',*,*,#1401,.T.);
#1401 = EDGE_CURVE('',#1326,#1402,#1404,.T.);
#1402 = VERTEX_POINT('',#1403);
#1403 = CARTESIAN_POINT('',(59.73091178,11.35184928,1.72191934));
#1404 = SURFACE_CURVE('',#1405,(#1409,#1416),.PCURVE_S1.);
#1405 = LINE('',#1406,#1407);
#1406 = CARTESIAN_POINT('',(60.10791382,11.46621024,1.72191934));
#1407 = VECTOR('',#1408,1.);
#1408 = DIRECTION('',(-0.956941293638,-0.290281519442,0.E+000));
#1409 = PCURVE('',#1364,#1410);
#1410 = DEFINITIONAL_REPRESENTATION('',(#1411),#1415);
#1411 = LINE('',#1412,#1413);
#1412 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1413 = VECTOR('',#1414,1.);
#1414 = DIRECTION('',(1.,0.E+000));
#1415 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1416 = PCURVE('',#83,#1417);
#1417 = DEFINITIONAL_REPRESENTATION('',(#1418),#1422);
#1418 = LINE('',#1419,#1420);
#1419 = CARTESIAN_POINT('',(60.10791382,9.46622186));
#1420 = VECTOR('',#1421,1.);
#1421 = DIRECTION('',(-0.956941293638,-0.290281519442));
#1422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1423 = ORIENTED_EDGE('',*,*,#1424,.F.);
#1424 = EDGE_CURVE('',#1425,#1402,#1427,.T.);
#1425 = VERTEX_POINT('',#1426);
#1426 = CARTESIAN_POINT('',(59.73091178,11.35184928,0.E+000));
#1427 = SURFACE_CURVE('',#1428,(#1432,#1439),.PCURVE_S1.);
#1428 = LINE('',#1429,#1430);
#1429 = CARTESIAN_POINT('',(59.73091178,11.35184928,0.E+000));
#1430 = VECTOR('',#1431,1.);
#1431 = DIRECTION('',(0.E+000,0.E+000,1.));
#1432 = PCURVE('',#1364,#1433);
#1433 = DEFINITIONAL_REPRESENTATION('',(#1434),#1438);
#1434 = LINE('',#1435,#1436);
#1435 = CARTESIAN_POINT('',(0.393965693096,0.E+000));
#1436 = VECTOR('',#1437,1.);
#1437 = DIRECTION('',(0.E+000,-1.));
#1438 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1439 = PCURVE('',#1440,#1445);
#1440 = PLANE('',#1441);
#1441 = AXIS2_PLACEMENT_3D('',#1442,#1443,#1444);
#1442 = CARTESIAN_POINT('',(59.73091178,11.35184928,0.E+000));
#1443 = DIRECTION('',(0.471397848625,-0.881920670078,0.E+000));
#1444 = DIRECTION('',(-0.881920670078,-0.471397848625,0.E+000));
#1445 = DEFINITIONAL_REPRESENTATION('',(#1446),#1450);
#1446 = LINE('',#1447,#1448);
#1447 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1448 = VECTOR('',#1449,1.);
#1449 = DIRECTION('',(0.E+000,-1.));
#1450 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1451 = ORIENTED_EDGE('',*,*,#1452,.F.);
#1452 = EDGE_CURVE('',#1349,#1425,#1453,.T.);
#1453 = SURFACE_CURVE('',#1454,(#1458,#1465),.PCURVE_S1.);
#1454 = LINE('',#1455,#1456);
#1455 = CARTESIAN_POINT('',(60.10791382,11.46621024,0.E+000));
#1456 = VECTOR('',#1457,1.);
#1457 = DIRECTION('',(-0.956941293638,-0.290281519442,0.E+000));
#1458 = PCURVE('',#1364,#1459);
#1459 = DEFINITIONAL_REPRESENTATION('',(#1460),#1464);
#1460 = LINE('',#1461,#1462);
#1461 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1462 = VECTOR('',#1463,1.);
#1463 = DIRECTION('',(1.,0.E+000));
#1464 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1465 = PCURVE('',#137,#1466);
#1466 = DEFINITIONAL_REPRESENTATION('',(#1467),#1471);
#1467 = LINE('',#1468,#1469);
#1468 = CARTESIAN_POINT('',(60.10791382,9.46622186));
#1469 = VECTOR('',#1470,1.);
#1470 = DIRECTION('',(-0.956941293638,-0.290281519442));
#1471 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1472 = ADVANCED_FACE('',(#1473),#1440,.F.);
#1473 = FACE_BOUND('',#1474,.F.);
#1474 = EDGE_LOOP('',(#1475,#1476,#1499,#1527));
#1475 = ORIENTED_EDGE('',*,*,#1424,.T.);
#1476 = ORIENTED_EDGE('',*,*,#1477,.T.);
#1477 = EDGE_CURVE('',#1402,#1478,#1480,.T.);
#1478 = VERTEX_POINT('',#1479);
#1479 = CARTESIAN_POINT('',(59.38346518,11.16613464,1.72191934));
#1480 = SURFACE_CURVE('',#1481,(#1485,#1492),.PCURVE_S1.);
#1481 = LINE('',#1482,#1483);
#1482 = CARTESIAN_POINT('',(59.73091178,11.35184928,1.72191934));
#1483 = VECTOR('',#1484,1.);
#1484 = DIRECTION('',(-0.881920670078,-0.471397848625,0.E+000));
#1485 = PCURVE('',#1440,#1486);
#1486 = DEFINITIONAL_REPRESENTATION('',(#1487),#1491);
#1487 = LINE('',#1488,#1489);
#1488 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1489 = VECTOR('',#1490,1.);
#1490 = DIRECTION('',(1.,0.E+000));
#1491 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1492 = PCURVE('',#83,#1493);
#1493 = DEFINITIONAL_REPRESENTATION('',(#1494),#1498);
#1494 = LINE('',#1495,#1496);
#1495 = CARTESIAN_POINT('',(59.73091178,9.3518609));
#1496 = VECTOR('',#1497,1.);
#1497 = DIRECTION('',(-0.881920670078,-0.471397848625));
#1498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1499 = ORIENTED_EDGE('',*,*,#1500,.F.);
#1500 = EDGE_CURVE('',#1501,#1478,#1503,.T.);
#1501 = VERTEX_POINT('',#1502);
#1502 = CARTESIAN_POINT('',(59.38346518,11.16613464,0.E+000));
#1503 = SURFACE_CURVE('',#1504,(#1508,#1515),.PCURVE_S1.);
#1504 = LINE('',#1505,#1506);
#1505 = CARTESIAN_POINT('',(59.38346518,11.16613464,0.E+000));
#1506 = VECTOR('',#1507,1.);
#1507 = DIRECTION('',(0.E+000,0.E+000,1.));
#1508 = PCURVE('',#1440,#1509);
#1509 = DEFINITIONAL_REPRESENTATION('',(#1510),#1514);
#1510 = LINE('',#1511,#1512);
#1511 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#1512 = VECTOR('',#1513,1.);
#1513 = DIRECTION('',(0.E+000,-1.));
#1514 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1515 = PCURVE('',#1516,#1521);
#1516 = PLANE('',#1517);
#1517 = AXIS2_PLACEMENT_3D('',#1518,#1519,#1520);
#1518 = CARTESIAN_POINT('',(59.38346518,11.16613464,0.E+000));
#1519 = DIRECTION('',(0.634397425685,-0.773007054486,0.E+000));
#1520 = DIRECTION('',(-0.773007054486,-0.634397425685,0.E+000));
#1521 = DEFINITIONAL_REPRESENTATION('',(#1522),#1526);
#1522 = LINE('',#1523,#1524);
#1523 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1524 = VECTOR('',#1525,1.);
#1525 = DIRECTION('',(0.E+000,-1.));
#1526 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1527 = ORIENTED_EDGE('',*,*,#1528,.F.);
#1528 = EDGE_CURVE('',#1425,#1501,#1529,.T.);
#1529 = SURFACE_CURVE('',#1530,(#1534,#1541),.PCURVE_S1.);
#1530 = LINE('',#1531,#1532);
#1531 = CARTESIAN_POINT('',(59.73091178,11.35184928,0.E+000));
#1532 = VECTOR('',#1533,1.);
#1533 = DIRECTION('',(-0.881920670078,-0.471397848625,0.E+000));
#1534 = PCURVE('',#1440,#1535);
#1535 = DEFINITIONAL_REPRESENTATION('',(#1536),#1540);
#1536 = LINE('',#1537,#1538);
#1537 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1538 = VECTOR('',#1539,1.);
#1539 = DIRECTION('',(1.,0.E+000));
#1540 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1541 = PCURVE('',#137,#1542);
#1542 = DEFINITIONAL_REPRESENTATION('',(#1543),#1547);
#1543 = LINE('',#1544,#1545);
#1544 = CARTESIAN_POINT('',(59.73091178,9.3518609));
#1545 = VECTOR('',#1546,1.);
#1546 = DIRECTION('',(-0.881920670078,-0.471397848625));
#1547 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1548 = ADVANCED_FACE('',(#1549),#1516,.F.);
#1549 = FACE_BOUND('',#1550,.F.);
#1550 = EDGE_LOOP('',(#1551,#1552,#1575,#1603));
#1551 = ORIENTED_EDGE('',*,*,#1500,.T.);
#1552 = ORIENTED_EDGE('',*,*,#1553,.T.);
#1553 = EDGE_CURVE('',#1478,#1554,#1556,.T.);
#1554 = VERTEX_POINT('',#1555);
#1555 = CARTESIAN_POINT('',(59.0789268,10.91620372,1.72191934));
#1556 = SURFACE_CURVE('',#1557,(#1561,#1568),.PCURVE_S1.);
#1557 = LINE('',#1558,#1559);
#1558 = CARTESIAN_POINT('',(59.38346518,11.16613464,1.72191934));
#1559 = VECTOR('',#1560,1.);
#1560 = DIRECTION('',(-0.773007054486,-0.634397425685,0.E+000));
#1561 = PCURVE('',#1516,#1562);
#1562 = DEFINITIONAL_REPRESENTATION('',(#1563),#1567);
#1563 = LINE('',#1564,#1565);
#1564 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1565 = VECTOR('',#1566,1.);
#1566 = DIRECTION('',(1.,0.E+000));
#1567 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1568 = PCURVE('',#83,#1569);
#1569 = DEFINITIONAL_REPRESENTATION('',(#1570),#1574);
#1570 = LINE('',#1571,#1572);
#1571 = CARTESIAN_POINT('',(59.38346518,9.16614626));
#1572 = VECTOR('',#1573,1.);
#1573 = DIRECTION('',(-0.773007054486,-0.634397425685));
#1574 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1575 = ORIENTED_EDGE('',*,*,#1576,.F.);
#1576 = EDGE_CURVE('',#1577,#1554,#1579,.T.);
#1577 = VERTEX_POINT('',#1578);
#1578 = CARTESIAN_POINT('',(59.0789268,10.91620372,0.E+000));
#1579 = SURFACE_CURVE('',#1580,(#1584,#1591),.PCURVE_S1.);
#1580 = LINE('',#1581,#1582);
#1581 = CARTESIAN_POINT('',(59.0789268,10.91620372,0.E+000));
#1582 = VECTOR('',#1583,1.);
#1583 = DIRECTION('',(0.E+000,0.E+000,1.));
#1584 = PCURVE('',#1516,#1585);
#1585 = DEFINITIONAL_REPRESENTATION('',(#1586),#1590);
#1586 = LINE('',#1587,#1588);
#1587 = CARTESIAN_POINT('',(0.393965848349,0.E+000));
#1588 = VECTOR('',#1589,1.);
#1589 = DIRECTION('',(0.E+000,-1.));
#1590 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1591 = PCURVE('',#1592,#1597);
#1592 = PLANE('',#1593);
#1593 = AXIS2_PLACEMENT_3D('',#1594,#1595,#1596);
#1594 = CARTESIAN_POINT('',(59.0789268,10.91620372,0.E+000));
#1595 = DIRECTION('',(0.773007054486,-0.634397425685,0.E+000));
#1596 = DIRECTION('',(-0.634397425685,-0.773007054486,0.E+000));
#1597 = DEFINITIONAL_REPRESENTATION('',(#1598),#1602);
#1598 = LINE('',#1599,#1600);
#1599 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1600 = VECTOR('',#1601,1.);
#1601 = DIRECTION('',(0.E+000,-1.));
#1602 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1603 = ORIENTED_EDGE('',*,*,#1604,.F.);
#1604 = EDGE_CURVE('',#1501,#1577,#1605,.T.);
#1605 = SURFACE_CURVE('',#1606,(#1610,#1617),.PCURVE_S1.);
#1606 = LINE('',#1607,#1608);
#1607 = CARTESIAN_POINT('',(59.38346518,11.16613464,0.E+000));
#1608 = VECTOR('',#1609,1.);
#1609 = DIRECTION('',(-0.773007054486,-0.634397425685,0.E+000));
#1610 = PCURVE('',#1516,#1611);
#1611 = DEFINITIONAL_REPRESENTATION('',(#1612),#1616);
#1612 = LINE('',#1613,#1614);
#1613 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1614 = VECTOR('',#1615,1.);
#1615 = DIRECTION('',(1.,0.E+000));
#1616 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1617 = PCURVE('',#137,#1618);
#1618 = DEFINITIONAL_REPRESENTATION('',(#1619),#1623);
#1619 = LINE('',#1620,#1621);
#1620 = CARTESIAN_POINT('',(59.38346518,9.16614626));
#1621 = VECTOR('',#1622,1.);
#1622 = DIRECTION('',(-0.773007054486,-0.634397425685));
#1623 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1624 = ADVANCED_FACE('',(#1625),#1592,.F.);
#1625 = FACE_BOUND('',#1626,.F.);
#1626 = EDGE_LOOP('',(#1627,#1628,#1651,#1679));
#1627 = ORIENTED_EDGE('',*,*,#1576,.T.);
#1628 = ORIENTED_EDGE('',*,*,#1629,.T.);
#1629 = EDGE_CURVE('',#1554,#1630,#1632,.T.);
#1630 = VERTEX_POINT('',#1631);
#1631 = CARTESIAN_POINT('',(58.82899588,10.61166534,1.72191934));
#1632 = SURFACE_CURVE('',#1633,(#1637,#1644),.PCURVE_S1.);
#1633 = LINE('',#1634,#1635);
#1634 = CARTESIAN_POINT('',(59.0789268,10.91620372,1.72191934));
#1635 = VECTOR('',#1636,1.);
#1636 = DIRECTION('',(-0.634397425685,-0.773007054486,0.E+000));
#1637 = PCURVE('',#1592,#1638);
#1638 = DEFINITIONAL_REPRESENTATION('',(#1639),#1643);
#1639 = LINE('',#1640,#1641);
#1640 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1641 = VECTOR('',#1642,1.);
#1642 = DIRECTION('',(1.,0.E+000));
#1643 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1644 = PCURVE('',#83,#1645);
#1645 = DEFINITIONAL_REPRESENTATION('',(#1646),#1650);
#1646 = LINE('',#1647,#1648);
#1647 = CARTESIAN_POINT('',(59.0789268,8.91621534));
#1648 = VECTOR('',#1649,1.);
#1649 = DIRECTION('',(-0.634397425685,-0.773007054486));
#1650 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1651 = ORIENTED_EDGE('',*,*,#1652,.F.);
#1652 = EDGE_CURVE('',#1653,#1630,#1655,.T.);
#1653 = VERTEX_POINT('',#1654);
#1654 = CARTESIAN_POINT('',(58.82899588,10.61166534,0.E+000));
#1655 = SURFACE_CURVE('',#1656,(#1660,#1667),.PCURVE_S1.);
#1656 = LINE('',#1657,#1658);
#1657 = CARTESIAN_POINT('',(58.82899588,10.61166534,0.E+000));
#1658 = VECTOR('',#1659,1.);
#1659 = DIRECTION('',(0.E+000,0.E+000,1.));
#1660 = PCURVE('',#1592,#1661);
#1661 = DEFINITIONAL_REPRESENTATION('',(#1662),#1666);
#1662 = LINE('',#1663,#1664);
#1663 = CARTESIAN_POINT('',(0.393965848349,0.E+000));
#1664 = VECTOR('',#1665,1.);
#1665 = DIRECTION('',(0.E+000,-1.));
#1666 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1667 = PCURVE('',#1668,#1673);
#1668 = PLANE('',#1669);
#1669 = AXIS2_PLACEMENT_3D('',#1670,#1671,#1672);
#1670 = CARTESIAN_POINT('',(58.82899588,10.61166534,0.E+000));
#1671 = DIRECTION('',(0.881920670078,-0.471397848625,0.E+000));
#1672 = DIRECTION('',(-0.471397848625,-0.881920670078,0.E+000));
#1673 = DEFINITIONAL_REPRESENTATION('',(#1674),#1678);
#1674 = LINE('',#1675,#1676);
#1675 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1676 = VECTOR('',#1677,1.);
#1677 = DIRECTION('',(0.E+000,-1.));
#1678 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1679 = ORIENTED_EDGE('',*,*,#1680,.F.);
#1680 = EDGE_CURVE('',#1577,#1653,#1681,.T.);
#1681 = SURFACE_CURVE('',#1682,(#1686,#1693),.PCURVE_S1.);
#1682 = LINE('',#1683,#1684);
#1683 = CARTESIAN_POINT('',(59.0789268,10.91620372,0.E+000));
#1684 = VECTOR('',#1685,1.);
#1685 = DIRECTION('',(-0.634397425685,-0.773007054486,0.E+000));
#1686 = PCURVE('',#1592,#1687);
#1687 = DEFINITIONAL_REPRESENTATION('',(#1688),#1692);
#1688 = LINE('',#1689,#1690);
#1689 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1690 = VECTOR('',#1691,1.);
#1691 = DIRECTION('',(1.,0.E+000));
#1692 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1693 = PCURVE('',#137,#1694);
#1694 = DEFINITIONAL_REPRESENTATION('',(#1695),#1699);
#1695 = LINE('',#1696,#1697);
#1696 = CARTESIAN_POINT('',(59.0789268,8.91621534));
#1697 = VECTOR('',#1698,1.);
#1698 = DIRECTION('',(-0.634397425685,-0.773007054486));
#1699 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1700 = ADVANCED_FACE('',(#1701),#1668,.F.);
#1701 = FACE_BOUND('',#1702,.F.);
#1702 = EDGE_LOOP('',(#1703,#1704,#1727,#1755));
#1703 = ORIENTED_EDGE('',*,*,#1652,.T.);
#1704 = ORIENTED_EDGE('',*,*,#1705,.T.);
#1705 = EDGE_CURVE('',#1630,#1706,#1708,.T.);
#1706 = VERTEX_POINT('',#1707);
#1707 = CARTESIAN_POINT('',(58.64328124,10.26421874,1.72191934));
#1708 = SURFACE_CURVE('',#1709,(#1713,#1720),.PCURVE_S1.);
#1709 = LINE('',#1710,#1711);
#1710 = CARTESIAN_POINT('',(58.82899588,10.61166534,1.72191934));
#1711 = VECTOR('',#1712,1.);
#1712 = DIRECTION('',(-0.471397848625,-0.881920670078,0.E+000));
#1713 = PCURVE('',#1668,#1714);
#1714 = DEFINITIONAL_REPRESENTATION('',(#1715),#1719);
#1715 = LINE('',#1716,#1717);
#1716 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1717 = VECTOR('',#1718,1.);
#1718 = DIRECTION('',(1.,0.E+000));
#1719 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1720 = PCURVE('',#83,#1721);
#1721 = DEFINITIONAL_REPRESENTATION('',(#1722),#1726);
#1722 = LINE('',#1723,#1724);
#1723 = CARTESIAN_POINT('',(58.82899588,8.61167696));
#1724 = VECTOR('',#1725,1.);
#1725 = DIRECTION('',(-0.471397848625,-0.881920670078));
#1726 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1727 = ORIENTED_EDGE('',*,*,#1728,.F.);
#1728 = EDGE_CURVE('',#1729,#1706,#1731,.T.);
#1729 = VERTEX_POINT('',#1730);
#1730 = CARTESIAN_POINT('',(58.64328124,10.26421874,0.E+000));
#1731 = SURFACE_CURVE('',#1732,(#1736,#1743),.PCURVE_S1.);
#1732 = LINE('',#1733,#1734);
#1733 = CARTESIAN_POINT('',(58.64328124,10.26421874,0.E+000));
#1734 = VECTOR('',#1735,1.);
#1735 = DIRECTION('',(0.E+000,0.E+000,1.));
#1736 = PCURVE('',#1668,#1737);
#1737 = DEFINITIONAL_REPRESENTATION('',(#1738),#1742);
#1738 = LINE('',#1739,#1740);
#1739 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#1740 = VECTOR('',#1741,1.);
#1741 = DIRECTION('',(0.E+000,-1.));
#1742 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1743 = PCURVE('',#1744,#1749);
#1744 = PLANE('',#1745);
#1745 = AXIS2_PLACEMENT_3D('',#1746,#1747,#1748);
#1746 = CARTESIAN_POINT('',(58.64328124,10.26421874,0.E+000));
#1747 = DIRECTION('',(0.956941293638,-0.290281519442,0.E+000));
#1748 = DIRECTION('',(-0.290281519442,-0.956941293638,0.E+000));
#1749 = DEFINITIONAL_REPRESENTATION('',(#1750),#1754);
#1750 = LINE('',#1751,#1752);
#1751 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1752 = VECTOR('',#1753,1.);
#1753 = DIRECTION('',(0.E+000,-1.));
#1754 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1755 = ORIENTED_EDGE('',*,*,#1756,.F.);
#1756 = EDGE_CURVE('',#1653,#1729,#1757,.T.);
#1757 = SURFACE_CURVE('',#1758,(#1762,#1769),.PCURVE_S1.);
#1758 = LINE('',#1759,#1760);
#1759 = CARTESIAN_POINT('',(58.82899588,10.61166534,0.E+000));
#1760 = VECTOR('',#1761,1.);
#1761 = DIRECTION('',(-0.471397848625,-0.881920670078,0.E+000));
#1762 = PCURVE('',#1668,#1763);
#1763 = DEFINITIONAL_REPRESENTATION('',(#1764),#1768);
#1764 = LINE('',#1765,#1766);
#1765 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1766 = VECTOR('',#1767,1.);
#1767 = DIRECTION('',(1.,0.E+000));
#1768 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1769 = PCURVE('',#137,#1770);
#1770 = DEFINITIONAL_REPRESENTATION('',(#1771),#1775);
#1771 = LINE('',#1772,#1773);
#1772 = CARTESIAN_POINT('',(58.82899588,8.61167696));
#1773 = VECTOR('',#1774,1.);
#1774 = DIRECTION('',(-0.471397848625,-0.881920670078));
#1775 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1776 = ADVANCED_FACE('',(#1777),#1744,.F.);
#1777 = FACE_BOUND('',#1778,.F.);
#1778 = EDGE_LOOP('',(#1779,#1780,#1803,#1831));
#1779 = ORIENTED_EDGE('',*,*,#1728,.T.);
#1780 = ORIENTED_EDGE('',*,*,#1781,.T.);
#1781 = EDGE_CURVE('',#1706,#1782,#1784,.T.);
#1782 = VERTEX_POINT('',#1783);
#1783 = CARTESIAN_POINT('',(58.52892028,9.8872167,1.72191934));
#1784 = SURFACE_CURVE('',#1785,(#1789,#1796),.PCURVE_S1.);
#1785 = LINE('',#1786,#1787);
#1786 = CARTESIAN_POINT('',(58.64328124,10.26421874,1.72191934));
#1787 = VECTOR('',#1788,1.);
#1788 = DIRECTION('',(-0.290281519442,-0.956941293638,0.E+000));
#1789 = PCURVE('',#1744,#1790);
#1790 = DEFINITIONAL_REPRESENTATION('',(#1791),#1795);
#1791 = LINE('',#1792,#1793);
#1792 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1793 = VECTOR('',#1794,1.);
#1794 = DIRECTION('',(1.,0.E+000));
#1795 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1796 = PCURVE('',#83,#1797);
#1797 = DEFINITIONAL_REPRESENTATION('',(#1798),#1802);
#1798 = LINE('',#1799,#1800);
#1799 = CARTESIAN_POINT('',(58.64328124,8.26423036));
#1800 = VECTOR('',#1801,1.);
#1801 = DIRECTION('',(-0.290281519442,-0.956941293638));
#1802 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1803 = ORIENTED_EDGE('',*,*,#1804,.F.);
#1804 = EDGE_CURVE('',#1805,#1782,#1807,.T.);
#1805 = VERTEX_POINT('',#1806);
#1806 = CARTESIAN_POINT('',(58.52892028,9.8872167,0.E+000));
#1807 = SURFACE_CURVE('',#1808,(#1812,#1819),.PCURVE_S1.);
#1808 = LINE('',#1809,#1810);
#1809 = CARTESIAN_POINT('',(58.52892028,9.8872167,0.E+000));
#1810 = VECTOR('',#1811,1.);
#1811 = DIRECTION('',(0.E+000,0.E+000,1.));
#1812 = PCURVE('',#1744,#1813);
#1813 = DEFINITIONAL_REPRESENTATION('',(#1814),#1818);
#1814 = LINE('',#1815,#1816);
#1815 = CARTESIAN_POINT('',(0.393965693096,0.E+000));
#1816 = VECTOR('',#1817,1.);
#1817 = DIRECTION('',(0.E+000,-1.));
#1818 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1819 = PCURVE('',#1820,#1825);
#1820 = PLANE('',#1821);
#1821 = AXIS2_PLACEMENT_3D('',#1822,#1823,#1824);
#1822 = CARTESIAN_POINT('',(58.52892028,9.8872167,0.E+000));
#1823 = DIRECTION('',(0.995184686447,-9.801754873774E-002,0.E+000));
#1824 = DIRECTION('',(-9.801754873774E-002,-0.995184686447,0.E+000));
#1825 = DEFINITIONAL_REPRESENTATION('',(#1826),#1830);
#1826 = LINE('',#1827,#1828);
#1827 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1828 = VECTOR('',#1829,1.);
#1829 = DIRECTION('',(0.E+000,-1.));
#1830 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1831 = ORIENTED_EDGE('',*,*,#1832,.F.);
#1832 = EDGE_CURVE('',#1729,#1805,#1833,.T.);
#1833 = SURFACE_CURVE('',#1834,(#1838,#1845),.PCURVE_S1.);
#1834 = LINE('',#1835,#1836);
#1835 = CARTESIAN_POINT('',(58.64328124,10.26421874,0.E+000));
#1836 = VECTOR('',#1837,1.);
#1837 = DIRECTION('',(-0.290281519442,-0.956941293638,0.E+000));
#1838 = PCURVE('',#1744,#1839);
#1839 = DEFINITIONAL_REPRESENTATION('',(#1840),#1844);
#1840 = LINE('',#1841,#1842);
#1841 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1842 = VECTOR('',#1843,1.);
#1843 = DIRECTION('',(1.,0.E+000));
#1844 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1845 = PCURVE('',#137,#1846);
#1846 = DEFINITIONAL_REPRESENTATION('',(#1847),#1851);
#1847 = LINE('',#1848,#1849);
#1848 = CARTESIAN_POINT('',(58.64328124,8.26423036));
#1849 = VECTOR('',#1850,1.);
#1850 = DIRECTION('',(-0.290281519442,-0.956941293638));
#1851 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1852 = ADVANCED_FACE('',(#1853),#1820,.F.);
#1853 = FACE_BOUND('',#1854,.F.);
#1854 = EDGE_LOOP('',(#1855,#1856,#1879,#1907));
#1855 = ORIENTED_EDGE('',*,*,#1804,.T.);
#1856 = ORIENTED_EDGE('',*,*,#1857,.T.);
#1857 = EDGE_CURVE('',#1782,#1858,#1860,.T.);
#1858 = VERTEX_POINT('',#1859);
#1859 = CARTESIAN_POINT('',(58.49030466,9.49514738,1.72191934));
#1860 = SURFACE_CURVE('',#1861,(#1865,#1872),.PCURVE_S1.);
#1861 = LINE('',#1862,#1863);
#1862 = CARTESIAN_POINT('',(58.52892028,9.8872167,1.72191934));
#1863 = VECTOR('',#1864,1.);
#1864 = DIRECTION('',(-9.801754873774E-002,-0.995184686447,0.E+000));
#1865 = PCURVE('',#1820,#1866);
#1866 = DEFINITIONAL_REPRESENTATION('',(#1867),#1871);
#1867 = LINE('',#1868,#1869);
#1868 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1869 = VECTOR('',#1870,1.);
#1870 = DIRECTION('',(1.,0.E+000));
#1871 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1872 = PCURVE('',#83,#1873);
#1873 = DEFINITIONAL_REPRESENTATION('',(#1874),#1878);
#1874 = LINE('',#1875,#1876);
#1875 = CARTESIAN_POINT('',(58.52892028,7.88722832));
#1876 = VECTOR('',#1877,1.);
#1877 = DIRECTION('',(-9.801754873774E-002,-0.995184686447));
#1878 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1879 = ORIENTED_EDGE('',*,*,#1880,.F.);
#1880 = EDGE_CURVE('',#1881,#1858,#1883,.T.);
#1881 = VERTEX_POINT('',#1882);
#1882 = CARTESIAN_POINT('',(58.49030466,9.49514738,0.E+000));
#1883 = SURFACE_CURVE('',#1884,(#1888,#1895),.PCURVE_S1.);
#1884 = LINE('',#1885,#1886);
#1885 = CARTESIAN_POINT('',(58.49030466,9.49514738,0.E+000));
#1886 = VECTOR('',#1887,1.);
#1887 = DIRECTION('',(0.E+000,0.E+000,1.));
#1888 = PCURVE('',#1820,#1889);
#1889 = DEFINITIONAL_REPRESENTATION('',(#1890),#1894);
#1890 = LINE('',#1891,#1892);
#1891 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#1892 = VECTOR('',#1893,1.);
#1893 = DIRECTION('',(0.E+000,-1.));
#1894 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1895 = PCURVE('',#1896,#1901);
#1896 = PLANE('',#1897);
#1897 = AXIS2_PLACEMENT_3D('',#1898,#1899,#1900);
#1898 = CARTESIAN_POINT('',(58.49030466,9.49514738,0.E+000));
#1899 = DIRECTION('',(0.995184686447,9.801754873774E-002,-0.E+000));
#1900 = DIRECTION('',(9.801754873774E-002,-0.995184686447,0.E+000));
#1901 = DEFINITIONAL_REPRESENTATION('',(#1902),#1906);
#1902 = LINE('',#1903,#1904);
#1903 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1904 = VECTOR('',#1905,1.);
#1905 = DIRECTION('',(0.E+000,-1.));
#1906 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1907 = ORIENTED_EDGE('',*,*,#1908,.F.);
#1908 = EDGE_CURVE('',#1805,#1881,#1909,.T.);
#1909 = SURFACE_CURVE('',#1910,(#1914,#1921),.PCURVE_S1.);
#1910 = LINE('',#1911,#1912);
#1911 = CARTESIAN_POINT('',(58.52892028,9.8872167,0.E+000));
#1912 = VECTOR('',#1913,1.);
#1913 = DIRECTION('',(-9.801754873774E-002,-0.995184686447,0.E+000));
#1914 = PCURVE('',#1820,#1915);
#1915 = DEFINITIONAL_REPRESENTATION('',(#1916),#1920);
#1916 = LINE('',#1917,#1918);
#1917 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1918 = VECTOR('',#1919,1.);
#1919 = DIRECTION('',(1.,0.E+000));
#1920 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1921 = PCURVE('',#137,#1922);
#1922 = DEFINITIONAL_REPRESENTATION('',(#1923),#1927);
#1923 = LINE('',#1924,#1925);
#1924 = CARTESIAN_POINT('',(58.52892028,7.88722832));
#1925 = VECTOR('',#1926,1.);
#1926 = DIRECTION('',(-9.801754873774E-002,-0.995184686447));
#1927 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1928 = ADVANCED_FACE('',(#1929),#1896,.F.);
#1929 = FACE_BOUND('',#1930,.F.);
#1930 = EDGE_LOOP('',(#1931,#1932,#1955,#1983));
#1931 = ORIENTED_EDGE('',*,*,#1880,.T.);
#1932 = ORIENTED_EDGE('',*,*,#1933,.T.);
#1933 = EDGE_CURVE('',#1858,#1934,#1936,.T.);
#1934 = VERTEX_POINT('',#1935);
#1935 = CARTESIAN_POINT('',(58.52892028,9.10307806,1.72191934));
#1936 = SURFACE_CURVE('',#1937,(#1941,#1948),.PCURVE_S1.);
#1937 = LINE('',#1938,#1939);
#1938 = CARTESIAN_POINT('',(58.49030466,9.49514738,1.72191934));
#1939 = VECTOR('',#1940,1.);
#1940 = DIRECTION('',(9.801754873774E-002,-0.995184686447,0.E+000));
#1941 = PCURVE('',#1896,#1942);
#1942 = DEFINITIONAL_REPRESENTATION('',(#1943),#1947);
#1943 = LINE('',#1944,#1945);
#1944 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#1945 = VECTOR('',#1946,1.);
#1946 = DIRECTION('',(1.,0.E+000));
#1947 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1948 = PCURVE('',#83,#1949);
#1949 = DEFINITIONAL_REPRESENTATION('',(#1950),#1954);
#1950 = LINE('',#1951,#1952);
#1951 = CARTESIAN_POINT('',(58.49030466,7.495159));
#1952 = VECTOR('',#1953,1.);
#1953 = DIRECTION('',(9.801754873774E-002,-0.995184686447));
#1954 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1955 = ORIENTED_EDGE('',*,*,#1956,.F.);
#1956 = EDGE_CURVE('',#1957,#1934,#1959,.T.);
#1957 = VERTEX_POINT('',#1958);
#1958 = CARTESIAN_POINT('',(58.52892028,9.10307806,0.E+000));
#1959 = SURFACE_CURVE('',#1960,(#1964,#1971),.PCURVE_S1.);
#1960 = LINE('',#1961,#1962);
#1961 = CARTESIAN_POINT('',(58.52892028,9.10307806,0.E+000));
#1962 = VECTOR('',#1963,1.);
#1963 = DIRECTION('',(0.E+000,0.E+000,1.));
#1964 = PCURVE('',#1896,#1965);
#1965 = DEFINITIONAL_REPRESENTATION('',(#1966),#1970);
#1966 = LINE('',#1967,#1968);
#1967 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#1968 = VECTOR('',#1969,1.);
#1969 = DIRECTION('',(0.E+000,-1.));
#1970 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1971 = PCURVE('',#1972,#1977);
#1972 = PLANE('',#1973);
#1973 = AXIS2_PLACEMENT_3D('',#1974,#1975,#1976);
#1974 = CARTESIAN_POINT('',(58.52892028,9.10307806,0.E+000));
#1975 = DIRECTION('',(0.956941293638,0.290281519442,-0.E+000));
#1976 = DIRECTION('',(0.290281519442,-0.956941293638,0.E+000));
#1977 = DEFINITIONAL_REPRESENTATION('',(#1978),#1982);
#1978 = LINE('',#1979,#1980);
#1979 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1980 = VECTOR('',#1981,1.);
#1981 = DIRECTION('',(0.E+000,-1.));
#1982 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1983 = ORIENTED_EDGE('',*,*,#1984,.F.);
#1984 = EDGE_CURVE('',#1881,#1957,#1985,.T.);
#1985 = SURFACE_CURVE('',#1986,(#1990,#1997),.PCURVE_S1.);
#1986 = LINE('',#1987,#1988);
#1987 = CARTESIAN_POINT('',(58.49030466,9.49514738,0.E+000));
#1988 = VECTOR('',#1989,1.);
#1989 = DIRECTION('',(9.801754873774E-002,-0.995184686447,0.E+000));
#1990 = PCURVE('',#1896,#1991);
#1991 = DEFINITIONAL_REPRESENTATION('',(#1992),#1996);
#1992 = LINE('',#1993,#1994);
#1993 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1994 = VECTOR('',#1995,1.);
#1995 = DIRECTION('',(1.,0.E+000));
#1996 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1997 = PCURVE('',#137,#1998);
#1998 = DEFINITIONAL_REPRESENTATION('',(#1999),#2003);
#1999 = LINE('',#2000,#2001);
#2000 = CARTESIAN_POINT('',(58.49030466,7.495159));
#2001 = VECTOR('',#2002,1.);
#2002 = DIRECTION('',(9.801754873774E-002,-0.995184686447));
#2003 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2004 = ADVANCED_FACE('',(#2005),#1972,.F.);
#2005 = FACE_BOUND('',#2006,.F.);
#2006 = EDGE_LOOP('',(#2007,#2008,#2031,#2059));
#2007 = ORIENTED_EDGE('',*,*,#1956,.T.);
#2008 = ORIENTED_EDGE('',*,*,#2009,.T.);
#2009 = EDGE_CURVE('',#1934,#2010,#2012,.T.);
#2010 = VERTEX_POINT('',#2011);
#2011 = CARTESIAN_POINT('',(58.64328124,8.72607602,1.72191934));
#2012 = SURFACE_CURVE('',#2013,(#2017,#2024),.PCURVE_S1.);
#2013 = LINE('',#2014,#2015);
#2014 = CARTESIAN_POINT('',(58.52892028,9.10307806,1.72191934));
#2015 = VECTOR('',#2016,1.);
#2016 = DIRECTION('',(0.290281519442,-0.956941293638,0.E+000));
#2017 = PCURVE('',#1972,#2018);
#2018 = DEFINITIONAL_REPRESENTATION('',(#2019),#2023);
#2019 = LINE('',#2020,#2021);
#2020 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2021 = VECTOR('',#2022,1.);
#2022 = DIRECTION('',(1.,0.E+000));
#2023 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2024 = PCURVE('',#83,#2025);
#2025 = DEFINITIONAL_REPRESENTATION('',(#2026),#2030);
#2026 = LINE('',#2027,#2028);
#2027 = CARTESIAN_POINT('',(58.52892028,7.10308968));
#2028 = VECTOR('',#2029,1.);
#2029 = DIRECTION('',(0.290281519442,-0.956941293638));
#2030 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2031 = ORIENTED_EDGE('',*,*,#2032,.F.);
#2032 = EDGE_CURVE('',#2033,#2010,#2035,.T.);
#2033 = VERTEX_POINT('',#2034);
#2034 = CARTESIAN_POINT('',(58.64328124,8.72607602,0.E+000));
#2035 = SURFACE_CURVE('',#2036,(#2040,#2047),.PCURVE_S1.);
#2036 = LINE('',#2037,#2038);
#2037 = CARTESIAN_POINT('',(58.64328124,8.72607602,0.E+000));
#2038 = VECTOR('',#2039,1.);
#2039 = DIRECTION('',(0.E+000,0.E+000,1.));
#2040 = PCURVE('',#1972,#2041);
#2041 = DEFINITIONAL_REPRESENTATION('',(#2042),#2046);
#2042 = LINE('',#2043,#2044);
#2043 = CARTESIAN_POINT('',(0.393965693096,0.E+000));
#2044 = VECTOR('',#2045,1.);
#2045 = DIRECTION('',(0.E+000,-1.));
#2046 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2047 = PCURVE('',#2048,#2053);
#2048 = PLANE('',#2049);
#2049 = AXIS2_PLACEMENT_3D('',#2050,#2051,#2052);
#2050 = CARTESIAN_POINT('',(58.64328124,8.72607602,0.E+000));
#2051 = DIRECTION('',(0.881920670078,0.471397848625,-0.E+000));
#2052 = DIRECTION('',(0.471397848625,-0.881920670078,0.E+000));
#2053 = DEFINITIONAL_REPRESENTATION('',(#2054),#2058);
#2054 = LINE('',#2055,#2056);
#2055 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2056 = VECTOR('',#2057,1.);
#2057 = DIRECTION('',(0.E+000,-1.));
#2058 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2059 = ORIENTED_EDGE('',*,*,#2060,.F.);
#2060 = EDGE_CURVE('',#1957,#2033,#2061,.T.);
#2061 = SURFACE_CURVE('',#2062,(#2066,#2073),.PCURVE_S1.);
#2062 = LINE('',#2063,#2064);
#2063 = CARTESIAN_POINT('',(58.52892028,9.10307806,0.E+000));
#2064 = VECTOR('',#2065,1.);
#2065 = DIRECTION('',(0.290281519442,-0.956941293638,0.E+000));
#2066 = PCURVE('',#1972,#2067);
#2067 = DEFINITIONAL_REPRESENTATION('',(#2068),#2072);
#2068 = LINE('',#2069,#2070);
#2069 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2070 = VECTOR('',#2071,1.);
#2071 = DIRECTION('',(1.,0.E+000));
#2072 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2073 = PCURVE('',#137,#2074);
#2074 = DEFINITIONAL_REPRESENTATION('',(#2075),#2079);
#2075 = LINE('',#2076,#2077);
#2076 = CARTESIAN_POINT('',(58.52892028,7.10308968));
#2077 = VECTOR('',#2078,1.);
#2078 = DIRECTION('',(0.290281519442,-0.956941293638));
#2079 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2080 = ADVANCED_FACE('',(#2081),#2048,.F.);
#2081 = FACE_BOUND('',#2082,.F.);
#2082 = EDGE_LOOP('',(#2083,#2084,#2107,#2135));
#2083 = ORIENTED_EDGE('',*,*,#2032,.T.);
#2084 = ORIENTED_EDGE('',*,*,#2085,.T.);
#2085 = EDGE_CURVE('',#2010,#2086,#2088,.T.);
#2086 = VERTEX_POINT('',#2087);
#2087 = CARTESIAN_POINT('',(58.82899588,8.37862942,1.72191934));
#2088 = SURFACE_CURVE('',#2089,(#2093,#2100),.PCURVE_S1.);
#2089 = LINE('',#2090,#2091);
#2090 = CARTESIAN_POINT('',(58.64328124,8.72607602,1.72191934));
#2091 = VECTOR('',#2092,1.);
#2092 = DIRECTION('',(0.471397848625,-0.881920670078,0.E+000));
#2093 = PCURVE('',#2048,#2094);
#2094 = DEFINITIONAL_REPRESENTATION('',(#2095),#2099);
#2095 = LINE('',#2096,#2097);
#2096 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2097 = VECTOR('',#2098,1.);
#2098 = DIRECTION('',(1.,0.E+000));
#2099 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2100 = PCURVE('',#83,#2101);
#2101 = DEFINITIONAL_REPRESENTATION('',(#2102),#2106);
#2102 = LINE('',#2103,#2104);
#2103 = CARTESIAN_POINT('',(58.64328124,6.72608764));
#2104 = VECTOR('',#2105,1.);
#2105 = DIRECTION('',(0.471397848625,-0.881920670078));
#2106 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2107 = ORIENTED_EDGE('',*,*,#2108,.F.);
#2108 = EDGE_CURVE('',#2109,#2086,#2111,.T.);
#2109 = VERTEX_POINT('',#2110);
#2110 = CARTESIAN_POINT('',(58.82899588,8.37862942,0.E+000));
#2111 = SURFACE_CURVE('',#2112,(#2116,#2123),.PCURVE_S1.);
#2112 = LINE('',#2113,#2114);
#2113 = CARTESIAN_POINT('',(58.82899588,8.37862942,0.E+000));
#2114 = VECTOR('',#2115,1.);
#2115 = DIRECTION('',(0.E+000,0.E+000,1.));
#2116 = PCURVE('',#2048,#2117);
#2117 = DEFINITIONAL_REPRESENTATION('',(#2118),#2122);
#2118 = LINE('',#2119,#2120);
#2119 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#2120 = VECTOR('',#2121,1.);
#2121 = DIRECTION('',(0.E+000,-1.));
#2122 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2123 = PCURVE('',#2124,#2129);
#2124 = PLANE('',#2125);
#2125 = AXIS2_PLACEMENT_3D('',#2126,#2127,#2128);
#2126 = CARTESIAN_POINT('',(58.82899588,8.37862942,0.E+000));
#2127 = DIRECTION('',(0.773007054486,0.634397425685,-0.E+000));
#2128 = DIRECTION('',(0.634397425685,-0.773007054486,0.E+000));
#2129 = DEFINITIONAL_REPRESENTATION('',(#2130),#2134);
#2130 = LINE('',#2131,#2132);
#2131 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2132 = VECTOR('',#2133,1.);
#2133 = DIRECTION('',(0.E+000,-1.));
#2134 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2135 = ORIENTED_EDGE('',*,*,#2136,.F.);
#2136 = EDGE_CURVE('',#2033,#2109,#2137,.T.);
#2137 = SURFACE_CURVE('',#2138,(#2142,#2149),.PCURVE_S1.);
#2138 = LINE('',#2139,#2140);
#2139 = CARTESIAN_POINT('',(58.64328124,8.72607602,0.E+000));
#2140 = VECTOR('',#2141,1.);
#2141 = DIRECTION('',(0.471397848625,-0.881920670078,0.E+000));
#2142 = PCURVE('',#2048,#2143);
#2143 = DEFINITIONAL_REPRESENTATION('',(#2144),#2148);
#2144 = LINE('',#2145,#2146);
#2145 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2146 = VECTOR('',#2147,1.);
#2147 = DIRECTION('',(1.,0.E+000));
#2148 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2149 = PCURVE('',#137,#2150);
#2150 = DEFINITIONAL_REPRESENTATION('',(#2151),#2155);
#2151 = LINE('',#2152,#2153);
#2152 = CARTESIAN_POINT('',(58.64328124,6.72608764));
#2153 = VECTOR('',#2154,1.);
#2154 = DIRECTION('',(0.471397848625,-0.881920670078));
#2155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2156 = ADVANCED_FACE('',(#2157),#2124,.F.);
#2157 = FACE_BOUND('',#2158,.F.);
#2158 = EDGE_LOOP('',(#2159,#2160,#2183,#2211));
#2159 = ORIENTED_EDGE('',*,*,#2108,.T.);
#2160 = ORIENTED_EDGE('',*,*,#2161,.T.);
#2161 = EDGE_CURVE('',#2086,#2162,#2164,.T.);
#2162 = VERTEX_POINT('',#2163);
#2163 = CARTESIAN_POINT('',(59.0789268,8.07409104,1.72191934));
#2164 = SURFACE_CURVE('',#2165,(#2169,#2176),.PCURVE_S1.);
#2165 = LINE('',#2166,#2167);
#2166 = CARTESIAN_POINT('',(58.82899588,8.37862942,1.72191934));
#2167 = VECTOR('',#2168,1.);
#2168 = DIRECTION('',(0.634397425685,-0.773007054486,0.E+000));
#2169 = PCURVE('',#2124,#2170);
#2170 = DEFINITIONAL_REPRESENTATION('',(#2171),#2175);
#2171 = LINE('',#2172,#2173);
#2172 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2173 = VECTOR('',#2174,1.);
#2174 = DIRECTION('',(1.,0.E+000));
#2175 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2176 = PCURVE('',#83,#2177);
#2177 = DEFINITIONAL_REPRESENTATION('',(#2178),#2182);
#2178 = LINE('',#2179,#2180);
#2179 = CARTESIAN_POINT('',(58.82899588,6.37864104));
#2180 = VECTOR('',#2181,1.);
#2181 = DIRECTION('',(0.634397425685,-0.773007054486));
#2182 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2183 = ORIENTED_EDGE('',*,*,#2184,.F.);
#2184 = EDGE_CURVE('',#2185,#2162,#2187,.T.);
#2185 = VERTEX_POINT('',#2186);
#2186 = CARTESIAN_POINT('',(59.0789268,8.07409104,0.E+000));
#2187 = SURFACE_CURVE('',#2188,(#2192,#2199),.PCURVE_S1.);
#2188 = LINE('',#2189,#2190);
#2189 = CARTESIAN_POINT('',(59.0789268,8.07409104,0.E+000));
#2190 = VECTOR('',#2191,1.);
#2191 = DIRECTION('',(0.E+000,0.E+000,1.));
#2192 = PCURVE('',#2124,#2193);
#2193 = DEFINITIONAL_REPRESENTATION('',(#2194),#2198);
#2194 = LINE('',#2195,#2196);
#2195 = CARTESIAN_POINT('',(0.393965848349,0.E+000));
#2196 = VECTOR('',#2197,1.);
#2197 = DIRECTION('',(0.E+000,-1.));
#2198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2199 = PCURVE('',#2200,#2205);
#2200 = PLANE('',#2201);
#2201 = AXIS2_PLACEMENT_3D('',#2202,#2203,#2204);
#2202 = CARTESIAN_POINT('',(59.0789268,8.07409104,0.E+000));
#2203 = DIRECTION('',(0.634397425685,0.773007054486,-0.E+000));
#2204 = DIRECTION('',(0.773007054486,-0.634397425685,0.E+000));
#2205 = DEFINITIONAL_REPRESENTATION('',(#2206),#2210);
#2206 = LINE('',#2207,#2208);
#2207 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2208 = VECTOR('',#2209,1.);
#2209 = DIRECTION('',(0.E+000,-1.));
#2210 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2211 = ORIENTED_EDGE('',*,*,#2212,.F.);
#2212 = EDGE_CURVE('',#2109,#2185,#2213,.T.);
#2213 = SURFACE_CURVE('',#2214,(#2218,#2225),.PCURVE_S1.);
#2214 = LINE('',#2215,#2216);
#2215 = CARTESIAN_POINT('',(58.82899588,8.37862942,0.E+000));
#2216 = VECTOR('',#2217,1.);
#2217 = DIRECTION('',(0.634397425685,-0.773007054486,0.E+000));
#2218 = PCURVE('',#2124,#2219);
#2219 = DEFINITIONAL_REPRESENTATION('',(#2220),#2224);
#2220 = LINE('',#2221,#2222);
#2221 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2222 = VECTOR('',#2223,1.);
#2223 = DIRECTION('',(1.,0.E+000));
#2224 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2225 = PCURVE('',#137,#2226);
#2226 = DEFINITIONAL_REPRESENTATION('',(#2227),#2231);
#2227 = LINE('',#2228,#2229);
#2228 = CARTESIAN_POINT('',(58.82899588,6.37864104));
#2229 = VECTOR('',#2230,1.);
#2230 = DIRECTION('',(0.634397425685,-0.773007054486));
#2231 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2232 = ADVANCED_FACE('',(#2233),#2200,.F.);
#2233 = FACE_BOUND('',#2234,.F.);
#2234 = EDGE_LOOP('',(#2235,#2236,#2259,#2287));
#2235 = ORIENTED_EDGE('',*,*,#2184,.T.);
#2236 = ORIENTED_EDGE('',*,*,#2237,.T.);
#2237 = EDGE_CURVE('',#2162,#2238,#2240,.T.);
#2238 = VERTEX_POINT('',#2239);
#2239 = CARTESIAN_POINT('',(59.38346518,7.82416012,1.72191934));
#2240 = SURFACE_CURVE('',#2241,(#2245,#2252),.PCURVE_S1.);
#2241 = LINE('',#2242,#2243);
#2242 = CARTESIAN_POINT('',(59.0789268,8.07409104,1.72191934));
#2243 = VECTOR('',#2244,1.);
#2244 = DIRECTION('',(0.773007054486,-0.634397425685,0.E+000));
#2245 = PCURVE('',#2200,#2246);
#2246 = DEFINITIONAL_REPRESENTATION('',(#2247),#2251);
#2247 = LINE('',#2248,#2249);
#2248 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2249 = VECTOR('',#2250,1.);
#2250 = DIRECTION('',(1.,0.E+000));
#2251 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2252 = PCURVE('',#83,#2253);
#2253 = DEFINITIONAL_REPRESENTATION('',(#2254),#2258);
#2254 = LINE('',#2255,#2256);
#2255 = CARTESIAN_POINT('',(59.0789268,6.07410266));
#2256 = VECTOR('',#2257,1.);
#2257 = DIRECTION('',(0.773007054486,-0.634397425685));
#2258 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2259 = ORIENTED_EDGE('',*,*,#2260,.F.);
#2260 = EDGE_CURVE('',#2261,#2238,#2263,.T.);
#2261 = VERTEX_POINT('',#2262);
#2262 = CARTESIAN_POINT('',(59.38346518,7.82416012,0.E+000));
#2263 = SURFACE_CURVE('',#2264,(#2268,#2275),.PCURVE_S1.);
#2264 = LINE('',#2265,#2266);
#2265 = CARTESIAN_POINT('',(59.38346518,7.82416012,0.E+000));
#2266 = VECTOR('',#2267,1.);
#2267 = DIRECTION('',(0.E+000,0.E+000,1.));
#2268 = PCURVE('',#2200,#2269);
#2269 = DEFINITIONAL_REPRESENTATION('',(#2270),#2274);
#2270 = LINE('',#2271,#2272);
#2271 = CARTESIAN_POINT('',(0.393965848349,0.E+000));
#2272 = VECTOR('',#2273,1.);
#2273 = DIRECTION('',(0.E+000,-1.));
#2274 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2275 = PCURVE('',#2276,#2281);
#2276 = PLANE('',#2277);
#2277 = AXIS2_PLACEMENT_3D('',#2278,#2279,#2280);
#2278 = CARTESIAN_POINT('',(59.38346518,7.82416012,0.E+000));
#2279 = DIRECTION('',(0.471397848625,0.881920670078,-0.E+000));
#2280 = DIRECTION('',(0.881920670078,-0.471397848625,0.E+000));
#2281 = DEFINITIONAL_REPRESENTATION('',(#2282),#2286);
#2282 = LINE('',#2283,#2284);
#2283 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2284 = VECTOR('',#2285,1.);
#2285 = DIRECTION('',(0.E+000,-1.));
#2286 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2287 = ORIENTED_EDGE('',*,*,#2288,.F.);
#2288 = EDGE_CURVE('',#2185,#2261,#2289,.T.);
#2289 = SURFACE_CURVE('',#2290,(#2294,#2301),.PCURVE_S1.);
#2290 = LINE('',#2291,#2292);
#2291 = CARTESIAN_POINT('',(59.0789268,8.07409104,0.E+000));
#2292 = VECTOR('',#2293,1.);
#2293 = DIRECTION('',(0.773007054486,-0.634397425685,0.E+000));
#2294 = PCURVE('',#2200,#2295);
#2295 = DEFINITIONAL_REPRESENTATION('',(#2296),#2300);
#2296 = LINE('',#2297,#2298);
#2297 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2298 = VECTOR('',#2299,1.);
#2299 = DIRECTION('',(1.,0.E+000));
#2300 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2301 = PCURVE('',#137,#2302);
#2302 = DEFINITIONAL_REPRESENTATION('',(#2303),#2307);
#2303 = LINE('',#2304,#2305);
#2304 = CARTESIAN_POINT('',(59.0789268,6.07410266));
#2305 = VECTOR('',#2306,1.);
#2306 = DIRECTION('',(0.773007054486,-0.634397425685));
#2307 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2308 = ADVANCED_FACE('',(#2309),#2276,.F.);
#2309 = FACE_BOUND('',#2310,.F.);
#2310 = EDGE_LOOP('',(#2311,#2312,#2335,#2363));
#2311 = ORIENTED_EDGE('',*,*,#2260,.T.);
#2312 = ORIENTED_EDGE('',*,*,#2313,.T.);
#2313 = EDGE_CURVE('',#2238,#2314,#2316,.T.);
#2314 = VERTEX_POINT('',#2315);
#2315 = CARTESIAN_POINT('',(59.73091178,7.63844548,1.72191934));
#2316 = SURFACE_CURVE('',#2317,(#2321,#2328),.PCURVE_S1.);
#2317 = LINE('',#2318,#2319);
#2318 = CARTESIAN_POINT('',(59.38346518,7.82416012,1.72191934));
#2319 = VECTOR('',#2320,1.);
#2320 = DIRECTION('',(0.881920670078,-0.471397848625,0.E+000));
#2321 = PCURVE('',#2276,#2322);
#2322 = DEFINITIONAL_REPRESENTATION('',(#2323),#2327);
#2323 = LINE('',#2324,#2325);
#2324 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2325 = VECTOR('',#2326,1.);
#2326 = DIRECTION('',(1.,0.E+000));
#2327 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2328 = PCURVE('',#83,#2329);
#2329 = DEFINITIONAL_REPRESENTATION('',(#2330),#2334);
#2330 = LINE('',#2331,#2332);
#2331 = CARTESIAN_POINT('',(59.38346518,5.82417174));
#2332 = VECTOR('',#2333,1.);
#2333 = DIRECTION('',(0.881920670078,-0.471397848625));
#2334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2335 = ORIENTED_EDGE('',*,*,#2336,.F.);
#2336 = EDGE_CURVE('',#2337,#2314,#2339,.T.);
#2337 = VERTEX_POINT('',#2338);
#2338 = CARTESIAN_POINT('',(59.73091178,7.63844548,0.E+000));
#2339 = SURFACE_CURVE('',#2340,(#2344,#2351),.PCURVE_S1.);
#2340 = LINE('',#2341,#2342);
#2341 = CARTESIAN_POINT('',(59.73091178,7.63844548,0.E+000));
#2342 = VECTOR('',#2343,1.);
#2343 = DIRECTION('',(0.E+000,0.E+000,1.));
#2344 = PCURVE('',#2276,#2345);
#2345 = DEFINITIONAL_REPRESENTATION('',(#2346),#2350);
#2346 = LINE('',#2347,#2348);
#2347 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#2348 = VECTOR('',#2349,1.);
#2349 = DIRECTION('',(0.E+000,-1.));
#2350 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2351 = PCURVE('',#2352,#2357);
#2352 = PLANE('',#2353);
#2353 = AXIS2_PLACEMENT_3D('',#2354,#2355,#2356);
#2354 = CARTESIAN_POINT('',(59.73091178,7.63844548,0.E+000));
#2355 = DIRECTION('',(0.290281519442,0.956941293638,-0.E+000));
#2356 = DIRECTION('',(0.956941293638,-0.290281519442,0.E+000));
#2357 = DEFINITIONAL_REPRESENTATION('',(#2358),#2362);
#2358 = LINE('',#2359,#2360);
#2359 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2360 = VECTOR('',#2361,1.);
#2361 = DIRECTION('',(0.E+000,-1.));
#2362 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2363 = ORIENTED_EDGE('',*,*,#2364,.F.);
#2364 = EDGE_CURVE('',#2261,#2337,#2365,.T.);
#2365 = SURFACE_CURVE('',#2366,(#2370,#2377),.PCURVE_S1.);
#2366 = LINE('',#2367,#2368);
#2367 = CARTESIAN_POINT('',(59.38346518,7.82416012,0.E+000));
#2368 = VECTOR('',#2369,1.);
#2369 = DIRECTION('',(0.881920670078,-0.471397848625,0.E+000));
#2370 = PCURVE('',#2276,#2371);
#2371 = DEFINITIONAL_REPRESENTATION('',(#2372),#2376);
#2372 = LINE('',#2373,#2374);
#2373 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2374 = VECTOR('',#2375,1.);
#2375 = DIRECTION('',(1.,0.E+000));
#2376 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2377 = PCURVE('',#137,#2378);
#2378 = DEFINITIONAL_REPRESENTATION('',(#2379),#2383);
#2379 = LINE('',#2380,#2381);
#2380 = CARTESIAN_POINT('',(59.38346518,5.82417174));
#2381 = VECTOR('',#2382,1.);
#2382 = DIRECTION('',(0.881920670078,-0.471397848625));
#2383 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2384 = ADVANCED_FACE('',(#2385),#2352,.F.);
#2385 = FACE_BOUND('',#2386,.F.);
#2386 = EDGE_LOOP('',(#2387,#2388,#2411,#2439));
#2387 = ORIENTED_EDGE('',*,*,#2336,.T.);
#2388 = ORIENTED_EDGE('',*,*,#2389,.T.);
#2389 = EDGE_CURVE('',#2314,#2390,#2392,.T.);
#2390 = VERTEX_POINT('',#2391);
#2391 = CARTESIAN_POINT('',(60.10791382,7.52408452,1.72191934));
#2392 = SURFACE_CURVE('',#2393,(#2397,#2404),.PCURVE_S1.);
#2393 = LINE('',#2394,#2395);
#2394 = CARTESIAN_POINT('',(59.73091178,7.63844548,1.72191934));
#2395 = VECTOR('',#2396,1.);
#2396 = DIRECTION('',(0.956941293638,-0.290281519442,0.E+000));
#2397 = PCURVE('',#2352,#2398);
#2398 = DEFINITIONAL_REPRESENTATION('',(#2399),#2403);
#2399 = LINE('',#2400,#2401);
#2400 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2401 = VECTOR('',#2402,1.);
#2402 = DIRECTION('',(1.,0.E+000));
#2403 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2404 = PCURVE('',#83,#2405);
#2405 = DEFINITIONAL_REPRESENTATION('',(#2406),#2410);
#2406 = LINE('',#2407,#2408);
#2407 = CARTESIAN_POINT('',(59.73091178,5.6384571));
#2408 = VECTOR('',#2409,1.);
#2409 = DIRECTION('',(0.956941293638,-0.290281519442));
#2410 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2411 = ORIENTED_EDGE('',*,*,#2412,.F.);
#2412 = EDGE_CURVE('',#2413,#2390,#2415,.T.);
#2413 = VERTEX_POINT('',#2414);
#2414 = CARTESIAN_POINT('',(60.10791382,7.52408452,0.E+000));
#2415 = SURFACE_CURVE('',#2416,(#2420,#2427),.PCURVE_S1.);
#2416 = LINE('',#2417,#2418);
#2417 = CARTESIAN_POINT('',(60.10791382,7.52408452,0.E+000));
#2418 = VECTOR('',#2419,1.);
#2419 = DIRECTION('',(0.E+000,0.E+000,1.));
#2420 = PCURVE('',#2352,#2421);
#2421 = DEFINITIONAL_REPRESENTATION('',(#2422),#2426);
#2422 = LINE('',#2423,#2424);
#2423 = CARTESIAN_POINT('',(0.393965693096,0.E+000));
#2424 = VECTOR('',#2425,1.);
#2425 = DIRECTION('',(0.E+000,-1.));
#2426 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2427 = PCURVE('',#2428,#2433);
#2428 = PLANE('',#2429);
#2429 = AXIS2_PLACEMENT_3D('',#2430,#2431,#2432);
#2430 = CARTESIAN_POINT('',(60.10791382,7.52408452,0.E+000));
#2431 = DIRECTION('',(9.801754873773E-002,0.995184686447,-0.E+000));
#2432 = DIRECTION('',(0.995184686447,-9.801754873773E-002,0.E+000));
#2433 = DEFINITIONAL_REPRESENTATION('',(#2434),#2438);
#2434 = LINE('',#2435,#2436);
#2435 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2436 = VECTOR('',#2437,1.);
#2437 = DIRECTION('',(0.E+000,-1.));
#2438 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2439 = ORIENTED_EDGE('',*,*,#2440,.F.);
#2440 = EDGE_CURVE('',#2337,#2413,#2441,.T.);
#2441 = SURFACE_CURVE('',#2442,(#2446,#2453),.PCURVE_S1.);
#2442 = LINE('',#2443,#2444);
#2443 = CARTESIAN_POINT('',(59.73091178,7.63844548,0.E+000));
#2444 = VECTOR('',#2445,1.);
#2445 = DIRECTION('',(0.956941293638,-0.290281519442,0.E+000));
#2446 = PCURVE('',#2352,#2447);
#2447 = DEFINITIONAL_REPRESENTATION('',(#2448),#2452);
#2448 = LINE('',#2449,#2450);
#2449 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2450 = VECTOR('',#2451,1.);
#2451 = DIRECTION('',(1.,0.E+000));
#2452 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2453 = PCURVE('',#137,#2454);
#2454 = DEFINITIONAL_REPRESENTATION('',(#2455),#2459);
#2455 = LINE('',#2456,#2457);
#2456 = CARTESIAN_POINT('',(59.73091178,5.6384571));
#2457 = VECTOR('',#2458,1.);
#2458 = DIRECTION('',(0.956941293638,-0.290281519442));
#2459 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2460 = ADVANCED_FACE('',(#2461),#2428,.F.);
#2461 = FACE_BOUND('',#2462,.F.);
#2462 = EDGE_LOOP('',(#2463,#2464,#2487,#2515));
#2463 = ORIENTED_EDGE('',*,*,#2412,.T.);
#2464 = ORIENTED_EDGE('',*,*,#2465,.T.);
#2465 = EDGE_CURVE('',#2390,#2466,#2468,.T.);
#2466 = VERTEX_POINT('',#2467);
#2467 = CARTESIAN_POINT('',(60.49998314,7.4854689,1.72191934));
#2468 = SURFACE_CURVE('',#2469,(#2473,#2480),.PCURVE_S1.);
#2469 = LINE('',#2470,#2471);
#2470 = CARTESIAN_POINT('',(60.10791382,7.52408452,1.72191934));
#2471 = VECTOR('',#2472,1.);
#2472 = DIRECTION('',(0.995184686447,-9.801754873773E-002,0.E+000));
#2473 = PCURVE('',#2428,#2474);
#2474 = DEFINITIONAL_REPRESENTATION('',(#2475),#2479);
#2475 = LINE('',#2476,#2477);
#2476 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2477 = VECTOR('',#2478,1.);
#2478 = DIRECTION('',(1.,0.E+000));
#2479 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2480 = PCURVE('',#83,#2481);
#2481 = DEFINITIONAL_REPRESENTATION('',(#2482),#2486);
#2482 = LINE('',#2483,#2484);
#2483 = CARTESIAN_POINT('',(60.10791382,5.52409614));
#2484 = VECTOR('',#2485,1.);
#2485 = DIRECTION('',(0.995184686447,-9.801754873773E-002));
#2486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2487 = ORIENTED_EDGE('',*,*,#2488,.F.);
#2488 = EDGE_CURVE('',#2489,#2466,#2491,.T.);
#2489 = VERTEX_POINT('',#2490);
#2490 = CARTESIAN_POINT('',(60.49998314,7.4854689,0.E+000));
#2491 = SURFACE_CURVE('',#2492,(#2496,#2503),.PCURVE_S1.);
#2492 = LINE('',#2493,#2494);
#2493 = CARTESIAN_POINT('',(60.49998314,7.4854689,0.E+000));
#2494 = VECTOR('',#2495,1.);
#2495 = DIRECTION('',(0.E+000,0.E+000,1.));
#2496 = PCURVE('',#2428,#2497);
#2497 = DEFINITIONAL_REPRESENTATION('',(#2498),#2502);
#2498 = LINE('',#2499,#2500);
#2499 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#2500 = VECTOR('',#2501,1.);
#2501 = DIRECTION('',(0.E+000,-1.));
#2502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2503 = PCURVE('',#2504,#2509);
#2504 = PLANE('',#2505);
#2505 = AXIS2_PLACEMENT_3D('',#2506,#2507,#2508);
#2506 = CARTESIAN_POINT('',(60.49998314,7.4854689,0.E+000));
#2507 = DIRECTION('',(-1.,0.E+000,0.E+000));
#2508 = DIRECTION('',(0.E+000,1.,0.E+000));
#2509 = DEFINITIONAL_REPRESENTATION('',(#2510),#2514);
#2510 = LINE('',#2511,#2512);
#2511 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2512 = VECTOR('',#2513,1.);
#2513 = DIRECTION('',(0.E+000,-1.));
#2514 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2515 = ORIENTED_EDGE('',*,*,#2516,.F.);
#2516 = EDGE_CURVE('',#2413,#2489,#2517,.T.);
#2517 = SURFACE_CURVE('',#2518,(#2522,#2529),.PCURVE_S1.);
#2518 = LINE('',#2519,#2520);
#2519 = CARTESIAN_POINT('',(60.10791382,7.52408452,0.E+000));
#2520 = VECTOR('',#2521,1.);
#2521 = DIRECTION('',(0.995184686447,-9.801754873773E-002,0.E+000));
#2522 = PCURVE('',#2428,#2523);
#2523 = DEFINITIONAL_REPRESENTATION('',(#2524),#2528);
#2524 = LINE('',#2525,#2526);
#2525 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2526 = VECTOR('',#2527,1.);
#2527 = DIRECTION('',(1.,0.E+000));
#2528 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2529 = PCURVE('',#137,#2530);
#2530 = DEFINITIONAL_REPRESENTATION('',(#2531),#2535);
#2531 = LINE('',#2532,#2533);
#2532 = CARTESIAN_POINT('',(60.10791382,5.52409614));
#2533 = VECTOR('',#2534,1.);
#2534 = DIRECTION('',(0.995184686447,-9.801754873773E-002));
#2535 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2536 = ADVANCED_FACE('',(#2537),#2504,.F.);
#2537 = FACE_BOUND('',#2538,.F.);
#2538 = EDGE_LOOP('',(#2539,#2540,#2563,#2591));
#2539 = ORIENTED_EDGE('',*,*,#2488,.T.);
#2540 = ORIENTED_EDGE('',*,*,#2541,.T.);
#2541 = EDGE_CURVE('',#2466,#2542,#2544,.T.);
#2542 = VERTEX_POINT('',#2543);
#2543 = CARTESIAN_POINT('',(60.49998314,7.49514884,1.72191934));
#2544 = SURFACE_CURVE('',#2545,(#2549,#2556),.PCURVE_S1.);
#2545 = LINE('',#2546,#2547);
#2546 = CARTESIAN_POINT('',(60.49998314,7.4854689,1.72191934));
#2547 = VECTOR('',#2548,1.);
#2548 = DIRECTION('',(0.E+000,1.,0.E+000));
#2549 = PCURVE('',#2504,#2550);
#2550 = DEFINITIONAL_REPRESENTATION('',(#2551),#2555);
#2551 = LINE('',#2552,#2553);
#2552 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2553 = VECTOR('',#2554,1.);
#2554 = DIRECTION('',(1.,0.E+000));
#2555 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2556 = PCURVE('',#83,#2557);
#2557 = DEFINITIONAL_REPRESENTATION('',(#2558),#2562);
#2558 = LINE('',#2559,#2560);
#2559 = CARTESIAN_POINT('',(60.49998314,5.48548052));
#2560 = VECTOR('',#2561,1.);
#2561 = DIRECTION('',(0.E+000,1.));
#2562 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2563 = ORIENTED_EDGE('',*,*,#2564,.F.);
#2564 = EDGE_CURVE('',#2565,#2542,#2567,.T.);
#2565 = VERTEX_POINT('',#2566);
#2566 = CARTESIAN_POINT('',(60.49998314,7.49514884,0.E+000));
#2567 = SURFACE_CURVE('',#2568,(#2572,#2579),.PCURVE_S1.);
#2568 = LINE('',#2569,#2570);
#2569 = CARTESIAN_POINT('',(60.49998314,7.49514884,0.E+000));
#2570 = VECTOR('',#2571,1.);
#2571 = DIRECTION('',(0.E+000,0.E+000,1.));
#2572 = PCURVE('',#2504,#2573);
#2573 = DEFINITIONAL_REPRESENTATION('',(#2574),#2578);
#2574 = LINE('',#2575,#2576);
#2575 = CARTESIAN_POINT('',(9.679939999998E-003,0.E+000));
#2576 = VECTOR('',#2577,1.);
#2577 = DIRECTION('',(0.E+000,-1.));
#2578 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2579 = PCURVE('',#2580,#2585);
#2580 = PLANE('',#2581);
#2581 = AXIS2_PLACEMENT_3D('',#2582,#2583,#2584);
#2582 = CARTESIAN_POINT('',(60.49998314,7.49514884,0.E+000));
#2583 = DIRECTION('',(1.935471396762E-003,0.999998126973,-0.E+000));
#2584 = DIRECTION('',(0.999998126973,-1.935471396762E-003,0.E+000));
#2585 = DEFINITIONAL_REPRESENTATION('',(#2586),#2590);
#2586 = LINE('',#2587,#2588);
#2587 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2588 = VECTOR('',#2589,1.);
#2589 = DIRECTION('',(0.E+000,-1.));
#2590 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2591 = ORIENTED_EDGE('',*,*,#2592,.F.);
#2592 = EDGE_CURVE('',#2489,#2565,#2593,.T.);
#2593 = SURFACE_CURVE('',#2594,(#2598,#2605),.PCURVE_S1.);
#2594 = LINE('',#2595,#2596);
#2595 = CARTESIAN_POINT('',(60.49998314,7.4854689,0.E+000));
#2596 = VECTOR('',#2597,1.);
#2597 = DIRECTION('',(0.E+000,1.,0.E+000));
#2598 = PCURVE('',#2504,#2599);
#2599 = DEFINITIONAL_REPRESENTATION('',(#2600),#2604);
#2600 = LINE('',#2601,#2602);
#2601 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2602 = VECTOR('',#2603,1.);
#2603 = DIRECTION('',(1.,0.E+000));
#2604 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2605 = PCURVE('',#137,#2606);
#2606 = DEFINITIONAL_REPRESENTATION('',(#2607),#2611);
#2607 = LINE('',#2608,#2609);
#2608 = CARTESIAN_POINT('',(60.49998314,5.48548052));
#2609 = VECTOR('',#2610,1.);
#2610 = DIRECTION('',(0.E+000,1.));
#2611 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2612 = ADVANCED_FACE('',(#2613),#2580,.F.);
#2613 = FACE_BOUND('',#2614,.F.);
#2614 = EDGE_LOOP('',(#2615,#2616,#2639,#2667));
#2615 = ORIENTED_EDGE('',*,*,#2564,.T.);
#2616 = ORIENTED_EDGE('',*,*,#2617,.T.);
#2617 = EDGE_CURVE('',#2542,#2618,#2620,.T.);
#2618 = VERTEX_POINT('',#2619);
#2619 = CARTESIAN_POINT('',(65.499996,7.48547144,1.72191934));
#2620 = SURFACE_CURVE('',#2621,(#2625,#2632),.PCURVE_S1.);
#2621 = LINE('',#2622,#2623);
#2622 = CARTESIAN_POINT('',(60.49998314,7.49514884,1.72191934));
#2623 = VECTOR('',#2624,1.);
#2624 = DIRECTION('',(0.999998126973,-1.935471396762E-003,0.E+000));
#2625 = PCURVE('',#2580,#2626);
#2626 = DEFINITIONAL_REPRESENTATION('',(#2627),#2631);
#2627 = LINE('',#2628,#2629);
#2628 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2629 = VECTOR('',#2630,1.);
#2630 = DIRECTION('',(1.,0.E+000));
#2631 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2632 = PCURVE('',#83,#2633);
#2633 = DEFINITIONAL_REPRESENTATION('',(#2634),#2638);
#2634 = LINE('',#2635,#2636);
#2635 = CARTESIAN_POINT('',(60.49998314,5.49516046));
#2636 = VECTOR('',#2637,1.);
#2637 = DIRECTION('',(0.999998126973,-1.935471396762E-003));
#2638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2639 = ORIENTED_EDGE('',*,*,#2640,.F.);
#2640 = EDGE_CURVE('',#2641,#2618,#2643,.T.);
#2641 = VERTEX_POINT('',#2642);
#2642 = CARTESIAN_POINT('',(65.499996,7.48547144,0.E+000));
#2643 = SURFACE_CURVE('',#2644,(#2648,#2655),.PCURVE_S1.);
#2644 = LINE('',#2645,#2646);
#2645 = CARTESIAN_POINT('',(65.499996,7.48547144,0.E+000));
#2646 = VECTOR('',#2647,1.);
#2647 = DIRECTION('',(0.E+000,0.E+000,1.));
#2648 = PCURVE('',#2580,#2649);
#2649 = DEFINITIONAL_REPRESENTATION('',(#2650),#2654);
#2650 = LINE('',#2651,#2652);
#2651 = CARTESIAN_POINT('',(5.000022225174,0.E+000));
#2652 = VECTOR('',#2653,1.);
#2653 = DIRECTION('',(0.E+000,-1.));
#2654 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2655 = PCURVE('',#2656,#2661);
#2656 = PLANE('',#2657);
#2657 = AXIS2_PLACEMENT_3D('',#2658,#2659,#2660);
#2658 = CARTESIAN_POINT('',(65.499996,7.48547144,0.E+000));
#2659 = DIRECTION('',(-9.801754873775E-002,0.995184686447,0.E+000));
#2660 = DIRECTION('',(0.995184686447,9.801754873775E-002,0.E+000));
#2661 = DEFINITIONAL_REPRESENTATION('',(#2662),#2666);
#2662 = LINE('',#2663,#2664);
#2663 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2664 = VECTOR('',#2665,1.);
#2665 = DIRECTION('',(0.E+000,-1.));
#2666 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2667 = ORIENTED_EDGE('',*,*,#2668,.F.);
#2668 = EDGE_CURVE('',#2565,#2641,#2669,.T.);
#2669 = SURFACE_CURVE('',#2670,(#2674,#2681),.PCURVE_S1.);
#2670 = LINE('',#2671,#2672);
#2671 = CARTESIAN_POINT('',(60.49998314,7.49514884,0.E+000));
#2672 = VECTOR('',#2673,1.);
#2673 = DIRECTION('',(0.999998126973,-1.935471396762E-003,0.E+000));
#2674 = PCURVE('',#2580,#2675);
#2675 = DEFINITIONAL_REPRESENTATION('',(#2676),#2680);
#2676 = LINE('',#2677,#2678);
#2677 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2678 = VECTOR('',#2679,1.);
#2679 = DIRECTION('',(1.,0.E+000));
#2680 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2681 = PCURVE('',#137,#2682);
#2682 = DEFINITIONAL_REPRESENTATION('',(#2683),#2687);
#2683 = LINE('',#2684,#2685);
#2684 = CARTESIAN_POINT('',(60.49998314,5.49516046));
#2685 = VECTOR('',#2686,1.);
#2686 = DIRECTION('',(0.999998126973,-1.935471396762E-003));
#2687 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2688 = ADVANCED_FACE('',(#2689),#2656,.F.);
#2689 = FACE_BOUND('',#2690,.F.);
#2690 = EDGE_LOOP('',(#2691,#2692,#2715,#2743));
#2691 = ORIENTED_EDGE('',*,*,#2640,.T.);
#2692 = ORIENTED_EDGE('',*,*,#2693,.T.);
#2693 = EDGE_CURVE('',#2618,#2694,#2696,.T.);
#2694 = VERTEX_POINT('',#2695);
#2695 = CARTESIAN_POINT('',(65.89206532,7.52408706,1.72191934));
#2696 = SURFACE_CURVE('',#2697,(#2701,#2708),.PCURVE_S1.);
#2697 = LINE('',#2698,#2699);
#2698 = CARTESIAN_POINT('',(65.499996,7.48547144,1.72191934));
#2699 = VECTOR('',#2700,1.);
#2700 = DIRECTION('',(0.995184686447,9.801754873775E-002,0.E+000));
#2701 = PCURVE('',#2656,#2702);
#2702 = DEFINITIONAL_REPRESENTATION('',(#2703),#2707);
#2703 = LINE('',#2704,#2705);
#2704 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2705 = VECTOR('',#2706,1.);
#2706 = DIRECTION('',(1.,0.E+000));
#2707 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2708 = PCURVE('',#83,#2709);
#2709 = DEFINITIONAL_REPRESENTATION('',(#2710),#2714);
#2710 = LINE('',#2711,#2712);
#2711 = CARTESIAN_POINT('',(65.499996,5.48548306));
#2712 = VECTOR('',#2713,1.);
#2713 = DIRECTION('',(0.995184686447,9.801754873775E-002));
#2714 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2715 = ORIENTED_EDGE('',*,*,#2716,.F.);
#2716 = EDGE_CURVE('',#2717,#2694,#2719,.T.);
#2717 = VERTEX_POINT('',#2718);
#2718 = CARTESIAN_POINT('',(65.89206532,7.52408706,0.E+000));
#2719 = SURFACE_CURVE('',#2720,(#2724,#2731),.PCURVE_S1.);
#2720 = LINE('',#2721,#2722);
#2721 = CARTESIAN_POINT('',(65.89206532,7.52408706,0.E+000));
#2722 = VECTOR('',#2723,1.);
#2723 = DIRECTION('',(0.E+000,0.E+000,1.));
#2724 = PCURVE('',#2656,#2725);
#2725 = DEFINITIONAL_REPRESENTATION('',(#2726),#2730);
#2726 = LINE('',#2727,#2728);
#2727 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#2728 = VECTOR('',#2729,1.);
#2729 = DIRECTION('',(0.E+000,-1.));
#2730 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2731 = PCURVE('',#2732,#2737);
#2732 = PLANE('',#2733);
#2733 = AXIS2_PLACEMENT_3D('',#2734,#2735,#2736);
#2734 = CARTESIAN_POINT('',(65.89206532,7.52408706,0.E+000));
#2735 = DIRECTION('',(-0.290283310389,0.956940750365,0.E+000));
#2736 = DIRECTION('',(0.956940750365,0.290283310389,0.E+000));
#2737 = DEFINITIONAL_REPRESENTATION('',(#2738),#2742);
#2738 = LINE('',#2739,#2740);
#2739 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2740 = VECTOR('',#2741,1.);
#2741 = DIRECTION('',(0.E+000,-1.));
#2742 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2743 = ORIENTED_EDGE('',*,*,#2744,.F.);
#2744 = EDGE_CURVE('',#2641,#2717,#2745,.T.);
#2745 = SURFACE_CURVE('',#2746,(#2750,#2757),.PCURVE_S1.);
#2746 = LINE('',#2747,#2748);
#2747 = CARTESIAN_POINT('',(65.499996,7.48547144,0.E+000));
#2748 = VECTOR('',#2749,1.);
#2749 = DIRECTION('',(0.995184686447,9.801754873775E-002,0.E+000));
#2750 = PCURVE('',#2656,#2751);
#2751 = DEFINITIONAL_REPRESENTATION('',(#2752),#2756);
#2752 = LINE('',#2753,#2754);
#2753 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2754 = VECTOR('',#2755,1.);
#2755 = DIRECTION('',(1.,0.E+000));
#2756 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2757 = PCURVE('',#137,#2758);
#2758 = DEFINITIONAL_REPRESENTATION('',(#2759),#2763);
#2759 = LINE('',#2760,#2761);
#2760 = CARTESIAN_POINT('',(65.499996,5.48548306));
#2761 = VECTOR('',#2762,1.);
#2762 = DIRECTION('',(0.995184686447,9.801754873775E-002));
#2763 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2764 = ADVANCED_FACE('',(#2765),#2732,.F.);
#2765 = FACE_BOUND('',#2766,.F.);
#2766 = EDGE_LOOP('',(#2767,#2768,#2791,#2819));
#2767 = ORIENTED_EDGE('',*,*,#2716,.T.);
#2768 = ORIENTED_EDGE('',*,*,#2769,.T.);
#2769 = EDGE_CURVE('',#2694,#2770,#2772,.T.);
#2770 = VERTEX_POINT('',#2771);
#2771 = CARTESIAN_POINT('',(66.26906482,7.63844802,1.72191934));
#2772 = SURFACE_CURVE('',#2773,(#2777,#2784),.PCURVE_S1.);
#2773 = LINE('',#2774,#2775);
#2774 = CARTESIAN_POINT('',(65.89206532,7.52408706,1.72191934));
#2775 = VECTOR('',#2776,1.);
#2776 = DIRECTION('',(0.956940750365,0.290283310389,0.E+000));
#2777 = PCURVE('',#2732,#2778);
#2778 = DEFINITIONAL_REPRESENTATION('',(#2779),#2783);
#2779 = LINE('',#2780,#2781);
#2780 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2781 = VECTOR('',#2782,1.);
#2782 = DIRECTION('',(1.,0.E+000));
#2783 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2784 = PCURVE('',#83,#2785);
#2785 = DEFINITIONAL_REPRESENTATION('',(#2786),#2790);
#2786 = LINE('',#2787,#2788);
#2787 = CARTESIAN_POINT('',(65.89206532,5.52409868));
#2788 = VECTOR('',#2789,1.);
#2789 = DIRECTION('',(0.956940750365,0.290283310389));
#2790 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2791 = ORIENTED_EDGE('',*,*,#2792,.F.);
#2792 = EDGE_CURVE('',#2793,#2770,#2795,.T.);
#2793 = VERTEX_POINT('',#2794);
#2794 = CARTESIAN_POINT('',(66.26906482,7.63844802,0.E+000));
#2795 = SURFACE_CURVE('',#2796,(#2800,#2807),.PCURVE_S1.);
#2796 = LINE('',#2797,#2798);
#2797 = CARTESIAN_POINT('',(66.26906482,7.63844802,0.E+000));
#2798 = VECTOR('',#2799,1.);
#2799 = DIRECTION('',(0.E+000,0.E+000,1.));
#2800 = PCURVE('',#2732,#2801);
#2801 = DEFINITIONAL_REPRESENTATION('',(#2802),#2806);
#2802 = LINE('',#2803,#2804);
#2803 = CARTESIAN_POINT('',(0.393963262465,0.E+000));
#2804 = VECTOR('',#2805,1.);
#2805 = DIRECTION('',(0.E+000,-1.));
#2806 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2807 = PCURVE('',#2808,#2813);
#2808 = PLANE('',#2809);
#2809 = AXIS2_PLACEMENT_3D('',#2810,#2811,#2812);
#2810 = CARTESIAN_POINT('',(66.26906482,7.63844802,0.E+000));
#2811 = DIRECTION('',(-0.471397848625,0.881920670078,0.E+000));
#2812 = DIRECTION('',(0.881920670078,0.471397848625,0.E+000));
#2813 = DEFINITIONAL_REPRESENTATION('',(#2814),#2818);
#2814 = LINE('',#2815,#2816);
#2815 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2816 = VECTOR('',#2817,1.);
#2817 = DIRECTION('',(0.E+000,-1.));
#2818 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2819 = ORIENTED_EDGE('',*,*,#2820,.F.);
#2820 = EDGE_CURVE('',#2717,#2793,#2821,.T.);
#2821 = SURFACE_CURVE('',#2822,(#2826,#2833),.PCURVE_S1.);
#2822 = LINE('',#2823,#2824);
#2823 = CARTESIAN_POINT('',(65.89206532,7.52408706,0.E+000));
#2824 = VECTOR('',#2825,1.);
#2825 = DIRECTION('',(0.956940750365,0.290283310389,0.E+000));
#2826 = PCURVE('',#2732,#2827);
#2827 = DEFINITIONAL_REPRESENTATION('',(#2828),#2832);
#2828 = LINE('',#2829,#2830);
#2829 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2830 = VECTOR('',#2831,1.);
#2831 = DIRECTION('',(1.,0.E+000));
#2832 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2833 = PCURVE('',#137,#2834);
#2834 = DEFINITIONAL_REPRESENTATION('',(#2835),#2839);
#2835 = LINE('',#2836,#2837);
#2836 = CARTESIAN_POINT('',(65.89206532,5.52409868));
#2837 = VECTOR('',#2838,1.);
#2838 = DIRECTION('',(0.956940750365,0.290283310389));
#2839 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2840 = ADVANCED_FACE('',(#2841),#2808,.F.);
#2841 = FACE_BOUND('',#2842,.F.);
#2842 = EDGE_LOOP('',(#2843,#2844,#2867,#2895));
#2843 = ORIENTED_EDGE('',*,*,#2792,.T.);
#2844 = ORIENTED_EDGE('',*,*,#2845,.T.);
#2845 = EDGE_CURVE('',#2770,#2846,#2848,.T.);
#2846 = VERTEX_POINT('',#2847);
#2847 = CARTESIAN_POINT('',(66.61651142,7.82416266,1.72191934));
#2848 = SURFACE_CURVE('',#2849,(#2853,#2860),.PCURVE_S1.);
#2849 = LINE('',#2850,#2851);
#2850 = CARTESIAN_POINT('',(66.26906482,7.63844802,1.72191934));
#2851 = VECTOR('',#2852,1.);
#2852 = DIRECTION('',(0.881920670078,0.471397848625,0.E+000));
#2853 = PCURVE('',#2808,#2854);
#2854 = DEFINITIONAL_REPRESENTATION('',(#2855),#2859);
#2855 = LINE('',#2856,#2857);
#2856 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2857 = VECTOR('',#2858,1.);
#2858 = DIRECTION('',(1.,0.E+000));
#2859 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2860 = PCURVE('',#83,#2861);
#2861 = DEFINITIONAL_REPRESENTATION('',(#2862),#2866);
#2862 = LINE('',#2863,#2864);
#2863 = CARTESIAN_POINT('',(66.26906482,5.63845964));
#2864 = VECTOR('',#2865,1.);
#2865 = DIRECTION('',(0.881920670078,0.471397848625));
#2866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2867 = ORIENTED_EDGE('',*,*,#2868,.F.);
#2868 = EDGE_CURVE('',#2869,#2846,#2871,.T.);
#2869 = VERTEX_POINT('',#2870);
#2870 = CARTESIAN_POINT('',(66.61651142,7.82416266,0.E+000));
#2871 = SURFACE_CURVE('',#2872,(#2876,#2883),.PCURVE_S1.);
#2872 = LINE('',#2873,#2874);
#2873 = CARTESIAN_POINT('',(66.61651142,7.82416266,0.E+000));
#2874 = VECTOR('',#2875,1.);
#2875 = DIRECTION('',(0.E+000,0.E+000,1.));
#2876 = PCURVE('',#2808,#2877);
#2877 = DEFINITIONAL_REPRESENTATION('',(#2878),#2882);
#2878 = LINE('',#2879,#2880);
#2879 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#2880 = VECTOR('',#2881,1.);
#2881 = DIRECTION('',(0.E+000,-1.));
#2882 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2883 = PCURVE('',#2884,#2889);
#2884 = PLANE('',#2885);
#2885 = AXIS2_PLACEMENT_3D('',#2886,#2887,#2888);
#2886 = CARTESIAN_POINT('',(66.61651142,7.82416266,0.E+000));
#2887 = DIRECTION('',(-0.634390411475,0.773012810909,0.E+000));
#2888 = DIRECTION('',(0.773012810909,0.634390411475,0.E+000));
#2889 = DEFINITIONAL_REPRESENTATION('',(#2890),#2894);
#2890 = LINE('',#2891,#2892);
#2891 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2892 = VECTOR('',#2893,1.);
#2893 = DIRECTION('',(0.E+000,-1.));
#2894 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2895 = ORIENTED_EDGE('',*,*,#2896,.F.);
#2896 = EDGE_CURVE('',#2793,#2869,#2897,.T.);
#2897 = SURFACE_CURVE('',#2898,(#2902,#2909),.PCURVE_S1.);
#2898 = LINE('',#2899,#2900);
#2899 = CARTESIAN_POINT('',(66.26906482,7.63844802,0.E+000));
#2900 = VECTOR('',#2901,1.);
#2901 = DIRECTION('',(0.881920670078,0.471397848625,0.E+000));
#2902 = PCURVE('',#2808,#2903);
#2903 = DEFINITIONAL_REPRESENTATION('',(#2904),#2908);
#2904 = LINE('',#2905,#2906);
#2905 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2906 = VECTOR('',#2907,1.);
#2907 = DIRECTION('',(1.,0.E+000));
#2908 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2909 = PCURVE('',#137,#2910);
#2910 = DEFINITIONAL_REPRESENTATION('',(#2911),#2915);
#2911 = LINE('',#2912,#2913);
#2912 = CARTESIAN_POINT('',(66.26906482,5.63845964));
#2913 = VECTOR('',#2914,1.);
#2914 = DIRECTION('',(0.881920670078,0.471397848625));
#2915 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2916 = ADVANCED_FACE('',(#2917),#2884,.F.);
#2917 = FACE_BOUND('',#2918,.F.);
#2918 = EDGE_LOOP('',(#2919,#2920,#2943,#2971));
#2919 = ORIENTED_EDGE('',*,*,#2868,.T.);
#2920 = ORIENTED_EDGE('',*,*,#2921,.T.);
#2921 = EDGE_CURVE('',#2846,#2922,#2924,.T.);
#2922 = VERTEX_POINT('',#2923);
#2923 = CARTESIAN_POINT('',(66.92105234,8.07409104,1.72191934));
#2924 = SURFACE_CURVE('',#2925,(#2929,#2936),.PCURVE_S1.);
#2925 = LINE('',#2926,#2927);
#2926 = CARTESIAN_POINT('',(66.61651142,7.82416266,1.72191934));
#2927 = VECTOR('',#2928,1.);
#2928 = DIRECTION('',(0.773012810909,0.634390411475,0.E+000));
#2929 = PCURVE('',#2884,#2930);
#2930 = DEFINITIONAL_REPRESENTATION('',(#2931),#2935);
#2931 = LINE('',#2932,#2933);
#2932 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#2933 = VECTOR('',#2934,1.);
#2934 = DIRECTION('',(1.,0.E+000));
#2935 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2936 = PCURVE('',#83,#2937);
#2937 = DEFINITIONAL_REPRESENTATION('',(#2938),#2942);
#2938 = LINE('',#2939,#2940);
#2939 = CARTESIAN_POINT('',(66.61651142,5.82417428));
#2940 = VECTOR('',#2941,1.);
#2941 = DIRECTION('',(0.773012810909,0.634390411475));
#2942 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2943 = ORIENTED_EDGE('',*,*,#2944,.F.);
#2944 = EDGE_CURVE('',#2945,#2922,#2947,.T.);
#2945 = VERTEX_POINT('',#2946);
#2946 = CARTESIAN_POINT('',(66.92105234,8.07409104,0.E+000));
#2947 = SURFACE_CURVE('',#2948,(#2952,#2959),.PCURVE_S1.);
#2948 = LINE('',#2949,#2950);
#2949 = CARTESIAN_POINT('',(66.92105234,8.07409104,0.E+000));
#2950 = VECTOR('',#2951,1.);
#2951 = DIRECTION('',(0.E+000,0.E+000,1.));
#2952 = PCURVE('',#2884,#2953);
#2953 = DEFINITIONAL_REPRESENTATION('',(#2954),#2958);
#2954 = LINE('',#2955,#2956);
#2955 = CARTESIAN_POINT('',(0.393966200433,0.E+000));
#2956 = VECTOR('',#2957,1.);
#2957 = DIRECTION('',(0.E+000,-1.));
#2958 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2959 = PCURVE('',#2960,#2965);
#2960 = PLANE('',#2961);
#2961 = AXIS2_PLACEMENT_3D('',#2962,#2963,#2964);
#2962 = CARTESIAN_POINT('',(66.92105234,8.07409104,0.E+000));
#2963 = DIRECTION('',(-0.773012810909,0.634390411475,0.E+000));
#2964 = DIRECTION('',(0.634390411475,0.773012810909,0.E+000));
#2965 = DEFINITIONAL_REPRESENTATION('',(#2966),#2970);
#2966 = LINE('',#2967,#2968);
#2967 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2968 = VECTOR('',#2969,1.);
#2969 = DIRECTION('',(0.E+000,-1.));
#2970 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2971 = ORIENTED_EDGE('',*,*,#2972,.F.);
#2972 = EDGE_CURVE('',#2869,#2945,#2973,.T.);
#2973 = SURFACE_CURVE('',#2974,(#2978,#2985),.PCURVE_S1.);
#2974 = LINE('',#2975,#2976);
#2975 = CARTESIAN_POINT('',(66.61651142,7.82416266,0.E+000));
#2976 = VECTOR('',#2977,1.);
#2977 = DIRECTION('',(0.773012810909,0.634390411475,0.E+000));
#2978 = PCURVE('',#2884,#2979);
#2979 = DEFINITIONAL_REPRESENTATION('',(#2980),#2984);
#2980 = LINE('',#2981,#2982);
#2981 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2982 = VECTOR('',#2983,1.);
#2983 = DIRECTION('',(1.,0.E+000));
#2984 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2985 = PCURVE('',#137,#2986);
#2986 = DEFINITIONAL_REPRESENTATION('',(#2987),#2991);
#2987 = LINE('',#2988,#2989);
#2988 = CARTESIAN_POINT('',(66.61651142,5.82417428));
#2989 = VECTOR('',#2990,1.);
#2990 = DIRECTION('',(0.773012810909,0.634390411475));
#2991 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2992 = ADVANCED_FACE('',(#2993),#2960,.F.);
#2993 = FACE_BOUND('',#2994,.F.);
#2994 = EDGE_LOOP('',(#2995,#2996,#3019,#3047));
#2995 = ORIENTED_EDGE('',*,*,#2944,.T.);
#2996 = ORIENTED_EDGE('',*,*,#2997,.T.);
#2997 = EDGE_CURVE('',#2922,#2998,#3000,.T.);
#2998 = VERTEX_POINT('',#2999);
#2999 = CARTESIAN_POINT('',(67.17098072,8.37863196,1.72191934));
#3000 = SURFACE_CURVE('',#3001,(#3005,#3012),.PCURVE_S1.);
#3001 = LINE('',#3002,#3003);
#3002 = CARTESIAN_POINT('',(66.92105234,8.07409104,1.72191934));
#3003 = VECTOR('',#3004,1.);
#3004 = DIRECTION('',(0.634390411475,0.773012810909,0.E+000));
#3005 = PCURVE('',#2960,#3006);
#3006 = DEFINITIONAL_REPRESENTATION('',(#3007),#3011);
#3007 = LINE('',#3008,#3009);
#3008 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3009 = VECTOR('',#3010,1.);
#3010 = DIRECTION('',(1.,0.E+000));
#3011 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3012 = PCURVE('',#83,#3013);
#3013 = DEFINITIONAL_REPRESENTATION('',(#3014),#3018);
#3014 = LINE('',#3015,#3016);
#3015 = CARTESIAN_POINT('',(66.92105234,6.07410266));
#3016 = VECTOR('',#3017,1.);
#3017 = DIRECTION('',(0.634390411475,0.773012810909));
#3018 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3019 = ORIENTED_EDGE('',*,*,#3020,.F.);
#3020 = EDGE_CURVE('',#3021,#2998,#3023,.T.);
#3021 = VERTEX_POINT('',#3022);
#3022 = CARTESIAN_POINT('',(67.17098072,8.37863196,0.E+000));
#3023 = SURFACE_CURVE('',#3024,(#3028,#3035),.PCURVE_S1.);
#3024 = LINE('',#3025,#3026);
#3025 = CARTESIAN_POINT('',(67.17098072,8.37863196,0.E+000));
#3026 = VECTOR('',#3027,1.);
#3027 = DIRECTION('',(0.E+000,0.E+000,1.));
#3028 = PCURVE('',#2960,#3029);
#3029 = DEFINITIONAL_REPRESENTATION('',(#3030),#3034);
#3030 = LINE('',#3031,#3032);
#3031 = CARTESIAN_POINT('',(0.393966200433,0.E+000));
#3032 = VECTOR('',#3033,1.);
#3033 = DIRECTION('',(0.E+000,-1.));
#3034 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3035 = PCURVE('',#3036,#3041);
#3036 = PLANE('',#3037);
#3037 = AXIS2_PLACEMENT_3D('',#3038,#3039,#3040);
#3038 = CARTESIAN_POINT('',(67.17098072,8.37863196,0.E+000));
#3039 = DIRECTION('',(-0.881920670078,0.471397848625,0.E+000));
#3040 = DIRECTION('',(0.471397848625,0.881920670078,0.E+000));
#3041 = DEFINITIONAL_REPRESENTATION('',(#3042),#3046);
#3042 = LINE('',#3043,#3044);
#3043 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3044 = VECTOR('',#3045,1.);
#3045 = DIRECTION('',(0.E+000,-1.));
#3046 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3047 = ORIENTED_EDGE('',*,*,#3048,.F.);
#3048 = EDGE_CURVE('',#2945,#3021,#3049,.T.);
#3049 = SURFACE_CURVE('',#3050,(#3054,#3061),.PCURVE_S1.);
#3050 = LINE('',#3051,#3052);
#3051 = CARTESIAN_POINT('',(66.92105234,8.07409104,0.E+000));
#3052 = VECTOR('',#3053,1.);
#3053 = DIRECTION('',(0.634390411475,0.773012810909,0.E+000));
#3054 = PCURVE('',#2960,#3055);
#3055 = DEFINITIONAL_REPRESENTATION('',(#3056),#3060);
#3056 = LINE('',#3057,#3058);
#3057 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3058 = VECTOR('',#3059,1.);
#3059 = DIRECTION('',(1.,0.E+000));
#3060 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3061 = PCURVE('',#137,#3062);
#3062 = DEFINITIONAL_REPRESENTATION('',(#3063),#3067);
#3063 = LINE('',#3064,#3065);
#3064 = CARTESIAN_POINT('',(66.92105234,6.07410266));
#3065 = VECTOR('',#3066,1.);
#3066 = DIRECTION('',(0.634390411475,0.773012810909));
#3067 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3068 = ADVANCED_FACE('',(#3069),#3036,.F.);
#3069 = FACE_BOUND('',#3070,.F.);
#3070 = EDGE_LOOP('',(#3071,#3072,#3095,#3123));
#3071 = ORIENTED_EDGE('',*,*,#3020,.T.);
#3072 = ORIENTED_EDGE('',*,*,#3073,.T.);
#3073 = EDGE_CURVE('',#2998,#3074,#3076,.T.);
#3074 = VERTEX_POINT('',#3075);
#3075 = CARTESIAN_POINT('',(67.35669536,8.72607856,1.72191934));
#3076 = SURFACE_CURVE('',#3077,(#3081,#3088),.PCURVE_S1.);
#3077 = LINE('',#3078,#3079);
#3078 = CARTESIAN_POINT('',(67.17098072,8.37863196,1.72191934));
#3079 = VECTOR('',#3080,1.);
#3080 = DIRECTION('',(0.471397848625,0.881920670078,0.E+000));
#3081 = PCURVE('',#3036,#3082);
#3082 = DEFINITIONAL_REPRESENTATION('',(#3083),#3087);
#3083 = LINE('',#3084,#3085);
#3084 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3085 = VECTOR('',#3086,1.);
#3086 = DIRECTION('',(1.,0.E+000));
#3087 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3088 = PCURVE('',#83,#3089);
#3089 = DEFINITIONAL_REPRESENTATION('',(#3090),#3094);
#3090 = LINE('',#3091,#3092);
#3091 = CARTESIAN_POINT('',(67.17098072,6.37864358));
#3092 = VECTOR('',#3093,1.);
#3093 = DIRECTION('',(0.471397848625,0.881920670078));
#3094 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3095 = ORIENTED_EDGE('',*,*,#3096,.F.);
#3096 = EDGE_CURVE('',#3097,#3074,#3099,.T.);
#3097 = VERTEX_POINT('',#3098);
#3098 = CARTESIAN_POINT('',(67.35669536,8.72607856,0.E+000));
#3099 = SURFACE_CURVE('',#3100,(#3104,#3111),.PCURVE_S1.);
#3100 = LINE('',#3101,#3102);
#3101 = CARTESIAN_POINT('',(67.35669536,8.72607856,0.E+000));
#3102 = VECTOR('',#3103,1.);
#3103 = DIRECTION('',(0.E+000,0.E+000,1.));
#3104 = PCURVE('',#3036,#3105);
#3105 = DEFINITIONAL_REPRESENTATION('',(#3106),#3110);
#3106 = LINE('',#3107,#3108);
#3107 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#3108 = VECTOR('',#3109,1.);
#3109 = DIRECTION('',(0.E+000,-1.));
#3110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3111 = PCURVE('',#3112,#3117);
#3112 = PLANE('',#3113);
#3113 = AXIS2_PLACEMENT_3D('',#3114,#3115,#3116);
#3114 = CARTESIAN_POINT('',(67.35669536,8.72607856,0.E+000));
#3115 = DIRECTION('',(-0.956940750365,0.290283310389,0.E+000));
#3116 = DIRECTION('',(0.290283310389,0.956940750365,0.E+000));
#3117 = DEFINITIONAL_REPRESENTATION('',(#3118),#3122);
#3118 = LINE('',#3119,#3120);
#3119 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3120 = VECTOR('',#3121,1.);
#3121 = DIRECTION('',(0.E+000,-1.));
#3122 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3123 = ORIENTED_EDGE('',*,*,#3124,.F.);
#3124 = EDGE_CURVE('',#3021,#3097,#3125,.T.);
#3125 = SURFACE_CURVE('',#3126,(#3130,#3137),.PCURVE_S1.);
#3126 = LINE('',#3127,#3128);
#3127 = CARTESIAN_POINT('',(67.17098072,8.37863196,0.E+000));
#3128 = VECTOR('',#3129,1.);
#3129 = DIRECTION('',(0.471397848625,0.881920670078,0.E+000));
#3130 = PCURVE('',#3036,#3131);
#3131 = DEFINITIONAL_REPRESENTATION('',(#3132),#3136);
#3132 = LINE('',#3133,#3134);
#3133 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3134 = VECTOR('',#3135,1.);
#3135 = DIRECTION('',(1.,0.E+000));
#3136 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3137 = PCURVE('',#137,#3138);
#3138 = DEFINITIONAL_REPRESENTATION('',(#3139),#3143);
#3139 = LINE('',#3140,#3141);
#3140 = CARTESIAN_POINT('',(67.17098072,6.37864358));
#3141 = VECTOR('',#3142,1.);
#3142 = DIRECTION('',(0.471397848625,0.881920670078));
#3143 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3144 = ADVANCED_FACE('',(#3145),#3112,.F.);
#3145 = FACE_BOUND('',#3146,.F.);
#3146 = EDGE_LOOP('',(#3147,#3148,#3171,#3199));
#3147 = ORIENTED_EDGE('',*,*,#3096,.T.);
#3148 = ORIENTED_EDGE('',*,*,#3149,.T.);
#3149 = EDGE_CURVE('',#3074,#3150,#3152,.T.);
#3150 = VERTEX_POINT('',#3151);
#3151 = CARTESIAN_POINT('',(67.47105632,9.10307806,1.72191934));
#3152 = SURFACE_CURVE('',#3153,(#3157,#3164),.PCURVE_S1.);
#3153 = LINE('',#3154,#3155);
#3154 = CARTESIAN_POINT('',(67.35669536,8.72607856,1.72191934));
#3155 = VECTOR('',#3156,1.);
#3156 = DIRECTION('',(0.290283310389,0.956940750365,0.E+000));
#3157 = PCURVE('',#3112,#3158);
#3158 = DEFINITIONAL_REPRESENTATION('',(#3159),#3163);
#3159 = LINE('',#3160,#3161);
#3160 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3161 = VECTOR('',#3162,1.);
#3162 = DIRECTION('',(1.,0.E+000));
#3163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3164 = PCURVE('',#83,#3165);
#3165 = DEFINITIONAL_REPRESENTATION('',(#3166),#3170);
#3166 = LINE('',#3167,#3168);
#3167 = CARTESIAN_POINT('',(67.35669536,6.72609018));
#3168 = VECTOR('',#3169,1.);
#3169 = DIRECTION('',(0.290283310389,0.956940750365));
#3170 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3171 = ORIENTED_EDGE('',*,*,#3172,.F.);
#3172 = EDGE_CURVE('',#3173,#3150,#3175,.T.);
#3173 = VERTEX_POINT('',#3174);
#3174 = CARTESIAN_POINT('',(67.47105632,9.10307806,0.E+000));
#3175 = SURFACE_CURVE('',#3176,(#3180,#3187),.PCURVE_S1.);
#3176 = LINE('',#3177,#3178);
#3177 = CARTESIAN_POINT('',(67.47105632,9.10307806,0.E+000));
#3178 = VECTOR('',#3179,1.);
#3179 = DIRECTION('',(0.E+000,0.E+000,1.));
#3180 = PCURVE('',#3112,#3181);
#3181 = DEFINITIONAL_REPRESENTATION('',(#3182),#3186);
#3182 = LINE('',#3183,#3184);
#3183 = CARTESIAN_POINT('',(0.393963262465,0.E+000));
#3184 = VECTOR('',#3185,1.);
#3185 = DIRECTION('',(0.E+000,-1.));
#3186 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3187 = PCURVE('',#3188,#3193);
#3188 = PLANE('',#3189);
#3189 = AXIS2_PLACEMENT_3D('',#3190,#3191,#3192);
#3190 = CARTESIAN_POINT('',(67.47105632,9.10307806,0.E+000));
#3191 = DIRECTION('',(-0.995184686447,9.801754873774E-002,0.E+000));
#3192 = DIRECTION('',(9.801754873774E-002,0.995184686447,0.E+000));
#3193 = DEFINITIONAL_REPRESENTATION('',(#3194),#3198);
#3194 = LINE('',#3195,#3196);
#3195 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3196 = VECTOR('',#3197,1.);
#3197 = DIRECTION('',(0.E+000,-1.));
#3198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3199 = ORIENTED_EDGE('',*,*,#3200,.F.);
#3200 = EDGE_CURVE('',#3097,#3173,#3201,.T.);
#3201 = SURFACE_CURVE('',#3202,(#3206,#3213),.PCURVE_S1.);
#3202 = LINE('',#3203,#3204);
#3203 = CARTESIAN_POINT('',(67.35669536,8.72607856,0.E+000));
#3204 = VECTOR('',#3205,1.);
#3205 = DIRECTION('',(0.290283310389,0.956940750365,0.E+000));
#3206 = PCURVE('',#3112,#3207);
#3207 = DEFINITIONAL_REPRESENTATION('',(#3208),#3212);
#3208 = LINE('',#3209,#3210);
#3209 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3210 = VECTOR('',#3211,1.);
#3211 = DIRECTION('',(1.,0.E+000));
#3212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3213 = PCURVE('',#137,#3214);
#3214 = DEFINITIONAL_REPRESENTATION('',(#3215),#3219);
#3215 = LINE('',#3216,#3217);
#3216 = CARTESIAN_POINT('',(67.35669536,6.72609018));
#3217 = VECTOR('',#3218,1.);
#3218 = DIRECTION('',(0.290283310389,0.956940750365));
#3219 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3220 = ADVANCED_FACE('',(#3221),#3188,.F.);
#3221 = FACE_BOUND('',#3222,.F.);
#3222 = EDGE_LOOP('',(#3223,#3224,#3247,#3275));
#3223 = ORIENTED_EDGE('',*,*,#3172,.T.);
#3224 = ORIENTED_EDGE('',*,*,#3225,.T.);
#3225 = EDGE_CURVE('',#3150,#3226,#3228,.T.);
#3226 = VERTEX_POINT('',#3227);
#3227 = CARTESIAN_POINT('',(67.50967194,9.49514738,1.72191934));
#3228 = SURFACE_CURVE('',#3229,(#3233,#3240),.PCURVE_S1.);
#3229 = LINE('',#3230,#3231);
#3230 = CARTESIAN_POINT('',(67.47105632,9.10307806,1.72191934));
#3231 = VECTOR('',#3232,1.);
#3232 = DIRECTION('',(9.801754873774E-002,0.995184686447,0.E+000));
#3233 = PCURVE('',#3188,#3234);
#3234 = DEFINITIONAL_REPRESENTATION('',(#3235),#3239);
#3235 = LINE('',#3236,#3237);
#3236 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3237 = VECTOR('',#3238,1.);
#3238 = DIRECTION('',(1.,0.E+000));
#3239 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3240 = PCURVE('',#83,#3241);
#3241 = DEFINITIONAL_REPRESENTATION('',(#3242),#3246);
#3242 = LINE('',#3243,#3244);
#3243 = CARTESIAN_POINT('',(67.47105632,7.10308968));
#3244 = VECTOR('',#3245,1.);
#3245 = DIRECTION('',(9.801754873774E-002,0.995184686447));
#3246 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3247 = ORIENTED_EDGE('',*,*,#3248,.F.);
#3248 = EDGE_CURVE('',#3249,#3226,#3251,.T.);
#3249 = VERTEX_POINT('',#3250);
#3250 = CARTESIAN_POINT('',(67.50967194,9.49514738,0.E+000));
#3251 = SURFACE_CURVE('',#3252,(#3256,#3263),.PCURVE_S1.);
#3252 = LINE('',#3253,#3254);
#3253 = CARTESIAN_POINT('',(67.50967194,9.49514738,0.E+000));
#3254 = VECTOR('',#3255,1.);
#3255 = DIRECTION('',(0.E+000,0.E+000,1.));
#3256 = PCURVE('',#3188,#3257);
#3257 = DEFINITIONAL_REPRESENTATION('',(#3258),#3262);
#3258 = LINE('',#3259,#3260);
#3259 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#3260 = VECTOR('',#3261,1.);
#3261 = DIRECTION('',(0.E+000,-1.));
#3262 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3263 = PCURVE('',#3264,#3269);
#3264 = PLANE('',#3265);
#3265 = AXIS2_PLACEMENT_3D('',#3266,#3267,#3268);
#3266 = CARTESIAN_POINT('',(67.50967194,9.49514738,0.E+000));
#3267 = DIRECTION('',(-0.995184686447,-9.801754873774E-002,0.E+000));
#3268 = DIRECTION('',(-9.801754873774E-002,0.995184686447,0.E+000));
#3269 = DEFINITIONAL_REPRESENTATION('',(#3270),#3274);
#3270 = LINE('',#3271,#3272);
#3271 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3272 = VECTOR('',#3273,1.);
#3273 = DIRECTION('',(0.E+000,-1.));
#3274 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3275 = ORIENTED_EDGE('',*,*,#3276,.F.);
#3276 = EDGE_CURVE('',#3173,#3249,#3277,.T.);
#3277 = SURFACE_CURVE('',#3278,(#3282,#3289),.PCURVE_S1.);
#3278 = LINE('',#3279,#3280);
#3279 = CARTESIAN_POINT('',(67.47105632,9.10307806,0.E+000));
#3280 = VECTOR('',#3281,1.);
#3281 = DIRECTION('',(9.801754873774E-002,0.995184686447,0.E+000));
#3282 = PCURVE('',#3188,#3283);
#3283 = DEFINITIONAL_REPRESENTATION('',(#3284),#3288);
#3284 = LINE('',#3285,#3286);
#3285 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3286 = VECTOR('',#3287,1.);
#3287 = DIRECTION('',(1.,0.E+000));
#3288 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3289 = PCURVE('',#137,#3290);
#3290 = DEFINITIONAL_REPRESENTATION('',(#3291),#3295);
#3291 = LINE('',#3292,#3293);
#3292 = CARTESIAN_POINT('',(67.47105632,7.10308968));
#3293 = VECTOR('',#3294,1.);
#3294 = DIRECTION('',(9.801754873774E-002,0.995184686447));
#3295 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3296 = ADVANCED_FACE('',(#3297),#3264,.F.);
#3297 = FACE_BOUND('',#3298,.F.);
#3298 = EDGE_LOOP('',(#3299,#3300,#3323,#3351));
#3299 = ORIENTED_EDGE('',*,*,#3248,.T.);
#3300 = ORIENTED_EDGE('',*,*,#3301,.T.);
#3301 = EDGE_CURVE('',#3226,#3302,#3304,.T.);
#3302 = VERTEX_POINT('',#3303);
#3303 = CARTESIAN_POINT('',(67.47105632,9.8872167,1.72191934));
#3304 = SURFACE_CURVE('',#3305,(#3309,#3316),.PCURVE_S1.);
#3305 = LINE('',#3306,#3307);
#3306 = CARTESIAN_POINT('',(67.50967194,9.49514738,1.72191934));
#3307 = VECTOR('',#3308,1.);
#3308 = DIRECTION('',(-9.801754873774E-002,0.995184686447,0.E+000));
#3309 = PCURVE('',#3264,#3310);
#3310 = DEFINITIONAL_REPRESENTATION('',(#3311),#3315);
#3311 = LINE('',#3312,#3313);
#3312 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3313 = VECTOR('',#3314,1.);
#3314 = DIRECTION('',(1.,0.E+000));
#3315 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3316 = PCURVE('',#83,#3317);
#3317 = DEFINITIONAL_REPRESENTATION('',(#3318),#3322);
#3318 = LINE('',#3319,#3320);
#3319 = CARTESIAN_POINT('',(67.50967194,7.495159));
#3320 = VECTOR('',#3321,1.);
#3321 = DIRECTION('',(-9.801754873774E-002,0.995184686447));
#3322 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3323 = ORIENTED_EDGE('',*,*,#3324,.F.);
#3324 = EDGE_CURVE('',#3325,#3302,#3327,.T.);
#3325 = VERTEX_POINT('',#3326);
#3326 = CARTESIAN_POINT('',(67.47105632,9.8872167,0.E+000));
#3327 = SURFACE_CURVE('',#3328,(#3332,#3339),.PCURVE_S1.);
#3328 = LINE('',#3329,#3330);
#3329 = CARTESIAN_POINT('',(67.47105632,9.8872167,0.E+000));
#3330 = VECTOR('',#3331,1.);
#3331 = DIRECTION('',(0.E+000,0.E+000,1.));
#3332 = PCURVE('',#3264,#3333);
#3333 = DEFINITIONAL_REPRESENTATION('',(#3334),#3338);
#3334 = LINE('',#3335,#3336);
#3335 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#3336 = VECTOR('',#3337,1.);
#3337 = DIRECTION('',(0.E+000,-1.));
#3338 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3339 = PCURVE('',#3340,#3345);
#3340 = PLANE('',#3341);
#3341 = AXIS2_PLACEMENT_3D('',#3342,#3343,#3344);
#3342 = CARTESIAN_POINT('',(67.47105632,9.8872167,0.E+000));
#3343 = DIRECTION('',(-0.956940750365,-0.290283310389,0.E+000));
#3344 = DIRECTION('',(-0.290283310389,0.956940750365,0.E+000));
#3345 = DEFINITIONAL_REPRESENTATION('',(#3346),#3350);
#3346 = LINE('',#3347,#3348);
#3347 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3348 = VECTOR('',#3349,1.);
#3349 = DIRECTION('',(0.E+000,-1.));
#3350 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3351 = ORIENTED_EDGE('',*,*,#3352,.F.);
#3352 = EDGE_CURVE('',#3249,#3325,#3353,.T.);
#3353 = SURFACE_CURVE('',#3354,(#3358,#3365),.PCURVE_S1.);
#3354 = LINE('',#3355,#3356);
#3355 = CARTESIAN_POINT('',(67.50967194,9.49514738,0.E+000));
#3356 = VECTOR('',#3357,1.);
#3357 = DIRECTION('',(-9.801754873774E-002,0.995184686447,0.E+000));
#3358 = PCURVE('',#3264,#3359);
#3359 = DEFINITIONAL_REPRESENTATION('',(#3360),#3364);
#3360 = LINE('',#3361,#3362);
#3361 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3362 = VECTOR('',#3363,1.);
#3363 = DIRECTION('',(1.,0.E+000));
#3364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3365 = PCURVE('',#137,#3366);
#3366 = DEFINITIONAL_REPRESENTATION('',(#3367),#3371);
#3367 = LINE('',#3368,#3369);
#3368 = CARTESIAN_POINT('',(67.50967194,7.495159));
#3369 = VECTOR('',#3370,1.);
#3370 = DIRECTION('',(-9.801754873774E-002,0.995184686447));
#3371 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3372 = ADVANCED_FACE('',(#3373),#3340,.F.);
#3373 = FACE_BOUND('',#3374,.F.);
#3374 = EDGE_LOOP('',(#3375,#3376,#3399,#3427));
#3375 = ORIENTED_EDGE('',*,*,#3324,.T.);
#3376 = ORIENTED_EDGE('',*,*,#3377,.T.);
#3377 = EDGE_CURVE('',#3302,#3378,#3380,.T.);
#3378 = VERTEX_POINT('',#3379);
#3379 = CARTESIAN_POINT('',(67.35669536,10.2642162,1.72191934));
#3380 = SURFACE_CURVE('',#3381,(#3385,#3392),.PCURVE_S1.);
#3381 = LINE('',#3382,#3383);
#3382 = CARTESIAN_POINT('',(67.47105632,9.8872167,1.72191934));
#3383 = VECTOR('',#3384,1.);
#3384 = DIRECTION('',(-0.290283310389,0.956940750365,0.E+000));
#3385 = PCURVE('',#3340,#3386);
#3386 = DEFINITIONAL_REPRESENTATION('',(#3387),#3391);
#3387 = LINE('',#3388,#3389);
#3388 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3389 = VECTOR('',#3390,1.);
#3390 = DIRECTION('',(1.,0.E+000));
#3391 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3392 = PCURVE('',#83,#3393);
#3393 = DEFINITIONAL_REPRESENTATION('',(#3394),#3398);
#3394 = LINE('',#3395,#3396);
#3395 = CARTESIAN_POINT('',(67.47105632,7.88722832));
#3396 = VECTOR('',#3397,1.);
#3397 = DIRECTION('',(-0.290283310389,0.956940750365));
#3398 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3399 = ORIENTED_EDGE('',*,*,#3400,.F.);
#3400 = EDGE_CURVE('',#3401,#3378,#3403,.T.);
#3401 = VERTEX_POINT('',#3402);
#3402 = CARTESIAN_POINT('',(67.35669536,10.2642162,0.E+000));
#3403 = SURFACE_CURVE('',#3404,(#3408,#3415),.PCURVE_S1.);
#3404 = LINE('',#3405,#3406);
#3405 = CARTESIAN_POINT('',(67.35669536,10.2642162,0.E+000));
#3406 = VECTOR('',#3407,1.);
#3407 = DIRECTION('',(0.E+000,0.E+000,1.));
#3408 = PCURVE('',#3340,#3409);
#3409 = DEFINITIONAL_REPRESENTATION('',(#3410),#3414);
#3410 = LINE('',#3411,#3412);
#3411 = CARTESIAN_POINT('',(0.393963262465,0.E+000));
#3412 = VECTOR('',#3413,1.);
#3413 = DIRECTION('',(0.E+000,-1.));
#3414 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3415 = PCURVE('',#3416,#3421);
#3416 = PLANE('',#3417);
#3417 = AXIS2_PLACEMENT_3D('',#3418,#3419,#3420);
#3418 = CARTESIAN_POINT('',(67.35669536,10.2642162,0.E+000));
#3419 = DIRECTION('',(-0.881920670078,-0.471397848625,0.E+000));
#3420 = DIRECTION('',(-0.471397848625,0.881920670078,0.E+000));
#3421 = DEFINITIONAL_REPRESENTATION('',(#3422),#3426);
#3422 = LINE('',#3423,#3424);
#3423 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3424 = VECTOR('',#3425,1.);
#3425 = DIRECTION('',(0.E+000,-1.));
#3426 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3427 = ORIENTED_EDGE('',*,*,#3428,.F.);
#3428 = EDGE_CURVE('',#3325,#3401,#3429,.T.);
#3429 = SURFACE_CURVE('',#3430,(#3434,#3441),.PCURVE_S1.);
#3430 = LINE('',#3431,#3432);
#3431 = CARTESIAN_POINT('',(67.47105632,9.8872167,0.E+000));
#3432 = VECTOR('',#3433,1.);
#3433 = DIRECTION('',(-0.290283310389,0.956940750365,0.E+000));
#3434 = PCURVE('',#3340,#3435);
#3435 = DEFINITIONAL_REPRESENTATION('',(#3436),#3440);
#3436 = LINE('',#3437,#3438);
#3437 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3438 = VECTOR('',#3439,1.);
#3439 = DIRECTION('',(1.,0.E+000));
#3440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3441 = PCURVE('',#137,#3442);
#3442 = DEFINITIONAL_REPRESENTATION('',(#3443),#3447);
#3443 = LINE('',#3444,#3445);
#3444 = CARTESIAN_POINT('',(67.47105632,7.88722832));
#3445 = VECTOR('',#3446,1.);
#3446 = DIRECTION('',(-0.290283310389,0.956940750365));
#3447 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3448 = ADVANCED_FACE('',(#3449),#3416,.F.);
#3449 = FACE_BOUND('',#3450,.F.);
#3450 = EDGE_LOOP('',(#3451,#3452,#3475,#3503));
#3451 = ORIENTED_EDGE('',*,*,#3400,.T.);
#3452 = ORIENTED_EDGE('',*,*,#3453,.T.);
#3453 = EDGE_CURVE('',#3378,#3454,#3456,.T.);
#3454 = VERTEX_POINT('',#3455);
#3455 = CARTESIAN_POINT('',(67.17098072,10.6116628,1.72191934));
#3456 = SURFACE_CURVE('',#3457,(#3461,#3468),.PCURVE_S1.);
#3457 = LINE('',#3458,#3459);
#3458 = CARTESIAN_POINT('',(67.35669536,10.2642162,1.72191934));
#3459 = VECTOR('',#3460,1.);
#3460 = DIRECTION('',(-0.471397848625,0.881920670078,0.E+000));
#3461 = PCURVE('',#3416,#3462);
#3462 = DEFINITIONAL_REPRESENTATION('',(#3463),#3467);
#3463 = LINE('',#3464,#3465);
#3464 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3465 = VECTOR('',#3466,1.);
#3466 = DIRECTION('',(1.,0.E+000));
#3467 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3468 = PCURVE('',#83,#3469);
#3469 = DEFINITIONAL_REPRESENTATION('',(#3470),#3474);
#3470 = LINE('',#3471,#3472);
#3471 = CARTESIAN_POINT('',(67.35669536,8.26422782));
#3472 = VECTOR('',#3473,1.);
#3473 = DIRECTION('',(-0.471397848625,0.881920670078));
#3474 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3475 = ORIENTED_EDGE('',*,*,#3476,.F.);
#3476 = EDGE_CURVE('',#3477,#3454,#3479,.T.);
#3477 = VERTEX_POINT('',#3478);
#3478 = CARTESIAN_POINT('',(67.17098072,10.6116628,0.E+000));
#3479 = SURFACE_CURVE('',#3480,(#3484,#3491),.PCURVE_S1.);
#3480 = LINE('',#3481,#3482);
#3481 = CARTESIAN_POINT('',(67.17098072,10.6116628,0.E+000));
#3482 = VECTOR('',#3483,1.);
#3483 = DIRECTION('',(0.E+000,0.E+000,1.));
#3484 = PCURVE('',#3416,#3485);
#3485 = DEFINITIONAL_REPRESENTATION('',(#3486),#3490);
#3486 = LINE('',#3487,#3488);
#3487 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#3488 = VECTOR('',#3489,1.);
#3489 = DIRECTION('',(0.E+000,-1.));
#3490 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3491 = PCURVE('',#3492,#3497);
#3492 = PLANE('',#3493);
#3493 = AXIS2_PLACEMENT_3D('',#3494,#3495,#3496);
#3494 = CARTESIAN_POINT('',(67.17098072,10.6116628,0.E+000));
#3495 = DIRECTION('',(-0.773012810909,-0.634390411475,0.E+000));
#3496 = DIRECTION('',(-0.634390411475,0.773012810909,0.E+000));
#3497 = DEFINITIONAL_REPRESENTATION('',(#3498),#3502);
#3498 = LINE('',#3499,#3500);
#3499 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3500 = VECTOR('',#3501,1.);
#3501 = DIRECTION('',(0.E+000,-1.));
#3502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3503 = ORIENTED_EDGE('',*,*,#3504,.F.);
#3504 = EDGE_CURVE('',#3401,#3477,#3505,.T.);
#3505 = SURFACE_CURVE('',#3506,(#3510,#3517),.PCURVE_S1.);
#3506 = LINE('',#3507,#3508);
#3507 = CARTESIAN_POINT('',(67.35669536,10.2642162,0.E+000));
#3508 = VECTOR('',#3509,1.);
#3509 = DIRECTION('',(-0.471397848625,0.881920670078,0.E+000));
#3510 = PCURVE('',#3416,#3511);
#3511 = DEFINITIONAL_REPRESENTATION('',(#3512),#3516);
#3512 = LINE('',#3513,#3514);
#3513 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3514 = VECTOR('',#3515,1.);
#3515 = DIRECTION('',(1.,0.E+000));
#3516 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3517 = PCURVE('',#137,#3518);
#3518 = DEFINITIONAL_REPRESENTATION('',(#3519),#3523);
#3519 = LINE('',#3520,#3521);
#3520 = CARTESIAN_POINT('',(67.35669536,8.26422782));
#3521 = VECTOR('',#3522,1.);
#3522 = DIRECTION('',(-0.471397848625,0.881920670078));
#3523 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3524 = ADVANCED_FACE('',(#3525),#3492,.F.);
#3525 = FACE_BOUND('',#3526,.F.);
#3526 = EDGE_LOOP('',(#3527,#3528,#3551,#3579));
#3527 = ORIENTED_EDGE('',*,*,#3476,.T.);
#3528 = ORIENTED_EDGE('',*,*,#3529,.T.);
#3529 = EDGE_CURVE('',#3454,#3530,#3532,.T.);
#3530 = VERTEX_POINT('',#3531);
#3531 = CARTESIAN_POINT('',(66.92105234,10.91620372,1.72191934));
#3532 = SURFACE_CURVE('',#3533,(#3537,#3544),.PCURVE_S1.);
#3533 = LINE('',#3534,#3535);
#3534 = CARTESIAN_POINT('',(67.17098072,10.6116628,1.72191934));
#3535 = VECTOR('',#3536,1.);
#3536 = DIRECTION('',(-0.634390411475,0.773012810909,0.E+000));
#3537 = PCURVE('',#3492,#3538);
#3538 = DEFINITIONAL_REPRESENTATION('',(#3539),#3543);
#3539 = LINE('',#3540,#3541);
#3540 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3541 = VECTOR('',#3542,1.);
#3542 = DIRECTION('',(1.,0.E+000));
#3543 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3544 = PCURVE('',#83,#3545);
#3545 = DEFINITIONAL_REPRESENTATION('',(#3546),#3550);
#3546 = LINE('',#3547,#3548);
#3547 = CARTESIAN_POINT('',(67.17098072,8.61167442));
#3548 = VECTOR('',#3549,1.);
#3549 = DIRECTION('',(-0.634390411475,0.773012810909));
#3550 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3551 = ORIENTED_EDGE('',*,*,#3552,.F.);
#3552 = EDGE_CURVE('',#3553,#3530,#3555,.T.);
#3553 = VERTEX_POINT('',#3554);
#3554 = CARTESIAN_POINT('',(66.92105234,10.91620372,0.E+000));
#3555 = SURFACE_CURVE('',#3556,(#3560,#3567),.PCURVE_S1.);
#3556 = LINE('',#3557,#3558);
#3557 = CARTESIAN_POINT('',(66.92105234,10.91620372,0.E+000));
#3558 = VECTOR('',#3559,1.);
#3559 = DIRECTION('',(0.E+000,0.E+000,1.));
#3560 = PCURVE('',#3492,#3561);
#3561 = DEFINITIONAL_REPRESENTATION('',(#3562),#3566);
#3562 = LINE('',#3563,#3564);
#3563 = CARTESIAN_POINT('',(0.393966200433,0.E+000));
#3564 = VECTOR('',#3565,1.);
#3565 = DIRECTION('',(0.E+000,-1.));
#3566 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3567 = PCURVE('',#3568,#3573);
#3568 = PLANE('',#3569);
#3569 = AXIS2_PLACEMENT_3D('',#3570,#3571,#3572);
#3570 = CARTESIAN_POINT('',(66.92105234,10.91620372,0.E+000));
#3571 = DIRECTION('',(-0.634390411475,-0.773012810909,0.E+000));
#3572 = DIRECTION('',(-0.773012810909,0.634390411475,0.E+000));
#3573 = DEFINITIONAL_REPRESENTATION('',(#3574),#3578);
#3574 = LINE('',#3575,#3576);
#3575 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3576 = VECTOR('',#3577,1.);
#3577 = DIRECTION('',(0.E+000,-1.));
#3578 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3579 = ORIENTED_EDGE('',*,*,#3580,.F.);
#3580 = EDGE_CURVE('',#3477,#3553,#3581,.T.);
#3581 = SURFACE_CURVE('',#3582,(#3586,#3593),.PCURVE_S1.);
#3582 = LINE('',#3583,#3584);
#3583 = CARTESIAN_POINT('',(67.17098072,10.6116628,0.E+000));
#3584 = VECTOR('',#3585,1.);
#3585 = DIRECTION('',(-0.634390411475,0.773012810909,0.E+000));
#3586 = PCURVE('',#3492,#3587);
#3587 = DEFINITIONAL_REPRESENTATION('',(#3588),#3592);
#3588 = LINE('',#3589,#3590);
#3589 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3590 = VECTOR('',#3591,1.);
#3591 = DIRECTION('',(1.,0.E+000));
#3592 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3593 = PCURVE('',#137,#3594);
#3594 = DEFINITIONAL_REPRESENTATION('',(#3595),#3599);
#3595 = LINE('',#3596,#3597);
#3596 = CARTESIAN_POINT('',(67.17098072,8.61167442));
#3597 = VECTOR('',#3598,1.);
#3598 = DIRECTION('',(-0.634390411475,0.773012810909));
#3599 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3600 = ADVANCED_FACE('',(#3601),#3568,.F.);
#3601 = FACE_BOUND('',#3602,.F.);
#3602 = EDGE_LOOP('',(#3603,#3604,#3627,#3655));
#3603 = ORIENTED_EDGE('',*,*,#3552,.T.);
#3604 = ORIENTED_EDGE('',*,*,#3605,.T.);
#3605 = EDGE_CURVE('',#3530,#3606,#3608,.T.);
#3606 = VERTEX_POINT('',#3607);
#3607 = CARTESIAN_POINT('',(66.61651142,11.1661321,1.72191934));
#3608 = SURFACE_CURVE('',#3609,(#3613,#3620),.PCURVE_S1.);
#3609 = LINE('',#3610,#3611);
#3610 = CARTESIAN_POINT('',(66.92105234,10.91620372,1.72191934));
#3611 = VECTOR('',#3612,1.);
#3612 = DIRECTION('',(-0.773012810909,0.634390411475,0.E+000));
#3613 = PCURVE('',#3568,#3614);
#3614 = DEFINITIONAL_REPRESENTATION('',(#3615),#3619);
#3615 = LINE('',#3616,#3617);
#3616 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3617 = VECTOR('',#3618,1.);
#3618 = DIRECTION('',(1.,0.E+000));
#3619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3620 = PCURVE('',#83,#3621);
#3621 = DEFINITIONAL_REPRESENTATION('',(#3622),#3626);
#3622 = LINE('',#3623,#3624);
#3623 = CARTESIAN_POINT('',(66.92105234,8.91621534));
#3624 = VECTOR('',#3625,1.);
#3625 = DIRECTION('',(-0.773012810909,0.634390411475));
#3626 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3627 = ORIENTED_EDGE('',*,*,#3628,.F.);
#3628 = EDGE_CURVE('',#3629,#3606,#3631,.T.);
#3629 = VERTEX_POINT('',#3630);
#3630 = CARTESIAN_POINT('',(66.61651142,11.1661321,0.E+000));
#3631 = SURFACE_CURVE('',#3632,(#3636,#3643),.PCURVE_S1.);
#3632 = LINE('',#3633,#3634);
#3633 = CARTESIAN_POINT('',(66.61651142,11.1661321,0.E+000));
#3634 = VECTOR('',#3635,1.);
#3635 = DIRECTION('',(0.E+000,0.E+000,1.));
#3636 = PCURVE('',#3568,#3637);
#3637 = DEFINITIONAL_REPRESENTATION('',(#3638),#3642);
#3638 = LINE('',#3639,#3640);
#3639 = CARTESIAN_POINT('',(0.393966200433,0.E+000));
#3640 = VECTOR('',#3641,1.);
#3641 = DIRECTION('',(0.E+000,-1.));
#3642 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3643 = PCURVE('',#3644,#3649);
#3644 = PLANE('',#3645);
#3645 = AXIS2_PLACEMENT_3D('',#3646,#3647,#3648);
#3646 = CARTESIAN_POINT('',(66.61651142,11.1661321,0.E+000));
#3647 = DIRECTION('',(-0.471397848625,-0.881920670078,0.E+000));
#3648 = DIRECTION('',(-0.881920670078,0.471397848625,0.E+000));
#3649 = DEFINITIONAL_REPRESENTATION('',(#3650),#3654);
#3650 = LINE('',#3651,#3652);
#3651 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3652 = VECTOR('',#3653,1.);
#3653 = DIRECTION('',(0.E+000,-1.));
#3654 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3655 = ORIENTED_EDGE('',*,*,#3656,.F.);
#3656 = EDGE_CURVE('',#3553,#3629,#3657,.T.);
#3657 = SURFACE_CURVE('',#3658,(#3662,#3669),.PCURVE_S1.);
#3658 = LINE('',#3659,#3660);
#3659 = CARTESIAN_POINT('',(66.92105234,10.91620372,0.E+000));
#3660 = VECTOR('',#3661,1.);
#3661 = DIRECTION('',(-0.773012810909,0.634390411475,0.E+000));
#3662 = PCURVE('',#3568,#3663);
#3663 = DEFINITIONAL_REPRESENTATION('',(#3664),#3668);
#3664 = LINE('',#3665,#3666);
#3665 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3666 = VECTOR('',#3667,1.);
#3667 = DIRECTION('',(1.,0.E+000));
#3668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3669 = PCURVE('',#137,#3670);
#3670 = DEFINITIONAL_REPRESENTATION('',(#3671),#3675);
#3671 = LINE('',#3672,#3673);
#3672 = CARTESIAN_POINT('',(66.92105234,8.91621534));
#3673 = VECTOR('',#3674,1.);
#3674 = DIRECTION('',(-0.773012810909,0.634390411475));
#3675 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3676 = ADVANCED_FACE('',(#3677),#3644,.F.);
#3677 = FACE_BOUND('',#3678,.F.);
#3678 = EDGE_LOOP('',(#3679,#3680,#3703,#3731));
#3679 = ORIENTED_EDGE('',*,*,#3628,.T.);
#3680 = ORIENTED_EDGE('',*,*,#3681,.T.);
#3681 = EDGE_CURVE('',#3606,#3682,#3684,.T.);
#3682 = VERTEX_POINT('',#3683);
#3683 = CARTESIAN_POINT('',(66.26906482,11.35184674,1.72191934));
#3684 = SURFACE_CURVE('',#3685,(#3689,#3696),.PCURVE_S1.);
#3685 = LINE('',#3686,#3687);
#3686 = CARTESIAN_POINT('',(66.61651142,11.1661321,1.72191934));
#3687 = VECTOR('',#3688,1.);
#3688 = DIRECTION('',(-0.881920670078,0.471397848625,0.E+000));
#3689 = PCURVE('',#3644,#3690);
#3690 = DEFINITIONAL_REPRESENTATION('',(#3691),#3695);
#3691 = LINE('',#3692,#3693);
#3692 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3693 = VECTOR('',#3694,1.);
#3694 = DIRECTION('',(1.,0.E+000));
#3695 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3696 = PCURVE('',#83,#3697);
#3697 = DEFINITIONAL_REPRESENTATION('',(#3698),#3702);
#3698 = LINE('',#3699,#3700);
#3699 = CARTESIAN_POINT('',(66.61651142,9.16614372));
#3700 = VECTOR('',#3701,1.);
#3701 = DIRECTION('',(-0.881920670078,0.471397848625));
#3702 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3703 = ORIENTED_EDGE('',*,*,#3704,.F.);
#3704 = EDGE_CURVE('',#3705,#3682,#3707,.T.);
#3705 = VERTEX_POINT('',#3706);
#3706 = CARTESIAN_POINT('',(66.26906482,11.35184674,0.E+000));
#3707 = SURFACE_CURVE('',#3708,(#3712,#3719),.PCURVE_S1.);
#3708 = LINE('',#3709,#3710);
#3709 = CARTESIAN_POINT('',(66.26906482,11.35184674,0.E+000));
#3710 = VECTOR('',#3711,1.);
#3711 = DIRECTION('',(0.E+000,0.E+000,1.));
#3712 = PCURVE('',#3644,#3713);
#3713 = DEFINITIONAL_REPRESENTATION('',(#3714),#3718);
#3714 = LINE('',#3715,#3716);
#3715 = CARTESIAN_POINT('',(0.393965820043,0.E+000));
#3716 = VECTOR('',#3717,1.);
#3717 = DIRECTION('',(0.E+000,-1.));
#3718 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3719 = PCURVE('',#3720,#3725);
#3720 = PLANE('',#3721);
#3721 = AXIS2_PLACEMENT_3D('',#3722,#3723,#3724);
#3722 = CARTESIAN_POINT('',(66.26906482,11.35184674,0.E+000));
#3723 = DIRECTION('',(-0.290283310389,-0.956940750365,0.E+000));
#3724 = DIRECTION('',(-0.956940750365,0.290283310389,0.E+000));
#3725 = DEFINITIONAL_REPRESENTATION('',(#3726),#3730);
#3726 = LINE('',#3727,#3728);
#3727 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3728 = VECTOR('',#3729,1.);
#3729 = DIRECTION('',(0.E+000,-1.));
#3730 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3731 = ORIENTED_EDGE('',*,*,#3732,.F.);
#3732 = EDGE_CURVE('',#3629,#3705,#3733,.T.);
#3733 = SURFACE_CURVE('',#3734,(#3738,#3745),.PCURVE_S1.);
#3734 = LINE('',#3735,#3736);
#3735 = CARTESIAN_POINT('',(66.61651142,11.1661321,0.E+000));
#3736 = VECTOR('',#3737,1.);
#3737 = DIRECTION('',(-0.881920670078,0.471397848625,0.E+000));
#3738 = PCURVE('',#3644,#3739);
#3739 = DEFINITIONAL_REPRESENTATION('',(#3740),#3744);
#3740 = LINE('',#3741,#3742);
#3741 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3742 = VECTOR('',#3743,1.);
#3743 = DIRECTION('',(1.,0.E+000));
#3744 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3745 = PCURVE('',#137,#3746);
#3746 = DEFINITIONAL_REPRESENTATION('',(#3747),#3751);
#3747 = LINE('',#3748,#3749);
#3748 = CARTESIAN_POINT('',(66.61651142,9.16614372));
#3749 = VECTOR('',#3750,1.);
#3750 = DIRECTION('',(-0.881920670078,0.471397848625));
#3751 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3752 = ADVANCED_FACE('',(#3753),#3720,.F.);
#3753 = FACE_BOUND('',#3754,.F.);
#3754 = EDGE_LOOP('',(#3755,#3756,#3779,#3807));
#3755 = ORIENTED_EDGE('',*,*,#3704,.T.);
#3756 = ORIENTED_EDGE('',*,*,#3757,.T.);
#3757 = EDGE_CURVE('',#3682,#3758,#3760,.T.);
#3758 = VERTEX_POINT('',#3759);
#3759 = CARTESIAN_POINT('',(65.89206532,11.4662077,1.72191934));
#3760 = SURFACE_CURVE('',#3761,(#3765,#3772),.PCURVE_S1.);
#3761 = LINE('',#3762,#3763);
#3762 = CARTESIAN_POINT('',(66.26906482,11.35184674,1.72191934));
#3763 = VECTOR('',#3764,1.);
#3764 = DIRECTION('',(-0.956940750365,0.290283310389,0.E+000));
#3765 = PCURVE('',#3720,#3766);
#3766 = DEFINITIONAL_REPRESENTATION('',(#3767),#3771);
#3767 = LINE('',#3768,#3769);
#3768 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3769 = VECTOR('',#3770,1.);
#3770 = DIRECTION('',(1.,0.E+000));
#3771 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3772 = PCURVE('',#83,#3773);
#3773 = DEFINITIONAL_REPRESENTATION('',(#3774),#3778);
#3774 = LINE('',#3775,#3776);
#3775 = CARTESIAN_POINT('',(66.26906482,9.35185836));
#3776 = VECTOR('',#3777,1.);
#3777 = DIRECTION('',(-0.956940750365,0.290283310389));
#3778 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3779 = ORIENTED_EDGE('',*,*,#3780,.F.);
#3780 = EDGE_CURVE('',#3781,#3758,#3783,.T.);
#3781 = VERTEX_POINT('',#3782);
#3782 = CARTESIAN_POINT('',(65.89206532,11.4662077,0.E+000));
#3783 = SURFACE_CURVE('',#3784,(#3788,#3795),.PCURVE_S1.);
#3784 = LINE('',#3785,#3786);
#3785 = CARTESIAN_POINT('',(65.89206532,11.4662077,0.E+000));
#3786 = VECTOR('',#3787,1.);
#3787 = DIRECTION('',(0.E+000,0.E+000,1.));
#3788 = PCURVE('',#3720,#3789);
#3789 = DEFINITIONAL_REPRESENTATION('',(#3790),#3794);
#3790 = LINE('',#3791,#3792);
#3791 = CARTESIAN_POINT('',(0.393963262465,0.E+000));
#3792 = VECTOR('',#3793,1.);
#3793 = DIRECTION('',(0.E+000,-1.));
#3794 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3795 = PCURVE('',#3796,#3801);
#3796 = PLANE('',#3797);
#3797 = AXIS2_PLACEMENT_3D('',#3798,#3799,#3800);
#3798 = CARTESIAN_POINT('',(65.89206532,11.4662077,0.E+000));
#3799 = DIRECTION('',(-9.801754873775E-002,-0.995184686447,0.E+000));
#3800 = DIRECTION('',(-0.995184686447,9.801754873775E-002,0.E+000));
#3801 = DEFINITIONAL_REPRESENTATION('',(#3802),#3806);
#3802 = LINE('',#3803,#3804);
#3803 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3804 = VECTOR('',#3805,1.);
#3805 = DIRECTION('',(0.E+000,-1.));
#3806 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3807 = ORIENTED_EDGE('',*,*,#3808,.F.);
#3808 = EDGE_CURVE('',#3705,#3781,#3809,.T.);
#3809 = SURFACE_CURVE('',#3810,(#3814,#3821),.PCURVE_S1.);
#3810 = LINE('',#3811,#3812);
#3811 = CARTESIAN_POINT('',(66.26906482,11.35184674,0.E+000));
#3812 = VECTOR('',#3813,1.);
#3813 = DIRECTION('',(-0.956940750365,0.290283310389,0.E+000));
#3814 = PCURVE('',#3720,#3815);
#3815 = DEFINITIONAL_REPRESENTATION('',(#3816),#3820);
#3816 = LINE('',#3817,#3818);
#3817 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3818 = VECTOR('',#3819,1.);
#3819 = DIRECTION('',(1.,0.E+000));
#3820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3821 = PCURVE('',#137,#3822);
#3822 = DEFINITIONAL_REPRESENTATION('',(#3823),#3827);
#3823 = LINE('',#3824,#3825);
#3824 = CARTESIAN_POINT('',(66.26906482,9.35185836));
#3825 = VECTOR('',#3826,1.);
#3826 = DIRECTION('',(-0.956940750365,0.290283310389));
#3827 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3828 = ADVANCED_FACE('',(#3829),#3796,.F.);
#3829 = FACE_BOUND('',#3830,.F.);
#3830 = EDGE_LOOP('',(#3831,#3832,#3855,#3883));
#3831 = ORIENTED_EDGE('',*,*,#3780,.T.);
#3832 = ORIENTED_EDGE('',*,*,#3833,.T.);
#3833 = EDGE_CURVE('',#3758,#3834,#3836,.T.);
#3834 = VERTEX_POINT('',#3835);
#3835 = CARTESIAN_POINT('',(65.499996,11.50482332,1.72191934));
#3836 = SURFACE_CURVE('',#3837,(#3841,#3848),.PCURVE_S1.);
#3837 = LINE('',#3838,#3839);
#3838 = CARTESIAN_POINT('',(65.89206532,11.4662077,1.72191934));
#3839 = VECTOR('',#3840,1.);
#3840 = DIRECTION('',(-0.995184686447,9.801754873775E-002,0.E+000));
#3841 = PCURVE('',#3796,#3842);
#3842 = DEFINITIONAL_REPRESENTATION('',(#3843),#3847);
#3843 = LINE('',#3844,#3845);
#3844 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3845 = VECTOR('',#3846,1.);
#3846 = DIRECTION('',(1.,0.E+000));
#3847 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3848 = PCURVE('',#83,#3849);
#3849 = DEFINITIONAL_REPRESENTATION('',(#3850),#3854);
#3850 = LINE('',#3851,#3852);
#3851 = CARTESIAN_POINT('',(65.89206532,9.46621932));
#3852 = VECTOR('',#3853,1.);
#3853 = DIRECTION('',(-0.995184686447,9.801754873775E-002));
#3854 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3855 = ORIENTED_EDGE('',*,*,#3856,.F.);
#3856 = EDGE_CURVE('',#3857,#3834,#3859,.T.);
#3857 = VERTEX_POINT('',#3858);
#3858 = CARTESIAN_POINT('',(65.499996,11.50482332,0.E+000));
#3859 = SURFACE_CURVE('',#3860,(#3864,#3871),.PCURVE_S1.);
#3860 = LINE('',#3861,#3862);
#3861 = CARTESIAN_POINT('',(65.499996,11.50482332,0.E+000));
#3862 = VECTOR('',#3863,1.);
#3863 = DIRECTION('',(0.E+000,0.E+000,1.));
#3864 = PCURVE('',#3796,#3865);
#3865 = DEFINITIONAL_REPRESENTATION('',(#3866),#3870);
#3866 = LINE('',#3867,#3868);
#3867 = CARTESIAN_POINT('',(0.393966391705,0.E+000));
#3868 = VECTOR('',#3869,1.);
#3869 = DIRECTION('',(0.E+000,-1.));
#3870 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3871 = PCURVE('',#3872,#3877);
#3872 = PLANE('',#3873);
#3873 = AXIS2_PLACEMENT_3D('',#3874,#3875,#3876);
#3874 = CARTESIAN_POINT('',(65.499996,11.50482332,0.E+000));
#3875 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3876 = DIRECTION('',(0.E+000,-1.,0.E+000));
#3877 = DEFINITIONAL_REPRESENTATION('',(#3878),#3882);
#3878 = LINE('',#3879,#3880);
#3879 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3880 = VECTOR('',#3881,1.);
#3881 = DIRECTION('',(0.E+000,-1.));
#3882 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3883 = ORIENTED_EDGE('',*,*,#3884,.F.);
#3884 = EDGE_CURVE('',#3781,#3857,#3885,.T.);
#3885 = SURFACE_CURVE('',#3886,(#3890,#3897),.PCURVE_S1.);
#3886 = LINE('',#3887,#3888);
#3887 = CARTESIAN_POINT('',(65.89206532,11.4662077,0.E+000));
#3888 = VECTOR('',#3889,1.);
#3889 = DIRECTION('',(-0.995184686447,9.801754873775E-002,0.E+000));
#3890 = PCURVE('',#3796,#3891);
#3891 = DEFINITIONAL_REPRESENTATION('',(#3892),#3896);
#3892 = LINE('',#3893,#3894);
#3893 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3894 = VECTOR('',#3895,1.);
#3895 = DIRECTION('',(1.,0.E+000));
#3896 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3897 = PCURVE('',#137,#3898);
#3898 = DEFINITIONAL_REPRESENTATION('',(#3899),#3903);
#3899 = LINE('',#3900,#3901);
#3900 = CARTESIAN_POINT('',(65.89206532,9.46621932));
#3901 = VECTOR('',#3902,1.);
#3902 = DIRECTION('',(-0.995184686447,9.801754873775E-002));
#3903 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3904 = ADVANCED_FACE('',(#3905),#3872,.F.);
#3905 = FACE_BOUND('',#3906,.F.);
#3906 = EDGE_LOOP('',(#3907,#3908,#3931,#3954));
#3907 = ORIENTED_EDGE('',*,*,#3856,.T.);
#3908 = ORIENTED_EDGE('',*,*,#3909,.T.);
#3909 = EDGE_CURVE('',#3834,#3910,#3912,.T.);
#3910 = VERTEX_POINT('',#3911);
#3911 = CARTESIAN_POINT('',(65.499996,11.49514846,1.72191934));
#3912 = SURFACE_CURVE('',#3913,(#3917,#3924),.PCURVE_S1.);
#3913 = LINE('',#3914,#3915);
#3914 = CARTESIAN_POINT('',(65.499996,11.50482332,1.72191934));
#3915 = VECTOR('',#3916,1.);
#3916 = DIRECTION('',(0.E+000,-1.,0.E+000));
#3917 = PCURVE('',#3872,#3918);
#3918 = DEFINITIONAL_REPRESENTATION('',(#3919),#3923);
#3919 = LINE('',#3920,#3921);
#3920 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3921 = VECTOR('',#3922,1.);
#3922 = DIRECTION('',(1.,0.E+000));
#3923 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3924 = PCURVE('',#83,#3925);
#3925 = DEFINITIONAL_REPRESENTATION('',(#3926),#3930);
#3926 = LINE('',#3927,#3928);
#3927 = CARTESIAN_POINT('',(65.499996,9.50483494));
#3928 = VECTOR('',#3929,1.);
#3929 = DIRECTION('',(0.E+000,-1.));
#3930 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3931 = ORIENTED_EDGE('',*,*,#3932,.F.);
#3932 = EDGE_CURVE('',#3933,#3910,#3935,.T.);
#3933 = VERTEX_POINT('',#3934);
#3934 = CARTESIAN_POINT('',(65.499996,11.49514846,0.E+000));
#3935 = SURFACE_CURVE('',#3936,(#3940,#3947),.PCURVE_S1.);
#3936 = LINE('',#3937,#3938);
#3937 = CARTESIAN_POINT('',(65.499996,11.49514846,0.E+000));
#3938 = VECTOR('',#3939,1.);
#3939 = DIRECTION('',(0.E+000,0.E+000,1.));
#3940 = PCURVE('',#3872,#3941);
#3941 = DEFINITIONAL_REPRESENTATION('',(#3942),#3946);
#3942 = LINE('',#3943,#3944);
#3943 = CARTESIAN_POINT('',(9.674860000004E-003,0.E+000));
#3944 = VECTOR('',#3945,1.);
#3945 = DIRECTION('',(0.E+000,-1.));
#3946 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3947 = PCURVE('',#1313,#3948);
#3948 = DEFINITIONAL_REPRESENTATION('',(#3949),#3953);
#3949 = LINE('',#3950,#3951);
#3950 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3951 = VECTOR('',#3952,1.);
#3952 = DIRECTION('',(0.E+000,-1.));
#3953 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3954 = ORIENTED_EDGE('',*,*,#3955,.F.);
#3955 = EDGE_CURVE('',#3857,#3933,#3956,.T.);
#3956 = SURFACE_CURVE('',#3957,(#3961,#3968),.PCURVE_S1.);
#3957 = LINE('',#3958,#3959);
#3958 = CARTESIAN_POINT('',(65.499996,11.50482332,0.E+000));
#3959 = VECTOR('',#3960,1.);
#3960 = DIRECTION('',(0.E+000,-1.,0.E+000));
#3961 = PCURVE('',#3872,#3962);
#3962 = DEFINITIONAL_REPRESENTATION('',(#3963),#3967);
#3963 = LINE('',#3964,#3965);
#3964 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3965 = VECTOR('',#3966,1.);
#3966 = DIRECTION('',(1.,0.E+000));
#3967 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3968 = PCURVE('',#137,#3969);
#3969 = DEFINITIONAL_REPRESENTATION('',(#3970),#3974);
#3970 = LINE('',#3971,#3972);
#3971 = CARTESIAN_POINT('',(65.499996,9.50483494));
#3972 = VECTOR('',#3973,1.);
#3973 = DIRECTION('',(0.E+000,-1.));
#3974 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3975 = ADVANCED_FACE('',(#3976),#1313,.F.);
#3976 = FACE_BOUND('',#3977,.F.);
#3977 = EDGE_LOOP('',(#3978,#3979,#4000,#4001));
#3978 = ORIENTED_EDGE('',*,*,#3932,.T.);
#3979 = ORIENTED_EDGE('',*,*,#3980,.T.);
#3980 = EDGE_CURVE('',#3910,#1293,#3981,.T.);
#3981 = SURFACE_CURVE('',#3982,(#3986,#3993),.PCURVE_S1.);
#3982 = LINE('',#3983,#3984);
#3983 = CARTESIAN_POINT('',(65.499996,11.49514846,1.72191934));
#3984 = VECTOR('',#3985,1.);
#3985 = DIRECTION('',(-0.999998126973,1.935471396762E-003,0.E+000));
#3986 = PCURVE('',#1313,#3987);
#3987 = DEFINITIONAL_REPRESENTATION('',(#3988),#3992);
#3988 = LINE('',#3989,#3990);
#3989 = CARTESIAN_POINT('',(0.E+000,-1.72191934));
#3990 = VECTOR('',#3991,1.);
#3991 = DIRECTION('',(1.,0.E+000));
#3992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3993 = PCURVE('',#83,#3994);
#3994 = DEFINITIONAL_REPRESENTATION('',(#3995),#3999);
#3995 = LINE('',#3996,#3997);
#3996 = CARTESIAN_POINT('',(65.499996,9.49516008));
#3997 = VECTOR('',#3998,1.);
#3998 = DIRECTION('',(-0.999998126973,1.935471396762E-003));
#3999 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4000 = ORIENTED_EDGE('',*,*,#1290,.F.);
#4001 = ORIENTED_EDGE('',*,*,#4002,.F.);
#4002 = EDGE_CURVE('',#3933,#1291,#4003,.T.);
#4003 = SURFACE_CURVE('',#4004,(#4008,#4015),.PCURVE_S1.);
#4004 = LINE('',#4005,#4006);
#4005 = CARTESIAN_POINT('',(65.499996,11.49514846,0.E+000));
#4006 = VECTOR('',#4007,1.);
#4007 = DIRECTION('',(-0.999998126973,1.935471396762E-003,0.E+000));
#4008 = PCURVE('',#1313,#4009);
#4009 = DEFINITIONAL_REPRESENTATION('',(#4010),#4014);
#4010 = LINE('',#4011,#4012);
#4011 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#4012 = VECTOR('',#4013,1.);
#4013 = DIRECTION('',(1.,0.E+000));
#4014 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4015 = PCURVE('',#137,#4016);
#4016 = DEFINITIONAL_REPRESENTATION('',(#4017),#4021);
#4017 = LINE('',#4018,#4019);
#4018 = CARTESIAN_POINT('',(65.499996,9.49516008));
#4019 = VECTOR('',#4020,1.);
#4020 = DIRECTION('',(-0.999998126973,1.935471396762E-003));
#4021 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4022 = ADVANCED_FACE('',(#4023),#4037,.T.);
#4023 = FACE_BOUND('',#4024,.F.);
#4024 = EDGE_LOOP('',(#4025,#4055,#4077,#4078));
#4025 = ORIENTED_EDGE('',*,*,#4026,.T.);
#4026 = EDGE_CURVE('',#4027,#4029,#4031,.T.);
#4027 = VERTEX_POINT('',#4028);
#4028 = CARTESIAN_POINT('',(77.74999944,11.24999782,0.E+000));
#4029 = VERTEX_POINT('',#4030);
#4030 = CARTESIAN_POINT('',(77.74999944,11.24999782,1.72191934));
#4031 = SEAM_CURVE('',#4032,(#4036,#4048),.PCURVE_S1.);
#4032 = LINE('',#4033,#4034);
#4033 = CARTESIAN_POINT('',(77.74999944,11.24999782,0.E+000));
#4034 = VECTOR('',#4035,1.);
#4035 = DIRECTION('',(0.E+000,0.E+000,1.));
#4036 = PCURVE('',#4037,#4042);
#4037 = CYLINDRICAL_SURFACE('',#4038,1.74999904);
#4038 = AXIS2_PLACEMENT_3D('',#4039,#4040,#4041);
#4039 = CARTESIAN_POINT('',(76.0000004,11.24999782,0.E+000));
#4040 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#4041 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4042 = DEFINITIONAL_REPRESENTATION('',(#4043),#4047);
#4043 = LINE('',#4044,#4045);
#4044 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4045 = VECTOR('',#4046,1.);
#4046 = DIRECTION('',(-0.E+000,-1.));
#4047 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4048 = PCURVE('',#4037,#4049);
#4049 = DEFINITIONAL_REPRESENTATION('',(#4050),#4054);
#4050 = LINE('',#4051,#4052);
#4051 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#4052 = VECTOR('',#4053,1.);
#4053 = DIRECTION('',(-0.E+000,-1.));
#4054 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4055 = ORIENTED_EDGE('',*,*,#4056,.T.);
#4056 = EDGE_CURVE('',#4029,#4029,#4057,.T.);
#4057 = SURFACE_CURVE('',#4058,(#4063,#4070),.PCURVE_S1.);
#4058 = CIRCLE('',#4059,1.74999904);
#4059 = AXIS2_PLACEMENT_3D('',#4060,#4061,#4062);
#4060 = CARTESIAN_POINT('',(76.0000004,11.24999782,1.72191934));
#4061 = DIRECTION('',(0.E+000,0.E+000,1.));
#4062 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4063 = PCURVE('',#4037,#4064);
#4064 = DEFINITIONAL_REPRESENTATION('',(#4065),#4069);
#4065 = LINE('',#4066,#4067);
#4066 = CARTESIAN_POINT('',(-0.E+000,-1.72191934));
#4067 = VECTOR('',#4068,1.);
#4068 = DIRECTION('',(-1.,0.E+000));
#4069 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4070 = PCURVE('',#83,#4071);
#4071 = DEFINITIONAL_REPRESENTATION('',(#4072),#4076);
#4072 = CIRCLE('',#4073,1.74999904);
#4073 = AXIS2_PLACEMENT_2D('',#4074,#4075);
#4074 = CARTESIAN_POINT('',(76.0000004,9.25000944));
#4075 = DIRECTION('',(1.,0.E+000));
#4076 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4077 = ORIENTED_EDGE('',*,*,#4026,.F.);
#4078 = ORIENTED_EDGE('',*,*,#4079,.F.);
#4079 = EDGE_CURVE('',#4027,#4027,#4080,.T.);
#4080 = SURFACE_CURVE('',#4081,(#4086,#4093),.PCURVE_S1.);
#4081 = CIRCLE('',#4082,1.74999904);
#4082 = AXIS2_PLACEMENT_3D('',#4083,#4084,#4085);
#4083 = CARTESIAN_POINT('',(76.0000004,11.24999782,0.E+000));
#4084 = DIRECTION('',(0.E+000,0.E+000,1.));
#4085 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4086 = PCURVE('',#4037,#4087);
#4087 = DEFINITIONAL_REPRESENTATION('',(#4088),#4092);
#4088 = LINE('',#4089,#4090);
#4089 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4090 = VECTOR('',#4091,1.);
#4091 = DIRECTION('',(-1.,0.E+000));
#4092 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4093 = PCURVE('',#137,#4094);
#4094 = DEFINITIONAL_REPRESENTATION('',(#4095),#4099);
#4095 = CIRCLE('',#4096,1.74999904);
#4096 = AXIS2_PLACEMENT_2D('',#4097,#4098);
#4097 = CARTESIAN_POINT('',(76.0000004,9.25000944));
#4098 = DIRECTION('',(1.,0.E+000));
#4099 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4100 = ADVANCED_FACE('',(#4101),#4115,.T.);
#4101 = FACE_BOUND('',#4102,.F.);
#4102 = EDGE_LOOP('',(#4103,#4133,#4155,#4156));
#4103 = ORIENTED_EDGE('',*,*,#4104,.T.);
#4104 = EDGE_CURVE('',#4105,#4107,#4109,.T.);
#4105 = VERTEX_POINT('',#4106);
#4106 = CARTESIAN_POINT('',(77.74999944,15.30999732,0.E+000));
#4107 = VERTEX_POINT('',#4108);
#4108 = CARTESIAN_POINT('',(77.74999944,15.30999732,1.72191934));
#4109 = SEAM_CURVE('',#4110,(#4114,#4126),.PCURVE_S1.);
#4110 = LINE('',#4111,#4112);
#4111 = CARTESIAN_POINT('',(77.74999944,15.30999732,0.E+000));
#4112 = VECTOR('',#4113,1.);
#4113 = DIRECTION('',(0.E+000,0.E+000,1.));
#4114 = PCURVE('',#4115,#4120);
#4115 = CYLINDRICAL_SURFACE('',#4116,1.74999904);
#4116 = AXIS2_PLACEMENT_3D('',#4117,#4118,#4119);
#4117 = CARTESIAN_POINT('',(76.0000004,15.30999732,0.E+000));
#4118 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#4119 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4120 = DEFINITIONAL_REPRESENTATION('',(#4121),#4125);
#4121 = LINE('',#4122,#4123);
#4122 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4123 = VECTOR('',#4124,1.);
#4124 = DIRECTION('',(-0.E+000,-1.));
#4125 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4126 = PCURVE('',#4115,#4127);
#4127 = DEFINITIONAL_REPRESENTATION('',(#4128),#4132);
#4128 = LINE('',#4129,#4130);
#4129 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#4130 = VECTOR('',#4131,1.);
#4131 = DIRECTION('',(-0.E+000,-1.));
#4132 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4133 = ORIENTED_EDGE('',*,*,#4134,.T.);
#4134 = EDGE_CURVE('',#4107,#4107,#4135,.T.);
#4135 = SURFACE_CURVE('',#4136,(#4141,#4148),.PCURVE_S1.);
#4136 = CIRCLE('',#4137,1.74999904);
#4137 = AXIS2_PLACEMENT_3D('',#4138,#4139,#4140);
#4138 = CARTESIAN_POINT('',(76.0000004,15.30999732,1.72191934));
#4139 = DIRECTION('',(0.E+000,0.E+000,1.));
#4140 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4141 = PCURVE('',#4115,#4142);
#4142 = DEFINITIONAL_REPRESENTATION('',(#4143),#4147);
#4143 = LINE('',#4144,#4145);
#4144 = CARTESIAN_POINT('',(-0.E+000,-1.72191934));
#4145 = VECTOR('',#4146,1.);
#4146 = DIRECTION('',(-1.,0.E+000));
#4147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4148 = PCURVE('',#83,#4149);
#4149 = DEFINITIONAL_REPRESENTATION('',(#4150),#4154);
#4150 = CIRCLE('',#4151,1.74999904);
#4151 = AXIS2_PLACEMENT_2D('',#4152,#4153);
#4152 = CARTESIAN_POINT('',(76.0000004,13.31000894));
#4153 = DIRECTION('',(1.,0.E+000));
#4154 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4155 = ORIENTED_EDGE('',*,*,#4104,.F.);
#4156 = ORIENTED_EDGE('',*,*,#4157,.F.);
#4157 = EDGE_CURVE('',#4105,#4105,#4158,.T.);
#4158 = SURFACE_CURVE('',#4159,(#4164,#4171),.PCURVE_S1.);
#4159 = CIRCLE('',#4160,1.74999904);
#4160 = AXIS2_PLACEMENT_3D('',#4161,#4162,#4163);
#4161 = CARTESIAN_POINT('',(76.0000004,15.30999732,0.E+000));
#4162 = DIRECTION('',(0.E+000,0.E+000,1.));
#4163 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4164 = PCURVE('',#4115,#4165);
#4165 = DEFINITIONAL_REPRESENTATION('',(#4166),#4170);
#4166 = LINE('',#4167,#4168);
#4167 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4168 = VECTOR('',#4169,1.);
#4169 = DIRECTION('',(-1.,0.E+000));
#4170 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4171 = PCURVE('',#137,#4172);
#4172 = DEFINITIONAL_REPRESENTATION('',(#4173),#4177);
#4173 = CIRCLE('',#4174,1.74999904);
#4174 = AXIS2_PLACEMENT_2D('',#4175,#4176);
#4175 = CARTESIAN_POINT('',(76.0000004,13.31000894));
#4176 = DIRECTION('',(1.,0.E+000));
#4177 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4178 = ADVANCED_FACE('',(#4179),#4193,.T.);
#4179 = FACE_BOUND('',#4180,.F.);
#4180 = EDGE_LOOP('',(#4181,#4211,#4233,#4234));
#4181 = ORIENTED_EDGE('',*,*,#4182,.T.);
#4182 = EDGE_CURVE('',#4183,#4185,#4187,.T.);
#4183 = VERTEX_POINT('',#4184);
#4184 = CARTESIAN_POINT('',(86.10001068,15.99998578,0.E+000));
#4185 = VERTEX_POINT('',#4186);
#4186 = CARTESIAN_POINT('',(86.10001068,15.99998578,1.72191934));
#4187 = SEAM_CURVE('',#4188,(#4192,#4204),.PCURVE_S1.);
#4188 = LINE('',#4189,#4190);
#4189 = CARTESIAN_POINT('',(86.10001068,15.99998578,0.E+000));
#4190 = VECTOR('',#4191,1.);
#4191 = DIRECTION('',(0.E+000,0.E+000,1.));
#4192 = PCURVE('',#4193,#4198);
#4193 = CYLINDRICAL_SURFACE('',#4194,1.59999934);
#4194 = AXIS2_PLACEMENT_3D('',#4195,#4196,#4197);
#4195 = CARTESIAN_POINT('',(84.50001134,15.99998578,0.E+000));
#4196 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#4197 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4198 = DEFINITIONAL_REPRESENTATION('',(#4199),#4203);
#4199 = LINE('',#4200,#4201);
#4200 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4201 = VECTOR('',#4202,1.);
#4202 = DIRECTION('',(-0.E+000,-1.));
#4203 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4204 = PCURVE('',#4193,#4205);
#4205 = DEFINITIONAL_REPRESENTATION('',(#4206),#4210);
#4206 = LINE('',#4207,#4208);
#4207 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#4208 = VECTOR('',#4209,1.);
#4209 = DIRECTION('',(-0.E+000,-1.));
#4210 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4211 = ORIENTED_EDGE('',*,*,#4212,.T.);
#4212 = EDGE_CURVE('',#4185,#4185,#4213,.T.);
#4213 = SURFACE_CURVE('',#4214,(#4219,#4226),.PCURVE_S1.);
#4214 = CIRCLE('',#4215,1.59999934);
#4215 = AXIS2_PLACEMENT_3D('',#4216,#4217,#4218);
#4216 = CARTESIAN_POINT('',(84.50001134,15.99998578,1.72191934));
#4217 = DIRECTION('',(0.E+000,0.E+000,1.));
#4218 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4219 = PCURVE('',#4193,#4220);
#4220 = DEFINITIONAL_REPRESENTATION('',(#4221),#4225);
#4221 = LINE('',#4222,#4223);
#4222 = CARTESIAN_POINT('',(-0.E+000,-1.72191934));
#4223 = VECTOR('',#4224,1.);
#4224 = DIRECTION('',(-1.,0.E+000));
#4225 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4226 = PCURVE('',#83,#4227);
#4227 = DEFINITIONAL_REPRESENTATION('',(#4228),#4232);
#4228 = CIRCLE('',#4229,1.59999934);
#4229 = AXIS2_PLACEMENT_2D('',#4230,#4231);
#4230 = CARTESIAN_POINT('',(84.50001134,13.9999974));
#4231 = DIRECTION('',(1.,0.E+000));
#4232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4233 = ORIENTED_EDGE('',*,*,#4182,.F.);
#4234 = ORIENTED_EDGE('',*,*,#4235,.F.);
#4235 = EDGE_CURVE('',#4183,#4183,#4236,.T.);
#4236 = SURFACE_CURVE('',#4237,(#4242,#4249),.PCURVE_S1.);
#4237 = CIRCLE('',#4238,1.59999934);
#4238 = AXIS2_PLACEMENT_3D('',#4239,#4240,#4241);
#4239 = CARTESIAN_POINT('',(84.50001134,15.99998578,0.E+000));
#4240 = DIRECTION('',(0.E+000,0.E+000,1.));
#4241 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4242 = PCURVE('',#4193,#4243);
#4243 = DEFINITIONAL_REPRESENTATION('',(#4244),#4248);
#4244 = LINE('',#4245,#4246);
#4245 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4246 = VECTOR('',#4247,1.);
#4247 = DIRECTION('',(-1.,0.E+000));
#4248 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4249 = PCURVE('',#137,#4250);
#4250 = DEFINITIONAL_REPRESENTATION('',(#4251),#4255);
#4251 = CIRCLE('',#4252,1.59999934);
#4252 = AXIS2_PLACEMENT_2D('',#4253,#4254);
#4253 = CARTESIAN_POINT('',(84.50001134,13.9999974));
#4254 = DIRECTION('',(1.,0.E+000));
#4255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4256 = ADVANCED_FACE('',(#4257,#4265,#4268,#4271,#4274,#4277,#4280,
    #4286,#4289,#4327,#4330,#4333),#137,.F.);
#4257 = FACE_BOUND('',#4258,.T.);
#4258 = EDGE_LOOP('',(#4259,#4260,#4261,#4262,#4263,#4264));
#4259 = ORIENTED_EDGE('',*,*,#123,.T.);
#4260 = ORIENTED_EDGE('',*,*,#204,.T.);
#4261 = ORIENTED_EDGE('',*,*,#280,.T.);
#4262 = ORIENTED_EDGE('',*,*,#361,.T.);
#4263 = ORIENTED_EDGE('',*,*,#437,.T.);
#4264 = ORIENTED_EDGE('',*,*,#489,.T.);
#4265 = FACE_BOUND('',#4266,.F.);
#4266 = EDGE_LOOP('',(#4267));
#4267 = ORIENTED_EDGE('',*,*,#571,.T.);
#4268 = FACE_BOUND('',#4269,.F.);
#4269 = EDGE_LOOP('',(#4270));
#4270 = ORIENTED_EDGE('',*,*,#649,.T.);
#4271 = FACE_BOUND('',#4272,.F.);
#4272 = EDGE_LOOP('',(#4273));
#4273 = ORIENTED_EDGE('',*,*,#727,.T.);
#4274 = FACE_BOUND('',#4275,.F.);
#4275 = EDGE_LOOP('',(#4276));
#4276 = ORIENTED_EDGE('',*,*,#805,.T.);
#4277 = FACE_BOUND('',#4278,.F.);
#4278 = EDGE_LOOP('',(#4279));
#4279 = ORIENTED_EDGE('',*,*,#883,.T.);
#4280 = FACE_BOUND('',#4281,.F.);
#4281 = EDGE_LOOP('',(#4282,#4283,#4284,#4285));
#4282 = ORIENTED_EDGE('',*,*,#994,.T.);
#4283 = ORIENTED_EDGE('',*,*,#1070,.T.);
#4284 = ORIENTED_EDGE('',*,*,#1141,.T.);
#4285 = ORIENTED_EDGE('',*,*,#1188,.T.);
#4286 = FACE_BOUND('',#4287,.F.);
#4287 = EDGE_LOOP('',(#4288));
#4288 = ORIENTED_EDGE('',*,*,#1265,.T.);
#4289 = FACE_BOUND('',#4290,.F.);
#4290 = EDGE_LOOP('',(#4291,#4292,#4293,#4294,#4295,#4296,#4297,#4298,
    #4299,#4300,#4301,#4302,#4303,#4304,#4305,#4306,#4307,#4308,#4309,
    #4310,#4311,#4312,#4313,#4314,#4315,#4316,#4317,#4318,#4319,#4320,
    #4321,#4322,#4323,#4324,#4325,#4326));
#4291 = ORIENTED_EDGE('',*,*,#1376,.T.);
#4292 = ORIENTED_EDGE('',*,*,#1452,.T.);
#4293 = ORIENTED_EDGE('',*,*,#1528,.T.);
#4294 = ORIENTED_EDGE('',*,*,#1604,.T.);
#4295 = ORIENTED_EDGE('',*,*,#1680,.T.);
#4296 = ORIENTED_EDGE('',*,*,#1756,.T.);
#4297 = ORIENTED_EDGE('',*,*,#1832,.T.);
#4298 = ORIENTED_EDGE('',*,*,#1908,.T.);
#4299 = ORIENTED_EDGE('',*,*,#1984,.T.);
#4300 = ORIENTED_EDGE('',*,*,#2060,.T.);
#4301 = ORIENTED_EDGE('',*,*,#2136,.T.);
#4302 = ORIENTED_EDGE('',*,*,#2212,.T.);
#4303 = ORIENTED_EDGE('',*,*,#2288,.T.);
#4304 = ORIENTED_EDGE('',*,*,#2364,.T.);
#4305 = ORIENTED_EDGE('',*,*,#2440,.T.);
#4306 = ORIENTED_EDGE('',*,*,#2516,.T.);
#4307 = ORIENTED_EDGE('',*,*,#2592,.T.);
#4308 = ORIENTED_EDGE('',*,*,#2668,.T.);
#4309 = ORIENTED_EDGE('',*,*,#2744,.T.);
#4310 = ORIENTED_EDGE('',*,*,#2820,.T.);
#4311 = ORIENTED_EDGE('',*,*,#2896,.T.);
#4312 = ORIENTED_EDGE('',*,*,#2972,.T.);
#4313 = ORIENTED_EDGE('',*,*,#3048,.T.);
#4314 = ORIENTED_EDGE('',*,*,#3124,.T.);
#4315 = ORIENTED_EDGE('',*,*,#3200,.T.);
#4316 = ORIENTED_EDGE('',*,*,#3276,.T.);
#4317 = ORIENTED_EDGE('',*,*,#3352,.T.);
#4318 = ORIENTED_EDGE('',*,*,#3428,.T.);
#4319 = ORIENTED_EDGE('',*,*,#3504,.T.);
#4320 = ORIENTED_EDGE('',*,*,#3580,.T.);
#4321 = ORIENTED_EDGE('',*,*,#3656,.T.);
#4322 = ORIENTED_EDGE('',*,*,#3732,.T.);
#4323 = ORIENTED_EDGE('',*,*,#3808,.T.);
#4324 = ORIENTED_EDGE('',*,*,#3884,.T.);
#4325 = ORIENTED_EDGE('',*,*,#3955,.T.);
#4326 = ORIENTED_EDGE('',*,*,#4002,.T.);
#4327 = FACE_BOUND('',#4328,.F.);
#4328 = EDGE_LOOP('',(#4329));
#4329 = ORIENTED_EDGE('',*,*,#4079,.T.);
#4330 = FACE_BOUND('',#4331,.F.);
#4331 = EDGE_LOOP('',(#4332));
#4332 = ORIENTED_EDGE('',*,*,#4157,.T.);
#4333 = FACE_BOUND('',#4334,.F.);
#4334 = EDGE_LOOP('',(#4335));
#4335 = ORIENTED_EDGE('',*,*,#4235,.T.);
#4336 = ADVANCED_FACE('',(#4337,#4345,#4348,#4351,#4354,#4357,#4360,
    #4366,#4369,#4407,#4410,#4413),#83,.T.);
#4337 = FACE_BOUND('',#4338,.F.);
#4338 = EDGE_LOOP('',(#4339,#4340,#4341,#4342,#4343,#4344));
#4339 = ORIENTED_EDGE('',*,*,#67,.T.);
#4340 = ORIENTED_EDGE('',*,*,#153,.T.);
#4341 = ORIENTED_EDGE('',*,*,#229,.T.);
#4342 = ORIENTED_EDGE('',*,*,#305,.T.);
#4343 = ORIENTED_EDGE('',*,*,#391,.T.);
#4344 = ORIENTED_EDGE('',*,*,#462,.T.);
#4345 = FACE_BOUND('',#4346,.T.);
#4346 = EDGE_LOOP('',(#4347));
#4347 = ORIENTED_EDGE('',*,*,#548,.T.);
#4348 = FACE_BOUND('',#4349,.T.);
#4349 = EDGE_LOOP('',(#4350));
#4350 = ORIENTED_EDGE('',*,*,#626,.T.);
#4351 = FACE_BOUND('',#4352,.T.);
#4352 = EDGE_LOOP('',(#4353));
#4353 = ORIENTED_EDGE('',*,*,#704,.T.);
#4354 = FACE_BOUND('',#4355,.T.);
#4355 = EDGE_LOOP('',(#4356));
#4356 = ORIENTED_EDGE('',*,*,#782,.T.);
#4357 = FACE_BOUND('',#4358,.T.);
#4358 = EDGE_LOOP('',(#4359));
#4359 = ORIENTED_EDGE('',*,*,#860,.T.);
#4360 = FACE_BOUND('',#4361,.T.);
#4361 = EDGE_LOOP('',(#4362,#4363,#4364,#4365));
#4362 = ORIENTED_EDGE('',*,*,#943,.T.);
#4363 = ORIENTED_EDGE('',*,*,#1019,.T.);
#4364 = ORIENTED_EDGE('',*,*,#1095,.T.);
#4365 = ORIENTED_EDGE('',*,*,#1166,.T.);
#4366 = FACE_BOUND('',#4367,.T.);
#4367 = EDGE_LOOP('',(#4368));
#4368 = ORIENTED_EDGE('',*,*,#1242,.T.);
#4369 = FACE_BOUND('',#4370,.T.);
#4370 = EDGE_LOOP('',(#4371,#4372,#4373,#4374,#4375,#4376,#4377,#4378,
    #4379,#4380,#4381,#4382,#4383,#4384,#4385,#4386,#4387,#4388,#4389,
    #4390,#4391,#4392,#4393,#4394,#4395,#4396,#4397,#4398,#4399,#4400,
    #4401,#4402,#4403,#4404,#4405,#4406));
#4371 = ORIENTED_EDGE('',*,*,#1325,.T.);
#4372 = ORIENTED_EDGE('',*,*,#1401,.T.);
#4373 = ORIENTED_EDGE('',*,*,#1477,.T.);
#4374 = ORIENTED_EDGE('',*,*,#1553,.T.);
#4375 = ORIENTED_EDGE('',*,*,#1629,.T.);
#4376 = ORIENTED_EDGE('',*,*,#1705,.T.);
#4377 = ORIENTED_EDGE('',*,*,#1781,.T.);
#4378 = ORIENTED_EDGE('',*,*,#1857,.T.);
#4379 = ORIENTED_EDGE('',*,*,#1933,.T.);
#4380 = ORIENTED_EDGE('',*,*,#2009,.T.);
#4381 = ORIENTED_EDGE('',*,*,#2085,.T.);
#4382 = ORIENTED_EDGE('',*,*,#2161,.T.);
#4383 = ORIENTED_EDGE('',*,*,#2237,.T.);
#4384 = ORIENTED_EDGE('',*,*,#2313,.T.);
#4385 = ORIENTED_EDGE('',*,*,#2389,.T.);
#4386 = ORIENTED_EDGE('',*,*,#2465,.T.);
#4387 = ORIENTED_EDGE('',*,*,#2541,.T.);
#4388 = ORIENTED_EDGE('',*,*,#2617,.T.);
#4389 = ORIENTED_EDGE('',*,*,#2693,.T.);
#4390 = ORIENTED_EDGE('',*,*,#2769,.T.);
#4391 = ORIENTED_EDGE('',*,*,#2845,.T.);
#4392 = ORIENTED_EDGE('',*,*,#2921,.T.);
#4393 = ORIENTED_EDGE('',*,*,#2997,.T.);
#4394 = ORIENTED_EDGE('',*,*,#3073,.T.);
#4395 = ORIENTED_EDGE('',*,*,#3149,.T.);
#4396 = ORIENTED_EDGE('',*,*,#3225,.T.);
#4397 = ORIENTED_EDGE('',*,*,#3301,.T.);
#4398 = ORIENTED_EDGE('',*,*,#3377,.T.);
#4399 = ORIENTED_EDGE('',*,*,#3453,.T.);
#4400 = ORIENTED_EDGE('',*,*,#3529,.T.);
#4401 = ORIENTED_EDGE('',*,*,#3605,.T.);
#4402 = ORIENTED_EDGE('',*,*,#3681,.T.);
#4403 = ORIENTED_EDGE('',*,*,#3757,.T.);
#4404 = ORIENTED_EDGE('',*,*,#3833,.T.);
#4405 = ORIENTED_EDGE('',*,*,#3909,.T.);
#4406 = ORIENTED_EDGE('',*,*,#3980,.T.);
#4407 = FACE_BOUND('',#4408,.T.);
#4408 = EDGE_LOOP('',(#4409));
#4409 = ORIENTED_EDGE('',*,*,#4056,.T.);
#4410 = FACE_BOUND('',#4411,.T.);
#4411 = EDGE_LOOP('',(#4412));
#4412 = ORIENTED_EDGE('',*,*,#4134,.T.);
#4413 = FACE_BOUND('',#4414,.T.);
#4414 = EDGE_LOOP('',(#4415));
#4415 = ORIENTED_EDGE('',*,*,#4212,.T.);
#4416 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4420)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4417,#4418,#4419)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#4417 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#4418 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#4419 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#4420 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#4417,
  'distance_accuracy_value','confusion accuracy');
#4421 = SHAPE_DEFINITION_REPRESENTATION(#4422,#25);
#4422 = PRODUCT_DEFINITION_SHAPE('','',#4423);
#4423 = PRODUCT_DEFINITION('design','',#4424,#4427);
#4424 = PRODUCT_DEFINITION_FORMATION('','',#4425);
#4425 = PRODUCT('Board','Board','',(#4426));
#4426 = PRODUCT_CONTEXT('',#2,'mechanical');
#4427 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#4428 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#4429,#4431);
#4429 = ( REPRESENTATION_RELATIONSHIP('','',#25,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#4430) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#4430 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#4431 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #4432);
#4432 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('2','=>[0:1:1:2]','',#5,#4423,$);
#4433 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#4425));
#4434 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #4435),#4416);
#4435 = STYLED_ITEM('color',(#4436),#26);
#4436 = PRESENTATION_STYLE_ASSIGNMENT((#4437,#4443));
#4437 = SURFACE_STYLE_USAGE(.BOTH.,#4438);
#4438 = SURFACE_SIDE_STYLE('',(#4439));
#4439 = SURFACE_STYLE_FILL_AREA(#4440);
#4440 = FILL_AREA_STYLE('',(#4441));
#4441 = FILL_AREA_STYLE_COLOUR('',#4442);
#4442 = COLOUR_RGB('',0.E+000,0.501960813999,0.E+000);
#4443 = CURVE_STYLE('',#4444,POSITIVE_LENGTH_MEASURE(0.1),#4442);
#4444 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
