ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2020-08-20T11:30:25',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 6.5','Open CASCADE 6.5'
  ,'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN_CC2 { 1 2 10303 214 -1 1 5 4 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('committee draft',
  'automotive_design',1997,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('PCB','PCB','',(#8));
#8 = MECHANICAL_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15),#19);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#13 = DIRECTION('',(0.E+000,0.E+000,1.));
#14 = DIRECTION('',(1.,0.E+000,-0.E+000));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.E+000,0.E+000,-1.64592));
#17 = DIRECTION('',(0.E+000,0.E+000,1.));
#18 = DIRECTION('',(1.,0.E+000,-0.E+000));
#19 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#23)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#20,#21,#22)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#20 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#21 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#22 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#23 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#20,
  'distance_accuracy_value','confusion accuracy');
#24 = PRODUCT_TYPE('part',$,(#7));
#25 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#26),#4712);
#26 = MANIFOLD_SOLID_BREP('',#27);
#27 = CLOSED_SHELL('',(#28,#148,#224,#300,#386,#457,#514,#592,#670,#780,
    #856,#932,#1008,#1084,#1160,#1236,#1312,#1388,#1464,#1540,#1616,
    #1692,#1768,#1844,#1920,#1996,#2072,#2148,#2224,#2300,#2376,#2452,
    #2528,#2604,#2680,#2756,#2832,#2908,#2984,#3055,#3102,#3180,#3258,
    #3336,#3414,#3492,#3570,#3648,#3726,#3804,#3882,#3960,#4038,#4116,
    #4194,#4272,#4350,#4428,#4506,#4609));
#28 = ADVANCED_FACE('',(#29),#43,.T.);
#29 = FACE_BOUND('',#30,.T.);
#30 = EDGE_LOOP('',(#31,#66,#94,#122));
#31 = ORIENTED_EDGE('',*,*,#32,.T.);
#32 = EDGE_CURVE('',#33,#35,#37,.T.);
#33 = VERTEX_POINT('',#34);
#34 = CARTESIAN_POINT('',(7.619999999937E-006,7.99998146,0.E+000));
#35 = VERTEX_POINT('',#36);
#36 = CARTESIAN_POINT('',(7.619999999937E-006,7.99998146,1.64592));
#37 = SURFACE_CURVE('',#38,(#42,#54),.PCURVE_S1.);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(7.619999999937E-006,7.99998146,0.E+000));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.E+000,0.E+000,1.));
#42 = PCURVE('',#43,#48);
#43 = PLANE('',#44);
#44 = AXIS2_PLACEMENT_3D('',#45,#46,#47);
#45 = CARTESIAN_POINT('',(7.62E-006,7.99998146,0.E+000));
#46 = DIRECTION('',(-1.,4.247490571951E-008,0.E+000));
#47 = DIRECTION('',(4.247490571951E-008,1.,0.E+000));
#48 = DEFINITIONAL_REPRESENTATION('',(#49),#53);
#49 = LINE('',#50,#51);
#50 = CARTESIAN_POINT('',(8.881784197001E-016,0.E+000));
#51 = VECTOR('',#52,1.);
#52 = DIRECTION('',(0.E+000,-1.));
#53 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#54 = PCURVE('',#55,#60);
#55 = CYLINDRICAL_SURFACE('',#56,7.999997164497);
#56 = AXIS2_PLACEMENT_3D('',#57,#58,#59);
#57 = CARTESIAN_POINT('',(8.000004784477,7.999999704497,0.E+000));
#58 = DIRECTION('',(0.E+000,0.E+000,-1.));
#59 = DIRECTION('',(-5.805959106317E-008,-1.,-0.E+000));
#60 = DEFINITIONAL_REPRESENTATION('',(#61),#65);
#61 = LINE('',#62,#63);
#62 = CARTESIAN_POINT('',(1.570793988172,0.E+000));
#63 = VECTOR('',#64,1.);
#64 = DIRECTION('',(0.E+000,-1.));
#65 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#66 = ORIENTED_EDGE('',*,*,#67,.T.);
#67 = EDGE_CURVE('',#35,#68,#70,.T.);
#68 = VERTEX_POINT('',#69);
#69 = CARTESIAN_POINT('',(1.27E-005,127.6000115,1.64592));
#70 = SURFACE_CURVE('',#71,(#75,#82),.PCURVE_S1.);
#71 = LINE('',#72,#73);
#72 = CARTESIAN_POINT('',(7.62E-006,7.99998146,1.64592));
#73 = VECTOR('',#74,1.);
#74 = DIRECTION('',(4.247490571951E-008,1.,0.E+000));
#75 = PCURVE('',#43,#76);
#76 = DEFINITIONAL_REPRESENTATION('',(#77),#81);
#77 = LINE('',#78,#79);
#78 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#79 = VECTOR('',#80,1.);
#80 = DIRECTION('',(1.,0.E+000));
#81 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#82 = PCURVE('',#83,#88);
#83 = PLANE('',#84);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(7.62E-006,7.99998146,1.64592));
#86 = DIRECTION('',(0.E+000,0.E+000,1.));
#87 = DIRECTION('',(1.,0.E+000,-0.E+000));
#88 = DEFINITIONAL_REPRESENTATION('',(#89),#93);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(4.247490571951E-008,1.));
#93 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#94 = ORIENTED_EDGE('',*,*,#95,.F.);
#95 = EDGE_CURVE('',#96,#68,#98,.T.);
#96 = VERTEX_POINT('',#97);
#97 = CARTESIAN_POINT('',(1.27E-005,127.6000115,0.E+000));
#98 = SURFACE_CURVE('',#99,(#103,#110),.PCURVE_S1.);
#99 = LINE('',#100,#101);
#100 = CARTESIAN_POINT('',(1.27E-005,127.6000115,0.E+000));
#101 = VECTOR('',#102,1.);
#102 = DIRECTION('',(0.E+000,0.E+000,1.));
#103 = PCURVE('',#43,#104);
#104 = DEFINITIONAL_REPRESENTATION('',(#105),#109);
#105 = LINE('',#106,#107);
#106 = CARTESIAN_POINT('',(119.60003004,0.E+000));
#107 = VECTOR('',#108,1.);
#108 = DIRECTION('',(0.E+000,-1.));
#109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#110 = PCURVE('',#111,#116);
#111 = PLANE('',#112);
#112 = AXIS2_PLACEMENT_3D('',#113,#114,#115);
#113 = CARTESIAN_POINT('',(1.27E-005,127.6000115,0.E+000));
#114 = DIRECTION('',(1.4269665396E-007,1.,-0.E+000));
#115 = DIRECTION('',(1.,-1.4269665396E-007,0.E+000));
#116 = DEFINITIONAL_REPRESENTATION('',(#117),#121);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(0.E+000,-1.));
#121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#122 = ORIENTED_EDGE('',*,*,#123,.F.);
#123 = EDGE_CURVE('',#33,#96,#124,.T.);
#124 = SURFACE_CURVE('',#125,(#129,#136),.PCURVE_S1.);
#125 = LINE('',#126,#127);
#126 = CARTESIAN_POINT('',(7.62E-006,7.99998146,0.E+000));
#127 = VECTOR('',#128,1.);
#128 = DIRECTION('',(4.247490571951E-008,1.,0.E+000));
#129 = PCURVE('',#43,#130);
#130 = DEFINITIONAL_REPRESENTATION('',(#131),#135);
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(1.,0.E+000));
#135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#136 = PCURVE('',#137,#142);
#137 = PLANE('',#138);
#138 = AXIS2_PLACEMENT_3D('',#139,#140,#141);
#139 = CARTESIAN_POINT('',(7.62E-006,7.99998146,0.E+000));
#140 = DIRECTION('',(0.E+000,0.E+000,1.));
#141 = DIRECTION('',(1.,0.E+000,-0.E+000));
#142 = DEFINITIONAL_REPRESENTATION('',(#143),#147);
#143 = LINE('',#144,#145);
#144 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#145 = VECTOR('',#146,1.);
#146 = DIRECTION('',(4.247490571951E-008,1.));
#147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#148 = ADVANCED_FACE('',(#149),#111,.T.);
#149 = FACE_BOUND('',#150,.T.);
#150 = EDGE_LOOP('',(#151,#152,#175,#203));
#151 = ORIENTED_EDGE('',*,*,#95,.T.);
#152 = ORIENTED_EDGE('',*,*,#153,.T.);
#153 = EDGE_CURVE('',#68,#154,#156,.T.);
#154 = VERTEX_POINT('',#155);
#155 = CARTESIAN_POINT('',(88.99999726,127.5999988,1.64592));
#156 = SURFACE_CURVE('',#157,(#161,#168),.PCURVE_S1.);
#157 = LINE('',#158,#159);
#158 = CARTESIAN_POINT('',(1.27E-005,127.6000115,1.64592));
#159 = VECTOR('',#160,1.);
#160 = DIRECTION('',(1.,-1.4269665396E-007,0.E+000));
#161 = PCURVE('',#111,#162);
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(1.,0.E+000));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = PCURVE('',#83,#169);
#169 = DEFINITIONAL_REPRESENTATION('',(#170),#174);
#170 = LINE('',#171,#172);
#171 = CARTESIAN_POINT('',(5.08E-006,119.60003004));
#172 = VECTOR('',#173,1.);
#173 = DIRECTION('',(1.,-1.4269665396E-007));
#174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#175 = ORIENTED_EDGE('',*,*,#176,.F.);
#176 = EDGE_CURVE('',#177,#154,#179,.T.);
#177 = VERTEX_POINT('',#178);
#178 = CARTESIAN_POINT('',(88.99999726,127.5999988,0.E+000));
#179 = SURFACE_CURVE('',#180,(#184,#191),.PCURVE_S1.);
#180 = LINE('',#181,#182);
#181 = CARTESIAN_POINT('',(88.99999726,127.5999988,0.E+000));
#182 = VECTOR('',#183,1.);
#183 = DIRECTION('',(0.E+000,0.E+000,1.));
#184 = PCURVE('',#111,#185);
#185 = DEFINITIONAL_REPRESENTATION('',(#186),#190);
#186 = LINE('',#187,#188);
#187 = CARTESIAN_POINT('',(88.999984560001,0.E+000));
#188 = VECTOR('',#189,1.);
#189 = DIRECTION('',(0.E+000,-1.));
#190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#191 = PCURVE('',#192,#197);
#192 = PLANE('',#193);
#193 = AXIS2_PLACEMENT_3D('',#194,#195,#196);
#194 = CARTESIAN_POINT('',(88.99999726,127.5999988,0.E+000));
#195 = DIRECTION('',(1.,4.247491649402E-008,-0.E+000));
#196 = DIRECTION('',(4.247491649402E-008,-1.,0.E+000));
#197 = DEFINITIONAL_REPRESENTATION('',(#198),#202);
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(0.E+000,-1.));
#202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#203 = ORIENTED_EDGE('',*,*,#204,.F.);
#204 = EDGE_CURVE('',#96,#177,#205,.T.);
#205 = SURFACE_CURVE('',#206,(#210,#217),.PCURVE_S1.);
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(1.27E-005,127.6000115,0.E+000));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(1.,-1.4269665396E-007,0.E+000));
#210 = PCURVE('',#111,#211);
#211 = DEFINITIONAL_REPRESENTATION('',(#212),#216);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(1.,0.E+000));
#216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#217 = PCURVE('',#137,#218);
#218 = DEFINITIONAL_REPRESENTATION('',(#219),#223);
#219 = LINE('',#220,#221);
#220 = CARTESIAN_POINT('',(5.08E-006,119.60003004));
#221 = VECTOR('',#222,1.);
#222 = DIRECTION('',(1.,-1.4269665396E-007));
#223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#224 = ADVANCED_FACE('',(#225),#192,.T.);
#225 = FACE_BOUND('',#226,.T.);
#226 = EDGE_LOOP('',(#227,#228,#251,#279));
#227 = ORIENTED_EDGE('',*,*,#176,.T.);
#228 = ORIENTED_EDGE('',*,*,#229,.T.);
#229 = EDGE_CURVE('',#154,#230,#232,.T.);
#230 = VERTEX_POINT('',#231);
#231 = CARTESIAN_POINT('',(89.00000234,7.99999924,1.64592));
#232 = SURFACE_CURVE('',#233,(#237,#244),.PCURVE_S1.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(88.99999726,127.5999988,1.64592));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(4.247491649402E-008,-1.,0.E+000));
#237 = PCURVE('',#192,#238);
#238 = DEFINITIONAL_REPRESENTATION('',(#239),#243);
#239 = LINE('',#240,#241);
#240 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#241 = VECTOR('',#242,1.);
#242 = DIRECTION('',(1.,0.E+000));
#243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#244 = PCURVE('',#83,#245);
#245 = DEFINITIONAL_REPRESENTATION('',(#246),#250);
#246 = LINE('',#247,#248);
#247 = CARTESIAN_POINT('',(88.99998964,119.60001734));
#248 = VECTOR('',#249,1.);
#249 = DIRECTION('',(4.247491649402E-008,-1.));
#250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#251 = ORIENTED_EDGE('',*,*,#252,.F.);
#252 = EDGE_CURVE('',#253,#230,#255,.T.);
#253 = VERTEX_POINT('',#254);
#254 = CARTESIAN_POINT('',(89.00000234,7.99999924,0.E+000));
#255 = SURFACE_CURVE('',#256,(#260,#267),.PCURVE_S1.);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(89.00000234,7.99999924,0.E+000));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(0.E+000,0.E+000,1.));
#260 = PCURVE('',#192,#261);
#261 = DEFINITIONAL_REPRESENTATION('',(#262),#266);
#262 = LINE('',#263,#264);
#263 = CARTESIAN_POINT('',(119.59999956,0.E+000));
#264 = VECTOR('',#265,1.);
#265 = DIRECTION('',(0.E+000,-1.));
#266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#267 = PCURVE('',#268,#273);
#268 = CYLINDRICAL_SURFACE('',#269,7.999997164471);
#269 = AXIS2_PLACEMENT_3D('',#270,#271,#272);
#270 = CARTESIAN_POINT('',(81.00000517553,7.99999716447,0.E+000));
#271 = DIRECTION('',(0.E+000,0.E+000,-1.));
#272 = DIRECTION('',(1.,2.59441309022E-007,0.E+000));
#273 = DEFINITIONAL_REPRESENTATION('',(#274),#278);
#274 = LINE('',#275,#276);
#275 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#276 = VECTOR('',#277,1.);
#277 = DIRECTION('',(0.E+000,-1.));
#278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#279 = ORIENTED_EDGE('',*,*,#280,.F.);
#280 = EDGE_CURVE('',#177,#253,#281,.T.);
#281 = SURFACE_CURVE('',#282,(#286,#293),.PCURVE_S1.);
#282 = LINE('',#283,#284);
#283 = CARTESIAN_POINT('',(88.99999726,127.5999988,0.E+000));
#284 = VECTOR('',#285,1.);
#285 = DIRECTION('',(4.247491649402E-008,-1.,0.E+000));
#286 = PCURVE('',#192,#287);
#287 = DEFINITIONAL_REPRESENTATION('',(#288),#292);
#288 = LINE('',#289,#290);
#289 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#290 = VECTOR('',#291,1.);
#291 = DIRECTION('',(1.,0.E+000));
#292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#293 = PCURVE('',#137,#294);
#294 = DEFINITIONAL_REPRESENTATION('',(#295),#299);
#295 = LINE('',#296,#297);
#296 = CARTESIAN_POINT('',(88.99998964,119.60001734));
#297 = VECTOR('',#298,1.);
#298 = DIRECTION('',(4.247491649402E-008,-1.));
#299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#300 = ADVANCED_FACE('',(#301),#268,.T.);
#301 = FACE_BOUND('',#302,.T.);
#302 = EDGE_LOOP('',(#303,#304,#332,#360));
#303 = ORIENTED_EDGE('',*,*,#252,.T.);
#304 = ORIENTED_EDGE('',*,*,#305,.T.);
#305 = EDGE_CURVE('',#230,#306,#308,.T.);
#306 = VERTEX_POINT('',#307);
#307 = CARTESIAN_POINT('',(81.0000031,2.422324493767E-015,1.64592));
#308 = SURFACE_CURVE('',#309,(#314,#321),.PCURVE_S1.);
#309 = CIRCLE('',#310,7.999997164471);
#310 = AXIS2_PLACEMENT_3D('',#311,#312,#313);
#311 = CARTESIAN_POINT('',(81.00000517553,7.99999716447,1.64592));
#312 = DIRECTION('',(0.E+000,0.E+000,-1.));
#313 = DIRECTION('',(1.,2.59441309022E-007,0.E+000));
#314 = PCURVE('',#268,#315);
#315 = DEFINITIONAL_REPRESENTATION('',(#316),#320);
#316 = LINE('',#317,#318);
#317 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#318 = VECTOR('',#319,1.);
#319 = DIRECTION('',(1.,0.E+000));
#320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#321 = PCURVE('',#83,#322);
#322 = DEFINITIONAL_REPRESENTATION('',(#323),#331);
#323 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#324,#325,#326,#327,#328,#329
,#330),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#324 = CARTESIAN_POINT('',(88.99999472,1.77800000003E-005));
#325 = CARTESIAN_POINT('',(88.999998314923,-13.85638376926));
#326 = CARTESIAN_POINT('',(77.000000770756,-6.928186107929));
#327 = CARTESIAN_POINT('',(65.000003226589,1.155341078877E-005));
#328 = CARTESIAN_POINT('',(76.999997175833,6.92821544134));
#329 = CARTESIAN_POINT('',(88.999991125077,13.856419329269));
#330 = CARTESIAN_POINT('',(88.99999472,1.77800000003E-005));
#331 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#332 = ORIENTED_EDGE('',*,*,#333,.F.);
#333 = EDGE_CURVE('',#334,#306,#336,.T.);
#334 = VERTEX_POINT('',#335);
#335 = CARTESIAN_POINT('',(81.0000031,2.422324493767E-015,0.E+000));
#336 = SURFACE_CURVE('',#337,(#341,#348),.PCURVE_S1.);
#337 = LINE('',#338,#339);
#338 = CARTESIAN_POINT('',(81.0000031,2.422324493767E-015,0.E+000));
#339 = VECTOR('',#340,1.);
#340 = DIRECTION('',(0.E+000,0.E+000,1.));
#341 = PCURVE('',#268,#342);
#342 = DEFINITIONAL_REPRESENTATION('',(#343),#347);
#343 = LINE('',#344,#345);
#344 = CARTESIAN_POINT('',(1.570796845678,0.E+000));
#345 = VECTOR('',#346,1.);
#346 = DIRECTION('',(0.E+000,-1.));
#347 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#348 = PCURVE('',#349,#354);
#349 = PLANE('',#350);
#350 = AXIS2_PLACEMENT_3D('',#351,#352,#353);
#351 = CARTESIAN_POINT('',(81.0000031,0.E+000,0.E+000));
#352 = DIRECTION('',(-3.479452112944E-008,-1.,0.E+000));
#353 = DIRECTION('',(-1.,3.479452112944E-008,0.E+000));
#354 = DEFINITIONAL_REPRESENTATION('',(#355),#359);
#355 = LINE('',#356,#357);
#356 = CARTESIAN_POINT('',(1.02038883306E-037,0.E+000));
#357 = VECTOR('',#358,1.);
#358 = DIRECTION('',(0.E+000,-1.));
#359 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#360 = ORIENTED_EDGE('',*,*,#361,.F.);
#361 = EDGE_CURVE('',#253,#334,#362,.T.);
#362 = SURFACE_CURVE('',#363,(#368,#375),.PCURVE_S1.);
#363 = CIRCLE('',#364,7.999997164471);
#364 = AXIS2_PLACEMENT_3D('',#365,#366,#367);
#365 = CARTESIAN_POINT('',(81.00000517553,7.99999716447,0.E+000));
#366 = DIRECTION('',(0.E+000,0.E+000,-1.));
#367 = DIRECTION('',(1.,2.59441309022E-007,0.E+000));
#368 = PCURVE('',#268,#369);
#369 = DEFINITIONAL_REPRESENTATION('',(#370),#374);
#370 = LINE('',#371,#372);
#371 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#372 = VECTOR('',#373,1.);
#373 = DIRECTION('',(1.,0.E+000));
#374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#375 = PCURVE('',#137,#376);
#376 = DEFINITIONAL_REPRESENTATION('',(#377),#385);
#377 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#378,#379,#380,#381,#382,#383
,#384),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#378 = CARTESIAN_POINT('',(88.99999472,1.77800000003E-005));
#379 = CARTESIAN_POINT('',(88.999998314923,-13.85638376926));
#380 = CARTESIAN_POINT('',(77.000000770756,-6.928186107929));
#381 = CARTESIAN_POINT('',(65.000003226589,1.155341078877E-005));
#382 = CARTESIAN_POINT('',(76.999997175833,6.92821544134));
#383 = CARTESIAN_POINT('',(88.999991125077,13.856419329269));
#384 = CARTESIAN_POINT('',(88.99999472,1.77800000003E-005));
#385 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#386 = ADVANCED_FACE('',(#387),#349,.T.);
#387 = FACE_BOUND('',#388,.T.);
#388 = EDGE_LOOP('',(#389,#390,#413,#436));
#389 = ORIENTED_EDGE('',*,*,#333,.T.);
#390 = ORIENTED_EDGE('',*,*,#391,.T.);
#391 = EDGE_CURVE('',#306,#392,#394,.T.);
#392 = VERTEX_POINT('',#393);
#393 = CARTESIAN_POINT('',(8.00000432,2.540000000275E-006,1.64592));
#394 = SURFACE_CURVE('',#395,(#399,#406),.PCURVE_S1.);
#395 = LINE('',#396,#397);
#396 = CARTESIAN_POINT('',(81.0000031,0.E+000,1.64592));
#397 = VECTOR('',#398,1.);
#398 = DIRECTION('',(-1.,3.479452112944E-008,0.E+000));
#399 = PCURVE('',#349,#400);
#400 = DEFINITIONAL_REPRESENTATION('',(#401),#405);
#401 = LINE('',#402,#403);
#402 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#403 = VECTOR('',#404,1.);
#404 = DIRECTION('',(1.,0.E+000));
#405 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#406 = PCURVE('',#83,#407);
#407 = DEFINITIONAL_REPRESENTATION('',(#408),#412);
#408 = LINE('',#409,#410);
#409 = CARTESIAN_POINT('',(80.99999548,-7.99998146));
#410 = VECTOR('',#411,1.);
#411 = DIRECTION('',(-1.,3.479452112944E-008));
#412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#413 = ORIENTED_EDGE('',*,*,#414,.F.);
#414 = EDGE_CURVE('',#415,#392,#417,.T.);
#415 = VERTEX_POINT('',#416);
#416 = CARTESIAN_POINT('',(8.00000432,2.540000000275E-006,0.E+000));
#417 = SURFACE_CURVE('',#418,(#422,#429),.PCURVE_S1.);
#418 = LINE('',#419,#420);
#419 = CARTESIAN_POINT('',(8.00000432,2.540000000275E-006,0.E+000));
#420 = VECTOR('',#421,1.);
#421 = DIRECTION('',(0.E+000,0.E+000,1.));
#422 = PCURVE('',#349,#423);
#423 = DEFINITIONAL_REPRESENTATION('',(#424),#428);
#424 = LINE('',#425,#426);
#425 = CARTESIAN_POINT('',(72.99999878,0.E+000));
#426 = VECTOR('',#427,1.);
#427 = DIRECTION('',(0.E+000,-1.));
#428 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#429 = PCURVE('',#55,#430);
#430 = DEFINITIONAL_REPRESENTATION('',(#431),#435);
#431 = LINE('',#432,#433);
#432 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#433 = VECTOR('',#434,1.);
#434 = DIRECTION('',(0.E+000,-1.));
#435 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#436 = ORIENTED_EDGE('',*,*,#437,.F.);
#437 = EDGE_CURVE('',#334,#415,#438,.T.);
#438 = SURFACE_CURVE('',#439,(#443,#450),.PCURVE_S1.);
#439 = LINE('',#440,#441);
#440 = CARTESIAN_POINT('',(81.0000031,0.E+000,0.E+000));
#441 = VECTOR('',#442,1.);
#442 = DIRECTION('',(-1.,3.479452112944E-008,0.E+000));
#443 = PCURVE('',#349,#444);
#444 = DEFINITIONAL_REPRESENTATION('',(#445),#449);
#445 = LINE('',#446,#447);
#446 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#447 = VECTOR('',#448,1.);
#448 = DIRECTION('',(1.,0.E+000));
#449 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#450 = PCURVE('',#137,#451);
#451 = DEFINITIONAL_REPRESENTATION('',(#452),#456);
#452 = LINE('',#453,#454);
#453 = CARTESIAN_POINT('',(80.99999548,-7.99998146));
#454 = VECTOR('',#455,1.);
#455 = DIRECTION('',(-1.,3.479452112944E-008));
#456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#457 = ADVANCED_FACE('',(#458),#55,.T.);
#458 = FACE_BOUND('',#459,.T.);
#459 = EDGE_LOOP('',(#460,#461,#487,#488));
#460 = ORIENTED_EDGE('',*,*,#414,.T.);
#461 = ORIENTED_EDGE('',*,*,#462,.T.);
#462 = EDGE_CURVE('',#392,#35,#463,.T.);
#463 = SURFACE_CURVE('',#464,(#469,#476),.PCURVE_S1.);
#464 = CIRCLE('',#465,7.999997164497);
#465 = AXIS2_PLACEMENT_3D('',#466,#467,#468);
#466 = CARTESIAN_POINT('',(8.000004784477,7.999999704497,1.64592));
#467 = DIRECTION('',(0.E+000,0.E+000,-1.));
#468 = DIRECTION('',(-5.805959106317E-008,-1.,-0.E+000));
#469 = PCURVE('',#55,#470);
#470 = DEFINITIONAL_REPRESENTATION('',(#471),#475);
#471 = LINE('',#472,#473);
#472 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#473 = VECTOR('',#474,1.);
#474 = DIRECTION('',(1.,0.E+000));
#475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#476 = PCURVE('',#83,#477);
#477 = DEFINITIONAL_REPRESENTATION('',(#478),#486);
#478 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#479,#480,#481,#482,#483,#484
,#485),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#479 = CARTESIAN_POINT('',(7.9999967,-7.99997892));
#480 = CARTESIAN_POINT('',(-5.856404849316,-7.999978115503));
#481 = CARTESIAN_POINT('',(1.071796622057,4.000017228995));
#482 = CARTESIAN_POINT('',(7.99999809343,16.000012573492));
#483 = CARTESIAN_POINT('',(14.928198171373,4.000016424498));
#484 = CARTESIAN_POINT('',(21.856398249316,-7.999979724497));
#485 = CARTESIAN_POINT('',(7.9999967,-7.99997892));
#486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#487 = ORIENTED_EDGE('',*,*,#32,.F.);
#488 = ORIENTED_EDGE('',*,*,#489,.F.);
#489 = EDGE_CURVE('',#415,#33,#490,.T.);
#490 = SURFACE_CURVE('',#491,(#496,#503),.PCURVE_S1.);
#491 = CIRCLE('',#492,7.999997164497);
#492 = AXIS2_PLACEMENT_3D('',#493,#494,#495);
#493 = CARTESIAN_POINT('',(8.000004784477,7.999999704497,0.E+000));
#494 = DIRECTION('',(0.E+000,0.E+000,-1.));
#495 = DIRECTION('',(-5.805959106317E-008,-1.,-0.E+000));
#496 = PCURVE('',#55,#497);
#497 = DEFINITIONAL_REPRESENTATION('',(#498),#502);
#498 = LINE('',#499,#500);
#499 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#500 = VECTOR('',#501,1.);
#501 = DIRECTION('',(1.,0.E+000));
#502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#503 = PCURVE('',#137,#504);
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#513);
#505 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#506,#507,#508,#509,#510,#511
,#512),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#506 = CARTESIAN_POINT('',(7.9999967,-7.99997892));
#507 = CARTESIAN_POINT('',(-5.856404849316,-7.999978115503));
#508 = CARTESIAN_POINT('',(1.071796622057,4.000017228995));
#509 = CARTESIAN_POINT('',(7.99999809343,16.000012573492));
#510 = CARTESIAN_POINT('',(14.928198171373,4.000016424498));
#511 = CARTESIAN_POINT('',(21.856398249316,-7.999979724497));
#512 = CARTESIAN_POINT('',(7.9999967,-7.99997892));
#513 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#514 = ADVANCED_FACE('',(#515),#529,.T.);
#515 = FACE_BOUND('',#516,.F.);
#516 = EDGE_LOOP('',(#517,#547,#569,#570));
#517 = ORIENTED_EDGE('',*,*,#518,.T.);
#518 = EDGE_CURVE('',#519,#521,#523,.T.);
#519 = VERTEX_POINT('',#520);
#520 = CARTESIAN_POINT('',(6.1000005,7.99999924,0.E+000));
#521 = VERTEX_POINT('',#522);
#522 = CARTESIAN_POINT('',(6.1000005,7.99999924,1.64592));
#523 = SEAM_CURVE('',#524,(#528,#540),.PCURVE_S1.);
#524 = LINE('',#525,#526);
#525 = CARTESIAN_POINT('',(6.1000005,7.99999924,0.E+000));
#526 = VECTOR('',#527,1.);
#527 = DIRECTION('',(0.E+000,0.E+000,1.));
#528 = PCURVE('',#529,#534);
#529 = CYLINDRICAL_SURFACE('',#530,1.59999934);
#530 = AXIS2_PLACEMENT_3D('',#531,#532,#533);
#531 = CARTESIAN_POINT('',(4.50000116,7.99999924,0.E+000));
#532 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#533 = DIRECTION('',(1.,0.E+000,-0.E+000));
#534 = DEFINITIONAL_REPRESENTATION('',(#535),#539);
#535 = LINE('',#536,#537);
#536 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#537 = VECTOR('',#538,1.);
#538 = DIRECTION('',(-0.E+000,-1.));
#539 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#540 = PCURVE('',#529,#541);
#541 = DEFINITIONAL_REPRESENTATION('',(#542),#546);
#542 = LINE('',#543,#544);
#543 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#544 = VECTOR('',#545,1.);
#545 = DIRECTION('',(-0.E+000,-1.));
#546 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#547 = ORIENTED_EDGE('',*,*,#548,.T.);
#548 = EDGE_CURVE('',#521,#521,#549,.T.);
#549 = SURFACE_CURVE('',#550,(#555,#562),.PCURVE_S1.);
#550 = CIRCLE('',#551,1.59999934);
#551 = AXIS2_PLACEMENT_3D('',#552,#553,#554);
#552 = CARTESIAN_POINT('',(4.50000116,7.99999924,1.64592));
#553 = DIRECTION('',(0.E+000,0.E+000,1.));
#554 = DIRECTION('',(1.,0.E+000,-0.E+000));
#555 = PCURVE('',#529,#556);
#556 = DEFINITIONAL_REPRESENTATION('',(#557),#561);
#557 = LINE('',#558,#559);
#558 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#559 = VECTOR('',#560,1.);
#560 = DIRECTION('',(-1.,0.E+000));
#561 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#562 = PCURVE('',#83,#563);
#563 = DEFINITIONAL_REPRESENTATION('',(#564),#568);
#564 = CIRCLE('',#565,1.59999934);
#565 = AXIS2_PLACEMENT_2D('',#566,#567);
#566 = CARTESIAN_POINT('',(4.49999354,1.777999999764E-005));
#567 = DIRECTION('',(1.,0.E+000));
#568 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#569 = ORIENTED_EDGE('',*,*,#518,.F.);
#570 = ORIENTED_EDGE('',*,*,#571,.F.);
#571 = EDGE_CURVE('',#519,#519,#572,.T.);
#572 = SURFACE_CURVE('',#573,(#578,#585),.PCURVE_S1.);
#573 = CIRCLE('',#574,1.59999934);
#574 = AXIS2_PLACEMENT_3D('',#575,#576,#577);
#575 = CARTESIAN_POINT('',(4.50000116,7.99999924,0.E+000));
#576 = DIRECTION('',(0.E+000,0.E+000,1.));
#577 = DIRECTION('',(1.,0.E+000,-0.E+000));
#578 = PCURVE('',#529,#579);
#579 = DEFINITIONAL_REPRESENTATION('',(#580),#584);
#580 = LINE('',#581,#582);
#581 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#582 = VECTOR('',#583,1.);
#583 = DIRECTION('',(-1.,0.E+000));
#584 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#585 = PCURVE('',#137,#586);
#586 = DEFINITIONAL_REPRESENTATION('',(#587),#591);
#587 = CIRCLE('',#588,1.59999934);
#588 = AXIS2_PLACEMENT_2D('',#589,#590);
#589 = CARTESIAN_POINT('',(4.49999354,1.777999999764E-005));
#590 = DIRECTION('',(1.,0.E+000));
#591 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#592 = ADVANCED_FACE('',(#593),#607,.T.);
#593 = FACE_BOUND('',#594,.F.);
#594 = EDGE_LOOP('',(#595,#625,#647,#648));
#595 = ORIENTED_EDGE('',*,*,#596,.T.);
#596 = EDGE_CURVE('',#597,#599,#601,.T.);
#597 = VERTEX_POINT('',#598);
#598 = CARTESIAN_POINT('',(12.6500001,33.49999904,0.E+000));
#599 = VERTEX_POINT('',#600);
#600 = CARTESIAN_POINT('',(12.6500001,33.49999904,1.64592));
#601 = SEAM_CURVE('',#602,(#606,#618),.PCURVE_S1.);
#602 = LINE('',#603,#604);
#603 = CARTESIAN_POINT('',(12.6500001,33.49999904,0.E+000));
#604 = VECTOR('',#605,1.);
#605 = DIRECTION('',(0.E+000,0.E+000,1.));
#606 = PCURVE('',#607,#612);
#607 = CYLINDRICAL_SURFACE('',#608,1.89999874);
#608 = AXIS2_PLACEMENT_3D('',#609,#610,#611);
#609 = CARTESIAN_POINT('',(10.75000136,33.49999904,0.E+000));
#610 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#611 = DIRECTION('',(1.,0.E+000,-0.E+000));
#612 = DEFINITIONAL_REPRESENTATION('',(#613),#617);
#613 = LINE('',#614,#615);
#614 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#615 = VECTOR('',#616,1.);
#616 = DIRECTION('',(-0.E+000,-1.));
#617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#618 = PCURVE('',#607,#619);
#619 = DEFINITIONAL_REPRESENTATION('',(#620),#624);
#620 = LINE('',#621,#622);
#621 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#622 = VECTOR('',#623,1.);
#623 = DIRECTION('',(-0.E+000,-1.));
#624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#625 = ORIENTED_EDGE('',*,*,#626,.T.);
#626 = EDGE_CURVE('',#599,#599,#627,.T.);
#627 = SURFACE_CURVE('',#628,(#633,#640),.PCURVE_S1.);
#628 = CIRCLE('',#629,1.89999874);
#629 = AXIS2_PLACEMENT_3D('',#630,#631,#632);
#630 = CARTESIAN_POINT('',(10.75000136,33.49999904,1.64592));
#631 = DIRECTION('',(0.E+000,0.E+000,1.));
#632 = DIRECTION('',(1.,0.E+000,-0.E+000));
#633 = PCURVE('',#607,#634);
#634 = DEFINITIONAL_REPRESENTATION('',(#635),#639);
#635 = LINE('',#636,#637);
#636 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#637 = VECTOR('',#638,1.);
#638 = DIRECTION('',(-1.,0.E+000));
#639 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#640 = PCURVE('',#83,#641);
#641 = DEFINITIONAL_REPRESENTATION('',(#642),#646);
#642 = CIRCLE('',#643,1.89999874);
#643 = AXIS2_PLACEMENT_2D('',#644,#645);
#644 = CARTESIAN_POINT('',(10.74999374,25.50001758));
#645 = DIRECTION('',(1.,0.E+000));
#646 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#647 = ORIENTED_EDGE('',*,*,#596,.F.);
#648 = ORIENTED_EDGE('',*,*,#649,.F.);
#649 = EDGE_CURVE('',#597,#597,#650,.T.);
#650 = SURFACE_CURVE('',#651,(#656,#663),.PCURVE_S1.);
#651 = CIRCLE('',#652,1.89999874);
#652 = AXIS2_PLACEMENT_3D('',#653,#654,#655);
#653 = CARTESIAN_POINT('',(10.75000136,33.49999904,0.E+000));
#654 = DIRECTION('',(0.E+000,0.E+000,1.));
#655 = DIRECTION('',(1.,0.E+000,-0.E+000));
#656 = PCURVE('',#607,#657);
#657 = DEFINITIONAL_REPRESENTATION('',(#658),#662);
#658 = LINE('',#659,#660);
#659 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#660 = VECTOR('',#661,1.);
#661 = DIRECTION('',(-1.,0.E+000));
#662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#663 = PCURVE('',#137,#664);
#664 = DEFINITIONAL_REPRESENTATION('',(#665),#669);
#665 = CIRCLE('',#666,1.89999874);
#666 = AXIS2_PLACEMENT_2D('',#667,#668);
#667 = CARTESIAN_POINT('',(10.74999374,25.50001758));
#668 = DIRECTION('',(1.,0.E+000));
#669 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#670 = ADVANCED_FACE('',(#671),#685,.F.);
#671 = FACE_BOUND('',#672,.F.);
#672 = EDGE_LOOP('',(#673,#708,#731,#759));
#673 = ORIENTED_EDGE('',*,*,#674,.T.);
#674 = EDGE_CURVE('',#675,#677,#679,.T.);
#675 = VERTEX_POINT('',#676);
#676 = CARTESIAN_POINT('',(71.00000278,14.26293058,0.E+000));
#677 = VERTEX_POINT('',#678);
#678 = CARTESIAN_POINT('',(71.00000278,14.26293058,1.64592));
#679 = SURFACE_CURVE('',#680,(#684,#696),.PCURVE_S1.);
#680 = LINE('',#681,#682);
#681 = CARTESIAN_POINT('',(71.00000278,14.26293058,0.E+000));
#682 = VECTOR('',#683,1.);
#683 = DIRECTION('',(0.E+000,0.E+000,1.));
#684 = PCURVE('',#685,#690);
#685 = PLANE('',#686);
#686 = AXIS2_PLACEMENT_3D('',#687,#688,#689);
#687 = CARTESIAN_POINT('',(71.00000278,14.26293058,0.E+000));
#688 = DIRECTION('',(1.627996183953E-004,-0.999999986748,0.E+000));
#689 = DIRECTION('',(-0.999999986748,-1.627996183953E-004,0.E+000));
#690 = DEFINITIONAL_REPRESENTATION('',(#691),#695);
#691 = LINE('',#692,#693);
#692 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#693 = VECTOR('',#694,1.);
#694 = DIRECTION('',(0.E+000,-1.));
#695 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#696 = PCURVE('',#697,#702);
#697 = PLANE('',#698);
#698 = AXIS2_PLACEMENT_3D('',#699,#700,#701);
#699 = CARTESIAN_POINT('',(71.26105636,14.22856184,0.E+000));
#700 = DIRECTION('',(-0.130527626456,-0.991444672552,0.E+000));
#701 = DIRECTION('',(-0.991444672552,0.130527626456,0.E+000));
#702 = DEFINITIONAL_REPRESENTATION('',(#703),#707);
#703 = LINE('',#704,#705);
#704 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#705 = VECTOR('',#706,1.);
#706 = DIRECTION('',(0.E+000,-1.));
#707 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#708 = ORIENTED_EDGE('',*,*,#709,.T.);
#709 = EDGE_CURVE('',#677,#710,#712,.T.);
#710 = VERTEX_POINT('',#711);
#711 = CARTESIAN_POINT('',(18.0000021,14.2543022,1.64592));
#712 = SURFACE_CURVE('',#713,(#717,#724),.PCURVE_S1.);
#713 = LINE('',#714,#715);
#714 = CARTESIAN_POINT('',(71.00000278,14.26293058,1.64592));
#715 = VECTOR('',#716,1.);
#716 = DIRECTION('',(-0.999999986748,-1.627996183953E-004,0.E+000));
#717 = PCURVE('',#685,#718);
#718 = DEFINITIONAL_REPRESENTATION('',(#719),#723);
#719 = LINE('',#720,#721);
#720 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#721 = VECTOR('',#722,1.);
#722 = DIRECTION('',(1.,0.E+000));
#723 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#724 = PCURVE('',#83,#725);
#725 = DEFINITIONAL_REPRESENTATION('',(#726),#730);
#726 = LINE('',#727,#728);
#727 = CARTESIAN_POINT('',(70.99999516,6.26294912));
#728 = VECTOR('',#729,1.);
#729 = DIRECTION('',(-0.999999986748,-1.627996183953E-004));
#730 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#731 = ORIENTED_EDGE('',*,*,#732,.F.);
#732 = EDGE_CURVE('',#733,#710,#735,.T.);
#733 = VERTEX_POINT('',#734);
#734 = CARTESIAN_POINT('',(18.0000021,14.2543022,0.E+000));
#735 = SURFACE_CURVE('',#736,(#740,#747),.PCURVE_S1.);
#736 = LINE('',#737,#738);
#737 = CARTESIAN_POINT('',(18.0000021,14.2543022,0.E+000));
#738 = VECTOR('',#739,1.);
#739 = DIRECTION('',(0.E+000,0.E+000,1.));
#740 = PCURVE('',#685,#741);
#741 = DEFINITIONAL_REPRESENTATION('',(#742),#746);
#742 = LINE('',#743,#744);
#743 = CARTESIAN_POINT('',(53.000001382348,0.E+000));
#744 = VECTOR('',#745,1.);
#745 = DIRECTION('',(0.E+000,-1.));
#746 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#747 = PCURVE('',#748,#753);
#748 = PLANE('',#749);
#749 = AXIS2_PLACEMENT_3D('',#750,#751,#752);
#750 = CARTESIAN_POINT('',(18.0000021,14.2543022,0.E+000));
#751 = DIRECTION('',(-1.,0.E+000,0.E+000));
#752 = DIRECTION('',(0.E+000,1.,0.E+000));
#753 = DEFINITIONAL_REPRESENTATION('',(#754),#758);
#754 = LINE('',#755,#756);
#755 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#756 = VECTOR('',#757,1.);
#757 = DIRECTION('',(0.E+000,-1.));
#758 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#759 = ORIENTED_EDGE('',*,*,#760,.F.);
#760 = EDGE_CURVE('',#675,#733,#761,.T.);
#761 = SURFACE_CURVE('',#762,(#766,#773),.PCURVE_S1.);
#762 = LINE('',#763,#764);
#763 = CARTESIAN_POINT('',(71.00000278,14.26293058,0.E+000));
#764 = VECTOR('',#765,1.);
#765 = DIRECTION('',(-0.999999986748,-1.627996183953E-004,0.E+000));
#766 = PCURVE('',#685,#767);
#767 = DEFINITIONAL_REPRESENTATION('',(#768),#772);
#768 = LINE('',#769,#770);
#769 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#770 = VECTOR('',#771,1.);
#771 = DIRECTION('',(1.,0.E+000));
#772 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#773 = PCURVE('',#137,#774);
#774 = DEFINITIONAL_REPRESENTATION('',(#775),#779);
#775 = LINE('',#776,#777);
#776 = CARTESIAN_POINT('',(70.99999516,6.26294912));
#777 = VECTOR('',#778,1.);
#778 = DIRECTION('',(-0.999999986748,-1.627996183953E-004));
#779 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#780 = ADVANCED_FACE('',(#781),#748,.F.);
#781 = FACE_BOUND('',#782,.F.);
#782 = EDGE_LOOP('',(#783,#784,#807,#835));
#783 = ORIENTED_EDGE('',*,*,#732,.T.);
#784 = ORIENTED_EDGE('',*,*,#785,.T.);
#785 = EDGE_CURVE('',#710,#786,#788,.T.);
#786 = VERTEX_POINT('',#787);
#787 = CARTESIAN_POINT('',(18.0000021,14.26293058,1.64592));
#788 = SURFACE_CURVE('',#789,(#793,#800),.PCURVE_S1.);
#789 = LINE('',#790,#791);
#790 = CARTESIAN_POINT('',(18.0000021,14.2543022,1.64592));
#791 = VECTOR('',#792,1.);
#792 = DIRECTION('',(0.E+000,1.,0.E+000));
#793 = PCURVE('',#748,#794);
#794 = DEFINITIONAL_REPRESENTATION('',(#795),#799);
#795 = LINE('',#796,#797);
#796 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#797 = VECTOR('',#798,1.);
#798 = DIRECTION('',(1.,0.E+000));
#799 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#800 = PCURVE('',#83,#801);
#801 = DEFINITIONAL_REPRESENTATION('',(#802),#806);
#802 = LINE('',#803,#804);
#803 = CARTESIAN_POINT('',(17.99999448,6.25432074));
#804 = VECTOR('',#805,1.);
#805 = DIRECTION('',(0.E+000,1.));
#806 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#807 = ORIENTED_EDGE('',*,*,#808,.F.);
#808 = EDGE_CURVE('',#809,#786,#811,.T.);
#809 = VERTEX_POINT('',#810);
#810 = CARTESIAN_POINT('',(18.0000021,14.26293058,0.E+000));
#811 = SURFACE_CURVE('',#812,(#816,#823),.PCURVE_S1.);
#812 = LINE('',#813,#814);
#813 = CARTESIAN_POINT('',(18.0000021,14.26293058,0.E+000));
#814 = VECTOR('',#815,1.);
#815 = DIRECTION('',(0.E+000,0.E+000,1.));
#816 = PCURVE('',#748,#817);
#817 = DEFINITIONAL_REPRESENTATION('',(#818),#822);
#818 = LINE('',#819,#820);
#819 = CARTESIAN_POINT('',(8.628379999998E-003,0.E+000));
#820 = VECTOR('',#821,1.);
#821 = DIRECTION('',(0.E+000,-1.));
#822 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#823 = PCURVE('',#824,#829);
#824 = PLANE('',#825);
#825 = AXIS2_PLACEMENT_3D('',#826,#827,#828);
#826 = CARTESIAN_POINT('',(18.0000021,14.26293058,0.E+000));
#827 = DIRECTION('',(0.130527626456,-0.991444672552,0.E+000));
#828 = DIRECTION('',(-0.991444672552,-0.130527626456,0.E+000));
#829 = DEFINITIONAL_REPRESENTATION('',(#830),#834);
#830 = LINE('',#831,#832);
#831 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#832 = VECTOR('',#833,1.);
#833 = DIRECTION('',(0.E+000,-1.));
#834 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#835 = ORIENTED_EDGE('',*,*,#836,.F.);
#836 = EDGE_CURVE('',#733,#809,#837,.T.);
#837 = SURFACE_CURVE('',#838,(#842,#849),.PCURVE_S1.);
#838 = LINE('',#839,#840);
#839 = CARTESIAN_POINT('',(18.0000021,14.2543022,0.E+000));
#840 = VECTOR('',#841,1.);
#841 = DIRECTION('',(0.E+000,1.,0.E+000));
#842 = PCURVE('',#748,#843);
#843 = DEFINITIONAL_REPRESENTATION('',(#844),#848);
#844 = LINE('',#845,#846);
#845 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#846 = VECTOR('',#847,1.);
#847 = DIRECTION('',(1.,0.E+000));
#848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#849 = PCURVE('',#137,#850);
#850 = DEFINITIONAL_REPRESENTATION('',(#851),#855);
#851 = LINE('',#852,#853);
#852 = CARTESIAN_POINT('',(17.99999448,6.25432074));
#853 = VECTOR('',#854,1.);
#854 = DIRECTION('',(0.E+000,1.));
#855 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#856 = ADVANCED_FACE('',(#857),#824,.F.);
#857 = FACE_BOUND('',#858,.F.);
#858 = EDGE_LOOP('',(#859,#860,#883,#911));
#859 = ORIENTED_EDGE('',*,*,#808,.T.);
#860 = ORIENTED_EDGE('',*,*,#861,.T.);
#861 = EDGE_CURVE('',#786,#862,#864,.T.);
#862 = VERTEX_POINT('',#863);
#863 = CARTESIAN_POINT('',(17.73894852,14.22856184,1.64592));
#864 = SURFACE_CURVE('',#865,(#869,#876),.PCURVE_S1.);
#865 = LINE('',#866,#867);
#866 = CARTESIAN_POINT('',(18.0000021,14.26293058,1.64592));
#867 = VECTOR('',#868,1.);
#868 = DIRECTION('',(-0.991444672552,-0.130527626456,0.E+000));
#869 = PCURVE('',#824,#870);
#870 = DEFINITIONAL_REPRESENTATION('',(#871),#875);
#871 = LINE('',#872,#873);
#872 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#873 = VECTOR('',#874,1.);
#874 = DIRECTION('',(1.,0.E+000));
#875 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#876 = PCURVE('',#83,#877);
#877 = DEFINITIONAL_REPRESENTATION('',(#878),#882);
#878 = LINE('',#879,#880);
#879 = CARTESIAN_POINT('',(17.99999448,6.26294912));
#880 = VECTOR('',#881,1.);
#881 = DIRECTION('',(-0.991444672552,-0.130527626456));
#882 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#883 = ORIENTED_EDGE('',*,*,#884,.F.);
#884 = EDGE_CURVE('',#885,#862,#887,.T.);
#885 = VERTEX_POINT('',#886);
#886 = CARTESIAN_POINT('',(17.73894852,14.22856184,0.E+000));
#887 = SURFACE_CURVE('',#888,(#892,#899),.PCURVE_S1.);
#888 = LINE('',#889,#890);
#889 = CARTESIAN_POINT('',(17.73894852,14.22856184,0.E+000));
#890 = VECTOR('',#891,1.);
#891 = DIRECTION('',(0.E+000,0.E+000,1.));
#892 = PCURVE('',#824,#893);
#893 = DEFINITIONAL_REPRESENTATION('',(#894),#898);
#894 = LINE('',#895,#896);
#895 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#896 = VECTOR('',#897,1.);
#897 = DIRECTION('',(0.E+000,-1.));
#898 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#899 = PCURVE('',#900,#905);
#900 = PLANE('',#901);
#901 = AXIS2_PLACEMENT_3D('',#902,#903,#904);
#902 = CARTESIAN_POINT('',(17.73894852,14.22856184,0.E+000));
#903 = DIRECTION('',(0.382682927661,-0.923879741566,0.E+000));
#904 = DIRECTION('',(-0.923879741566,-0.382682927661,0.E+000));
#905 = DEFINITIONAL_REPRESENTATION('',(#906),#910);
#906 = LINE('',#907,#908);
#907 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#908 = VECTOR('',#909,1.);
#909 = DIRECTION('',(0.E+000,-1.));
#910 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#911 = ORIENTED_EDGE('',*,*,#912,.F.);
#912 = EDGE_CURVE('',#809,#885,#913,.T.);
#913 = SURFACE_CURVE('',#914,(#918,#925),.PCURVE_S1.);
#914 = LINE('',#915,#916);
#915 = CARTESIAN_POINT('',(18.0000021,14.26293058,0.E+000));
#916 = VECTOR('',#917,1.);
#917 = DIRECTION('',(-0.991444672552,-0.130527626456,0.E+000));
#918 = PCURVE('',#824,#919);
#919 = DEFINITIONAL_REPRESENTATION('',(#920),#924);
#920 = LINE('',#921,#922);
#921 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#922 = VECTOR('',#923,1.);
#923 = DIRECTION('',(1.,0.E+000));
#924 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#925 = PCURVE('',#137,#926);
#926 = DEFINITIONAL_REPRESENTATION('',(#927),#931);
#927 = LINE('',#928,#929);
#928 = CARTESIAN_POINT('',(17.99999448,6.26294912));
#929 = VECTOR('',#930,1.);
#930 = DIRECTION('',(-0.991444672552,-0.130527626456));
#931 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#932 = ADVANCED_FACE('',(#933),#900,.F.);
#933 = FACE_BOUND('',#934,.F.);
#934 = EDGE_LOOP('',(#935,#936,#959,#987));
#935 = ORIENTED_EDGE('',*,*,#884,.T.);
#936 = ORIENTED_EDGE('',*,*,#937,.T.);
#937 = EDGE_CURVE('',#862,#938,#940,.T.);
#938 = VERTEX_POINT('',#939);
#939 = CARTESIAN_POINT('',(17.49568764,14.12780004,1.64592));
#940 = SURFACE_CURVE('',#941,(#945,#952),.PCURVE_S1.);
#941 = LINE('',#942,#943);
#942 = CARTESIAN_POINT('',(17.73894852,14.22856184,1.64592));
#943 = VECTOR('',#944,1.);
#944 = DIRECTION('',(-0.923879741566,-0.382682927661,0.E+000));
#945 = PCURVE('',#900,#946);
#946 = DEFINITIONAL_REPRESENTATION('',(#947),#951);
#947 = LINE('',#948,#949);
#948 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#949 = VECTOR('',#950,1.);
#950 = DIRECTION('',(1.,0.E+000));
#951 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#952 = PCURVE('',#83,#953);
#953 = DEFINITIONAL_REPRESENTATION('',(#954),#958);
#954 = LINE('',#955,#956);
#955 = CARTESIAN_POINT('',(17.7389409,6.22858038));
#956 = VECTOR('',#957,1.);
#957 = DIRECTION('',(-0.923879741566,-0.382682927661));
#958 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#959 = ORIENTED_EDGE('',*,*,#960,.F.);
#960 = EDGE_CURVE('',#961,#938,#963,.T.);
#961 = VERTEX_POINT('',#962);
#962 = CARTESIAN_POINT('',(17.49568764,14.12780004,0.E+000));
#963 = SURFACE_CURVE('',#964,(#968,#975),.PCURVE_S1.);
#964 = LINE('',#965,#966);
#965 = CARTESIAN_POINT('',(17.49568764,14.12780004,0.E+000));
#966 = VECTOR('',#967,1.);
#967 = DIRECTION('',(0.E+000,0.E+000,1.));
#968 = PCURVE('',#900,#969);
#969 = DEFINITIONAL_REPRESENTATION('',(#970),#974);
#970 = LINE('',#971,#972);
#971 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#972 = VECTOR('',#973,1.);
#973 = DIRECTION('',(0.E+000,-1.));
#974 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#975 = PCURVE('',#976,#981);
#976 = PLANE('',#977);
#977 = AXIS2_PLACEMENT_3D('',#978,#979,#980);
#978 = CARTESIAN_POINT('',(17.49568764,14.12780004,0.E+000));
#979 = DIRECTION('',(0.608758355861,-0.793355698391,0.E+000));
#980 = DIRECTION('',(-0.793355698391,-0.608758355861,0.E+000));
#981 = DEFINITIONAL_REPRESENTATION('',(#982),#986);
#982 = LINE('',#983,#984);
#983 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#984 = VECTOR('',#985,1.);
#985 = DIRECTION('',(0.E+000,-1.));
#986 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#987 = ORIENTED_EDGE('',*,*,#988,.F.);
#988 = EDGE_CURVE('',#885,#961,#989,.T.);
#989 = SURFACE_CURVE('',#990,(#994,#1001),.PCURVE_S1.);
#990 = LINE('',#991,#992);
#991 = CARTESIAN_POINT('',(17.73894852,14.22856184,0.E+000));
#992 = VECTOR('',#993,1.);
#993 = DIRECTION('',(-0.923879741566,-0.382682927661,0.E+000));
#994 = PCURVE('',#900,#995);
#995 = DEFINITIONAL_REPRESENTATION('',(#996),#1000);
#996 = LINE('',#997,#998);
#997 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#998 = VECTOR('',#999,1.);
#999 = DIRECTION('',(1.,0.E+000));
#1000 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1001 = PCURVE('',#137,#1002);
#1002 = DEFINITIONAL_REPRESENTATION('',(#1003),#1007);
#1003 = LINE('',#1004,#1005);
#1004 = CARTESIAN_POINT('',(17.7389409,6.22858038));
#1005 = VECTOR('',#1006,1.);
#1006 = DIRECTION('',(-0.923879741566,-0.382682927661));
#1007 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1008 = ADVANCED_FACE('',(#1009),#976,.F.);
#1009 = FACE_BOUND('',#1010,.F.);
#1010 = EDGE_LOOP('',(#1011,#1012,#1035,#1063));
#1011 = ORIENTED_EDGE('',*,*,#960,.T.);
#1012 = ORIENTED_EDGE('',*,*,#1013,.T.);
#1013 = EDGE_CURVE('',#938,#1014,#1016,.T.);
#1014 = VERTEX_POINT('',#1015);
#1015 = CARTESIAN_POINT('',(17.28679296,13.9675108,1.64592));
#1016 = SURFACE_CURVE('',#1017,(#1021,#1028),.PCURVE_S1.);
#1017 = LINE('',#1018,#1019);
#1018 = CARTESIAN_POINT('',(17.49568764,14.12780004,1.64592));
#1019 = VECTOR('',#1020,1.);
#1020 = DIRECTION('',(-0.793355698391,-0.608758355861,0.E+000));
#1021 = PCURVE('',#976,#1022);
#1022 = DEFINITIONAL_REPRESENTATION('',(#1023),#1027);
#1023 = LINE('',#1024,#1025);
#1024 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1025 = VECTOR('',#1026,1.);
#1026 = DIRECTION('',(1.,0.E+000));
#1027 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1028 = PCURVE('',#83,#1029);
#1029 = DEFINITIONAL_REPRESENTATION('',(#1030),#1034);
#1030 = LINE('',#1031,#1032);
#1031 = CARTESIAN_POINT('',(17.49568002,6.12781858));
#1032 = VECTOR('',#1033,1.);
#1033 = DIRECTION('',(-0.793355698391,-0.608758355861));
#1034 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1035 = ORIENTED_EDGE('',*,*,#1036,.F.);
#1036 = EDGE_CURVE('',#1037,#1014,#1039,.T.);
#1037 = VERTEX_POINT('',#1038);
#1038 = CARTESIAN_POINT('',(17.28679296,13.9675108,0.E+000));
#1039 = SURFACE_CURVE('',#1040,(#1044,#1051),.PCURVE_S1.);
#1040 = LINE('',#1041,#1042);
#1041 = CARTESIAN_POINT('',(17.28679296,13.9675108,0.E+000));
#1042 = VECTOR('',#1043,1.);
#1043 = DIRECTION('',(0.E+000,0.E+000,1.));
#1044 = PCURVE('',#976,#1045);
#1045 = DEFINITIONAL_REPRESENTATION('',(#1046),#1050);
#1046 = LINE('',#1047,#1048);
#1047 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#1048 = VECTOR('',#1049,1.);
#1049 = DIRECTION('',(0.E+000,-1.));
#1050 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1051 = PCURVE('',#1052,#1057);
#1052 = PLANE('',#1053);
#1053 = AXIS2_PLACEMENT_3D('',#1054,#1055,#1056);
#1054 = CARTESIAN_POINT('',(17.28679296,13.9675108,0.E+000));
#1055 = DIRECTION('',(0.793355698391,-0.608758355861,0.E+000));
#1056 = DIRECTION('',(-0.608758355861,-0.793355698391,0.E+000));
#1057 = DEFINITIONAL_REPRESENTATION('',(#1058),#1062);
#1058 = LINE('',#1059,#1060);
#1059 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1060 = VECTOR('',#1061,1.);
#1061 = DIRECTION('',(0.E+000,-1.));
#1062 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1063 = ORIENTED_EDGE('',*,*,#1064,.F.);
#1064 = EDGE_CURVE('',#961,#1037,#1065,.T.);
#1065 = SURFACE_CURVE('',#1066,(#1070,#1077),.PCURVE_S1.);
#1066 = LINE('',#1067,#1068);
#1067 = CARTESIAN_POINT('',(17.49568764,14.12780004,0.E+000));
#1068 = VECTOR('',#1069,1.);
#1069 = DIRECTION('',(-0.793355698391,-0.608758355861,0.E+000));
#1070 = PCURVE('',#976,#1071);
#1071 = DEFINITIONAL_REPRESENTATION('',(#1072),#1076);
#1072 = LINE('',#1073,#1074);
#1073 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1074 = VECTOR('',#1075,1.);
#1075 = DIRECTION('',(1.,0.E+000));
#1076 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1077 = PCURVE('',#137,#1078);
#1078 = DEFINITIONAL_REPRESENTATION('',(#1079),#1083);
#1079 = LINE('',#1080,#1081);
#1080 = CARTESIAN_POINT('',(17.49568002,6.12781858));
#1081 = VECTOR('',#1082,1.);
#1082 = DIRECTION('',(-0.793355698391,-0.608758355861));
#1083 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1084 = ADVANCED_FACE('',(#1085),#1052,.F.);
#1085 = FACE_BOUND('',#1086,.F.);
#1086 = EDGE_LOOP('',(#1087,#1088,#1111,#1139));
#1087 = ORIENTED_EDGE('',*,*,#1036,.T.);
#1088 = ORIENTED_EDGE('',*,*,#1089,.T.);
#1089 = EDGE_CURVE('',#1014,#1090,#1092,.T.);
#1090 = VERTEX_POINT('',#1091);
#1091 = CARTESIAN_POINT('',(17.12650372,13.75861612,1.64592));
#1092 = SURFACE_CURVE('',#1093,(#1097,#1104),.PCURVE_S1.);
#1093 = LINE('',#1094,#1095);
#1094 = CARTESIAN_POINT('',(17.28679296,13.9675108,1.64592));
#1095 = VECTOR('',#1096,1.);
#1096 = DIRECTION('',(-0.608758355861,-0.793355698391,0.E+000));
#1097 = PCURVE('',#1052,#1098);
#1098 = DEFINITIONAL_REPRESENTATION('',(#1099),#1103);
#1099 = LINE('',#1100,#1101);
#1100 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1101 = VECTOR('',#1102,1.);
#1102 = DIRECTION('',(1.,0.E+000));
#1103 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1104 = PCURVE('',#83,#1105);
#1105 = DEFINITIONAL_REPRESENTATION('',(#1106),#1110);
#1106 = LINE('',#1107,#1108);
#1107 = CARTESIAN_POINT('',(17.28678534,5.96752934));
#1108 = VECTOR('',#1109,1.);
#1109 = DIRECTION('',(-0.608758355861,-0.793355698391));
#1110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1111 = ORIENTED_EDGE('',*,*,#1112,.F.);
#1112 = EDGE_CURVE('',#1113,#1090,#1115,.T.);
#1113 = VERTEX_POINT('',#1114);
#1114 = CARTESIAN_POINT('',(17.12650372,13.75861612,0.E+000));
#1115 = SURFACE_CURVE('',#1116,(#1120,#1127),.PCURVE_S1.);
#1116 = LINE('',#1117,#1118);
#1117 = CARTESIAN_POINT('',(17.12650372,13.75861612,0.E+000));
#1118 = VECTOR('',#1119,1.);
#1119 = DIRECTION('',(0.E+000,0.E+000,1.));
#1120 = PCURVE('',#1052,#1121);
#1121 = DEFINITIONAL_REPRESENTATION('',(#1122),#1126);
#1122 = LINE('',#1123,#1124);
#1123 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#1124 = VECTOR('',#1125,1.);
#1125 = DIRECTION('',(0.E+000,-1.));
#1126 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1127 = PCURVE('',#1128,#1133);
#1128 = PLANE('',#1129);
#1129 = AXIS2_PLACEMENT_3D('',#1130,#1131,#1132);
#1130 = CARTESIAN_POINT('',(17.12650372,13.75861612,0.E+000));
#1131 = DIRECTION('',(0.923879741566,-0.382682927661,0.E+000));
#1132 = DIRECTION('',(-0.382682927661,-0.923879741566,0.E+000));
#1133 = DEFINITIONAL_REPRESENTATION('',(#1134),#1138);
#1134 = LINE('',#1135,#1136);
#1135 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1136 = VECTOR('',#1137,1.);
#1137 = DIRECTION('',(0.E+000,-1.));
#1138 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1139 = ORIENTED_EDGE('',*,*,#1140,.F.);
#1140 = EDGE_CURVE('',#1037,#1113,#1141,.T.);
#1141 = SURFACE_CURVE('',#1142,(#1146,#1153),.PCURVE_S1.);
#1142 = LINE('',#1143,#1144);
#1143 = CARTESIAN_POINT('',(17.28679296,13.9675108,0.E+000));
#1144 = VECTOR('',#1145,1.);
#1145 = DIRECTION('',(-0.608758355861,-0.793355698391,0.E+000));
#1146 = PCURVE('',#1052,#1147);
#1147 = DEFINITIONAL_REPRESENTATION('',(#1148),#1152);
#1148 = LINE('',#1149,#1150);
#1149 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1150 = VECTOR('',#1151,1.);
#1151 = DIRECTION('',(1.,0.E+000));
#1152 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1153 = PCURVE('',#137,#1154);
#1154 = DEFINITIONAL_REPRESENTATION('',(#1155),#1159);
#1155 = LINE('',#1156,#1157);
#1156 = CARTESIAN_POINT('',(17.28678534,5.96752934));
#1157 = VECTOR('',#1158,1.);
#1158 = DIRECTION('',(-0.608758355861,-0.793355698391));
#1159 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1160 = ADVANCED_FACE('',(#1161),#1128,.F.);
#1161 = FACE_BOUND('',#1162,.F.);
#1162 = EDGE_LOOP('',(#1163,#1164,#1187,#1215));
#1163 = ORIENTED_EDGE('',*,*,#1112,.T.);
#1164 = ORIENTED_EDGE('',*,*,#1165,.T.);
#1165 = EDGE_CURVE('',#1090,#1166,#1168,.T.);
#1166 = VERTEX_POINT('',#1167);
#1167 = CARTESIAN_POINT('',(17.02574192,13.51535524,1.64592));
#1168 = SURFACE_CURVE('',#1169,(#1173,#1180),.PCURVE_S1.);
#1169 = LINE('',#1170,#1171);
#1170 = CARTESIAN_POINT('',(17.12650372,13.75861612,1.64592));
#1171 = VECTOR('',#1172,1.);
#1172 = DIRECTION('',(-0.382682927661,-0.923879741566,0.E+000));
#1173 = PCURVE('',#1128,#1174);
#1174 = DEFINITIONAL_REPRESENTATION('',(#1175),#1179);
#1175 = LINE('',#1176,#1177);
#1176 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1177 = VECTOR('',#1178,1.);
#1178 = DIRECTION('',(1.,0.E+000));
#1179 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1180 = PCURVE('',#83,#1181);
#1181 = DEFINITIONAL_REPRESENTATION('',(#1182),#1186);
#1182 = LINE('',#1183,#1184);
#1183 = CARTESIAN_POINT('',(17.1264961,5.75863466));
#1184 = VECTOR('',#1185,1.);
#1185 = DIRECTION('',(-0.382682927661,-0.923879741566));
#1186 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1187 = ORIENTED_EDGE('',*,*,#1188,.F.);
#1188 = EDGE_CURVE('',#1189,#1166,#1191,.T.);
#1189 = VERTEX_POINT('',#1190);
#1190 = CARTESIAN_POINT('',(17.02574192,13.51535524,0.E+000));
#1191 = SURFACE_CURVE('',#1192,(#1196,#1203),.PCURVE_S1.);
#1192 = LINE('',#1193,#1194);
#1193 = CARTESIAN_POINT('',(17.02574192,13.51535524,0.E+000));
#1194 = VECTOR('',#1195,1.);
#1195 = DIRECTION('',(0.E+000,0.E+000,1.));
#1196 = PCURVE('',#1128,#1197);
#1197 = DEFINITIONAL_REPRESENTATION('',(#1198),#1202);
#1198 = LINE('',#1199,#1200);
#1199 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#1200 = VECTOR('',#1201,1.);
#1201 = DIRECTION('',(0.E+000,-1.));
#1202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1203 = PCURVE('',#1204,#1209);
#1204 = PLANE('',#1205);
#1205 = AXIS2_PLACEMENT_3D('',#1206,#1207,#1208);
#1206 = CARTESIAN_POINT('',(17.02574192,13.51535524,0.E+000));
#1207 = DIRECTION('',(0.991444672552,-0.130527626456,0.E+000));
#1208 = DIRECTION('',(-0.130527626456,-0.991444672552,0.E+000));
#1209 = DEFINITIONAL_REPRESENTATION('',(#1210),#1214);
#1210 = LINE('',#1211,#1212);
#1211 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1212 = VECTOR('',#1213,1.);
#1213 = DIRECTION('',(0.E+000,-1.));
#1214 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1215 = ORIENTED_EDGE('',*,*,#1216,.F.);
#1216 = EDGE_CURVE('',#1113,#1189,#1217,.T.);
#1217 = SURFACE_CURVE('',#1218,(#1222,#1229),.PCURVE_S1.);
#1218 = LINE('',#1219,#1220);
#1219 = CARTESIAN_POINT('',(17.12650372,13.75861612,0.E+000));
#1220 = VECTOR('',#1221,1.);
#1221 = DIRECTION('',(-0.382682927661,-0.923879741566,0.E+000));
#1222 = PCURVE('',#1128,#1223);
#1223 = DEFINITIONAL_REPRESENTATION('',(#1224),#1228);
#1224 = LINE('',#1225,#1226);
#1225 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1226 = VECTOR('',#1227,1.);
#1227 = DIRECTION('',(1.,0.E+000));
#1228 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1229 = PCURVE('',#137,#1230);
#1230 = DEFINITIONAL_REPRESENTATION('',(#1231),#1235);
#1231 = LINE('',#1232,#1233);
#1232 = CARTESIAN_POINT('',(17.1264961,5.75863466));
#1233 = VECTOR('',#1234,1.);
#1234 = DIRECTION('',(-0.382682927661,-0.923879741566));
#1235 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1236 = ADVANCED_FACE('',(#1237),#1204,.F.);
#1237 = FACE_BOUND('',#1238,.F.);
#1238 = EDGE_LOOP('',(#1239,#1240,#1263,#1291));
#1239 = ORIENTED_EDGE('',*,*,#1188,.T.);
#1240 = ORIENTED_EDGE('',*,*,#1241,.T.);
#1241 = EDGE_CURVE('',#1166,#1242,#1244,.T.);
#1242 = VERTEX_POINT('',#1243);
#1243 = CARTESIAN_POINT('',(16.99137318,13.25430166,1.64592));
#1244 = SURFACE_CURVE('',#1245,(#1249,#1256),.PCURVE_S1.);
#1245 = LINE('',#1246,#1247);
#1246 = CARTESIAN_POINT('',(17.02574192,13.51535524,1.64592));
#1247 = VECTOR('',#1248,1.);
#1248 = DIRECTION('',(-0.130527626456,-0.991444672552,0.E+000));
#1249 = PCURVE('',#1204,#1250);
#1250 = DEFINITIONAL_REPRESENTATION('',(#1251),#1255);
#1251 = LINE('',#1252,#1253);
#1252 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1253 = VECTOR('',#1254,1.);
#1254 = DIRECTION('',(1.,0.E+000));
#1255 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1256 = PCURVE('',#83,#1257);
#1257 = DEFINITIONAL_REPRESENTATION('',(#1258),#1262);
#1258 = LINE('',#1259,#1260);
#1259 = CARTESIAN_POINT('',(17.0257343,5.51537378));
#1260 = VECTOR('',#1261,1.);
#1261 = DIRECTION('',(-0.130527626456,-0.991444672552));
#1262 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1263 = ORIENTED_EDGE('',*,*,#1264,.F.);
#1264 = EDGE_CURVE('',#1265,#1242,#1267,.T.);
#1265 = VERTEX_POINT('',#1266);
#1266 = CARTESIAN_POINT('',(16.99137318,13.25430166,0.E+000));
#1267 = SURFACE_CURVE('',#1268,(#1272,#1279),.PCURVE_S1.);
#1268 = LINE('',#1269,#1270);
#1269 = CARTESIAN_POINT('',(16.99137318,13.25430166,0.E+000));
#1270 = VECTOR('',#1271,1.);
#1271 = DIRECTION('',(0.E+000,0.E+000,1.));
#1272 = PCURVE('',#1204,#1273);
#1273 = DEFINITIONAL_REPRESENTATION('',(#1274),#1278);
#1274 = LINE('',#1275,#1276);
#1275 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#1276 = VECTOR('',#1277,1.);
#1277 = DIRECTION('',(0.E+000,-1.));
#1278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1279 = PCURVE('',#1280,#1285);
#1280 = PLANE('',#1281);
#1281 = AXIS2_PLACEMENT_3D('',#1282,#1283,#1284);
#1282 = CARTESIAN_POINT('',(16.99137318,13.25430166,0.E+000));
#1283 = DIRECTION('',(0.999990693999,4.314153001476E-003,-0.E+000));
#1284 = DIRECTION('',(4.314153001476E-003,-0.999990693999,0.E+000));
#1285 = DEFINITIONAL_REPRESENTATION('',(#1286),#1290);
#1286 = LINE('',#1287,#1288);
#1287 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1288 = VECTOR('',#1289,1.);
#1289 = DIRECTION('',(0.E+000,-1.));
#1290 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1291 = ORIENTED_EDGE('',*,*,#1292,.F.);
#1292 = EDGE_CURVE('',#1189,#1265,#1293,.T.);
#1293 = SURFACE_CURVE('',#1294,(#1298,#1305),.PCURVE_S1.);
#1294 = LINE('',#1295,#1296);
#1295 = CARTESIAN_POINT('',(17.02574192,13.51535524,0.E+000));
#1296 = VECTOR('',#1297,1.);
#1297 = DIRECTION('',(-0.130527626456,-0.991444672552,0.E+000));
#1298 = PCURVE('',#1204,#1299);
#1299 = DEFINITIONAL_REPRESENTATION('',(#1300),#1304);
#1300 = LINE('',#1301,#1302);
#1301 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1302 = VECTOR('',#1303,1.);
#1303 = DIRECTION('',(1.,0.E+000));
#1304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1305 = PCURVE('',#137,#1306);
#1306 = DEFINITIONAL_REPRESENTATION('',(#1307),#1311);
#1307 = LINE('',#1308,#1309);
#1308 = CARTESIAN_POINT('',(17.0257343,5.51537378));
#1309 = VECTOR('',#1310,1.);
#1310 = DIRECTION('',(-0.130527626456,-0.991444672552));
#1311 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1312 = ADVANCED_FACE('',(#1313),#1280,.F.);
#1313 = FACE_BOUND('',#1314,.F.);
#1314 = EDGE_LOOP('',(#1315,#1316,#1339,#1367));
#1315 = ORIENTED_EDGE('',*,*,#1264,.T.);
#1316 = ORIENTED_EDGE('',*,*,#1317,.T.);
#1317 = EDGE_CURVE('',#1242,#1318,#1320,.T.);
#1318 = VERTEX_POINT('',#1319);
#1319 = CARTESIAN_POINT('',(17.00000156,11.25430312,1.64592));
#1320 = SURFACE_CURVE('',#1321,(#1325,#1332),.PCURVE_S1.);
#1321 = LINE('',#1322,#1323);
#1322 = CARTESIAN_POINT('',(16.99137318,13.25430166,1.64592));
#1323 = VECTOR('',#1324,1.);
#1324 = DIRECTION('',(4.314153001476E-003,-0.999990693999,0.E+000));
#1325 = PCURVE('',#1280,#1326);
#1326 = DEFINITIONAL_REPRESENTATION('',(#1327),#1331);
#1327 = LINE('',#1328,#1329);
#1328 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1329 = VECTOR('',#1330,1.);
#1330 = DIRECTION('',(1.,0.E+000));
#1331 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1332 = PCURVE('',#83,#1333);
#1333 = DEFINITIONAL_REPRESENTATION('',(#1334),#1338);
#1334 = LINE('',#1335,#1336);
#1335 = CARTESIAN_POINT('',(16.99136556,5.2543202));
#1336 = VECTOR('',#1337,1.);
#1337 = DIRECTION('',(4.314153001476E-003,-0.999990693999));
#1338 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1339 = ORIENTED_EDGE('',*,*,#1340,.F.);
#1340 = EDGE_CURVE('',#1341,#1318,#1343,.T.);
#1341 = VERTEX_POINT('',#1342);
#1342 = CARTESIAN_POINT('',(17.00000156,11.25430312,0.E+000));
#1343 = SURFACE_CURVE('',#1344,(#1348,#1355),.PCURVE_S1.);
#1344 = LINE('',#1345,#1346);
#1345 = CARTESIAN_POINT('',(17.00000156,11.25430312,0.E+000));
#1346 = VECTOR('',#1347,1.);
#1347 = DIRECTION('',(0.E+000,0.E+000,1.));
#1348 = PCURVE('',#1280,#1349);
#1349 = DEFINITIONAL_REPRESENTATION('',(#1350),#1354);
#1350 = LINE('',#1351,#1352);
#1351 = CARTESIAN_POINT('',(2.000017152162,0.E+000));
#1352 = VECTOR('',#1353,1.);
#1353 = DIRECTION('',(0.E+000,-1.));
#1354 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1355 = PCURVE('',#1356,#1361);
#1356 = PLANE('',#1357);
#1357 = AXIS2_PLACEMENT_3D('',#1358,#1359,#1360);
#1358 = CARTESIAN_POINT('',(17.00000156,11.25430312,0.E+000));
#1359 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1360 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1361 = DEFINITIONAL_REPRESENTATION('',(#1362),#1366);
#1362 = LINE('',#1363,#1364);
#1363 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#1364 = VECTOR('',#1365,1.);
#1365 = DIRECTION('',(0.E+000,-1.));
#1366 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1367 = ORIENTED_EDGE('',*,*,#1368,.F.);
#1368 = EDGE_CURVE('',#1265,#1341,#1369,.T.);
#1369 = SURFACE_CURVE('',#1370,(#1374,#1381),.PCURVE_S1.);
#1370 = LINE('',#1371,#1372);
#1371 = CARTESIAN_POINT('',(16.99137318,13.25430166,0.E+000));
#1372 = VECTOR('',#1373,1.);
#1373 = DIRECTION('',(4.314153001476E-003,-0.999990693999,0.E+000));
#1374 = PCURVE('',#1280,#1375);
#1375 = DEFINITIONAL_REPRESENTATION('',(#1376),#1380);
#1376 = LINE('',#1377,#1378);
#1377 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1378 = VECTOR('',#1379,1.);
#1379 = DIRECTION('',(1.,0.E+000));
#1380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1381 = PCURVE('',#137,#1382);
#1382 = DEFINITIONAL_REPRESENTATION('',(#1383),#1387);
#1383 = LINE('',#1384,#1385);
#1384 = CARTESIAN_POINT('',(16.99136556,5.2543202));
#1385 = VECTOR('',#1386,1.);
#1386 = DIRECTION('',(4.314153001476E-003,-0.999990693999));
#1387 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1388 = ADVANCED_FACE('',(#1389),#1356,.F.);
#1389 = FACE_BOUND('',#1390,.F.);
#1390 = EDGE_LOOP('',(#1391,#1392,#1415,#1443));
#1391 = ORIENTED_EDGE('',*,*,#1340,.T.);
#1392 = ORIENTED_EDGE('',*,*,#1393,.T.);
#1393 = EDGE_CURVE('',#1318,#1394,#1396,.T.);
#1394 = VERTEX_POINT('',#1395);
#1395 = CARTESIAN_POINT('',(16.99137318,11.25430312,1.64592));
#1396 = SURFACE_CURVE('',#1397,(#1401,#1408),.PCURVE_S1.);
#1397 = LINE('',#1398,#1399);
#1398 = CARTESIAN_POINT('',(17.00000156,11.25430312,1.64592));
#1399 = VECTOR('',#1400,1.);
#1400 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1401 = PCURVE('',#1356,#1402);
#1402 = DEFINITIONAL_REPRESENTATION('',(#1403),#1407);
#1403 = LINE('',#1404,#1405);
#1404 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1405 = VECTOR('',#1406,1.);
#1406 = DIRECTION('',(1.,0.E+000));
#1407 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1408 = PCURVE('',#83,#1409);
#1409 = DEFINITIONAL_REPRESENTATION('',(#1410),#1414);
#1410 = LINE('',#1411,#1412);
#1411 = CARTESIAN_POINT('',(16.99999394,3.25432166));
#1412 = VECTOR('',#1413,1.);
#1413 = DIRECTION('',(-1.,0.E+000));
#1414 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1415 = ORIENTED_EDGE('',*,*,#1416,.F.);
#1416 = EDGE_CURVE('',#1417,#1394,#1419,.T.);
#1417 = VERTEX_POINT('',#1418);
#1418 = CARTESIAN_POINT('',(16.99137318,11.25430312,0.E+000));
#1419 = SURFACE_CURVE('',#1420,(#1424,#1431),.PCURVE_S1.);
#1420 = LINE('',#1421,#1422);
#1421 = CARTESIAN_POINT('',(16.99137318,11.25430312,0.E+000));
#1422 = VECTOR('',#1423,1.);
#1423 = DIRECTION('',(0.E+000,0.E+000,1.));
#1424 = PCURVE('',#1356,#1425);
#1425 = DEFINITIONAL_REPRESENTATION('',(#1426),#1430);
#1426 = LINE('',#1427,#1428);
#1427 = CARTESIAN_POINT('',(8.628380000005E-003,0.E+000));
#1428 = VECTOR('',#1429,1.);
#1429 = DIRECTION('',(0.E+000,-1.));
#1430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1431 = PCURVE('',#1432,#1437);
#1432 = PLANE('',#1433);
#1433 = AXIS2_PLACEMENT_3D('',#1434,#1435,#1436);
#1434 = CARTESIAN_POINT('',(16.99137318,11.25430312,0.E+000));
#1435 = DIRECTION('',(0.991444672552,0.130527626456,-0.E+000));
#1436 = DIRECTION('',(0.130527626456,-0.991444672552,0.E+000));
#1437 = DEFINITIONAL_REPRESENTATION('',(#1438),#1442);
#1438 = LINE('',#1439,#1440);
#1439 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1440 = VECTOR('',#1441,1.);
#1441 = DIRECTION('',(0.E+000,-1.));
#1442 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1443 = ORIENTED_EDGE('',*,*,#1444,.F.);
#1444 = EDGE_CURVE('',#1341,#1417,#1445,.T.);
#1445 = SURFACE_CURVE('',#1446,(#1450,#1457),.PCURVE_S1.);
#1446 = LINE('',#1447,#1448);
#1447 = CARTESIAN_POINT('',(17.00000156,11.25430312,0.E+000));
#1448 = VECTOR('',#1449,1.);
#1449 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1450 = PCURVE('',#1356,#1451);
#1451 = DEFINITIONAL_REPRESENTATION('',(#1452),#1456);
#1452 = LINE('',#1453,#1454);
#1453 = CARTESIAN_POINT('',(0.E+000,-0.E+000));
#1454 = VECTOR('',#1455,1.);
#1455 = DIRECTION('',(1.,0.E+000));
#1456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1457 = PCURVE('',#137,#1458);
#1458 = DEFINITIONAL_REPRESENTATION('',(#1459),#1463);
#1459 = LINE('',#1460,#1461);
#1460 = CARTESIAN_POINT('',(16.99999394,3.25432166));
#1461 = VECTOR('',#1462,1.);
#1462 = DIRECTION('',(-1.,0.E+000));
#1463 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1464 = ADVANCED_FACE('',(#1465),#1432,.F.);
#1465 = FACE_BOUND('',#1466,.F.);
#1466 = EDGE_LOOP('',(#1467,#1468,#1491,#1519));
#1467 = ORIENTED_EDGE('',*,*,#1416,.T.);
#1468 = ORIENTED_EDGE('',*,*,#1469,.T.);
#1469 = EDGE_CURVE('',#1394,#1470,#1472,.T.);
#1470 = VERTEX_POINT('',#1471);
#1471 = CARTESIAN_POINT('',(17.02574192,10.99324954,1.64592));
#1472 = SURFACE_CURVE('',#1473,(#1477,#1484),.PCURVE_S1.);
#1473 = LINE('',#1474,#1475);
#1474 = CARTESIAN_POINT('',(16.99137318,11.25430312,1.64592));
#1475 = VECTOR('',#1476,1.);
#1476 = DIRECTION('',(0.130527626456,-0.991444672552,0.E+000));
#1477 = PCURVE('',#1432,#1478);
#1478 = DEFINITIONAL_REPRESENTATION('',(#1479),#1483);
#1479 = LINE('',#1480,#1481);
#1480 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1481 = VECTOR('',#1482,1.);
#1482 = DIRECTION('',(1.,0.E+000));
#1483 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1484 = PCURVE('',#83,#1485);
#1485 = DEFINITIONAL_REPRESENTATION('',(#1486),#1490);
#1486 = LINE('',#1487,#1488);
#1487 = CARTESIAN_POINT('',(16.99136556,3.25432166));
#1488 = VECTOR('',#1489,1.);
#1489 = DIRECTION('',(0.130527626456,-0.991444672552));
#1490 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1491 = ORIENTED_EDGE('',*,*,#1492,.F.);
#1492 = EDGE_CURVE('',#1493,#1470,#1495,.T.);
#1493 = VERTEX_POINT('',#1494);
#1494 = CARTESIAN_POINT('',(17.02574192,10.99324954,0.E+000));
#1495 = SURFACE_CURVE('',#1496,(#1500,#1507),.PCURVE_S1.);
#1496 = LINE('',#1497,#1498);
#1497 = CARTESIAN_POINT('',(17.02574192,10.99324954,0.E+000));
#1498 = VECTOR('',#1499,1.);
#1499 = DIRECTION('',(0.E+000,0.E+000,1.));
#1500 = PCURVE('',#1432,#1501);
#1501 = DEFINITIONAL_REPRESENTATION('',(#1502),#1506);
#1502 = LINE('',#1503,#1504);
#1503 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#1504 = VECTOR('',#1505,1.);
#1505 = DIRECTION('',(0.E+000,-1.));
#1506 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1507 = PCURVE('',#1508,#1513);
#1508 = PLANE('',#1509);
#1509 = AXIS2_PLACEMENT_3D('',#1510,#1511,#1512);
#1510 = CARTESIAN_POINT('',(17.02574192,10.99324954,0.E+000));
#1511 = DIRECTION('',(0.923879741566,0.382682927661,-0.E+000));
#1512 = DIRECTION('',(0.382682927661,-0.923879741566,0.E+000));
#1513 = DEFINITIONAL_REPRESENTATION('',(#1514),#1518);
#1514 = LINE('',#1515,#1516);
#1515 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1516 = VECTOR('',#1517,1.);
#1517 = DIRECTION('',(0.E+000,-1.));
#1518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1519 = ORIENTED_EDGE('',*,*,#1520,.F.);
#1520 = EDGE_CURVE('',#1417,#1493,#1521,.T.);
#1521 = SURFACE_CURVE('',#1522,(#1526,#1533),.PCURVE_S1.);
#1522 = LINE('',#1523,#1524);
#1523 = CARTESIAN_POINT('',(16.99137318,11.25430312,0.E+000));
#1524 = VECTOR('',#1525,1.);
#1525 = DIRECTION('',(0.130527626456,-0.991444672552,0.E+000));
#1526 = PCURVE('',#1432,#1527);
#1527 = DEFINITIONAL_REPRESENTATION('',(#1528),#1532);
#1528 = LINE('',#1529,#1530);
#1529 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1530 = VECTOR('',#1531,1.);
#1531 = DIRECTION('',(1.,0.E+000));
#1532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1533 = PCURVE('',#137,#1534);
#1534 = DEFINITIONAL_REPRESENTATION('',(#1535),#1539);
#1535 = LINE('',#1536,#1537);
#1536 = CARTESIAN_POINT('',(16.99136556,3.25432166));
#1537 = VECTOR('',#1538,1.);
#1538 = DIRECTION('',(0.130527626456,-0.991444672552));
#1539 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1540 = ADVANCED_FACE('',(#1541),#1508,.F.);
#1541 = FACE_BOUND('',#1542,.F.);
#1542 = EDGE_LOOP('',(#1543,#1544,#1567,#1595));
#1543 = ORIENTED_EDGE('',*,*,#1492,.T.);
#1544 = ORIENTED_EDGE('',*,*,#1545,.T.);
#1545 = EDGE_CURVE('',#1470,#1546,#1548,.T.);
#1546 = VERTEX_POINT('',#1547);
#1547 = CARTESIAN_POINT('',(17.12650372,10.74998866,1.64592));
#1548 = SURFACE_CURVE('',#1549,(#1553,#1560),.PCURVE_S1.);
#1549 = LINE('',#1550,#1551);
#1550 = CARTESIAN_POINT('',(17.02574192,10.99324954,1.64592));
#1551 = VECTOR('',#1552,1.);
#1552 = DIRECTION('',(0.382682927661,-0.923879741566,0.E+000));
#1553 = PCURVE('',#1508,#1554);
#1554 = DEFINITIONAL_REPRESENTATION('',(#1555),#1559);
#1555 = LINE('',#1556,#1557);
#1556 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1557 = VECTOR('',#1558,1.);
#1558 = DIRECTION('',(1.,0.E+000));
#1559 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1560 = PCURVE('',#83,#1561);
#1561 = DEFINITIONAL_REPRESENTATION('',(#1562),#1566);
#1562 = LINE('',#1563,#1564);
#1563 = CARTESIAN_POINT('',(17.0257343,2.99326808));
#1564 = VECTOR('',#1565,1.);
#1565 = DIRECTION('',(0.382682927661,-0.923879741566));
#1566 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1567 = ORIENTED_EDGE('',*,*,#1568,.F.);
#1568 = EDGE_CURVE('',#1569,#1546,#1571,.T.);
#1569 = VERTEX_POINT('',#1570);
#1570 = CARTESIAN_POINT('',(17.12650372,10.74998866,0.E+000));
#1571 = SURFACE_CURVE('',#1572,(#1576,#1583),.PCURVE_S1.);
#1572 = LINE('',#1573,#1574);
#1573 = CARTESIAN_POINT('',(17.12650372,10.74998866,0.E+000));
#1574 = VECTOR('',#1575,1.);
#1575 = DIRECTION('',(0.E+000,0.E+000,1.));
#1576 = PCURVE('',#1508,#1577);
#1577 = DEFINITIONAL_REPRESENTATION('',(#1578),#1582);
#1578 = LINE('',#1579,#1580);
#1579 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#1580 = VECTOR('',#1581,1.);
#1581 = DIRECTION('',(0.E+000,-1.));
#1582 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1583 = PCURVE('',#1584,#1589);
#1584 = PLANE('',#1585);
#1585 = AXIS2_PLACEMENT_3D('',#1586,#1587,#1588);
#1586 = CARTESIAN_POINT('',(17.12650372,10.74998866,0.E+000));
#1587 = DIRECTION('',(0.793355698391,0.608758355861,-0.E+000));
#1588 = DIRECTION('',(0.608758355861,-0.793355698391,0.E+000));
#1589 = DEFINITIONAL_REPRESENTATION('',(#1590),#1594);
#1590 = LINE('',#1591,#1592);
#1591 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1592 = VECTOR('',#1593,1.);
#1593 = DIRECTION('',(0.E+000,-1.));
#1594 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1595 = ORIENTED_EDGE('',*,*,#1596,.F.);
#1596 = EDGE_CURVE('',#1493,#1569,#1597,.T.);
#1597 = SURFACE_CURVE('',#1598,(#1602,#1609),.PCURVE_S1.);
#1598 = LINE('',#1599,#1600);
#1599 = CARTESIAN_POINT('',(17.02574192,10.99324954,0.E+000));
#1600 = VECTOR('',#1601,1.);
#1601 = DIRECTION('',(0.382682927661,-0.923879741566,0.E+000));
#1602 = PCURVE('',#1508,#1603);
#1603 = DEFINITIONAL_REPRESENTATION('',(#1604),#1608);
#1604 = LINE('',#1605,#1606);
#1605 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1606 = VECTOR('',#1607,1.);
#1607 = DIRECTION('',(1.,0.E+000));
#1608 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1609 = PCURVE('',#137,#1610);
#1610 = DEFINITIONAL_REPRESENTATION('',(#1611),#1615);
#1611 = LINE('',#1612,#1613);
#1612 = CARTESIAN_POINT('',(17.0257343,2.99326808));
#1613 = VECTOR('',#1614,1.);
#1614 = DIRECTION('',(0.382682927661,-0.923879741566));
#1615 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1616 = ADVANCED_FACE('',(#1617),#1584,.F.);
#1617 = FACE_BOUND('',#1618,.F.);
#1618 = EDGE_LOOP('',(#1619,#1620,#1643,#1671));
#1619 = ORIENTED_EDGE('',*,*,#1568,.T.);
#1620 = ORIENTED_EDGE('',*,*,#1621,.T.);
#1621 = EDGE_CURVE('',#1546,#1622,#1624,.T.);
#1622 = VERTEX_POINT('',#1623);
#1623 = CARTESIAN_POINT('',(17.28679296,10.54109398,1.64592));
#1624 = SURFACE_CURVE('',#1625,(#1629,#1636),.PCURVE_S1.);
#1625 = LINE('',#1626,#1627);
#1626 = CARTESIAN_POINT('',(17.12650372,10.74998866,1.64592));
#1627 = VECTOR('',#1628,1.);
#1628 = DIRECTION('',(0.608758355861,-0.793355698391,0.E+000));
#1629 = PCURVE('',#1584,#1630);
#1630 = DEFINITIONAL_REPRESENTATION('',(#1631),#1635);
#1631 = LINE('',#1632,#1633);
#1632 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1633 = VECTOR('',#1634,1.);
#1634 = DIRECTION('',(1.,0.E+000));
#1635 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1636 = PCURVE('',#83,#1637);
#1637 = DEFINITIONAL_REPRESENTATION('',(#1638),#1642);
#1638 = LINE('',#1639,#1640);
#1639 = CARTESIAN_POINT('',(17.1264961,2.7500072));
#1640 = VECTOR('',#1641,1.);
#1641 = DIRECTION('',(0.608758355861,-0.793355698391));
#1642 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1643 = ORIENTED_EDGE('',*,*,#1644,.F.);
#1644 = EDGE_CURVE('',#1645,#1622,#1647,.T.);
#1645 = VERTEX_POINT('',#1646);
#1646 = CARTESIAN_POINT('',(17.28679296,10.54109398,0.E+000));
#1647 = SURFACE_CURVE('',#1648,(#1652,#1659),.PCURVE_S1.);
#1648 = LINE('',#1649,#1650);
#1649 = CARTESIAN_POINT('',(17.28679296,10.54109398,0.E+000));
#1650 = VECTOR('',#1651,1.);
#1651 = DIRECTION('',(0.E+000,0.E+000,1.));
#1652 = PCURVE('',#1584,#1653);
#1653 = DEFINITIONAL_REPRESENTATION('',(#1654),#1658);
#1654 = LINE('',#1655,#1656);
#1655 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#1656 = VECTOR('',#1657,1.);
#1657 = DIRECTION('',(0.E+000,-1.));
#1658 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1659 = PCURVE('',#1660,#1665);
#1660 = PLANE('',#1661);
#1661 = AXIS2_PLACEMENT_3D('',#1662,#1663,#1664);
#1662 = CARTESIAN_POINT('',(17.28679296,10.54109398,0.E+000));
#1663 = DIRECTION('',(0.608758355861,0.793355698391,-0.E+000));
#1664 = DIRECTION('',(0.793355698391,-0.608758355861,0.E+000));
#1665 = DEFINITIONAL_REPRESENTATION('',(#1666),#1670);
#1666 = LINE('',#1667,#1668);
#1667 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1668 = VECTOR('',#1669,1.);
#1669 = DIRECTION('',(0.E+000,-1.));
#1670 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1671 = ORIENTED_EDGE('',*,*,#1672,.F.);
#1672 = EDGE_CURVE('',#1569,#1645,#1673,.T.);
#1673 = SURFACE_CURVE('',#1674,(#1678,#1685),.PCURVE_S1.);
#1674 = LINE('',#1675,#1676);
#1675 = CARTESIAN_POINT('',(17.12650372,10.74998866,0.E+000));
#1676 = VECTOR('',#1677,1.);
#1677 = DIRECTION('',(0.608758355861,-0.793355698391,0.E+000));
#1678 = PCURVE('',#1584,#1679);
#1679 = DEFINITIONAL_REPRESENTATION('',(#1680),#1684);
#1680 = LINE('',#1681,#1682);
#1681 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1682 = VECTOR('',#1683,1.);
#1683 = DIRECTION('',(1.,0.E+000));
#1684 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1685 = PCURVE('',#137,#1686);
#1686 = DEFINITIONAL_REPRESENTATION('',(#1687),#1691);
#1687 = LINE('',#1688,#1689);
#1688 = CARTESIAN_POINT('',(17.1264961,2.7500072));
#1689 = VECTOR('',#1690,1.);
#1690 = DIRECTION('',(0.608758355861,-0.793355698391));
#1691 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1692 = ADVANCED_FACE('',(#1693),#1660,.F.);
#1693 = FACE_BOUND('',#1694,.F.);
#1694 = EDGE_LOOP('',(#1695,#1696,#1719,#1747));
#1695 = ORIENTED_EDGE('',*,*,#1644,.T.);
#1696 = ORIENTED_EDGE('',*,*,#1697,.T.);
#1697 = EDGE_CURVE('',#1622,#1698,#1700,.T.);
#1698 = VERTEX_POINT('',#1699);
#1699 = CARTESIAN_POINT('',(17.49568764,10.38080474,1.64592));
#1700 = SURFACE_CURVE('',#1701,(#1705,#1712),.PCURVE_S1.);
#1701 = LINE('',#1702,#1703);
#1702 = CARTESIAN_POINT('',(17.28679296,10.54109398,1.64592));
#1703 = VECTOR('',#1704,1.);
#1704 = DIRECTION('',(0.793355698391,-0.608758355861,0.E+000));
#1705 = PCURVE('',#1660,#1706);
#1706 = DEFINITIONAL_REPRESENTATION('',(#1707),#1711);
#1707 = LINE('',#1708,#1709);
#1708 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1709 = VECTOR('',#1710,1.);
#1710 = DIRECTION('',(1.,0.E+000));
#1711 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1712 = PCURVE('',#83,#1713);
#1713 = DEFINITIONAL_REPRESENTATION('',(#1714),#1718);
#1714 = LINE('',#1715,#1716);
#1715 = CARTESIAN_POINT('',(17.28678534,2.54111252));
#1716 = VECTOR('',#1717,1.);
#1717 = DIRECTION('',(0.793355698391,-0.608758355861));
#1718 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1719 = ORIENTED_EDGE('',*,*,#1720,.F.);
#1720 = EDGE_CURVE('',#1721,#1698,#1723,.T.);
#1721 = VERTEX_POINT('',#1722);
#1722 = CARTESIAN_POINT('',(17.49568764,10.38080474,0.E+000));
#1723 = SURFACE_CURVE('',#1724,(#1728,#1735),.PCURVE_S1.);
#1724 = LINE('',#1725,#1726);
#1725 = CARTESIAN_POINT('',(17.49568764,10.38080474,0.E+000));
#1726 = VECTOR('',#1727,1.);
#1727 = DIRECTION('',(0.E+000,0.E+000,1.));
#1728 = PCURVE('',#1660,#1729);
#1729 = DEFINITIONAL_REPRESENTATION('',(#1730),#1734);
#1730 = LINE('',#1731,#1732);
#1731 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#1732 = VECTOR('',#1733,1.);
#1733 = DIRECTION('',(0.E+000,-1.));
#1734 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1735 = PCURVE('',#1736,#1741);
#1736 = PLANE('',#1737);
#1737 = AXIS2_PLACEMENT_3D('',#1738,#1739,#1740);
#1738 = CARTESIAN_POINT('',(17.49568764,10.38080474,0.E+000));
#1739 = DIRECTION('',(0.382682927661,0.923879741566,-0.E+000));
#1740 = DIRECTION('',(0.923879741566,-0.382682927661,0.E+000));
#1741 = DEFINITIONAL_REPRESENTATION('',(#1742),#1746);
#1742 = LINE('',#1743,#1744);
#1743 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1744 = VECTOR('',#1745,1.);
#1745 = DIRECTION('',(0.E+000,-1.));
#1746 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1747 = ORIENTED_EDGE('',*,*,#1748,.F.);
#1748 = EDGE_CURVE('',#1645,#1721,#1749,.T.);
#1749 = SURFACE_CURVE('',#1750,(#1754,#1761),.PCURVE_S1.);
#1750 = LINE('',#1751,#1752);
#1751 = CARTESIAN_POINT('',(17.28679296,10.54109398,0.E+000));
#1752 = VECTOR('',#1753,1.);
#1753 = DIRECTION('',(0.793355698391,-0.608758355861,0.E+000));
#1754 = PCURVE('',#1660,#1755);
#1755 = DEFINITIONAL_REPRESENTATION('',(#1756),#1760);
#1756 = LINE('',#1757,#1758);
#1757 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1758 = VECTOR('',#1759,1.);
#1759 = DIRECTION('',(1.,0.E+000));
#1760 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1761 = PCURVE('',#137,#1762);
#1762 = DEFINITIONAL_REPRESENTATION('',(#1763),#1767);
#1763 = LINE('',#1764,#1765);
#1764 = CARTESIAN_POINT('',(17.28678534,2.54111252));
#1765 = VECTOR('',#1766,1.);
#1766 = DIRECTION('',(0.793355698391,-0.608758355861));
#1767 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1768 = ADVANCED_FACE('',(#1769),#1736,.F.);
#1769 = FACE_BOUND('',#1770,.F.);
#1770 = EDGE_LOOP('',(#1771,#1772,#1795,#1823));
#1771 = ORIENTED_EDGE('',*,*,#1720,.T.);
#1772 = ORIENTED_EDGE('',*,*,#1773,.T.);
#1773 = EDGE_CURVE('',#1698,#1774,#1776,.T.);
#1774 = VERTEX_POINT('',#1775);
#1775 = CARTESIAN_POINT('',(17.73894852,10.28004294,1.64592));
#1776 = SURFACE_CURVE('',#1777,(#1781,#1788),.PCURVE_S1.);
#1777 = LINE('',#1778,#1779);
#1778 = CARTESIAN_POINT('',(17.49568764,10.38080474,1.64592));
#1779 = VECTOR('',#1780,1.);
#1780 = DIRECTION('',(0.923879741566,-0.382682927661,0.E+000));
#1781 = PCURVE('',#1736,#1782);
#1782 = DEFINITIONAL_REPRESENTATION('',(#1783),#1787);
#1783 = LINE('',#1784,#1785);
#1784 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1785 = VECTOR('',#1786,1.);
#1786 = DIRECTION('',(1.,0.E+000));
#1787 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1788 = PCURVE('',#83,#1789);
#1789 = DEFINITIONAL_REPRESENTATION('',(#1790),#1794);
#1790 = LINE('',#1791,#1792);
#1791 = CARTESIAN_POINT('',(17.49568002,2.38082328));
#1792 = VECTOR('',#1793,1.);
#1793 = DIRECTION('',(0.923879741566,-0.382682927661));
#1794 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1795 = ORIENTED_EDGE('',*,*,#1796,.F.);
#1796 = EDGE_CURVE('',#1797,#1774,#1799,.T.);
#1797 = VERTEX_POINT('',#1798);
#1798 = CARTESIAN_POINT('',(17.73894852,10.28004294,0.E+000));
#1799 = SURFACE_CURVE('',#1800,(#1804,#1811),.PCURVE_S1.);
#1800 = LINE('',#1801,#1802);
#1801 = CARTESIAN_POINT('',(17.73894852,10.28004294,0.E+000));
#1802 = VECTOR('',#1803,1.);
#1803 = DIRECTION('',(0.E+000,0.E+000,1.));
#1804 = PCURVE('',#1736,#1805);
#1805 = DEFINITIONAL_REPRESENTATION('',(#1806),#1810);
#1806 = LINE('',#1807,#1808);
#1807 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#1808 = VECTOR('',#1809,1.);
#1809 = DIRECTION('',(0.E+000,-1.));
#1810 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1811 = PCURVE('',#1812,#1817);
#1812 = PLANE('',#1813);
#1813 = AXIS2_PLACEMENT_3D('',#1814,#1815,#1816);
#1814 = CARTESIAN_POINT('',(17.73894852,10.28004294,0.E+000));
#1815 = DIRECTION('',(0.130527626456,0.991444672552,-0.E+000));
#1816 = DIRECTION('',(0.991444672552,-0.130527626456,0.E+000));
#1817 = DEFINITIONAL_REPRESENTATION('',(#1818),#1822);
#1818 = LINE('',#1819,#1820);
#1819 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1820 = VECTOR('',#1821,1.);
#1821 = DIRECTION('',(0.E+000,-1.));
#1822 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1823 = ORIENTED_EDGE('',*,*,#1824,.F.);
#1824 = EDGE_CURVE('',#1721,#1797,#1825,.T.);
#1825 = SURFACE_CURVE('',#1826,(#1830,#1837),.PCURVE_S1.);
#1826 = LINE('',#1827,#1828);
#1827 = CARTESIAN_POINT('',(17.49568764,10.38080474,0.E+000));
#1828 = VECTOR('',#1829,1.);
#1829 = DIRECTION('',(0.923879741566,-0.382682927661,0.E+000));
#1830 = PCURVE('',#1736,#1831);
#1831 = DEFINITIONAL_REPRESENTATION('',(#1832),#1836);
#1832 = LINE('',#1833,#1834);
#1833 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1834 = VECTOR('',#1835,1.);
#1835 = DIRECTION('',(1.,0.E+000));
#1836 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1837 = PCURVE('',#137,#1838);
#1838 = DEFINITIONAL_REPRESENTATION('',(#1839),#1843);
#1839 = LINE('',#1840,#1841);
#1840 = CARTESIAN_POINT('',(17.49568002,2.38082328));
#1841 = VECTOR('',#1842,1.);
#1842 = DIRECTION('',(0.923879741566,-0.382682927661));
#1843 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1844 = ADVANCED_FACE('',(#1845),#1812,.F.);
#1845 = FACE_BOUND('',#1846,.F.);
#1846 = EDGE_LOOP('',(#1847,#1848,#1871,#1899));
#1847 = ORIENTED_EDGE('',*,*,#1796,.T.);
#1848 = ORIENTED_EDGE('',*,*,#1849,.T.);
#1849 = EDGE_CURVE('',#1774,#1850,#1852,.T.);
#1850 = VERTEX_POINT('',#1851);
#1851 = CARTESIAN_POINT('',(18.0000021,10.2456742,1.64592));
#1852 = SURFACE_CURVE('',#1853,(#1857,#1864),.PCURVE_S1.);
#1853 = LINE('',#1854,#1855);
#1854 = CARTESIAN_POINT('',(17.73894852,10.28004294,1.64592));
#1855 = VECTOR('',#1856,1.);
#1856 = DIRECTION('',(0.991444672552,-0.130527626456,0.E+000));
#1857 = PCURVE('',#1812,#1858);
#1858 = DEFINITIONAL_REPRESENTATION('',(#1859),#1863);
#1859 = LINE('',#1860,#1861);
#1860 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1861 = VECTOR('',#1862,1.);
#1862 = DIRECTION('',(1.,0.E+000));
#1863 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1864 = PCURVE('',#83,#1865);
#1865 = DEFINITIONAL_REPRESENTATION('',(#1866),#1870);
#1866 = LINE('',#1867,#1868);
#1867 = CARTESIAN_POINT('',(17.7389409,2.28006148));
#1868 = VECTOR('',#1869,1.);
#1869 = DIRECTION('',(0.991444672552,-0.130527626456));
#1870 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1871 = ORIENTED_EDGE('',*,*,#1872,.F.);
#1872 = EDGE_CURVE('',#1873,#1850,#1875,.T.);
#1873 = VERTEX_POINT('',#1874);
#1874 = CARTESIAN_POINT('',(18.0000021,10.2456742,0.E+000));
#1875 = SURFACE_CURVE('',#1876,(#1880,#1887),.PCURVE_S1.);
#1876 = LINE('',#1877,#1878);
#1877 = CARTESIAN_POINT('',(18.0000021,10.2456742,0.E+000));
#1878 = VECTOR('',#1879,1.);
#1879 = DIRECTION('',(0.E+000,0.E+000,1.));
#1880 = PCURVE('',#1812,#1881);
#1881 = DEFINITIONAL_REPRESENTATION('',(#1882),#1886);
#1882 = LINE('',#1883,#1884);
#1883 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#1884 = VECTOR('',#1885,1.);
#1885 = DIRECTION('',(0.E+000,-1.));
#1886 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1887 = PCURVE('',#1888,#1893);
#1888 = PLANE('',#1889);
#1889 = AXIS2_PLACEMENT_3D('',#1890,#1891,#1892);
#1890 = CARTESIAN_POINT('',(18.0000021,10.2456742,0.E+000));
#1891 = DIRECTION('',(-1.627996183953E-004,0.999999986748,0.E+000));
#1892 = DIRECTION('',(0.999999986748,1.627996183953E-004,0.E+000));
#1893 = DEFINITIONAL_REPRESENTATION('',(#1894),#1898);
#1894 = LINE('',#1895,#1896);
#1895 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1896 = VECTOR('',#1897,1.);
#1897 = DIRECTION('',(0.E+000,-1.));
#1898 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1899 = ORIENTED_EDGE('',*,*,#1900,.F.);
#1900 = EDGE_CURVE('',#1797,#1873,#1901,.T.);
#1901 = SURFACE_CURVE('',#1902,(#1906,#1913),.PCURVE_S1.);
#1902 = LINE('',#1903,#1904);
#1903 = CARTESIAN_POINT('',(17.73894852,10.28004294,0.E+000));
#1904 = VECTOR('',#1905,1.);
#1905 = DIRECTION('',(0.991444672552,-0.130527626456,0.E+000));
#1906 = PCURVE('',#1812,#1907);
#1907 = DEFINITIONAL_REPRESENTATION('',(#1908),#1912);
#1908 = LINE('',#1909,#1910);
#1909 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1910 = VECTOR('',#1911,1.);
#1911 = DIRECTION('',(1.,0.E+000));
#1912 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1913 = PCURVE('',#137,#1914);
#1914 = DEFINITIONAL_REPRESENTATION('',(#1915),#1919);
#1915 = LINE('',#1916,#1917);
#1916 = CARTESIAN_POINT('',(17.7389409,2.28006148));
#1917 = VECTOR('',#1918,1.);
#1918 = DIRECTION('',(0.991444672552,-0.130527626456));
#1919 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1920 = ADVANCED_FACE('',(#1921),#1888,.F.);
#1921 = FACE_BOUND('',#1922,.F.);
#1922 = EDGE_LOOP('',(#1923,#1924,#1947,#1975));
#1923 = ORIENTED_EDGE('',*,*,#1872,.T.);
#1924 = ORIENTED_EDGE('',*,*,#1925,.T.);
#1925 = EDGE_CURVE('',#1850,#1926,#1928,.T.);
#1926 = VERTEX_POINT('',#1927);
#1927 = CARTESIAN_POINT('',(71.00000278,10.25430258,1.64592));
#1928 = SURFACE_CURVE('',#1929,(#1933,#1940),.PCURVE_S1.);
#1929 = LINE('',#1930,#1931);
#1930 = CARTESIAN_POINT('',(18.0000021,10.2456742,1.64592));
#1931 = VECTOR('',#1932,1.);
#1932 = DIRECTION('',(0.999999986748,1.627996183953E-004,0.E+000));
#1933 = PCURVE('',#1888,#1934);
#1934 = DEFINITIONAL_REPRESENTATION('',(#1935),#1939);
#1935 = LINE('',#1936,#1937);
#1936 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#1937 = VECTOR('',#1938,1.);
#1938 = DIRECTION('',(1.,0.E+000));
#1939 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1940 = PCURVE('',#83,#1941);
#1941 = DEFINITIONAL_REPRESENTATION('',(#1942),#1946);
#1942 = LINE('',#1943,#1944);
#1943 = CARTESIAN_POINT('',(17.99999448,2.24569274));
#1944 = VECTOR('',#1945,1.);
#1945 = DIRECTION('',(0.999999986748,1.627996183953E-004));
#1946 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1947 = ORIENTED_EDGE('',*,*,#1948,.F.);
#1948 = EDGE_CURVE('',#1949,#1926,#1951,.T.);
#1949 = VERTEX_POINT('',#1950);
#1950 = CARTESIAN_POINT('',(71.00000278,10.25430258,0.E+000));
#1951 = SURFACE_CURVE('',#1952,(#1956,#1963),.PCURVE_S1.);
#1952 = LINE('',#1953,#1954);
#1953 = CARTESIAN_POINT('',(71.00000278,10.25430258,0.E+000));
#1954 = VECTOR('',#1955,1.);
#1955 = DIRECTION('',(0.E+000,0.E+000,1.));
#1956 = PCURVE('',#1888,#1957);
#1957 = DEFINITIONAL_REPRESENTATION('',(#1958),#1962);
#1958 = LINE('',#1959,#1960);
#1959 = CARTESIAN_POINT('',(53.000001382348,0.E+000));
#1960 = VECTOR('',#1961,1.);
#1961 = DIRECTION('',(0.E+000,-1.));
#1962 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1963 = PCURVE('',#1964,#1969);
#1964 = PLANE('',#1965);
#1965 = AXIS2_PLACEMENT_3D('',#1966,#1967,#1968);
#1966 = CARTESIAN_POINT('',(71.00000278,10.25430258,0.E+000));
#1967 = DIRECTION('',(1.,0.E+000,-0.E+000));
#1968 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1969 = DEFINITIONAL_REPRESENTATION('',(#1970),#1974);
#1970 = LINE('',#1971,#1972);
#1971 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1972 = VECTOR('',#1973,1.);
#1973 = DIRECTION('',(0.E+000,-1.));
#1974 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1975 = ORIENTED_EDGE('',*,*,#1976,.F.);
#1976 = EDGE_CURVE('',#1873,#1949,#1977,.T.);
#1977 = SURFACE_CURVE('',#1978,(#1982,#1989),.PCURVE_S1.);
#1978 = LINE('',#1979,#1980);
#1979 = CARTESIAN_POINT('',(18.0000021,10.2456742,0.E+000));
#1980 = VECTOR('',#1981,1.);
#1981 = DIRECTION('',(0.999999986748,1.627996183953E-004,0.E+000));
#1982 = PCURVE('',#1888,#1983);
#1983 = DEFINITIONAL_REPRESENTATION('',(#1984),#1988);
#1984 = LINE('',#1985,#1986);
#1985 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1986 = VECTOR('',#1987,1.);
#1987 = DIRECTION('',(1.,0.E+000));
#1988 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1989 = PCURVE('',#137,#1990);
#1990 = DEFINITIONAL_REPRESENTATION('',(#1991),#1995);
#1991 = LINE('',#1992,#1993);
#1992 = CARTESIAN_POINT('',(17.99999448,2.24569274));
#1993 = VECTOR('',#1994,1.);
#1994 = DIRECTION('',(0.999999986748,1.627996183953E-004));
#1995 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1996 = ADVANCED_FACE('',(#1997),#1964,.F.);
#1997 = FACE_BOUND('',#1998,.F.);
#1998 = EDGE_LOOP('',(#1999,#2000,#2023,#2051));
#1999 = ORIENTED_EDGE('',*,*,#1948,.T.);
#2000 = ORIENTED_EDGE('',*,*,#2001,.T.);
#2001 = EDGE_CURVE('',#1926,#2002,#2004,.T.);
#2002 = VERTEX_POINT('',#2003);
#2003 = CARTESIAN_POINT('',(71.00000278,10.2456742,1.64592));
#2004 = SURFACE_CURVE('',#2005,(#2009,#2016),.PCURVE_S1.);
#2005 = LINE('',#2006,#2007);
#2006 = CARTESIAN_POINT('',(71.00000278,10.25430258,1.64592));
#2007 = VECTOR('',#2008,1.);
#2008 = DIRECTION('',(0.E+000,-1.,0.E+000));
#2009 = PCURVE('',#1964,#2010);
#2010 = DEFINITIONAL_REPRESENTATION('',(#2011),#2015);
#2011 = LINE('',#2012,#2013);
#2012 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2013 = VECTOR('',#2014,1.);
#2014 = DIRECTION('',(1.,0.E+000));
#2015 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2016 = PCURVE('',#83,#2017);
#2017 = DEFINITIONAL_REPRESENTATION('',(#2018),#2022);
#2018 = LINE('',#2019,#2020);
#2019 = CARTESIAN_POINT('',(70.99999516,2.25432112));
#2020 = VECTOR('',#2021,1.);
#2021 = DIRECTION('',(0.E+000,-1.));
#2022 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2023 = ORIENTED_EDGE('',*,*,#2024,.F.);
#2024 = EDGE_CURVE('',#2025,#2002,#2027,.T.);
#2025 = VERTEX_POINT('',#2026);
#2026 = CARTESIAN_POINT('',(71.00000278,10.2456742,0.E+000));
#2027 = SURFACE_CURVE('',#2028,(#2032,#2039),.PCURVE_S1.);
#2028 = LINE('',#2029,#2030);
#2029 = CARTESIAN_POINT('',(71.00000278,10.2456742,0.E+000));
#2030 = VECTOR('',#2031,1.);
#2031 = DIRECTION('',(0.E+000,0.E+000,1.));
#2032 = PCURVE('',#1964,#2033);
#2033 = DEFINITIONAL_REPRESENTATION('',(#2034),#2038);
#2034 = LINE('',#2035,#2036);
#2035 = CARTESIAN_POINT('',(8.628379999998E-003,0.E+000));
#2036 = VECTOR('',#2037,1.);
#2037 = DIRECTION('',(0.E+000,-1.));
#2038 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2039 = PCURVE('',#2040,#2045);
#2040 = PLANE('',#2041);
#2041 = AXIS2_PLACEMENT_3D('',#2042,#2043,#2044);
#2042 = CARTESIAN_POINT('',(71.00000278,10.2456742,0.E+000));
#2043 = DIRECTION('',(-0.130527626456,0.991444672552,0.E+000));
#2044 = DIRECTION('',(0.991444672552,0.130527626456,0.E+000));
#2045 = DEFINITIONAL_REPRESENTATION('',(#2046),#2050);
#2046 = LINE('',#2047,#2048);
#2047 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2048 = VECTOR('',#2049,1.);
#2049 = DIRECTION('',(0.E+000,-1.));
#2050 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2051 = ORIENTED_EDGE('',*,*,#2052,.F.);
#2052 = EDGE_CURVE('',#1949,#2025,#2053,.T.);
#2053 = SURFACE_CURVE('',#2054,(#2058,#2065),.PCURVE_S1.);
#2054 = LINE('',#2055,#2056);
#2055 = CARTESIAN_POINT('',(71.00000278,10.25430258,0.E+000));
#2056 = VECTOR('',#2057,1.);
#2057 = DIRECTION('',(0.E+000,-1.,0.E+000));
#2058 = PCURVE('',#1964,#2059);
#2059 = DEFINITIONAL_REPRESENTATION('',(#2060),#2064);
#2060 = LINE('',#2061,#2062);
#2061 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2062 = VECTOR('',#2063,1.);
#2063 = DIRECTION('',(1.,0.E+000));
#2064 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2065 = PCURVE('',#137,#2066);
#2066 = DEFINITIONAL_REPRESENTATION('',(#2067),#2071);
#2067 = LINE('',#2068,#2069);
#2068 = CARTESIAN_POINT('',(70.99999516,2.25432112));
#2069 = VECTOR('',#2070,1.);
#2070 = DIRECTION('',(0.E+000,-1.));
#2071 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2072 = ADVANCED_FACE('',(#2073),#2040,.F.);
#2073 = FACE_BOUND('',#2074,.F.);
#2074 = EDGE_LOOP('',(#2075,#2076,#2099,#2127));
#2075 = ORIENTED_EDGE('',*,*,#2024,.T.);
#2076 = ORIENTED_EDGE('',*,*,#2077,.T.);
#2077 = EDGE_CURVE('',#2002,#2078,#2080,.T.);
#2078 = VERTEX_POINT('',#2079);
#2079 = CARTESIAN_POINT('',(71.26105636,10.28004294,1.64592));
#2080 = SURFACE_CURVE('',#2081,(#2085,#2092),.PCURVE_S1.);
#2081 = LINE('',#2082,#2083);
#2082 = CARTESIAN_POINT('',(71.00000278,10.2456742,1.64592));
#2083 = VECTOR('',#2084,1.);
#2084 = DIRECTION('',(0.991444672552,0.130527626456,0.E+000));
#2085 = PCURVE('',#2040,#2086);
#2086 = DEFINITIONAL_REPRESENTATION('',(#2087),#2091);
#2087 = LINE('',#2088,#2089);
#2088 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2089 = VECTOR('',#2090,1.);
#2090 = DIRECTION('',(1.,0.E+000));
#2091 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2092 = PCURVE('',#83,#2093);
#2093 = DEFINITIONAL_REPRESENTATION('',(#2094),#2098);
#2094 = LINE('',#2095,#2096);
#2095 = CARTESIAN_POINT('',(70.99999516,2.24569274));
#2096 = VECTOR('',#2097,1.);
#2097 = DIRECTION('',(0.991444672552,0.130527626456));
#2098 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2099 = ORIENTED_EDGE('',*,*,#2100,.F.);
#2100 = EDGE_CURVE('',#2101,#2078,#2103,.T.);
#2101 = VERTEX_POINT('',#2102);
#2102 = CARTESIAN_POINT('',(71.26105636,10.28004294,0.E+000));
#2103 = SURFACE_CURVE('',#2104,(#2108,#2115),.PCURVE_S1.);
#2104 = LINE('',#2105,#2106);
#2105 = CARTESIAN_POINT('',(71.26105636,10.28004294,0.E+000));
#2106 = VECTOR('',#2107,1.);
#2107 = DIRECTION('',(0.E+000,0.E+000,1.));
#2108 = PCURVE('',#2040,#2109);
#2109 = DEFINITIONAL_REPRESENTATION('',(#2110),#2114);
#2110 = LINE('',#2111,#2112);
#2111 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#2112 = VECTOR('',#2113,1.);
#2113 = DIRECTION('',(0.E+000,-1.));
#2114 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2115 = PCURVE('',#2116,#2121);
#2116 = PLANE('',#2117);
#2117 = AXIS2_PLACEMENT_3D('',#2118,#2119,#2120);
#2118 = CARTESIAN_POINT('',(71.26105636,10.28004294,0.E+000));
#2119 = DIRECTION('',(-0.382682927661,0.923879741566,0.E+000));
#2120 = DIRECTION('',(0.923879741566,0.382682927661,0.E+000));
#2121 = DEFINITIONAL_REPRESENTATION('',(#2122),#2126);
#2122 = LINE('',#2123,#2124);
#2123 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2124 = VECTOR('',#2125,1.);
#2125 = DIRECTION('',(0.E+000,-1.));
#2126 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2127 = ORIENTED_EDGE('',*,*,#2128,.F.);
#2128 = EDGE_CURVE('',#2025,#2101,#2129,.T.);
#2129 = SURFACE_CURVE('',#2130,(#2134,#2141),.PCURVE_S1.);
#2130 = LINE('',#2131,#2132);
#2131 = CARTESIAN_POINT('',(71.00000278,10.2456742,0.E+000));
#2132 = VECTOR('',#2133,1.);
#2133 = DIRECTION('',(0.991444672552,0.130527626456,0.E+000));
#2134 = PCURVE('',#2040,#2135);
#2135 = DEFINITIONAL_REPRESENTATION('',(#2136),#2140);
#2136 = LINE('',#2137,#2138);
#2137 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2138 = VECTOR('',#2139,1.);
#2139 = DIRECTION('',(1.,0.E+000));
#2140 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2141 = PCURVE('',#137,#2142);
#2142 = DEFINITIONAL_REPRESENTATION('',(#2143),#2147);
#2143 = LINE('',#2144,#2145);
#2144 = CARTESIAN_POINT('',(70.99999516,2.24569274));
#2145 = VECTOR('',#2146,1.);
#2146 = DIRECTION('',(0.991444672552,0.130527626456));
#2147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2148 = ADVANCED_FACE('',(#2149),#2116,.F.);
#2149 = FACE_BOUND('',#2150,.F.);
#2150 = EDGE_LOOP('',(#2151,#2152,#2175,#2203));
#2151 = ORIENTED_EDGE('',*,*,#2100,.T.);
#2152 = ORIENTED_EDGE('',*,*,#2153,.T.);
#2153 = EDGE_CURVE('',#2078,#2154,#2156,.T.);
#2154 = VERTEX_POINT('',#2155);
#2155 = CARTESIAN_POINT('',(71.50431724,10.38080474,1.64592));
#2156 = SURFACE_CURVE('',#2157,(#2161,#2168),.PCURVE_S1.);
#2157 = LINE('',#2158,#2159);
#2158 = CARTESIAN_POINT('',(71.26105636,10.28004294,1.64592));
#2159 = VECTOR('',#2160,1.);
#2160 = DIRECTION('',(0.923879741566,0.382682927661,0.E+000));
#2161 = PCURVE('',#2116,#2162);
#2162 = DEFINITIONAL_REPRESENTATION('',(#2163),#2167);
#2163 = LINE('',#2164,#2165);
#2164 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2165 = VECTOR('',#2166,1.);
#2166 = DIRECTION('',(1.,0.E+000));
#2167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2168 = PCURVE('',#83,#2169);
#2169 = DEFINITIONAL_REPRESENTATION('',(#2170),#2174);
#2170 = LINE('',#2171,#2172);
#2171 = CARTESIAN_POINT('',(71.26104874,2.28006148));
#2172 = VECTOR('',#2173,1.);
#2173 = DIRECTION('',(0.923879741566,0.382682927661));
#2174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2175 = ORIENTED_EDGE('',*,*,#2176,.F.);
#2176 = EDGE_CURVE('',#2177,#2154,#2179,.T.);
#2177 = VERTEX_POINT('',#2178);
#2178 = CARTESIAN_POINT('',(71.50431724,10.38080474,0.E+000));
#2179 = SURFACE_CURVE('',#2180,(#2184,#2191),.PCURVE_S1.);
#2180 = LINE('',#2181,#2182);
#2181 = CARTESIAN_POINT('',(71.50431724,10.38080474,0.E+000));
#2182 = VECTOR('',#2183,1.);
#2183 = DIRECTION('',(0.E+000,0.E+000,1.));
#2184 = PCURVE('',#2116,#2185);
#2185 = DEFINITIONAL_REPRESENTATION('',(#2186),#2190);
#2186 = LINE('',#2187,#2188);
#2187 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#2188 = VECTOR('',#2189,1.);
#2189 = DIRECTION('',(0.E+000,-1.));
#2190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2191 = PCURVE('',#2192,#2197);
#2192 = PLANE('',#2193);
#2193 = AXIS2_PLACEMENT_3D('',#2194,#2195,#2196);
#2194 = CARTESIAN_POINT('',(71.50431724,10.38080474,0.E+000));
#2195 = DIRECTION('',(-0.608758355861,0.793355698391,0.E+000));
#2196 = DIRECTION('',(0.793355698391,0.608758355861,0.E+000));
#2197 = DEFINITIONAL_REPRESENTATION('',(#2198),#2202);
#2198 = LINE('',#2199,#2200);
#2199 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2200 = VECTOR('',#2201,1.);
#2201 = DIRECTION('',(0.E+000,-1.));
#2202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2203 = ORIENTED_EDGE('',*,*,#2204,.F.);
#2204 = EDGE_CURVE('',#2101,#2177,#2205,.T.);
#2205 = SURFACE_CURVE('',#2206,(#2210,#2217),.PCURVE_S1.);
#2206 = LINE('',#2207,#2208);
#2207 = CARTESIAN_POINT('',(71.26105636,10.28004294,0.E+000));
#2208 = VECTOR('',#2209,1.);
#2209 = DIRECTION('',(0.923879741566,0.382682927661,0.E+000));
#2210 = PCURVE('',#2116,#2211);
#2211 = DEFINITIONAL_REPRESENTATION('',(#2212),#2216);
#2212 = LINE('',#2213,#2214);
#2213 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2214 = VECTOR('',#2215,1.);
#2215 = DIRECTION('',(1.,0.E+000));
#2216 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2217 = PCURVE('',#137,#2218);
#2218 = DEFINITIONAL_REPRESENTATION('',(#2219),#2223);
#2219 = LINE('',#2220,#2221);
#2220 = CARTESIAN_POINT('',(71.26104874,2.28006148));
#2221 = VECTOR('',#2222,1.);
#2222 = DIRECTION('',(0.923879741566,0.382682927661));
#2223 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2224 = ADVANCED_FACE('',(#2225),#2192,.F.);
#2225 = FACE_BOUND('',#2226,.F.);
#2226 = EDGE_LOOP('',(#2227,#2228,#2251,#2279));
#2227 = ORIENTED_EDGE('',*,*,#2176,.T.);
#2228 = ORIENTED_EDGE('',*,*,#2229,.T.);
#2229 = EDGE_CURVE('',#2154,#2230,#2232,.T.);
#2230 = VERTEX_POINT('',#2231);
#2231 = CARTESIAN_POINT('',(71.71321192,10.54109398,1.64592));
#2232 = SURFACE_CURVE('',#2233,(#2237,#2244),.PCURVE_S1.);
#2233 = LINE('',#2234,#2235);
#2234 = CARTESIAN_POINT('',(71.50431724,10.38080474,1.64592));
#2235 = VECTOR('',#2236,1.);
#2236 = DIRECTION('',(0.793355698391,0.608758355861,0.E+000));
#2237 = PCURVE('',#2192,#2238);
#2238 = DEFINITIONAL_REPRESENTATION('',(#2239),#2243);
#2239 = LINE('',#2240,#2241);
#2240 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2241 = VECTOR('',#2242,1.);
#2242 = DIRECTION('',(1.,0.E+000));
#2243 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2244 = PCURVE('',#83,#2245);
#2245 = DEFINITIONAL_REPRESENTATION('',(#2246),#2250);
#2246 = LINE('',#2247,#2248);
#2247 = CARTESIAN_POINT('',(71.50430962,2.38082328));
#2248 = VECTOR('',#2249,1.);
#2249 = DIRECTION('',(0.793355698391,0.608758355861));
#2250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2251 = ORIENTED_EDGE('',*,*,#2252,.F.);
#2252 = EDGE_CURVE('',#2253,#2230,#2255,.T.);
#2253 = VERTEX_POINT('',#2254);
#2254 = CARTESIAN_POINT('',(71.71321192,10.54109398,0.E+000));
#2255 = SURFACE_CURVE('',#2256,(#2260,#2267),.PCURVE_S1.);
#2256 = LINE('',#2257,#2258);
#2257 = CARTESIAN_POINT('',(71.71321192,10.54109398,0.E+000));
#2258 = VECTOR('',#2259,1.);
#2259 = DIRECTION('',(0.E+000,0.E+000,1.));
#2260 = PCURVE('',#2192,#2261);
#2261 = DEFINITIONAL_REPRESENTATION('',(#2262),#2266);
#2262 = LINE('',#2263,#2264);
#2263 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#2264 = VECTOR('',#2265,1.);
#2265 = DIRECTION('',(0.E+000,-1.));
#2266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2267 = PCURVE('',#2268,#2273);
#2268 = PLANE('',#2269);
#2269 = AXIS2_PLACEMENT_3D('',#2270,#2271,#2272);
#2270 = CARTESIAN_POINT('',(71.71321192,10.54109398,0.E+000));
#2271 = DIRECTION('',(-0.793355698391,0.608758355861,0.E+000));
#2272 = DIRECTION('',(0.608758355861,0.793355698391,0.E+000));
#2273 = DEFINITIONAL_REPRESENTATION('',(#2274),#2278);
#2274 = LINE('',#2275,#2276);
#2275 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2276 = VECTOR('',#2277,1.);
#2277 = DIRECTION('',(0.E+000,-1.));
#2278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2279 = ORIENTED_EDGE('',*,*,#2280,.F.);
#2280 = EDGE_CURVE('',#2177,#2253,#2281,.T.);
#2281 = SURFACE_CURVE('',#2282,(#2286,#2293),.PCURVE_S1.);
#2282 = LINE('',#2283,#2284);
#2283 = CARTESIAN_POINT('',(71.50431724,10.38080474,0.E+000));
#2284 = VECTOR('',#2285,1.);
#2285 = DIRECTION('',(0.793355698391,0.608758355861,0.E+000));
#2286 = PCURVE('',#2192,#2287);
#2287 = DEFINITIONAL_REPRESENTATION('',(#2288),#2292);
#2288 = LINE('',#2289,#2290);
#2289 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2290 = VECTOR('',#2291,1.);
#2291 = DIRECTION('',(1.,0.E+000));
#2292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2293 = PCURVE('',#137,#2294);
#2294 = DEFINITIONAL_REPRESENTATION('',(#2295),#2299);
#2295 = LINE('',#2296,#2297);
#2296 = CARTESIAN_POINT('',(71.50430962,2.38082328));
#2297 = VECTOR('',#2298,1.);
#2298 = DIRECTION('',(0.793355698391,0.608758355861));
#2299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2300 = ADVANCED_FACE('',(#2301),#2268,.F.);
#2301 = FACE_BOUND('',#2302,.F.);
#2302 = EDGE_LOOP('',(#2303,#2304,#2327,#2355));
#2303 = ORIENTED_EDGE('',*,*,#2252,.T.);
#2304 = ORIENTED_EDGE('',*,*,#2305,.T.);
#2305 = EDGE_CURVE('',#2230,#2306,#2308,.T.);
#2306 = VERTEX_POINT('',#2307);
#2307 = CARTESIAN_POINT('',(71.87350116,10.74998866,1.64592));
#2308 = SURFACE_CURVE('',#2309,(#2313,#2320),.PCURVE_S1.);
#2309 = LINE('',#2310,#2311);
#2310 = CARTESIAN_POINT('',(71.71321192,10.54109398,1.64592));
#2311 = VECTOR('',#2312,1.);
#2312 = DIRECTION('',(0.608758355861,0.793355698391,0.E+000));
#2313 = PCURVE('',#2268,#2314);
#2314 = DEFINITIONAL_REPRESENTATION('',(#2315),#2319);
#2315 = LINE('',#2316,#2317);
#2316 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2317 = VECTOR('',#2318,1.);
#2318 = DIRECTION('',(1.,0.E+000));
#2319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2320 = PCURVE('',#83,#2321);
#2321 = DEFINITIONAL_REPRESENTATION('',(#2322),#2326);
#2322 = LINE('',#2323,#2324);
#2323 = CARTESIAN_POINT('',(71.7132043,2.54111252));
#2324 = VECTOR('',#2325,1.);
#2325 = DIRECTION('',(0.608758355861,0.793355698391));
#2326 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2327 = ORIENTED_EDGE('',*,*,#2328,.F.);
#2328 = EDGE_CURVE('',#2329,#2306,#2331,.T.);
#2329 = VERTEX_POINT('',#2330);
#2330 = CARTESIAN_POINT('',(71.87350116,10.74998866,0.E+000));
#2331 = SURFACE_CURVE('',#2332,(#2336,#2343),.PCURVE_S1.);
#2332 = LINE('',#2333,#2334);
#2333 = CARTESIAN_POINT('',(71.87350116,10.74998866,0.E+000));
#2334 = VECTOR('',#2335,1.);
#2335 = DIRECTION('',(0.E+000,0.E+000,1.));
#2336 = PCURVE('',#2268,#2337);
#2337 = DEFINITIONAL_REPRESENTATION('',(#2338),#2342);
#2338 = LINE('',#2339,#2340);
#2339 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#2340 = VECTOR('',#2341,1.);
#2341 = DIRECTION('',(0.E+000,-1.));
#2342 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2343 = PCURVE('',#2344,#2349);
#2344 = PLANE('',#2345);
#2345 = AXIS2_PLACEMENT_3D('',#2346,#2347,#2348);
#2346 = CARTESIAN_POINT('',(71.87350116,10.74998866,0.E+000));
#2347 = DIRECTION('',(-0.923879741566,0.382682927661,0.E+000));
#2348 = DIRECTION('',(0.382682927661,0.923879741566,0.E+000));
#2349 = DEFINITIONAL_REPRESENTATION('',(#2350),#2354);
#2350 = LINE('',#2351,#2352);
#2351 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2352 = VECTOR('',#2353,1.);
#2353 = DIRECTION('',(0.E+000,-1.));
#2354 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2355 = ORIENTED_EDGE('',*,*,#2356,.F.);
#2356 = EDGE_CURVE('',#2253,#2329,#2357,.T.);
#2357 = SURFACE_CURVE('',#2358,(#2362,#2369),.PCURVE_S1.);
#2358 = LINE('',#2359,#2360);
#2359 = CARTESIAN_POINT('',(71.71321192,10.54109398,0.E+000));
#2360 = VECTOR('',#2361,1.);
#2361 = DIRECTION('',(0.608758355861,0.793355698391,0.E+000));
#2362 = PCURVE('',#2268,#2363);
#2363 = DEFINITIONAL_REPRESENTATION('',(#2364),#2368);
#2364 = LINE('',#2365,#2366);
#2365 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2366 = VECTOR('',#2367,1.);
#2367 = DIRECTION('',(1.,0.E+000));
#2368 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2369 = PCURVE('',#137,#2370);
#2370 = DEFINITIONAL_REPRESENTATION('',(#2371),#2375);
#2371 = LINE('',#2372,#2373);
#2372 = CARTESIAN_POINT('',(71.7132043,2.54111252));
#2373 = VECTOR('',#2374,1.);
#2374 = DIRECTION('',(0.608758355861,0.793355698391));
#2375 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2376 = ADVANCED_FACE('',(#2377),#2344,.F.);
#2377 = FACE_BOUND('',#2378,.F.);
#2378 = EDGE_LOOP('',(#2379,#2380,#2403,#2431));
#2379 = ORIENTED_EDGE('',*,*,#2328,.T.);
#2380 = ORIENTED_EDGE('',*,*,#2381,.T.);
#2381 = EDGE_CURVE('',#2306,#2382,#2384,.T.);
#2382 = VERTEX_POINT('',#2383);
#2383 = CARTESIAN_POINT('',(71.97426296,10.99324954,1.64592));
#2384 = SURFACE_CURVE('',#2385,(#2389,#2396),.PCURVE_S1.);
#2385 = LINE('',#2386,#2387);
#2386 = CARTESIAN_POINT('',(71.87350116,10.74998866,1.64592));
#2387 = VECTOR('',#2388,1.);
#2388 = DIRECTION('',(0.382682927661,0.923879741566,0.E+000));
#2389 = PCURVE('',#2344,#2390);
#2390 = DEFINITIONAL_REPRESENTATION('',(#2391),#2395);
#2391 = LINE('',#2392,#2393);
#2392 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2393 = VECTOR('',#2394,1.);
#2394 = DIRECTION('',(1.,0.E+000));
#2395 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2396 = PCURVE('',#83,#2397);
#2397 = DEFINITIONAL_REPRESENTATION('',(#2398),#2402);
#2398 = LINE('',#2399,#2400);
#2399 = CARTESIAN_POINT('',(71.87349354,2.7500072));
#2400 = VECTOR('',#2401,1.);
#2401 = DIRECTION('',(0.382682927661,0.923879741566));
#2402 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2403 = ORIENTED_EDGE('',*,*,#2404,.F.);
#2404 = EDGE_CURVE('',#2405,#2382,#2407,.T.);
#2405 = VERTEX_POINT('',#2406);
#2406 = CARTESIAN_POINT('',(71.97426296,10.99324954,0.E+000));
#2407 = SURFACE_CURVE('',#2408,(#2412,#2419),.PCURVE_S1.);
#2408 = LINE('',#2409,#2410);
#2409 = CARTESIAN_POINT('',(71.97426296,10.99324954,0.E+000));
#2410 = VECTOR('',#2411,1.);
#2411 = DIRECTION('',(0.E+000,0.E+000,1.));
#2412 = PCURVE('',#2344,#2413);
#2413 = DEFINITIONAL_REPRESENTATION('',(#2414),#2418);
#2414 = LINE('',#2415,#2416);
#2415 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#2416 = VECTOR('',#2417,1.);
#2417 = DIRECTION('',(0.E+000,-1.));
#2418 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2419 = PCURVE('',#2420,#2425);
#2420 = PLANE('',#2421);
#2421 = AXIS2_PLACEMENT_3D('',#2422,#2423,#2424);
#2422 = CARTESIAN_POINT('',(71.97426296,10.99324954,0.E+000));
#2423 = DIRECTION('',(-0.991444672552,0.130527626456,0.E+000));
#2424 = DIRECTION('',(0.130527626456,0.991444672552,0.E+000));
#2425 = DEFINITIONAL_REPRESENTATION('',(#2426),#2430);
#2426 = LINE('',#2427,#2428);
#2427 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2428 = VECTOR('',#2429,1.);
#2429 = DIRECTION('',(0.E+000,-1.));
#2430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2431 = ORIENTED_EDGE('',*,*,#2432,.F.);
#2432 = EDGE_CURVE('',#2329,#2405,#2433,.T.);
#2433 = SURFACE_CURVE('',#2434,(#2438,#2445),.PCURVE_S1.);
#2434 = LINE('',#2435,#2436);
#2435 = CARTESIAN_POINT('',(71.87350116,10.74998866,0.E+000));
#2436 = VECTOR('',#2437,1.);
#2437 = DIRECTION('',(0.382682927661,0.923879741566,0.E+000));
#2438 = PCURVE('',#2344,#2439);
#2439 = DEFINITIONAL_REPRESENTATION('',(#2440),#2444);
#2440 = LINE('',#2441,#2442);
#2441 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2442 = VECTOR('',#2443,1.);
#2443 = DIRECTION('',(1.,0.E+000));
#2444 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2445 = PCURVE('',#137,#2446);
#2446 = DEFINITIONAL_REPRESENTATION('',(#2447),#2451);
#2447 = LINE('',#2448,#2449);
#2448 = CARTESIAN_POINT('',(71.87349354,2.7500072));
#2449 = VECTOR('',#2450,1.);
#2450 = DIRECTION('',(0.382682927661,0.923879741566));
#2451 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2452 = ADVANCED_FACE('',(#2453),#2420,.F.);
#2453 = FACE_BOUND('',#2454,.F.);
#2454 = EDGE_LOOP('',(#2455,#2456,#2479,#2507));
#2455 = ORIENTED_EDGE('',*,*,#2404,.T.);
#2456 = ORIENTED_EDGE('',*,*,#2457,.T.);
#2457 = EDGE_CURVE('',#2382,#2458,#2460,.T.);
#2458 = VERTEX_POINT('',#2459);
#2459 = CARTESIAN_POINT('',(72.0086317,11.25430312,1.64592));
#2460 = SURFACE_CURVE('',#2461,(#2465,#2472),.PCURVE_S1.);
#2461 = LINE('',#2462,#2463);
#2462 = CARTESIAN_POINT('',(71.97426296,10.99324954,1.64592));
#2463 = VECTOR('',#2464,1.);
#2464 = DIRECTION('',(0.130527626456,0.991444672552,0.E+000));
#2465 = PCURVE('',#2420,#2466);
#2466 = DEFINITIONAL_REPRESENTATION('',(#2467),#2471);
#2467 = LINE('',#2468,#2469);
#2468 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2469 = VECTOR('',#2470,1.);
#2470 = DIRECTION('',(1.,0.E+000));
#2471 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2472 = PCURVE('',#83,#2473);
#2473 = DEFINITIONAL_REPRESENTATION('',(#2474),#2478);
#2474 = LINE('',#2475,#2476);
#2475 = CARTESIAN_POINT('',(71.97425534,2.99326808));
#2476 = VECTOR('',#2477,1.);
#2477 = DIRECTION('',(0.130527626456,0.991444672552));
#2478 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2479 = ORIENTED_EDGE('',*,*,#2480,.F.);
#2480 = EDGE_CURVE('',#2481,#2458,#2483,.T.);
#2481 = VERTEX_POINT('',#2482);
#2482 = CARTESIAN_POINT('',(72.0086317,11.25430312,0.E+000));
#2483 = SURFACE_CURVE('',#2484,(#2488,#2495),.PCURVE_S1.);
#2484 = LINE('',#2485,#2486);
#2485 = CARTESIAN_POINT('',(72.0086317,11.25430312,0.E+000));
#2486 = VECTOR('',#2487,1.);
#2487 = DIRECTION('',(0.E+000,0.E+000,1.));
#2488 = PCURVE('',#2420,#2489);
#2489 = DEFINITIONAL_REPRESENTATION('',(#2490),#2494);
#2490 = LINE('',#2491,#2492);
#2491 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#2492 = VECTOR('',#2493,1.);
#2493 = DIRECTION('',(0.E+000,-1.));
#2494 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2495 = PCURVE('',#2496,#2501);
#2496 = PLANE('',#2497);
#2497 = AXIS2_PLACEMENT_3D('',#2498,#2499,#2500);
#2498 = CARTESIAN_POINT('',(72.0086317,11.25430312,0.E+000));
#2499 = DIRECTION('',(-0.999990693999,-4.314153001469E-003,0.E+000));
#2500 = DIRECTION('',(-4.314153001469E-003,0.999990693999,0.E+000));
#2501 = DEFINITIONAL_REPRESENTATION('',(#2502),#2506);
#2502 = LINE('',#2503,#2504);
#2503 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2504 = VECTOR('',#2505,1.);
#2505 = DIRECTION('',(0.E+000,-1.));
#2506 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2507 = ORIENTED_EDGE('',*,*,#2508,.F.);
#2508 = EDGE_CURVE('',#2405,#2481,#2509,.T.);
#2509 = SURFACE_CURVE('',#2510,(#2514,#2521),.PCURVE_S1.);
#2510 = LINE('',#2511,#2512);
#2511 = CARTESIAN_POINT('',(71.97426296,10.99324954,0.E+000));
#2512 = VECTOR('',#2513,1.);
#2513 = DIRECTION('',(0.130527626456,0.991444672552,0.E+000));
#2514 = PCURVE('',#2420,#2515);
#2515 = DEFINITIONAL_REPRESENTATION('',(#2516),#2520);
#2516 = LINE('',#2517,#2518);
#2517 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2518 = VECTOR('',#2519,1.);
#2519 = DIRECTION('',(1.,0.E+000));
#2520 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2521 = PCURVE('',#137,#2522);
#2522 = DEFINITIONAL_REPRESENTATION('',(#2523),#2527);
#2523 = LINE('',#2524,#2525);
#2524 = CARTESIAN_POINT('',(71.97425534,2.99326808));
#2525 = VECTOR('',#2526,1.);
#2526 = DIRECTION('',(0.130527626456,0.991444672552));
#2527 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2528 = ADVANCED_FACE('',(#2529),#2496,.F.);
#2529 = FACE_BOUND('',#2530,.F.);
#2530 = EDGE_LOOP('',(#2531,#2532,#2555,#2583));
#2531 = ORIENTED_EDGE('',*,*,#2480,.T.);
#2532 = ORIENTED_EDGE('',*,*,#2533,.T.);
#2533 = EDGE_CURVE('',#2458,#2534,#2536,.T.);
#2534 = VERTEX_POINT('',#2535);
#2535 = CARTESIAN_POINT('',(72.00000332,13.25430166,1.64592));
#2536 = SURFACE_CURVE('',#2537,(#2541,#2548),.PCURVE_S1.);
#2537 = LINE('',#2538,#2539);
#2538 = CARTESIAN_POINT('',(72.0086317,11.25430312,1.64592));
#2539 = VECTOR('',#2540,1.);
#2540 = DIRECTION('',(-4.314153001469E-003,0.999990693999,0.E+000));
#2541 = PCURVE('',#2496,#2542);
#2542 = DEFINITIONAL_REPRESENTATION('',(#2543),#2547);
#2543 = LINE('',#2544,#2545);
#2544 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2545 = VECTOR('',#2546,1.);
#2546 = DIRECTION('',(1.,0.E+000));
#2547 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2548 = PCURVE('',#83,#2549);
#2549 = DEFINITIONAL_REPRESENTATION('',(#2550),#2554);
#2550 = LINE('',#2551,#2552);
#2551 = CARTESIAN_POINT('',(72.00862408,3.25432166));
#2552 = VECTOR('',#2553,1.);
#2553 = DIRECTION('',(-4.314153001469E-003,0.999990693999));
#2554 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2555 = ORIENTED_EDGE('',*,*,#2556,.F.);
#2556 = EDGE_CURVE('',#2557,#2534,#2559,.T.);
#2557 = VERTEX_POINT('',#2558);
#2558 = CARTESIAN_POINT('',(72.00000332,13.25430166,0.E+000));
#2559 = SURFACE_CURVE('',#2560,(#2564,#2571),.PCURVE_S1.);
#2560 = LINE('',#2561,#2562);
#2561 = CARTESIAN_POINT('',(72.00000332,13.25430166,0.E+000));
#2562 = VECTOR('',#2563,1.);
#2563 = DIRECTION('',(0.E+000,0.E+000,1.));
#2564 = PCURVE('',#2496,#2565);
#2565 = DEFINITIONAL_REPRESENTATION('',(#2566),#2570);
#2566 = LINE('',#2567,#2568);
#2567 = CARTESIAN_POINT('',(2.000017152162,0.E+000));
#2568 = VECTOR('',#2569,1.);
#2569 = DIRECTION('',(0.E+000,-1.));
#2570 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2571 = PCURVE('',#2572,#2577);
#2572 = PLANE('',#2573);
#2573 = AXIS2_PLACEMENT_3D('',#2574,#2575,#2576);
#2574 = CARTESIAN_POINT('',(72.00000332,13.25430166,0.E+000));
#2575 = DIRECTION('',(0.E+000,1.,0.E+000));
#2576 = DIRECTION('',(1.,0.E+000,0.E+000));
#2577 = DEFINITIONAL_REPRESENTATION('',(#2578),#2582);
#2578 = LINE('',#2579,#2580);
#2579 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2580 = VECTOR('',#2581,1.);
#2581 = DIRECTION('',(0.E+000,-1.));
#2582 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2583 = ORIENTED_EDGE('',*,*,#2584,.F.);
#2584 = EDGE_CURVE('',#2481,#2557,#2585,.T.);
#2585 = SURFACE_CURVE('',#2586,(#2590,#2597),.PCURVE_S1.);
#2586 = LINE('',#2587,#2588);
#2587 = CARTESIAN_POINT('',(72.0086317,11.25430312,0.E+000));
#2588 = VECTOR('',#2589,1.);
#2589 = DIRECTION('',(-4.314153001469E-003,0.999990693999,0.E+000));
#2590 = PCURVE('',#2496,#2591);
#2591 = DEFINITIONAL_REPRESENTATION('',(#2592),#2596);
#2592 = LINE('',#2593,#2594);
#2593 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2594 = VECTOR('',#2595,1.);
#2595 = DIRECTION('',(1.,0.E+000));
#2596 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2597 = PCURVE('',#137,#2598);
#2598 = DEFINITIONAL_REPRESENTATION('',(#2599),#2603);
#2599 = LINE('',#2600,#2601);
#2600 = CARTESIAN_POINT('',(72.00862408,3.25432166));
#2601 = VECTOR('',#2602,1.);
#2602 = DIRECTION('',(-4.314153001469E-003,0.999990693999));
#2603 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2604 = ADVANCED_FACE('',(#2605),#2572,.F.);
#2605 = FACE_BOUND('',#2606,.F.);
#2606 = EDGE_LOOP('',(#2607,#2608,#2631,#2659));
#2607 = ORIENTED_EDGE('',*,*,#2556,.T.);
#2608 = ORIENTED_EDGE('',*,*,#2609,.T.);
#2609 = EDGE_CURVE('',#2534,#2610,#2612,.T.);
#2610 = VERTEX_POINT('',#2611);
#2611 = CARTESIAN_POINT('',(72.0086317,13.25430166,1.64592));
#2612 = SURFACE_CURVE('',#2613,(#2617,#2624),.PCURVE_S1.);
#2613 = LINE('',#2614,#2615);
#2614 = CARTESIAN_POINT('',(72.00000332,13.25430166,1.64592));
#2615 = VECTOR('',#2616,1.);
#2616 = DIRECTION('',(1.,0.E+000,0.E+000));
#2617 = PCURVE('',#2572,#2618);
#2618 = DEFINITIONAL_REPRESENTATION('',(#2619),#2623);
#2619 = LINE('',#2620,#2621);
#2620 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2621 = VECTOR('',#2622,1.);
#2622 = DIRECTION('',(1.,0.E+000));
#2623 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2624 = PCURVE('',#83,#2625);
#2625 = DEFINITIONAL_REPRESENTATION('',(#2626),#2630);
#2626 = LINE('',#2627,#2628);
#2627 = CARTESIAN_POINT('',(71.9999957,5.2543202));
#2628 = VECTOR('',#2629,1.);
#2629 = DIRECTION('',(1.,0.E+000));
#2630 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2631 = ORIENTED_EDGE('',*,*,#2632,.F.);
#2632 = EDGE_CURVE('',#2633,#2610,#2635,.T.);
#2633 = VERTEX_POINT('',#2634);
#2634 = CARTESIAN_POINT('',(72.0086317,13.25430166,0.E+000));
#2635 = SURFACE_CURVE('',#2636,(#2640,#2647),.PCURVE_S1.);
#2636 = LINE('',#2637,#2638);
#2637 = CARTESIAN_POINT('',(72.0086317,13.25430166,0.E+000));
#2638 = VECTOR('',#2639,1.);
#2639 = DIRECTION('',(0.E+000,0.E+000,1.));
#2640 = PCURVE('',#2572,#2641);
#2641 = DEFINITIONAL_REPRESENTATION('',(#2642),#2646);
#2642 = LINE('',#2643,#2644);
#2643 = CARTESIAN_POINT('',(8.62837999999E-003,0.E+000));
#2644 = VECTOR('',#2645,1.);
#2645 = DIRECTION('',(0.E+000,-1.));
#2646 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2647 = PCURVE('',#2648,#2653);
#2648 = PLANE('',#2649);
#2649 = AXIS2_PLACEMENT_3D('',#2650,#2651,#2652);
#2650 = CARTESIAN_POINT('',(72.0086317,13.25430166,0.E+000));
#2651 = DIRECTION('',(-0.991444672552,-0.130527626456,0.E+000));
#2652 = DIRECTION('',(-0.130527626456,0.991444672552,0.E+000));
#2653 = DEFINITIONAL_REPRESENTATION('',(#2654),#2658);
#2654 = LINE('',#2655,#2656);
#2655 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2656 = VECTOR('',#2657,1.);
#2657 = DIRECTION('',(0.E+000,-1.));
#2658 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2659 = ORIENTED_EDGE('',*,*,#2660,.F.);
#2660 = EDGE_CURVE('',#2557,#2633,#2661,.T.);
#2661 = SURFACE_CURVE('',#2662,(#2666,#2673),.PCURVE_S1.);
#2662 = LINE('',#2663,#2664);
#2663 = CARTESIAN_POINT('',(72.00000332,13.25430166,0.E+000));
#2664 = VECTOR('',#2665,1.);
#2665 = DIRECTION('',(1.,0.E+000,0.E+000));
#2666 = PCURVE('',#2572,#2667);
#2667 = DEFINITIONAL_REPRESENTATION('',(#2668),#2672);
#2668 = LINE('',#2669,#2670);
#2669 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2670 = VECTOR('',#2671,1.);
#2671 = DIRECTION('',(1.,0.E+000));
#2672 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2673 = PCURVE('',#137,#2674);
#2674 = DEFINITIONAL_REPRESENTATION('',(#2675),#2679);
#2675 = LINE('',#2676,#2677);
#2676 = CARTESIAN_POINT('',(71.9999957,5.2543202));
#2677 = VECTOR('',#2678,1.);
#2678 = DIRECTION('',(1.,0.E+000));
#2679 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2680 = ADVANCED_FACE('',(#2681),#2648,.F.);
#2681 = FACE_BOUND('',#2682,.F.);
#2682 = EDGE_LOOP('',(#2683,#2684,#2707,#2735));
#2683 = ORIENTED_EDGE('',*,*,#2632,.T.);
#2684 = ORIENTED_EDGE('',*,*,#2685,.T.);
#2685 = EDGE_CURVE('',#2610,#2686,#2688,.T.);
#2686 = VERTEX_POINT('',#2687);
#2687 = CARTESIAN_POINT('',(71.97426296,13.51535524,1.64592));
#2688 = SURFACE_CURVE('',#2689,(#2693,#2700),.PCURVE_S1.);
#2689 = LINE('',#2690,#2691);
#2690 = CARTESIAN_POINT('',(72.0086317,13.25430166,1.64592));
#2691 = VECTOR('',#2692,1.);
#2692 = DIRECTION('',(-0.130527626456,0.991444672552,0.E+000));
#2693 = PCURVE('',#2648,#2694);
#2694 = DEFINITIONAL_REPRESENTATION('',(#2695),#2699);
#2695 = LINE('',#2696,#2697);
#2696 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2697 = VECTOR('',#2698,1.);
#2698 = DIRECTION('',(1.,0.E+000));
#2699 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2700 = PCURVE('',#83,#2701);
#2701 = DEFINITIONAL_REPRESENTATION('',(#2702),#2706);
#2702 = LINE('',#2703,#2704);
#2703 = CARTESIAN_POINT('',(72.00862408,5.2543202));
#2704 = VECTOR('',#2705,1.);
#2705 = DIRECTION('',(-0.130527626456,0.991444672552));
#2706 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2707 = ORIENTED_EDGE('',*,*,#2708,.F.);
#2708 = EDGE_CURVE('',#2709,#2686,#2711,.T.);
#2709 = VERTEX_POINT('',#2710);
#2710 = CARTESIAN_POINT('',(71.97426296,13.51535524,0.E+000));
#2711 = SURFACE_CURVE('',#2712,(#2716,#2723),.PCURVE_S1.);
#2712 = LINE('',#2713,#2714);
#2713 = CARTESIAN_POINT('',(71.97426296,13.51535524,0.E+000));
#2714 = VECTOR('',#2715,1.);
#2715 = DIRECTION('',(0.E+000,0.E+000,1.));
#2716 = PCURVE('',#2648,#2717);
#2717 = DEFINITIONAL_REPRESENTATION('',(#2718),#2722);
#2718 = LINE('',#2719,#2720);
#2719 = CARTESIAN_POINT('',(0.263306251198,0.E+000));
#2720 = VECTOR('',#2721,1.);
#2721 = DIRECTION('',(0.E+000,-1.));
#2722 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2723 = PCURVE('',#2724,#2729);
#2724 = PLANE('',#2725);
#2725 = AXIS2_PLACEMENT_3D('',#2726,#2727,#2728);
#2726 = CARTESIAN_POINT('',(71.97426296,13.51535524,0.E+000));
#2727 = DIRECTION('',(-0.923879741566,-0.382682927661,0.E+000));
#2728 = DIRECTION('',(-0.382682927661,0.923879741566,0.E+000));
#2729 = DEFINITIONAL_REPRESENTATION('',(#2730),#2734);
#2730 = LINE('',#2731,#2732);
#2731 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2732 = VECTOR('',#2733,1.);
#2733 = DIRECTION('',(0.E+000,-1.));
#2734 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2735 = ORIENTED_EDGE('',*,*,#2736,.F.);
#2736 = EDGE_CURVE('',#2633,#2709,#2737,.T.);
#2737 = SURFACE_CURVE('',#2738,(#2742,#2749),.PCURVE_S1.);
#2738 = LINE('',#2739,#2740);
#2739 = CARTESIAN_POINT('',(72.0086317,13.25430166,0.E+000));
#2740 = VECTOR('',#2741,1.);
#2741 = DIRECTION('',(-0.130527626456,0.991444672552,0.E+000));
#2742 = PCURVE('',#2648,#2743);
#2743 = DEFINITIONAL_REPRESENTATION('',(#2744),#2748);
#2744 = LINE('',#2745,#2746);
#2745 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2746 = VECTOR('',#2747,1.);
#2747 = DIRECTION('',(1.,0.E+000));
#2748 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2749 = PCURVE('',#137,#2750);
#2750 = DEFINITIONAL_REPRESENTATION('',(#2751),#2755);
#2751 = LINE('',#2752,#2753);
#2752 = CARTESIAN_POINT('',(72.00862408,5.2543202));
#2753 = VECTOR('',#2754,1.);
#2754 = DIRECTION('',(-0.130527626456,0.991444672552));
#2755 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2756 = ADVANCED_FACE('',(#2757),#2724,.F.);
#2757 = FACE_BOUND('',#2758,.F.);
#2758 = EDGE_LOOP('',(#2759,#2760,#2783,#2811));
#2759 = ORIENTED_EDGE('',*,*,#2708,.T.);
#2760 = ORIENTED_EDGE('',*,*,#2761,.T.);
#2761 = EDGE_CURVE('',#2686,#2762,#2764,.T.);
#2762 = VERTEX_POINT('',#2763);
#2763 = CARTESIAN_POINT('',(71.87350116,13.75861612,1.64592));
#2764 = SURFACE_CURVE('',#2765,(#2769,#2776),.PCURVE_S1.);
#2765 = LINE('',#2766,#2767);
#2766 = CARTESIAN_POINT('',(71.97426296,13.51535524,1.64592));
#2767 = VECTOR('',#2768,1.);
#2768 = DIRECTION('',(-0.382682927661,0.923879741566,0.E+000));
#2769 = PCURVE('',#2724,#2770);
#2770 = DEFINITIONAL_REPRESENTATION('',(#2771),#2775);
#2771 = LINE('',#2772,#2773);
#2772 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2773 = VECTOR('',#2774,1.);
#2774 = DIRECTION('',(1.,0.E+000));
#2775 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2776 = PCURVE('',#83,#2777);
#2777 = DEFINITIONAL_REPRESENTATION('',(#2778),#2782);
#2778 = LINE('',#2779,#2780);
#2779 = CARTESIAN_POINT('',(71.97425534,5.51537378));
#2780 = VECTOR('',#2781,1.);
#2781 = DIRECTION('',(-0.382682927661,0.923879741566));
#2782 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2783 = ORIENTED_EDGE('',*,*,#2784,.F.);
#2784 = EDGE_CURVE('',#2785,#2762,#2787,.T.);
#2785 = VERTEX_POINT('',#2786);
#2786 = CARTESIAN_POINT('',(71.87350116,13.75861612,0.E+000));
#2787 = SURFACE_CURVE('',#2788,(#2792,#2799),.PCURVE_S1.);
#2788 = LINE('',#2789,#2790);
#2789 = CARTESIAN_POINT('',(71.87350116,13.75861612,0.E+000));
#2790 = VECTOR('',#2791,1.);
#2791 = DIRECTION('',(0.E+000,0.E+000,1.));
#2792 = PCURVE('',#2724,#2793);
#2793 = DEFINITIONAL_REPRESENTATION('',(#2794),#2798);
#2794 = LINE('',#2795,#2796);
#2795 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#2796 = VECTOR('',#2797,1.);
#2797 = DIRECTION('',(0.E+000,-1.));
#2798 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2799 = PCURVE('',#2800,#2805);
#2800 = PLANE('',#2801);
#2801 = AXIS2_PLACEMENT_3D('',#2802,#2803,#2804);
#2802 = CARTESIAN_POINT('',(71.87350116,13.75861612,0.E+000));
#2803 = DIRECTION('',(-0.793355698391,-0.608758355861,0.E+000));
#2804 = DIRECTION('',(-0.608758355861,0.793355698391,0.E+000));
#2805 = DEFINITIONAL_REPRESENTATION('',(#2806),#2810);
#2806 = LINE('',#2807,#2808);
#2807 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2808 = VECTOR('',#2809,1.);
#2809 = DIRECTION('',(0.E+000,-1.));
#2810 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2811 = ORIENTED_EDGE('',*,*,#2812,.F.);
#2812 = EDGE_CURVE('',#2709,#2785,#2813,.T.);
#2813 = SURFACE_CURVE('',#2814,(#2818,#2825),.PCURVE_S1.);
#2814 = LINE('',#2815,#2816);
#2815 = CARTESIAN_POINT('',(71.97426296,13.51535524,0.E+000));
#2816 = VECTOR('',#2817,1.);
#2817 = DIRECTION('',(-0.382682927661,0.923879741566,0.E+000));
#2818 = PCURVE('',#2724,#2819);
#2819 = DEFINITIONAL_REPRESENTATION('',(#2820),#2824);
#2820 = LINE('',#2821,#2822);
#2821 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2822 = VECTOR('',#2823,1.);
#2823 = DIRECTION('',(1.,0.E+000));
#2824 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2825 = PCURVE('',#137,#2826);
#2826 = DEFINITIONAL_REPRESENTATION('',(#2827),#2831);
#2827 = LINE('',#2828,#2829);
#2828 = CARTESIAN_POINT('',(71.97425534,5.51537378));
#2829 = VECTOR('',#2830,1.);
#2830 = DIRECTION('',(-0.382682927661,0.923879741566));
#2831 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2832 = ADVANCED_FACE('',(#2833),#2800,.F.);
#2833 = FACE_BOUND('',#2834,.F.);
#2834 = EDGE_LOOP('',(#2835,#2836,#2859,#2887));
#2835 = ORIENTED_EDGE('',*,*,#2784,.T.);
#2836 = ORIENTED_EDGE('',*,*,#2837,.T.);
#2837 = EDGE_CURVE('',#2762,#2838,#2840,.T.);
#2838 = VERTEX_POINT('',#2839);
#2839 = CARTESIAN_POINT('',(71.71321192,13.9675108,1.64592));
#2840 = SURFACE_CURVE('',#2841,(#2845,#2852),.PCURVE_S1.);
#2841 = LINE('',#2842,#2843);
#2842 = CARTESIAN_POINT('',(71.87350116,13.75861612,1.64592));
#2843 = VECTOR('',#2844,1.);
#2844 = DIRECTION('',(-0.608758355861,0.793355698391,0.E+000));
#2845 = PCURVE('',#2800,#2846);
#2846 = DEFINITIONAL_REPRESENTATION('',(#2847),#2851);
#2847 = LINE('',#2848,#2849);
#2848 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2849 = VECTOR('',#2850,1.);
#2850 = DIRECTION('',(1.,0.E+000));
#2851 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2852 = PCURVE('',#83,#2853);
#2853 = DEFINITIONAL_REPRESENTATION('',(#2854),#2858);
#2854 = LINE('',#2855,#2856);
#2855 = CARTESIAN_POINT('',(71.87349354,5.75863466));
#2856 = VECTOR('',#2857,1.);
#2857 = DIRECTION('',(-0.608758355861,0.793355698391));
#2858 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2859 = ORIENTED_EDGE('',*,*,#2860,.F.);
#2860 = EDGE_CURVE('',#2861,#2838,#2863,.T.);
#2861 = VERTEX_POINT('',#2862);
#2862 = CARTESIAN_POINT('',(71.71321192,13.9675108,0.E+000));
#2863 = SURFACE_CURVE('',#2864,(#2868,#2875),.PCURVE_S1.);
#2864 = LINE('',#2865,#2866);
#2865 = CARTESIAN_POINT('',(71.71321192,13.9675108,0.E+000));
#2866 = VECTOR('',#2867,1.);
#2867 = DIRECTION('',(0.E+000,0.E+000,1.));
#2868 = PCURVE('',#2800,#2869);
#2869 = DEFINITIONAL_REPRESENTATION('',(#2870),#2874);
#2870 = LINE('',#2871,#2872);
#2871 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#2872 = VECTOR('',#2873,1.);
#2873 = DIRECTION('',(0.E+000,-1.));
#2874 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2875 = PCURVE('',#2876,#2881);
#2876 = PLANE('',#2877);
#2877 = AXIS2_PLACEMENT_3D('',#2878,#2879,#2880);
#2878 = CARTESIAN_POINT('',(71.71321192,13.9675108,0.E+000));
#2879 = DIRECTION('',(-0.608758355861,-0.793355698391,0.E+000));
#2880 = DIRECTION('',(-0.793355698391,0.608758355861,0.E+000));
#2881 = DEFINITIONAL_REPRESENTATION('',(#2882),#2886);
#2882 = LINE('',#2883,#2884);
#2883 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2884 = VECTOR('',#2885,1.);
#2885 = DIRECTION('',(0.E+000,-1.));
#2886 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2887 = ORIENTED_EDGE('',*,*,#2888,.F.);
#2888 = EDGE_CURVE('',#2785,#2861,#2889,.T.);
#2889 = SURFACE_CURVE('',#2890,(#2894,#2901),.PCURVE_S1.);
#2890 = LINE('',#2891,#2892);
#2891 = CARTESIAN_POINT('',(71.87350116,13.75861612,0.E+000));
#2892 = VECTOR('',#2893,1.);
#2893 = DIRECTION('',(-0.608758355861,0.793355698391,0.E+000));
#2894 = PCURVE('',#2800,#2895);
#2895 = DEFINITIONAL_REPRESENTATION('',(#2896),#2900);
#2896 = LINE('',#2897,#2898);
#2897 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2898 = VECTOR('',#2899,1.);
#2899 = DIRECTION('',(1.,0.E+000));
#2900 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2901 = PCURVE('',#137,#2902);
#2902 = DEFINITIONAL_REPRESENTATION('',(#2903),#2907);
#2903 = LINE('',#2904,#2905);
#2904 = CARTESIAN_POINT('',(71.87349354,5.75863466));
#2905 = VECTOR('',#2906,1.);
#2906 = DIRECTION('',(-0.608758355861,0.793355698391));
#2907 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2908 = ADVANCED_FACE('',(#2909),#2876,.F.);
#2909 = FACE_BOUND('',#2910,.F.);
#2910 = EDGE_LOOP('',(#2911,#2912,#2935,#2963));
#2911 = ORIENTED_EDGE('',*,*,#2860,.T.);
#2912 = ORIENTED_EDGE('',*,*,#2913,.T.);
#2913 = EDGE_CURVE('',#2838,#2914,#2916,.T.);
#2914 = VERTEX_POINT('',#2915);
#2915 = CARTESIAN_POINT('',(71.50431724,14.12780004,1.64592));
#2916 = SURFACE_CURVE('',#2917,(#2921,#2928),.PCURVE_S1.);
#2917 = LINE('',#2918,#2919);
#2918 = CARTESIAN_POINT('',(71.71321192,13.9675108,1.64592));
#2919 = VECTOR('',#2920,1.);
#2920 = DIRECTION('',(-0.793355698391,0.608758355861,0.E+000));
#2921 = PCURVE('',#2876,#2922);
#2922 = DEFINITIONAL_REPRESENTATION('',(#2923),#2927);
#2923 = LINE('',#2924,#2925);
#2924 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#2925 = VECTOR('',#2926,1.);
#2926 = DIRECTION('',(1.,0.E+000));
#2927 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2928 = PCURVE('',#83,#2929);
#2929 = DEFINITIONAL_REPRESENTATION('',(#2930),#2934);
#2930 = LINE('',#2931,#2932);
#2931 = CARTESIAN_POINT('',(71.7132043,5.96752934));
#2932 = VECTOR('',#2933,1.);
#2933 = DIRECTION('',(-0.793355698391,0.608758355861));
#2934 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2935 = ORIENTED_EDGE('',*,*,#2936,.F.);
#2936 = EDGE_CURVE('',#2937,#2914,#2939,.T.);
#2937 = VERTEX_POINT('',#2938);
#2938 = CARTESIAN_POINT('',(71.50431724,14.12780004,0.E+000));
#2939 = SURFACE_CURVE('',#2940,(#2944,#2951),.PCURVE_S1.);
#2940 = LINE('',#2941,#2942);
#2941 = CARTESIAN_POINT('',(71.50431724,14.12780004,0.E+000));
#2942 = VECTOR('',#2943,1.);
#2943 = DIRECTION('',(0.E+000,0.E+000,1.));
#2944 = PCURVE('',#2876,#2945);
#2945 = DEFINITIONAL_REPRESENTATION('',(#2946),#2950);
#2946 = LINE('',#2947,#2948);
#2947 = CARTESIAN_POINT('',(0.263305198946,0.E+000));
#2948 = VECTOR('',#2949,1.);
#2949 = DIRECTION('',(0.E+000,-1.));
#2950 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2951 = PCURVE('',#2952,#2957);
#2952 = PLANE('',#2953);
#2953 = AXIS2_PLACEMENT_3D('',#2954,#2955,#2956);
#2954 = CARTESIAN_POINT('',(71.50431724,14.12780004,0.E+000));
#2955 = DIRECTION('',(-0.382682927661,-0.923879741566,0.E+000));
#2956 = DIRECTION('',(-0.923879741566,0.382682927661,0.E+000));
#2957 = DEFINITIONAL_REPRESENTATION('',(#2958),#2962);
#2958 = LINE('',#2959,#2960);
#2959 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2960 = VECTOR('',#2961,1.);
#2961 = DIRECTION('',(0.E+000,-1.));
#2962 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2963 = ORIENTED_EDGE('',*,*,#2964,.F.);
#2964 = EDGE_CURVE('',#2861,#2937,#2965,.T.);
#2965 = SURFACE_CURVE('',#2966,(#2970,#2977),.PCURVE_S1.);
#2966 = LINE('',#2967,#2968);
#2967 = CARTESIAN_POINT('',(71.71321192,13.9675108,0.E+000));
#2968 = VECTOR('',#2969,1.);
#2969 = DIRECTION('',(-0.793355698391,0.608758355861,0.E+000));
#2970 = PCURVE('',#2876,#2971);
#2971 = DEFINITIONAL_REPRESENTATION('',(#2972),#2976);
#2972 = LINE('',#2973,#2974);
#2973 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#2974 = VECTOR('',#2975,1.);
#2975 = DIRECTION('',(1.,0.E+000));
#2976 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2977 = PCURVE('',#137,#2978);
#2978 = DEFINITIONAL_REPRESENTATION('',(#2979),#2983);
#2979 = LINE('',#2980,#2981);
#2980 = CARTESIAN_POINT('',(71.7132043,5.96752934));
#2981 = VECTOR('',#2982,1.);
#2982 = DIRECTION('',(-0.793355698391,0.608758355861));
#2983 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2984 = ADVANCED_FACE('',(#2985),#2952,.F.);
#2985 = FACE_BOUND('',#2986,.F.);
#2986 = EDGE_LOOP('',(#2987,#2988,#3011,#3034));
#2987 = ORIENTED_EDGE('',*,*,#2936,.T.);
#2988 = ORIENTED_EDGE('',*,*,#2989,.T.);
#2989 = EDGE_CURVE('',#2914,#2990,#2992,.T.);
#2990 = VERTEX_POINT('',#2991);
#2991 = CARTESIAN_POINT('',(71.26105636,14.22856184,1.64592));
#2992 = SURFACE_CURVE('',#2993,(#2997,#3004),.PCURVE_S1.);
#2993 = LINE('',#2994,#2995);
#2994 = CARTESIAN_POINT('',(71.50431724,14.12780004,1.64592));
#2995 = VECTOR('',#2996,1.);
#2996 = DIRECTION('',(-0.923879741566,0.382682927661,0.E+000));
#2997 = PCURVE('',#2952,#2998);
#2998 = DEFINITIONAL_REPRESENTATION('',(#2999),#3003);
#2999 = LINE('',#3000,#3001);
#3000 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#3001 = VECTOR('',#3002,1.);
#3002 = DIRECTION('',(1.,0.E+000));
#3003 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3004 = PCURVE('',#83,#3005);
#3005 = DEFINITIONAL_REPRESENTATION('',(#3006),#3010);
#3006 = LINE('',#3007,#3008);
#3007 = CARTESIAN_POINT('',(71.50430962,6.12781858));
#3008 = VECTOR('',#3009,1.);
#3009 = DIRECTION('',(-0.923879741566,0.382682927661));
#3010 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3011 = ORIENTED_EDGE('',*,*,#3012,.F.);
#3012 = EDGE_CURVE('',#3013,#2990,#3015,.T.);
#3013 = VERTEX_POINT('',#3014);
#3014 = CARTESIAN_POINT('',(71.26105636,14.22856184,0.E+000));
#3015 = SURFACE_CURVE('',#3016,(#3020,#3027),.PCURVE_S1.);
#3016 = LINE('',#3017,#3018);
#3017 = CARTESIAN_POINT('',(71.26105636,14.22856184,0.E+000));
#3018 = VECTOR('',#3019,1.);
#3019 = DIRECTION('',(0.E+000,0.E+000,1.));
#3020 = PCURVE('',#2952,#3021);
#3021 = DEFINITIONAL_REPRESENTATION('',(#3022),#3026);
#3022 = LINE('',#3023,#3024);
#3023 = CARTESIAN_POINT('',(0.263303619568,0.E+000));
#3024 = VECTOR('',#3025,1.);
#3025 = DIRECTION('',(0.E+000,-1.));
#3026 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3027 = PCURVE('',#697,#3028);
#3028 = DEFINITIONAL_REPRESENTATION('',(#3029),#3033);
#3029 = LINE('',#3030,#3031);
#3030 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3031 = VECTOR('',#3032,1.);
#3032 = DIRECTION('',(0.E+000,-1.));
#3033 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3034 = ORIENTED_EDGE('',*,*,#3035,.F.);
#3035 = EDGE_CURVE('',#2937,#3013,#3036,.T.);
#3036 = SURFACE_CURVE('',#3037,(#3041,#3048),.PCURVE_S1.);
#3037 = LINE('',#3038,#3039);
#3038 = CARTESIAN_POINT('',(71.50431724,14.12780004,0.E+000));
#3039 = VECTOR('',#3040,1.);
#3040 = DIRECTION('',(-0.923879741566,0.382682927661,0.E+000));
#3041 = PCURVE('',#2952,#3042);
#3042 = DEFINITIONAL_REPRESENTATION('',(#3043),#3047);
#3043 = LINE('',#3044,#3045);
#3044 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3045 = VECTOR('',#3046,1.);
#3046 = DIRECTION('',(1.,0.E+000));
#3047 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3048 = PCURVE('',#137,#3049);
#3049 = DEFINITIONAL_REPRESENTATION('',(#3050),#3054);
#3050 = LINE('',#3051,#3052);
#3051 = CARTESIAN_POINT('',(71.50430962,6.12781858));
#3052 = VECTOR('',#3053,1.);
#3053 = DIRECTION('',(-0.923879741566,0.382682927661));
#3054 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3055 = ADVANCED_FACE('',(#3056),#697,.F.);
#3056 = FACE_BOUND('',#3057,.F.);
#3057 = EDGE_LOOP('',(#3058,#3059,#3080,#3081));
#3058 = ORIENTED_EDGE('',*,*,#3012,.T.);
#3059 = ORIENTED_EDGE('',*,*,#3060,.T.);
#3060 = EDGE_CURVE('',#2990,#677,#3061,.T.);
#3061 = SURFACE_CURVE('',#3062,(#3066,#3073),.PCURVE_S1.);
#3062 = LINE('',#3063,#3064);
#3063 = CARTESIAN_POINT('',(71.26105636,14.22856184,1.64592));
#3064 = VECTOR('',#3065,1.);
#3065 = DIRECTION('',(-0.991444672552,0.130527626456,0.E+000));
#3066 = PCURVE('',#697,#3067);
#3067 = DEFINITIONAL_REPRESENTATION('',(#3068),#3072);
#3068 = LINE('',#3069,#3070);
#3069 = CARTESIAN_POINT('',(0.E+000,-1.64592));
#3070 = VECTOR('',#3071,1.);
#3071 = DIRECTION('',(1.,0.E+000));
#3072 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3073 = PCURVE('',#83,#3074);
#3074 = DEFINITIONAL_REPRESENTATION('',(#3075),#3079);
#3075 = LINE('',#3076,#3077);
#3076 = CARTESIAN_POINT('',(71.26104874,6.22858038));
#3077 = VECTOR('',#3078,1.);
#3078 = DIRECTION('',(-0.991444672552,0.130527626456));
#3079 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3080 = ORIENTED_EDGE('',*,*,#674,.F.);
#3081 = ORIENTED_EDGE('',*,*,#3082,.F.);
#3082 = EDGE_CURVE('',#3013,#675,#3083,.T.);
#3083 = SURFACE_CURVE('',#3084,(#3088,#3095),.PCURVE_S1.);
#3084 = LINE('',#3085,#3086);
#3085 = CARTESIAN_POINT('',(71.26105636,14.22856184,0.E+000));
#3086 = VECTOR('',#3087,1.);
#3087 = DIRECTION('',(-0.991444672552,0.130527626456,0.E+000));
#3088 = PCURVE('',#697,#3089);
#3089 = DEFINITIONAL_REPRESENTATION('',(#3090),#3094);
#3090 = LINE('',#3091,#3092);
#3091 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3092 = VECTOR('',#3093,1.);
#3093 = DIRECTION('',(1.,0.E+000));
#3094 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3095 = PCURVE('',#137,#3096);
#3096 = DEFINITIONAL_REPRESENTATION('',(#3097),#3101);
#3097 = LINE('',#3098,#3099);
#3098 = CARTESIAN_POINT('',(71.26104874,6.22858038));
#3099 = VECTOR('',#3100,1.);
#3100 = DIRECTION('',(-0.991444672552,0.130527626456));
#3101 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3102 = ADVANCED_FACE('',(#3103),#3117,.T.);
#3103 = FACE_BOUND('',#3104,.F.);
#3104 = EDGE_LOOP('',(#3105,#3135,#3157,#3158));
#3105 = ORIENTED_EDGE('',*,*,#3106,.T.);
#3106 = EDGE_CURVE('',#3107,#3109,#3111,.T.);
#3107 = VERTEX_POINT('',#3108);
#3108 = CARTESIAN_POINT('',(31.39999816,33.49999904,0.E+000));
#3109 = VERTEX_POINT('',#3110);
#3110 = CARTESIAN_POINT('',(31.39999816,33.49999904,1.64592));
#3111 = SEAM_CURVE('',#3112,(#3116,#3128),.PCURVE_S1.);
#3112 = LINE('',#3113,#3114);
#3113 = CARTESIAN_POINT('',(31.39999816,33.49999904,0.E+000));
#3114 = VECTOR('',#3115,1.);
#3115 = DIRECTION('',(0.E+000,0.E+000,1.));
#3116 = PCURVE('',#3117,#3122);
#3117 = CYLINDRICAL_SURFACE('',#3118,1.89999874);
#3118 = AXIS2_PLACEMENT_3D('',#3119,#3120,#3121);
#3119 = CARTESIAN_POINT('',(29.49999942,33.49999904,0.E+000));
#3120 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3121 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3122 = DEFINITIONAL_REPRESENTATION('',(#3123),#3127);
#3123 = LINE('',#3124,#3125);
#3124 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3125 = VECTOR('',#3126,1.);
#3126 = DIRECTION('',(-0.E+000,-1.));
#3127 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3128 = PCURVE('',#3117,#3129);
#3129 = DEFINITIONAL_REPRESENTATION('',(#3130),#3134);
#3130 = LINE('',#3131,#3132);
#3131 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3132 = VECTOR('',#3133,1.);
#3133 = DIRECTION('',(-0.E+000,-1.));
#3134 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3135 = ORIENTED_EDGE('',*,*,#3136,.T.);
#3136 = EDGE_CURVE('',#3109,#3109,#3137,.T.);
#3137 = SURFACE_CURVE('',#3138,(#3143,#3150),.PCURVE_S1.);
#3138 = CIRCLE('',#3139,1.89999874);
#3139 = AXIS2_PLACEMENT_3D('',#3140,#3141,#3142);
#3140 = CARTESIAN_POINT('',(29.49999942,33.49999904,1.64592));
#3141 = DIRECTION('',(0.E+000,0.E+000,1.));
#3142 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3143 = PCURVE('',#3117,#3144);
#3144 = DEFINITIONAL_REPRESENTATION('',(#3145),#3149);
#3145 = LINE('',#3146,#3147);
#3146 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3147 = VECTOR('',#3148,1.);
#3148 = DIRECTION('',(-1.,0.E+000));
#3149 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3150 = PCURVE('',#83,#3151);
#3151 = DEFINITIONAL_REPRESENTATION('',(#3152),#3156);
#3152 = CIRCLE('',#3153,1.89999874);
#3153 = AXIS2_PLACEMENT_2D('',#3154,#3155);
#3154 = CARTESIAN_POINT('',(29.4999918,25.50001758));
#3155 = DIRECTION('',(1.,0.E+000));
#3156 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3157 = ORIENTED_EDGE('',*,*,#3106,.F.);
#3158 = ORIENTED_EDGE('',*,*,#3159,.F.);
#3159 = EDGE_CURVE('',#3107,#3107,#3160,.T.);
#3160 = SURFACE_CURVE('',#3161,(#3166,#3173),.PCURVE_S1.);
#3161 = CIRCLE('',#3162,1.89999874);
#3162 = AXIS2_PLACEMENT_3D('',#3163,#3164,#3165);
#3163 = CARTESIAN_POINT('',(29.49999942,33.49999904,0.E+000));
#3164 = DIRECTION('',(0.E+000,0.E+000,1.));
#3165 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3166 = PCURVE('',#3117,#3167);
#3167 = DEFINITIONAL_REPRESENTATION('',(#3168),#3172);
#3168 = LINE('',#3169,#3170);
#3169 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3170 = VECTOR('',#3171,1.);
#3171 = DIRECTION('',(-1.,0.E+000));
#3172 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3173 = PCURVE('',#137,#3174);
#3174 = DEFINITIONAL_REPRESENTATION('',(#3175),#3179);
#3175 = CIRCLE('',#3176,1.89999874);
#3176 = AXIS2_PLACEMENT_2D('',#3177,#3178);
#3177 = CARTESIAN_POINT('',(29.4999918,25.50001758));
#3178 = DIRECTION('',(1.,0.E+000));
#3179 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3180 = ADVANCED_FACE('',(#3181),#3195,.T.);
#3181 = FACE_BOUND('',#3182,.F.);
#3182 = EDGE_LOOP('',(#3183,#3213,#3235,#3236));
#3183 = ORIENTED_EDGE('',*,*,#3184,.T.);
#3184 = EDGE_CURVE('',#3185,#3187,#3189,.T.);
#3185 = VERTEX_POINT('',#3186);
#3186 = CARTESIAN_POINT('',(38.8999984,33.49999904,0.E+000));
#3187 = VERTEX_POINT('',#3188);
#3188 = CARTESIAN_POINT('',(38.8999984,33.49999904,1.64592));
#3189 = SEAM_CURVE('',#3190,(#3194,#3206),.PCURVE_S1.);
#3190 = LINE('',#3191,#3192);
#3191 = CARTESIAN_POINT('',(38.8999984,33.49999904,0.E+000));
#3192 = VECTOR('',#3193,1.);
#3193 = DIRECTION('',(0.E+000,0.E+000,1.));
#3194 = PCURVE('',#3195,#3200);
#3195 = CYLINDRICAL_SURFACE('',#3196,1.89999874);
#3196 = AXIS2_PLACEMENT_3D('',#3197,#3198,#3199);
#3197 = CARTESIAN_POINT('',(36.99999966,33.49999904,0.E+000));
#3198 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3199 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3200 = DEFINITIONAL_REPRESENTATION('',(#3201),#3205);
#3201 = LINE('',#3202,#3203);
#3202 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3203 = VECTOR('',#3204,1.);
#3204 = DIRECTION('',(-0.E+000,-1.));
#3205 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3206 = PCURVE('',#3195,#3207);
#3207 = DEFINITIONAL_REPRESENTATION('',(#3208),#3212);
#3208 = LINE('',#3209,#3210);
#3209 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3210 = VECTOR('',#3211,1.);
#3211 = DIRECTION('',(-0.E+000,-1.));
#3212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3213 = ORIENTED_EDGE('',*,*,#3214,.T.);
#3214 = EDGE_CURVE('',#3187,#3187,#3215,.T.);
#3215 = SURFACE_CURVE('',#3216,(#3221,#3228),.PCURVE_S1.);
#3216 = CIRCLE('',#3217,1.89999874);
#3217 = AXIS2_PLACEMENT_3D('',#3218,#3219,#3220);
#3218 = CARTESIAN_POINT('',(36.99999966,33.49999904,1.64592));
#3219 = DIRECTION('',(0.E+000,0.E+000,1.));
#3220 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3221 = PCURVE('',#3195,#3222);
#3222 = DEFINITIONAL_REPRESENTATION('',(#3223),#3227);
#3223 = LINE('',#3224,#3225);
#3224 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3225 = VECTOR('',#3226,1.);
#3226 = DIRECTION('',(-1.,0.E+000));
#3227 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3228 = PCURVE('',#83,#3229);
#3229 = DEFINITIONAL_REPRESENTATION('',(#3230),#3234);
#3230 = CIRCLE('',#3231,1.89999874);
#3231 = AXIS2_PLACEMENT_2D('',#3232,#3233);
#3232 = CARTESIAN_POINT('',(36.99999204,25.50001758));
#3233 = DIRECTION('',(1.,0.E+000));
#3234 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3235 = ORIENTED_EDGE('',*,*,#3184,.F.);
#3236 = ORIENTED_EDGE('',*,*,#3237,.F.);
#3237 = EDGE_CURVE('',#3185,#3185,#3238,.T.);
#3238 = SURFACE_CURVE('',#3239,(#3244,#3251),.PCURVE_S1.);
#3239 = CIRCLE('',#3240,1.89999874);
#3240 = AXIS2_PLACEMENT_3D('',#3241,#3242,#3243);
#3241 = CARTESIAN_POINT('',(36.99999966,33.49999904,0.E+000));
#3242 = DIRECTION('',(0.E+000,0.E+000,1.));
#3243 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3244 = PCURVE('',#3195,#3245);
#3245 = DEFINITIONAL_REPRESENTATION('',(#3246),#3250);
#3246 = LINE('',#3247,#3248);
#3247 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3248 = VECTOR('',#3249,1.);
#3249 = DIRECTION('',(-1.,0.E+000));
#3250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3251 = PCURVE('',#137,#3252);
#3252 = DEFINITIONAL_REPRESENTATION('',(#3253),#3257);
#3253 = CIRCLE('',#3254,1.89999874);
#3254 = AXIS2_PLACEMENT_2D('',#3255,#3256);
#3255 = CARTESIAN_POINT('',(36.99999204,25.50001758));
#3256 = DIRECTION('',(1.,0.E+000));
#3257 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3258 = ADVANCED_FACE('',(#3259),#3273,.T.);
#3259 = FACE_BOUND('',#3260,.F.);
#3260 = EDGE_LOOP('',(#3261,#3291,#3313,#3314));
#3261 = ORIENTED_EDGE('',*,*,#3262,.T.);
#3262 = EDGE_CURVE('',#3263,#3265,#3267,.T.);
#3263 = VERTEX_POINT('',#3264);
#3264 = CARTESIAN_POINT('',(6.1000005,80.00000002,0.E+000));
#3265 = VERTEX_POINT('',#3266);
#3266 = CARTESIAN_POINT('',(6.1000005,80.00000002,1.64592));
#3267 = SEAM_CURVE('',#3268,(#3272,#3284),.PCURVE_S1.);
#3268 = LINE('',#3269,#3270);
#3269 = CARTESIAN_POINT('',(6.1000005,80.00000002,0.E+000));
#3270 = VECTOR('',#3271,1.);
#3271 = DIRECTION('',(0.E+000,0.E+000,1.));
#3272 = PCURVE('',#3273,#3278);
#3273 = CYLINDRICAL_SURFACE('',#3274,1.59999934);
#3274 = AXIS2_PLACEMENT_3D('',#3275,#3276,#3277);
#3275 = CARTESIAN_POINT('',(4.50000116,80.00000002,0.E+000));
#3276 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3277 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3278 = DEFINITIONAL_REPRESENTATION('',(#3279),#3283);
#3279 = LINE('',#3280,#3281);
#3280 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3281 = VECTOR('',#3282,1.);
#3282 = DIRECTION('',(-0.E+000,-1.));
#3283 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3284 = PCURVE('',#3273,#3285);
#3285 = DEFINITIONAL_REPRESENTATION('',(#3286),#3290);
#3286 = LINE('',#3287,#3288);
#3287 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3288 = VECTOR('',#3289,1.);
#3289 = DIRECTION('',(-0.E+000,-1.));
#3290 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3291 = ORIENTED_EDGE('',*,*,#3292,.T.);
#3292 = EDGE_CURVE('',#3265,#3265,#3293,.T.);
#3293 = SURFACE_CURVE('',#3294,(#3299,#3306),.PCURVE_S1.);
#3294 = CIRCLE('',#3295,1.59999934);
#3295 = AXIS2_PLACEMENT_3D('',#3296,#3297,#3298);
#3296 = CARTESIAN_POINT('',(4.50000116,80.00000002,1.64592));
#3297 = DIRECTION('',(0.E+000,0.E+000,1.));
#3298 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3299 = PCURVE('',#3273,#3300);
#3300 = DEFINITIONAL_REPRESENTATION('',(#3301),#3305);
#3301 = LINE('',#3302,#3303);
#3302 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3303 = VECTOR('',#3304,1.);
#3304 = DIRECTION('',(-1.,0.E+000));
#3305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3306 = PCURVE('',#83,#3307);
#3307 = DEFINITIONAL_REPRESENTATION('',(#3308),#3312);
#3308 = CIRCLE('',#3309,1.59999934);
#3309 = AXIS2_PLACEMENT_2D('',#3310,#3311);
#3310 = CARTESIAN_POINT('',(4.49999354,72.00001856));
#3311 = DIRECTION('',(1.,0.E+000));
#3312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3313 = ORIENTED_EDGE('',*,*,#3262,.F.);
#3314 = ORIENTED_EDGE('',*,*,#3315,.F.);
#3315 = EDGE_CURVE('',#3263,#3263,#3316,.T.);
#3316 = SURFACE_CURVE('',#3317,(#3322,#3329),.PCURVE_S1.);
#3317 = CIRCLE('',#3318,1.59999934);
#3318 = AXIS2_PLACEMENT_3D('',#3319,#3320,#3321);
#3319 = CARTESIAN_POINT('',(4.50000116,80.00000002,0.E+000));
#3320 = DIRECTION('',(0.E+000,0.E+000,1.));
#3321 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3322 = PCURVE('',#3273,#3323);
#3323 = DEFINITIONAL_REPRESENTATION('',(#3324),#3328);
#3324 = LINE('',#3325,#3326);
#3325 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3326 = VECTOR('',#3327,1.);
#3327 = DIRECTION('',(-1.,0.E+000));
#3328 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3329 = PCURVE('',#137,#3330);
#3330 = DEFINITIONAL_REPRESENTATION('',(#3331),#3335);
#3331 = CIRCLE('',#3332,1.59999934);
#3332 = AXIS2_PLACEMENT_2D('',#3333,#3334);
#3333 = CARTESIAN_POINT('',(4.49999354,72.00001856));
#3334 = DIRECTION('',(1.,0.E+000));
#3335 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3336 = ADVANCED_FACE('',(#3337),#3351,.T.);
#3337 = FACE_BOUND('',#3338,.F.);
#3338 = EDGE_LOOP('',(#3339,#3369,#3391,#3392));
#3339 = ORIENTED_EDGE('',*,*,#3340,.T.);
#3340 = EDGE_CURVE('',#3341,#3343,#3345,.T.);
#3341 = VERTEX_POINT('',#3342);
#3342 = CARTESIAN_POINT('',(6.1000005,115.00000114,0.E+000));
#3343 = VERTEX_POINT('',#3344);
#3344 = CARTESIAN_POINT('',(6.1000005,115.00000114,1.64592));
#3345 = SEAM_CURVE('',#3346,(#3350,#3362),.PCURVE_S1.);
#3346 = LINE('',#3347,#3348);
#3347 = CARTESIAN_POINT('',(6.1000005,115.00000114,0.E+000));
#3348 = VECTOR('',#3349,1.);
#3349 = DIRECTION('',(0.E+000,0.E+000,1.));
#3350 = PCURVE('',#3351,#3356);
#3351 = CYLINDRICAL_SURFACE('',#3352,1.59999934);
#3352 = AXIS2_PLACEMENT_3D('',#3353,#3354,#3355);
#3353 = CARTESIAN_POINT('',(4.50000116,115.00000114,0.E+000));
#3354 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3355 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3356 = DEFINITIONAL_REPRESENTATION('',(#3357),#3361);
#3357 = LINE('',#3358,#3359);
#3358 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3359 = VECTOR('',#3360,1.);
#3360 = DIRECTION('',(-0.E+000,-1.));
#3361 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3362 = PCURVE('',#3351,#3363);
#3363 = DEFINITIONAL_REPRESENTATION('',(#3364),#3368);
#3364 = LINE('',#3365,#3366);
#3365 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3366 = VECTOR('',#3367,1.);
#3367 = DIRECTION('',(-0.E+000,-1.));
#3368 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3369 = ORIENTED_EDGE('',*,*,#3370,.T.);
#3370 = EDGE_CURVE('',#3343,#3343,#3371,.T.);
#3371 = SURFACE_CURVE('',#3372,(#3377,#3384),.PCURVE_S1.);
#3372 = CIRCLE('',#3373,1.59999934);
#3373 = AXIS2_PLACEMENT_3D('',#3374,#3375,#3376);
#3374 = CARTESIAN_POINT('',(4.50000116,115.00000114,1.64592));
#3375 = DIRECTION('',(0.E+000,0.E+000,1.));
#3376 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3377 = PCURVE('',#3351,#3378);
#3378 = DEFINITIONAL_REPRESENTATION('',(#3379),#3383);
#3379 = LINE('',#3380,#3381);
#3380 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3381 = VECTOR('',#3382,1.);
#3382 = DIRECTION('',(-1.,0.E+000));
#3383 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3384 = PCURVE('',#83,#3385);
#3385 = DEFINITIONAL_REPRESENTATION('',(#3386),#3390);
#3386 = CIRCLE('',#3387,1.59999934);
#3387 = AXIS2_PLACEMENT_2D('',#3388,#3389);
#3388 = CARTESIAN_POINT('',(4.49999354,107.00001968));
#3389 = DIRECTION('',(1.,0.E+000));
#3390 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3391 = ORIENTED_EDGE('',*,*,#3340,.F.);
#3392 = ORIENTED_EDGE('',*,*,#3393,.F.);
#3393 = EDGE_CURVE('',#3341,#3341,#3394,.T.);
#3394 = SURFACE_CURVE('',#3395,(#3400,#3407),.PCURVE_S1.);
#3395 = CIRCLE('',#3396,1.59999934);
#3396 = AXIS2_PLACEMENT_3D('',#3397,#3398,#3399);
#3397 = CARTESIAN_POINT('',(4.50000116,115.00000114,0.E+000));
#3398 = DIRECTION('',(0.E+000,0.E+000,1.));
#3399 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3400 = PCURVE('',#3351,#3401);
#3401 = DEFINITIONAL_REPRESENTATION('',(#3402),#3406);
#3402 = LINE('',#3403,#3404);
#3403 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3404 = VECTOR('',#3405,1.);
#3405 = DIRECTION('',(-1.,0.E+000));
#3406 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3407 = PCURVE('',#137,#3408);
#3408 = DEFINITIONAL_REPRESENTATION('',(#3409),#3413);
#3409 = CIRCLE('',#3410,1.59999934);
#3410 = AXIS2_PLACEMENT_2D('',#3411,#3412);
#3411 = CARTESIAN_POINT('',(4.49999354,107.00001968));
#3412 = DIRECTION('',(1.,0.E+000));
#3413 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3414 = ADVANCED_FACE('',(#3415),#3429,.T.);
#3415 = FACE_BOUND('',#3416,.F.);
#3416 = EDGE_LOOP('',(#3417,#3447,#3469,#3470));
#3417 = ORIENTED_EDGE('',*,*,#3418,.T.);
#3418 = EDGE_CURVE('',#3419,#3421,#3423,.T.);
#3419 = VERTEX_POINT('',#3420);
#3420 = CARTESIAN_POINT('',(23.90000046,33.49999904,0.E+000));
#3421 = VERTEX_POINT('',#3422);
#3422 = CARTESIAN_POINT('',(23.90000046,33.49999904,1.64592));
#3423 = SEAM_CURVE('',#3424,(#3428,#3440),.PCURVE_S1.);
#3424 = LINE('',#3425,#3426);
#3425 = CARTESIAN_POINT('',(23.90000046,33.49999904,0.E+000));
#3426 = VECTOR('',#3427,1.);
#3427 = DIRECTION('',(0.E+000,0.E+000,1.));
#3428 = PCURVE('',#3429,#3434);
#3429 = CYLINDRICAL_SURFACE('',#3430,1.89999874);
#3430 = AXIS2_PLACEMENT_3D('',#3431,#3432,#3433);
#3431 = CARTESIAN_POINT('',(22.00000172,33.49999904,0.E+000));
#3432 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3433 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3434 = DEFINITIONAL_REPRESENTATION('',(#3435),#3439);
#3435 = LINE('',#3436,#3437);
#3436 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3437 = VECTOR('',#3438,1.);
#3438 = DIRECTION('',(-0.E+000,-1.));
#3439 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3440 = PCURVE('',#3429,#3441);
#3441 = DEFINITIONAL_REPRESENTATION('',(#3442),#3446);
#3442 = LINE('',#3443,#3444);
#3443 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3444 = VECTOR('',#3445,1.);
#3445 = DIRECTION('',(-0.E+000,-1.));
#3446 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3447 = ORIENTED_EDGE('',*,*,#3448,.T.);
#3448 = EDGE_CURVE('',#3421,#3421,#3449,.T.);
#3449 = SURFACE_CURVE('',#3450,(#3455,#3462),.PCURVE_S1.);
#3450 = CIRCLE('',#3451,1.89999874);
#3451 = AXIS2_PLACEMENT_3D('',#3452,#3453,#3454);
#3452 = CARTESIAN_POINT('',(22.00000172,33.49999904,1.64592));
#3453 = DIRECTION('',(0.E+000,0.E+000,1.));
#3454 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3455 = PCURVE('',#3429,#3456);
#3456 = DEFINITIONAL_REPRESENTATION('',(#3457),#3461);
#3457 = LINE('',#3458,#3459);
#3458 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3459 = VECTOR('',#3460,1.);
#3460 = DIRECTION('',(-1.,0.E+000));
#3461 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3462 = PCURVE('',#83,#3463);
#3463 = DEFINITIONAL_REPRESENTATION('',(#3464),#3468);
#3464 = CIRCLE('',#3465,1.89999874);
#3465 = AXIS2_PLACEMENT_2D('',#3466,#3467);
#3466 = CARTESIAN_POINT('',(21.9999941,25.50001758));
#3467 = DIRECTION('',(1.,0.E+000));
#3468 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3469 = ORIENTED_EDGE('',*,*,#3418,.F.);
#3470 = ORIENTED_EDGE('',*,*,#3471,.F.);
#3471 = EDGE_CURVE('',#3419,#3419,#3472,.T.);
#3472 = SURFACE_CURVE('',#3473,(#3478,#3485),.PCURVE_S1.);
#3473 = CIRCLE('',#3474,1.89999874);
#3474 = AXIS2_PLACEMENT_3D('',#3475,#3476,#3477);
#3475 = CARTESIAN_POINT('',(22.00000172,33.49999904,0.E+000));
#3476 = DIRECTION('',(0.E+000,0.E+000,1.));
#3477 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3478 = PCURVE('',#3429,#3479);
#3479 = DEFINITIONAL_REPRESENTATION('',(#3480),#3484);
#3480 = LINE('',#3481,#3482);
#3481 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3482 = VECTOR('',#3483,1.);
#3483 = DIRECTION('',(-1.,0.E+000));
#3484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3485 = PCURVE('',#137,#3486);
#3486 = DEFINITIONAL_REPRESENTATION('',(#3487),#3491);
#3487 = CIRCLE('',#3488,1.89999874);
#3488 = AXIS2_PLACEMENT_2D('',#3489,#3490);
#3489 = CARTESIAN_POINT('',(21.9999941,25.50001758));
#3490 = DIRECTION('',(1.,0.E+000));
#3491 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3492 = ADVANCED_FACE('',(#3493),#3507,.T.);
#3493 = FACE_BOUND('',#3494,.F.);
#3494 = EDGE_LOOP('',(#3495,#3525,#3547,#3548));
#3495 = ORIENTED_EDGE('',*,*,#3496,.T.);
#3496 = EDGE_CURVE('',#3497,#3499,#3501,.T.);
#3497 = VERTEX_POINT('',#3498);
#3498 = CARTESIAN_POINT('',(6.1000005,42.99999782,0.E+000));
#3499 = VERTEX_POINT('',#3500);
#3500 = CARTESIAN_POINT('',(6.1000005,42.99999782,1.64592));
#3501 = SEAM_CURVE('',#3502,(#3506,#3518),.PCURVE_S1.);
#3502 = LINE('',#3503,#3504);
#3503 = CARTESIAN_POINT('',(6.1000005,42.99999782,0.E+000));
#3504 = VECTOR('',#3505,1.);
#3505 = DIRECTION('',(0.E+000,0.E+000,1.));
#3506 = PCURVE('',#3507,#3512);
#3507 = CYLINDRICAL_SURFACE('',#3508,1.59999934);
#3508 = AXIS2_PLACEMENT_3D('',#3509,#3510,#3511);
#3509 = CARTESIAN_POINT('',(4.50000116,42.99999782,0.E+000));
#3510 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3511 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3512 = DEFINITIONAL_REPRESENTATION('',(#3513),#3517);
#3513 = LINE('',#3514,#3515);
#3514 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3515 = VECTOR('',#3516,1.);
#3516 = DIRECTION('',(-0.E+000,-1.));
#3517 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3518 = PCURVE('',#3507,#3519);
#3519 = DEFINITIONAL_REPRESENTATION('',(#3520),#3524);
#3520 = LINE('',#3521,#3522);
#3521 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3522 = VECTOR('',#3523,1.);
#3523 = DIRECTION('',(-0.E+000,-1.));
#3524 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3525 = ORIENTED_EDGE('',*,*,#3526,.T.);
#3526 = EDGE_CURVE('',#3499,#3499,#3527,.T.);
#3527 = SURFACE_CURVE('',#3528,(#3533,#3540),.PCURVE_S1.);
#3528 = CIRCLE('',#3529,1.59999934);
#3529 = AXIS2_PLACEMENT_3D('',#3530,#3531,#3532);
#3530 = CARTESIAN_POINT('',(4.50000116,42.99999782,1.64592));
#3531 = DIRECTION('',(0.E+000,0.E+000,1.));
#3532 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3533 = PCURVE('',#3507,#3534);
#3534 = DEFINITIONAL_REPRESENTATION('',(#3535),#3539);
#3535 = LINE('',#3536,#3537);
#3536 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3537 = VECTOR('',#3538,1.);
#3538 = DIRECTION('',(-1.,0.E+000));
#3539 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3540 = PCURVE('',#83,#3541);
#3541 = DEFINITIONAL_REPRESENTATION('',(#3542),#3546);
#3542 = CIRCLE('',#3543,1.59999934);
#3543 = AXIS2_PLACEMENT_2D('',#3544,#3545);
#3544 = CARTESIAN_POINT('',(4.49999354,35.00001636));
#3545 = DIRECTION('',(1.,0.E+000));
#3546 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3547 = ORIENTED_EDGE('',*,*,#3496,.F.);
#3548 = ORIENTED_EDGE('',*,*,#3549,.F.);
#3549 = EDGE_CURVE('',#3497,#3497,#3550,.T.);
#3550 = SURFACE_CURVE('',#3551,(#3556,#3563),.PCURVE_S1.);
#3551 = CIRCLE('',#3552,1.59999934);
#3552 = AXIS2_PLACEMENT_3D('',#3553,#3554,#3555);
#3553 = CARTESIAN_POINT('',(4.50000116,42.99999782,0.E+000));
#3554 = DIRECTION('',(0.E+000,0.E+000,1.));
#3555 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3556 = PCURVE('',#3507,#3557);
#3557 = DEFINITIONAL_REPRESENTATION('',(#3558),#3562);
#3558 = LINE('',#3559,#3560);
#3559 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3560 = VECTOR('',#3561,1.);
#3561 = DIRECTION('',(-1.,0.E+000));
#3562 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3563 = PCURVE('',#137,#3564);
#3564 = DEFINITIONAL_REPRESENTATION('',(#3565),#3569);
#3565 = CIRCLE('',#3566,1.59999934);
#3566 = AXIS2_PLACEMENT_2D('',#3567,#3568);
#3567 = CARTESIAN_POINT('',(4.49999354,35.00001636));
#3568 = DIRECTION('',(1.,0.E+000));
#3569 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3570 = ADVANCED_FACE('',(#3571),#3585,.T.);
#3571 = FACE_BOUND('',#3572,.F.);
#3572 = EDGE_LOOP('',(#3573,#3603,#3625,#3626));
#3573 = ORIENTED_EDGE('',*,*,#3574,.T.);
#3574 = EDGE_CURVE('',#3575,#3577,#3579,.T.);
#3575 = VERTEX_POINT('',#3576);
#3576 = CARTESIAN_POINT('',(12.6500001,42.49999882,0.E+000));
#3577 = VERTEX_POINT('',#3578);
#3578 = CARTESIAN_POINT('',(12.6500001,42.49999882,1.64592));
#3579 = SEAM_CURVE('',#3580,(#3584,#3596),.PCURVE_S1.);
#3580 = LINE('',#3581,#3582);
#3581 = CARTESIAN_POINT('',(12.6500001,42.49999882,0.E+000));
#3582 = VECTOR('',#3583,1.);
#3583 = DIRECTION('',(0.E+000,0.E+000,1.));
#3584 = PCURVE('',#3585,#3590);
#3585 = CYLINDRICAL_SURFACE('',#3586,1.89999874);
#3586 = AXIS2_PLACEMENT_3D('',#3587,#3588,#3589);
#3587 = CARTESIAN_POINT('',(10.75000136,42.49999882,0.E+000));
#3588 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3589 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3590 = DEFINITIONAL_REPRESENTATION('',(#3591),#3595);
#3591 = LINE('',#3592,#3593);
#3592 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3593 = VECTOR('',#3594,1.);
#3594 = DIRECTION('',(-0.E+000,-1.));
#3595 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3596 = PCURVE('',#3585,#3597);
#3597 = DEFINITIONAL_REPRESENTATION('',(#3598),#3602);
#3598 = LINE('',#3599,#3600);
#3599 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3600 = VECTOR('',#3601,1.);
#3601 = DIRECTION('',(-0.E+000,-1.));
#3602 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3603 = ORIENTED_EDGE('',*,*,#3604,.T.);
#3604 = EDGE_CURVE('',#3577,#3577,#3605,.T.);
#3605 = SURFACE_CURVE('',#3606,(#3611,#3618),.PCURVE_S1.);
#3606 = CIRCLE('',#3607,1.89999874);
#3607 = AXIS2_PLACEMENT_3D('',#3608,#3609,#3610);
#3608 = CARTESIAN_POINT('',(10.75000136,42.49999882,1.64592));
#3609 = DIRECTION('',(0.E+000,0.E+000,1.));
#3610 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3611 = PCURVE('',#3585,#3612);
#3612 = DEFINITIONAL_REPRESENTATION('',(#3613),#3617);
#3613 = LINE('',#3614,#3615);
#3614 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3615 = VECTOR('',#3616,1.);
#3616 = DIRECTION('',(-1.,0.E+000));
#3617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3618 = PCURVE('',#83,#3619);
#3619 = DEFINITIONAL_REPRESENTATION('',(#3620),#3624);
#3620 = CIRCLE('',#3621,1.89999874);
#3621 = AXIS2_PLACEMENT_2D('',#3622,#3623);
#3622 = CARTESIAN_POINT('',(10.74999374,34.50001736));
#3623 = DIRECTION('',(1.,0.E+000));
#3624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3625 = ORIENTED_EDGE('',*,*,#3574,.F.);
#3626 = ORIENTED_EDGE('',*,*,#3627,.F.);
#3627 = EDGE_CURVE('',#3575,#3575,#3628,.T.);
#3628 = SURFACE_CURVE('',#3629,(#3634,#3641),.PCURVE_S1.);
#3629 = CIRCLE('',#3630,1.89999874);
#3630 = AXIS2_PLACEMENT_3D('',#3631,#3632,#3633);
#3631 = CARTESIAN_POINT('',(10.75000136,42.49999882,0.E+000));
#3632 = DIRECTION('',(0.E+000,0.E+000,1.));
#3633 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3634 = PCURVE('',#3585,#3635);
#3635 = DEFINITIONAL_REPRESENTATION('',(#3636),#3640);
#3636 = LINE('',#3637,#3638);
#3637 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3638 = VECTOR('',#3639,1.);
#3639 = DIRECTION('',(-1.,0.E+000));
#3640 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3641 = PCURVE('',#137,#3642);
#3642 = DEFINITIONAL_REPRESENTATION('',(#3643),#3647);
#3643 = CIRCLE('',#3644,1.89999874);
#3644 = AXIS2_PLACEMENT_2D('',#3645,#3646);
#3645 = CARTESIAN_POINT('',(10.74999374,34.50001736));
#3646 = DIRECTION('',(1.,0.E+000));
#3647 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3648 = ADVANCED_FACE('',(#3649),#3663,.T.);
#3649 = FACE_BOUND('',#3650,.F.);
#3650 = EDGE_LOOP('',(#3651,#3681,#3703,#3704));
#3651 = ORIENTED_EDGE('',*,*,#3652,.T.);
#3652 = EDGE_CURVE('',#3653,#3655,#3657,.T.);
#3653 = VERTEX_POINT('',#3654);
#3654 = CARTESIAN_POINT('',(61.39999912,33.49999904,0.E+000));
#3655 = VERTEX_POINT('',#3656);
#3656 = CARTESIAN_POINT('',(61.39999912,33.49999904,1.64592));
#3657 = SEAM_CURVE('',#3658,(#3662,#3674),.PCURVE_S1.);
#3658 = LINE('',#3659,#3660);
#3659 = CARTESIAN_POINT('',(61.39999912,33.49999904,0.E+000));
#3660 = VECTOR('',#3661,1.);
#3661 = DIRECTION('',(0.E+000,0.E+000,1.));
#3662 = PCURVE('',#3663,#3668);
#3663 = CYLINDRICAL_SURFACE('',#3664,1.89999874);
#3664 = AXIS2_PLACEMENT_3D('',#3665,#3666,#3667);
#3665 = CARTESIAN_POINT('',(59.50000038,33.49999904,0.E+000));
#3666 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3667 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3668 = DEFINITIONAL_REPRESENTATION('',(#3669),#3673);
#3669 = LINE('',#3670,#3671);
#3670 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3671 = VECTOR('',#3672,1.);
#3672 = DIRECTION('',(-0.E+000,-1.));
#3673 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3674 = PCURVE('',#3663,#3675);
#3675 = DEFINITIONAL_REPRESENTATION('',(#3676),#3680);
#3676 = LINE('',#3677,#3678);
#3677 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3678 = VECTOR('',#3679,1.);
#3679 = DIRECTION('',(-0.E+000,-1.));
#3680 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3681 = ORIENTED_EDGE('',*,*,#3682,.T.);
#3682 = EDGE_CURVE('',#3655,#3655,#3683,.T.);
#3683 = SURFACE_CURVE('',#3684,(#3689,#3696),.PCURVE_S1.);
#3684 = CIRCLE('',#3685,1.89999874);
#3685 = AXIS2_PLACEMENT_3D('',#3686,#3687,#3688);
#3686 = CARTESIAN_POINT('',(59.50000038,33.49999904,1.64592));
#3687 = DIRECTION('',(0.E+000,0.E+000,1.));
#3688 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3689 = PCURVE('',#3663,#3690);
#3690 = DEFINITIONAL_REPRESENTATION('',(#3691),#3695);
#3691 = LINE('',#3692,#3693);
#3692 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3693 = VECTOR('',#3694,1.);
#3694 = DIRECTION('',(-1.,0.E+000));
#3695 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3696 = PCURVE('',#83,#3697);
#3697 = DEFINITIONAL_REPRESENTATION('',(#3698),#3702);
#3698 = CIRCLE('',#3699,1.89999874);
#3699 = AXIS2_PLACEMENT_2D('',#3700,#3701);
#3700 = CARTESIAN_POINT('',(59.49999276,25.50001758));
#3701 = DIRECTION('',(1.,0.E+000));
#3702 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3703 = ORIENTED_EDGE('',*,*,#3652,.F.);
#3704 = ORIENTED_EDGE('',*,*,#3705,.F.);
#3705 = EDGE_CURVE('',#3653,#3653,#3706,.T.);
#3706 = SURFACE_CURVE('',#3707,(#3712,#3719),.PCURVE_S1.);
#3707 = CIRCLE('',#3708,1.89999874);
#3708 = AXIS2_PLACEMENT_3D('',#3709,#3710,#3711);
#3709 = CARTESIAN_POINT('',(59.50000038,33.49999904,0.E+000));
#3710 = DIRECTION('',(0.E+000,0.E+000,1.));
#3711 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3712 = PCURVE('',#3663,#3713);
#3713 = DEFINITIONAL_REPRESENTATION('',(#3714),#3718);
#3714 = LINE('',#3715,#3716);
#3715 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3716 = VECTOR('',#3717,1.);
#3717 = DIRECTION('',(-1.,0.E+000));
#3718 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3719 = PCURVE('',#137,#3720);
#3720 = DEFINITIONAL_REPRESENTATION('',(#3721),#3725);
#3721 = CIRCLE('',#3722,1.89999874);
#3722 = AXIS2_PLACEMENT_2D('',#3723,#3724);
#3723 = CARTESIAN_POINT('',(59.49999276,25.50001758));
#3724 = DIRECTION('',(1.,0.E+000));
#3725 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3726 = ADVANCED_FACE('',(#3727),#3741,.T.);
#3727 = FACE_BOUND('',#3728,.F.);
#3728 = EDGE_LOOP('',(#3729,#3759,#3781,#3782));
#3729 = ORIENTED_EDGE('',*,*,#3730,.T.);
#3730 = EDGE_CURVE('',#3731,#3733,#3735,.T.);
#3731 = VERTEX_POINT('',#3732);
#3732 = CARTESIAN_POINT('',(53.89999888,33.49999904,0.E+000));
#3733 = VERTEX_POINT('',#3734);
#3734 = CARTESIAN_POINT('',(53.89999888,33.49999904,1.64592));
#3735 = SEAM_CURVE('',#3736,(#3740,#3752),.PCURVE_S1.);
#3736 = LINE('',#3737,#3738);
#3737 = CARTESIAN_POINT('',(53.89999888,33.49999904,0.E+000));
#3738 = VECTOR('',#3739,1.);
#3739 = DIRECTION('',(0.E+000,0.E+000,1.));
#3740 = PCURVE('',#3741,#3746);
#3741 = CYLINDRICAL_SURFACE('',#3742,1.89999874);
#3742 = AXIS2_PLACEMENT_3D('',#3743,#3744,#3745);
#3743 = CARTESIAN_POINT('',(52.00000014,33.49999904,0.E+000));
#3744 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3745 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3746 = DEFINITIONAL_REPRESENTATION('',(#3747),#3751);
#3747 = LINE('',#3748,#3749);
#3748 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3749 = VECTOR('',#3750,1.);
#3750 = DIRECTION('',(-0.E+000,-1.));
#3751 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3752 = PCURVE('',#3741,#3753);
#3753 = DEFINITIONAL_REPRESENTATION('',(#3754),#3758);
#3754 = LINE('',#3755,#3756);
#3755 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3756 = VECTOR('',#3757,1.);
#3757 = DIRECTION('',(-0.E+000,-1.));
#3758 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3759 = ORIENTED_EDGE('',*,*,#3760,.T.);
#3760 = EDGE_CURVE('',#3733,#3733,#3761,.T.);
#3761 = SURFACE_CURVE('',#3762,(#3767,#3774),.PCURVE_S1.);
#3762 = CIRCLE('',#3763,1.89999874);
#3763 = AXIS2_PLACEMENT_3D('',#3764,#3765,#3766);
#3764 = CARTESIAN_POINT('',(52.00000014,33.49999904,1.64592));
#3765 = DIRECTION('',(0.E+000,0.E+000,1.));
#3766 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3767 = PCURVE('',#3741,#3768);
#3768 = DEFINITIONAL_REPRESENTATION('',(#3769),#3773);
#3769 = LINE('',#3770,#3771);
#3770 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3771 = VECTOR('',#3772,1.);
#3772 = DIRECTION('',(-1.,0.E+000));
#3773 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3774 = PCURVE('',#83,#3775);
#3775 = DEFINITIONAL_REPRESENTATION('',(#3776),#3780);
#3776 = CIRCLE('',#3777,1.89999874);
#3777 = AXIS2_PLACEMENT_2D('',#3778,#3779);
#3778 = CARTESIAN_POINT('',(51.99999252,25.50001758));
#3779 = DIRECTION('',(1.,0.E+000));
#3780 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3781 = ORIENTED_EDGE('',*,*,#3730,.F.);
#3782 = ORIENTED_EDGE('',*,*,#3783,.F.);
#3783 = EDGE_CURVE('',#3731,#3731,#3784,.T.);
#3784 = SURFACE_CURVE('',#3785,(#3790,#3797),.PCURVE_S1.);
#3785 = CIRCLE('',#3786,1.89999874);
#3786 = AXIS2_PLACEMENT_3D('',#3787,#3788,#3789);
#3787 = CARTESIAN_POINT('',(52.00000014,33.49999904,0.E+000));
#3788 = DIRECTION('',(0.E+000,0.E+000,1.));
#3789 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3790 = PCURVE('',#3741,#3791);
#3791 = DEFINITIONAL_REPRESENTATION('',(#3792),#3796);
#3792 = LINE('',#3793,#3794);
#3793 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3794 = VECTOR('',#3795,1.);
#3795 = DIRECTION('',(-1.,0.E+000));
#3796 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3797 = PCURVE('',#137,#3798);
#3798 = DEFINITIONAL_REPRESENTATION('',(#3799),#3803);
#3799 = CIRCLE('',#3800,1.89999874);
#3800 = AXIS2_PLACEMENT_2D('',#3801,#3802);
#3801 = CARTESIAN_POINT('',(51.99999252,25.50001758));
#3802 = DIRECTION('',(1.,0.E+000));
#3803 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3804 = ADVANCED_FACE('',(#3805),#3819,.T.);
#3805 = FACE_BOUND('',#3806,.F.);
#3806 = EDGE_LOOP('',(#3807,#3837,#3859,#3860));
#3807 = ORIENTED_EDGE('',*,*,#3808,.T.);
#3808 = EDGE_CURVE('',#3809,#3811,#3813,.T.);
#3809 = VERTEX_POINT('',#3810);
#3810 = CARTESIAN_POINT('',(86.10000052,7.99999924,0.E+000));
#3811 = VERTEX_POINT('',#3812);
#3812 = CARTESIAN_POINT('',(86.10000052,7.99999924,1.64592));
#3813 = SEAM_CURVE('',#3814,(#3818,#3830),.PCURVE_S1.);
#3814 = LINE('',#3815,#3816);
#3815 = CARTESIAN_POINT('',(86.10000052,7.99999924,0.E+000));
#3816 = VECTOR('',#3817,1.);
#3817 = DIRECTION('',(0.E+000,0.E+000,1.));
#3818 = PCURVE('',#3819,#3824);
#3819 = CYLINDRICAL_SURFACE('',#3820,1.59999934);
#3820 = AXIS2_PLACEMENT_3D('',#3821,#3822,#3823);
#3821 = CARTESIAN_POINT('',(84.50000118,7.99999924,0.E+000));
#3822 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3823 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3824 = DEFINITIONAL_REPRESENTATION('',(#3825),#3829);
#3825 = LINE('',#3826,#3827);
#3826 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3827 = VECTOR('',#3828,1.);
#3828 = DIRECTION('',(-0.E+000,-1.));
#3829 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3830 = PCURVE('',#3819,#3831);
#3831 = DEFINITIONAL_REPRESENTATION('',(#3832),#3836);
#3832 = LINE('',#3833,#3834);
#3833 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3834 = VECTOR('',#3835,1.);
#3835 = DIRECTION('',(-0.E+000,-1.));
#3836 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3837 = ORIENTED_EDGE('',*,*,#3838,.T.);
#3838 = EDGE_CURVE('',#3811,#3811,#3839,.T.);
#3839 = SURFACE_CURVE('',#3840,(#3845,#3852),.PCURVE_S1.);
#3840 = CIRCLE('',#3841,1.59999934);
#3841 = AXIS2_PLACEMENT_3D('',#3842,#3843,#3844);
#3842 = CARTESIAN_POINT('',(84.50000118,7.99999924,1.64592));
#3843 = DIRECTION('',(0.E+000,0.E+000,1.));
#3844 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3845 = PCURVE('',#3819,#3846);
#3846 = DEFINITIONAL_REPRESENTATION('',(#3847),#3851);
#3847 = LINE('',#3848,#3849);
#3848 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3849 = VECTOR('',#3850,1.);
#3850 = DIRECTION('',(-1.,0.E+000));
#3851 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3852 = PCURVE('',#83,#3853);
#3853 = DEFINITIONAL_REPRESENTATION('',(#3854),#3858);
#3854 = CIRCLE('',#3855,1.59999934);
#3855 = AXIS2_PLACEMENT_2D('',#3856,#3857);
#3856 = CARTESIAN_POINT('',(84.49999356,1.777999999764E-005));
#3857 = DIRECTION('',(1.,0.E+000));
#3858 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3859 = ORIENTED_EDGE('',*,*,#3808,.F.);
#3860 = ORIENTED_EDGE('',*,*,#3861,.F.);
#3861 = EDGE_CURVE('',#3809,#3809,#3862,.T.);
#3862 = SURFACE_CURVE('',#3863,(#3868,#3875),.PCURVE_S1.);
#3863 = CIRCLE('',#3864,1.59999934);
#3864 = AXIS2_PLACEMENT_3D('',#3865,#3866,#3867);
#3865 = CARTESIAN_POINT('',(84.50000118,7.99999924,0.E+000));
#3866 = DIRECTION('',(0.E+000,0.E+000,1.));
#3867 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3868 = PCURVE('',#3819,#3869);
#3869 = DEFINITIONAL_REPRESENTATION('',(#3870),#3874);
#3870 = LINE('',#3871,#3872);
#3871 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3872 = VECTOR('',#3873,1.);
#3873 = DIRECTION('',(-1.,0.E+000));
#3874 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3875 = PCURVE('',#137,#3876);
#3876 = DEFINITIONAL_REPRESENTATION('',(#3877),#3881);
#3877 = CIRCLE('',#3878,1.59999934);
#3878 = AXIS2_PLACEMENT_2D('',#3879,#3880);
#3879 = CARTESIAN_POINT('',(84.49999356,1.777999999764E-005));
#3880 = DIRECTION('',(1.,0.E+000));
#3881 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3882 = ADVANCED_FACE('',(#3883),#3897,.T.);
#3883 = FACE_BOUND('',#3884,.F.);
#3884 = EDGE_LOOP('',(#3885,#3915,#3937,#3938));
#3885 = ORIENTED_EDGE('',*,*,#3886,.T.);
#3886 = EDGE_CURVE('',#3887,#3889,#3891,.T.);
#3887 = VERTEX_POINT('',#3888);
#3888 = CARTESIAN_POINT('',(68.89999936,33.49999904,0.E+000));
#3889 = VERTEX_POINT('',#3890);
#3890 = CARTESIAN_POINT('',(68.89999936,33.49999904,1.64592));
#3891 = SEAM_CURVE('',#3892,(#3896,#3908),.PCURVE_S1.);
#3892 = LINE('',#3893,#3894);
#3893 = CARTESIAN_POINT('',(68.89999936,33.49999904,0.E+000));
#3894 = VECTOR('',#3895,1.);
#3895 = DIRECTION('',(0.E+000,0.E+000,1.));
#3896 = PCURVE('',#3897,#3902);
#3897 = CYLINDRICAL_SURFACE('',#3898,1.89999874);
#3898 = AXIS2_PLACEMENT_3D('',#3899,#3900,#3901);
#3899 = CARTESIAN_POINT('',(67.00000062,33.49999904,0.E+000));
#3900 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3901 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3902 = DEFINITIONAL_REPRESENTATION('',(#3903),#3907);
#3903 = LINE('',#3904,#3905);
#3904 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3905 = VECTOR('',#3906,1.);
#3906 = DIRECTION('',(-0.E+000,-1.));
#3907 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3908 = PCURVE('',#3897,#3909);
#3909 = DEFINITIONAL_REPRESENTATION('',(#3910),#3914);
#3910 = LINE('',#3911,#3912);
#3911 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3912 = VECTOR('',#3913,1.);
#3913 = DIRECTION('',(-0.E+000,-1.));
#3914 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3915 = ORIENTED_EDGE('',*,*,#3916,.T.);
#3916 = EDGE_CURVE('',#3889,#3889,#3917,.T.);
#3917 = SURFACE_CURVE('',#3918,(#3923,#3930),.PCURVE_S1.);
#3918 = CIRCLE('',#3919,1.89999874);
#3919 = AXIS2_PLACEMENT_3D('',#3920,#3921,#3922);
#3920 = CARTESIAN_POINT('',(67.00000062,33.49999904,1.64592));
#3921 = DIRECTION('',(0.E+000,0.E+000,1.));
#3922 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3923 = PCURVE('',#3897,#3924);
#3924 = DEFINITIONAL_REPRESENTATION('',(#3925),#3929);
#3925 = LINE('',#3926,#3927);
#3926 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#3927 = VECTOR('',#3928,1.);
#3928 = DIRECTION('',(-1.,0.E+000));
#3929 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3930 = PCURVE('',#83,#3931);
#3931 = DEFINITIONAL_REPRESENTATION('',(#3932),#3936);
#3932 = CIRCLE('',#3933,1.89999874);
#3933 = AXIS2_PLACEMENT_2D('',#3934,#3935);
#3934 = CARTESIAN_POINT('',(66.999993,25.50001758));
#3935 = DIRECTION('',(1.,0.E+000));
#3936 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3937 = ORIENTED_EDGE('',*,*,#3886,.F.);
#3938 = ORIENTED_EDGE('',*,*,#3939,.F.);
#3939 = EDGE_CURVE('',#3887,#3887,#3940,.T.);
#3940 = SURFACE_CURVE('',#3941,(#3946,#3953),.PCURVE_S1.);
#3941 = CIRCLE('',#3942,1.89999874);
#3942 = AXIS2_PLACEMENT_3D('',#3943,#3944,#3945);
#3943 = CARTESIAN_POINT('',(67.00000062,33.49999904,0.E+000));
#3944 = DIRECTION('',(0.E+000,0.E+000,1.));
#3945 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3946 = PCURVE('',#3897,#3947);
#3947 = DEFINITIONAL_REPRESENTATION('',(#3948),#3952);
#3948 = LINE('',#3949,#3950);
#3949 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3950 = VECTOR('',#3951,1.);
#3951 = DIRECTION('',(-1.,0.E+000));
#3952 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3953 = PCURVE('',#137,#3954);
#3954 = DEFINITIONAL_REPRESENTATION('',(#3955),#3959);
#3955 = CIRCLE('',#3956,1.89999874);
#3956 = AXIS2_PLACEMENT_2D('',#3957,#3958);
#3957 = CARTESIAN_POINT('',(66.999993,25.50001758));
#3958 = DIRECTION('',(1.,0.E+000));
#3959 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3960 = ADVANCED_FACE('',(#3961),#3975,.T.);
#3961 = FACE_BOUND('',#3962,.F.);
#3962 = EDGE_LOOP('',(#3963,#3993,#4015,#4016));
#3963 = ORIENTED_EDGE('',*,*,#3964,.T.);
#3964 = EDGE_CURVE('',#3965,#3967,#3969,.T.);
#3965 = VERTEX_POINT('',#3966);
#3966 = CARTESIAN_POINT('',(80.14999972,33.49999904,0.E+000));
#3967 = VERTEX_POINT('',#3968);
#3968 = CARTESIAN_POINT('',(80.14999972,33.49999904,1.64592));
#3969 = SEAM_CURVE('',#3970,(#3974,#3986),.PCURVE_S1.);
#3970 = LINE('',#3971,#3972);
#3971 = CARTESIAN_POINT('',(80.14999972,33.49999904,0.E+000));
#3972 = VECTOR('',#3973,1.);
#3973 = DIRECTION('',(0.E+000,0.E+000,1.));
#3974 = PCURVE('',#3975,#3980);
#3975 = CYLINDRICAL_SURFACE('',#3976,1.89999874);
#3976 = AXIS2_PLACEMENT_3D('',#3977,#3978,#3979);
#3977 = CARTESIAN_POINT('',(78.25000098,33.49999904,0.E+000));
#3978 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#3979 = DIRECTION('',(1.,0.E+000,-0.E+000));
#3980 = DEFINITIONAL_REPRESENTATION('',(#3981),#3985);
#3981 = LINE('',#3982,#3983);
#3982 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#3983 = VECTOR('',#3984,1.);
#3984 = DIRECTION('',(-0.E+000,-1.));
#3985 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3986 = PCURVE('',#3975,#3987);
#3987 = DEFINITIONAL_REPRESENTATION('',(#3988),#3992);
#3988 = LINE('',#3989,#3990);
#3989 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#3990 = VECTOR('',#3991,1.);
#3991 = DIRECTION('',(-0.E+000,-1.));
#3992 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3993 = ORIENTED_EDGE('',*,*,#3994,.T.);
#3994 = EDGE_CURVE('',#3967,#3967,#3995,.T.);
#3995 = SURFACE_CURVE('',#3996,(#4001,#4008),.PCURVE_S1.);
#3996 = CIRCLE('',#3997,1.89999874);
#3997 = AXIS2_PLACEMENT_3D('',#3998,#3999,#4000);
#3998 = CARTESIAN_POINT('',(78.25000098,33.49999904,1.64592));
#3999 = DIRECTION('',(0.E+000,0.E+000,1.));
#4000 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4001 = PCURVE('',#3975,#4002);
#4002 = DEFINITIONAL_REPRESENTATION('',(#4003),#4007);
#4003 = LINE('',#4004,#4005);
#4004 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#4005 = VECTOR('',#4006,1.);
#4006 = DIRECTION('',(-1.,0.E+000));
#4007 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4008 = PCURVE('',#83,#4009);
#4009 = DEFINITIONAL_REPRESENTATION('',(#4010),#4014);
#4010 = CIRCLE('',#4011,1.89999874);
#4011 = AXIS2_PLACEMENT_2D('',#4012,#4013);
#4012 = CARTESIAN_POINT('',(78.24999336,25.50001758));
#4013 = DIRECTION('',(1.,0.E+000));
#4014 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4015 = ORIENTED_EDGE('',*,*,#3964,.F.);
#4016 = ORIENTED_EDGE('',*,*,#4017,.F.);
#4017 = EDGE_CURVE('',#3965,#3965,#4018,.T.);
#4018 = SURFACE_CURVE('',#4019,(#4024,#4031),.PCURVE_S1.);
#4019 = CIRCLE('',#4020,1.89999874);
#4020 = AXIS2_PLACEMENT_3D('',#4021,#4022,#4023);
#4021 = CARTESIAN_POINT('',(78.25000098,33.49999904,0.E+000));
#4022 = DIRECTION('',(0.E+000,0.E+000,1.));
#4023 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4024 = PCURVE('',#3975,#4025);
#4025 = DEFINITIONAL_REPRESENTATION('',(#4026),#4030);
#4026 = LINE('',#4027,#4028);
#4027 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4028 = VECTOR('',#4029,1.);
#4029 = DIRECTION('',(-1.,0.E+000));
#4030 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4031 = PCURVE('',#137,#4032);
#4032 = DEFINITIONAL_REPRESENTATION('',(#4033),#4037);
#4033 = CIRCLE('',#4034,1.89999874);
#4034 = AXIS2_PLACEMENT_2D('',#4035,#4036);
#4035 = CARTESIAN_POINT('',(78.24999336,25.50001758));
#4036 = DIRECTION('',(1.,0.E+000));
#4037 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4038 = ADVANCED_FACE('',(#4039),#4053,.T.);
#4039 = FACE_BOUND('',#4040,.F.);
#4040 = EDGE_LOOP('',(#4041,#4071,#4093,#4094));
#4041 = ORIENTED_EDGE('',*,*,#4042,.T.);
#4042 = EDGE_CURVE('',#4043,#4045,#4047,.T.);
#4043 = VERTEX_POINT('',#4044);
#4044 = CARTESIAN_POINT('',(46.39999864,33.49999904,0.E+000));
#4045 = VERTEX_POINT('',#4046);
#4046 = CARTESIAN_POINT('',(46.39999864,33.49999904,1.64592));
#4047 = SEAM_CURVE('',#4048,(#4052,#4064),.PCURVE_S1.);
#4048 = LINE('',#4049,#4050);
#4049 = CARTESIAN_POINT('',(46.39999864,33.49999904,0.E+000));
#4050 = VECTOR('',#4051,1.);
#4051 = DIRECTION('',(0.E+000,0.E+000,1.));
#4052 = PCURVE('',#4053,#4058);
#4053 = CYLINDRICAL_SURFACE('',#4054,1.89999874);
#4054 = AXIS2_PLACEMENT_3D('',#4055,#4056,#4057);
#4055 = CARTESIAN_POINT('',(44.4999999,33.49999904,0.E+000));
#4056 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#4057 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4058 = DEFINITIONAL_REPRESENTATION('',(#4059),#4063);
#4059 = LINE('',#4060,#4061);
#4060 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4061 = VECTOR('',#4062,1.);
#4062 = DIRECTION('',(-0.E+000,-1.));
#4063 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4064 = PCURVE('',#4053,#4065);
#4065 = DEFINITIONAL_REPRESENTATION('',(#4066),#4070);
#4066 = LINE('',#4067,#4068);
#4067 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#4068 = VECTOR('',#4069,1.);
#4069 = DIRECTION('',(-0.E+000,-1.));
#4070 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4071 = ORIENTED_EDGE('',*,*,#4072,.T.);
#4072 = EDGE_CURVE('',#4045,#4045,#4073,.T.);
#4073 = SURFACE_CURVE('',#4074,(#4079,#4086),.PCURVE_S1.);
#4074 = CIRCLE('',#4075,1.89999874);
#4075 = AXIS2_PLACEMENT_3D('',#4076,#4077,#4078);
#4076 = CARTESIAN_POINT('',(44.4999999,33.49999904,1.64592));
#4077 = DIRECTION('',(0.E+000,0.E+000,1.));
#4078 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4079 = PCURVE('',#4053,#4080);
#4080 = DEFINITIONAL_REPRESENTATION('',(#4081),#4085);
#4081 = LINE('',#4082,#4083);
#4082 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#4083 = VECTOR('',#4084,1.);
#4084 = DIRECTION('',(-1.,0.E+000));
#4085 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4086 = PCURVE('',#83,#4087);
#4087 = DEFINITIONAL_REPRESENTATION('',(#4088),#4092);
#4088 = CIRCLE('',#4089,1.89999874);
#4089 = AXIS2_PLACEMENT_2D('',#4090,#4091);
#4090 = CARTESIAN_POINT('',(44.49999228,25.50001758));
#4091 = DIRECTION('',(1.,0.E+000));
#4092 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4093 = ORIENTED_EDGE('',*,*,#4042,.F.);
#4094 = ORIENTED_EDGE('',*,*,#4095,.F.);
#4095 = EDGE_CURVE('',#4043,#4043,#4096,.T.);
#4096 = SURFACE_CURVE('',#4097,(#4102,#4109),.PCURVE_S1.);
#4097 = CIRCLE('',#4098,1.89999874);
#4098 = AXIS2_PLACEMENT_3D('',#4099,#4100,#4101);
#4099 = CARTESIAN_POINT('',(44.4999999,33.49999904,0.E+000));
#4100 = DIRECTION('',(0.E+000,0.E+000,1.));
#4101 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4102 = PCURVE('',#4053,#4103);
#4103 = DEFINITIONAL_REPRESENTATION('',(#4104),#4108);
#4104 = LINE('',#4105,#4106);
#4105 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4106 = VECTOR('',#4107,1.);
#4107 = DIRECTION('',(-1.,0.E+000));
#4108 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4109 = PCURVE('',#137,#4110);
#4110 = DEFINITIONAL_REPRESENTATION('',(#4111),#4115);
#4111 = CIRCLE('',#4112,1.89999874);
#4112 = AXIS2_PLACEMENT_2D('',#4113,#4114);
#4113 = CARTESIAN_POINT('',(44.49999228,25.50001758));
#4114 = DIRECTION('',(1.,0.E+000));
#4115 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4116 = ADVANCED_FACE('',(#4117),#4131,.T.);
#4117 = FACE_BOUND('',#4118,.F.);
#4118 = EDGE_LOOP('',(#4119,#4149,#4171,#4172));
#4119 = ORIENTED_EDGE('',*,*,#4120,.T.);
#4120 = EDGE_CURVE('',#4121,#4123,#4125,.T.);
#4121 = VERTEX_POINT('',#4122);
#4122 = CARTESIAN_POINT('',(51.0999994,83.50000064,0.E+000));
#4123 = VERTEX_POINT('',#4124);
#4124 = CARTESIAN_POINT('',(51.0999994,83.50000064,1.64592));
#4125 = SEAM_CURVE('',#4126,(#4130,#4142),.PCURVE_S1.);
#4126 = LINE('',#4127,#4128);
#4127 = CARTESIAN_POINT('',(51.0999994,83.50000064,0.E+000));
#4128 = VECTOR('',#4129,1.);
#4129 = DIRECTION('',(0.E+000,0.E+000,1.));
#4130 = PCURVE('',#4131,#4136);
#4131 = CYLINDRICAL_SURFACE('',#4132,6.5999995);
#4132 = AXIS2_PLACEMENT_3D('',#4133,#4134,#4135);
#4133 = CARTESIAN_POINT('',(44.4999999,83.50000064,0.E+000));
#4134 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#4135 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4136 = DEFINITIONAL_REPRESENTATION('',(#4137),#4141);
#4137 = LINE('',#4138,#4139);
#4138 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4139 = VECTOR('',#4140,1.);
#4140 = DIRECTION('',(-0.E+000,-1.));
#4141 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4142 = PCURVE('',#4131,#4143);
#4143 = DEFINITIONAL_REPRESENTATION('',(#4144),#4148);
#4144 = LINE('',#4145,#4146);
#4145 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#4146 = VECTOR('',#4147,1.);
#4147 = DIRECTION('',(-0.E+000,-1.));
#4148 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4149 = ORIENTED_EDGE('',*,*,#4150,.T.);
#4150 = EDGE_CURVE('',#4123,#4123,#4151,.T.);
#4151 = SURFACE_CURVE('',#4152,(#4157,#4164),.PCURVE_S1.);
#4152 = CIRCLE('',#4153,6.5999995);
#4153 = AXIS2_PLACEMENT_3D('',#4154,#4155,#4156);
#4154 = CARTESIAN_POINT('',(44.4999999,83.50000064,1.64592));
#4155 = DIRECTION('',(0.E+000,0.E+000,1.));
#4156 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4157 = PCURVE('',#4131,#4158);
#4158 = DEFINITIONAL_REPRESENTATION('',(#4159),#4163);
#4159 = LINE('',#4160,#4161);
#4160 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#4161 = VECTOR('',#4162,1.);
#4162 = DIRECTION('',(-1.,0.E+000));
#4163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4164 = PCURVE('',#83,#4165);
#4165 = DEFINITIONAL_REPRESENTATION('',(#4166),#4170);
#4166 = CIRCLE('',#4167,6.5999995);
#4167 = AXIS2_PLACEMENT_2D('',#4168,#4169);
#4168 = CARTESIAN_POINT('',(44.49999228,75.50001918));
#4169 = DIRECTION('',(1.,0.E+000));
#4170 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4171 = ORIENTED_EDGE('',*,*,#4120,.F.);
#4172 = ORIENTED_EDGE('',*,*,#4173,.F.);
#4173 = EDGE_CURVE('',#4121,#4121,#4174,.T.);
#4174 = SURFACE_CURVE('',#4175,(#4180,#4187),.PCURVE_S1.);
#4175 = CIRCLE('',#4176,6.5999995);
#4176 = AXIS2_PLACEMENT_3D('',#4177,#4178,#4179);
#4177 = CARTESIAN_POINT('',(44.4999999,83.50000064,0.E+000));
#4178 = DIRECTION('',(0.E+000,0.E+000,1.));
#4179 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4180 = PCURVE('',#4131,#4181);
#4181 = DEFINITIONAL_REPRESENTATION('',(#4182),#4186);
#4182 = LINE('',#4183,#4184);
#4183 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4184 = VECTOR('',#4185,1.);
#4185 = DIRECTION('',(-1.,0.E+000));
#4186 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4187 = PCURVE('',#137,#4188);
#4188 = DEFINITIONAL_REPRESENTATION('',(#4189),#4193);
#4189 = CIRCLE('',#4190,6.5999995);
#4190 = AXIS2_PLACEMENT_2D('',#4191,#4192);
#4191 = CARTESIAN_POINT('',(44.49999228,75.50001918));
#4192 = DIRECTION('',(1.,0.E+000));
#4193 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4194 = ADVANCED_FACE('',(#4195),#4209,.T.);
#4195 = FACE_BOUND('',#4196,.F.);
#4196 = EDGE_LOOP('',(#4197,#4227,#4249,#4250));
#4197 = ORIENTED_EDGE('',*,*,#4198,.T.);
#4198 = EDGE_CURVE('',#4199,#4201,#4203,.T.);
#4199 = VERTEX_POINT('',#4200);
#4200 = CARTESIAN_POINT('',(80.14999972,42.49999882,0.E+000));
#4201 = VERTEX_POINT('',#4202);
#4202 = CARTESIAN_POINT('',(80.14999972,42.49999882,1.64592));
#4203 = SEAM_CURVE('',#4204,(#4208,#4220),.PCURVE_S1.);
#4204 = LINE('',#4205,#4206);
#4205 = CARTESIAN_POINT('',(80.14999972,42.49999882,0.E+000));
#4206 = VECTOR('',#4207,1.);
#4207 = DIRECTION('',(0.E+000,0.E+000,1.));
#4208 = PCURVE('',#4209,#4214);
#4209 = CYLINDRICAL_SURFACE('',#4210,1.89999874);
#4210 = AXIS2_PLACEMENT_3D('',#4211,#4212,#4213);
#4211 = CARTESIAN_POINT('',(78.25000098,42.49999882,0.E+000));
#4212 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#4213 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4214 = DEFINITIONAL_REPRESENTATION('',(#4215),#4219);
#4215 = LINE('',#4216,#4217);
#4216 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4217 = VECTOR('',#4218,1.);
#4218 = DIRECTION('',(-0.E+000,-1.));
#4219 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4220 = PCURVE('',#4209,#4221);
#4221 = DEFINITIONAL_REPRESENTATION('',(#4222),#4226);
#4222 = LINE('',#4223,#4224);
#4223 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#4224 = VECTOR('',#4225,1.);
#4225 = DIRECTION('',(-0.E+000,-1.));
#4226 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4227 = ORIENTED_EDGE('',*,*,#4228,.T.);
#4228 = EDGE_CURVE('',#4201,#4201,#4229,.T.);
#4229 = SURFACE_CURVE('',#4230,(#4235,#4242),.PCURVE_S1.);
#4230 = CIRCLE('',#4231,1.89999874);
#4231 = AXIS2_PLACEMENT_3D('',#4232,#4233,#4234);
#4232 = CARTESIAN_POINT('',(78.25000098,42.49999882,1.64592));
#4233 = DIRECTION('',(0.E+000,0.E+000,1.));
#4234 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4235 = PCURVE('',#4209,#4236);
#4236 = DEFINITIONAL_REPRESENTATION('',(#4237),#4241);
#4237 = LINE('',#4238,#4239);
#4238 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#4239 = VECTOR('',#4240,1.);
#4240 = DIRECTION('',(-1.,0.E+000));
#4241 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4242 = PCURVE('',#83,#4243);
#4243 = DEFINITIONAL_REPRESENTATION('',(#4244),#4248);
#4244 = CIRCLE('',#4245,1.89999874);
#4245 = AXIS2_PLACEMENT_2D('',#4246,#4247);
#4246 = CARTESIAN_POINT('',(78.24999336,34.50001736));
#4247 = DIRECTION('',(1.,0.E+000));
#4248 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4249 = ORIENTED_EDGE('',*,*,#4198,.F.);
#4250 = ORIENTED_EDGE('',*,*,#4251,.F.);
#4251 = EDGE_CURVE('',#4199,#4199,#4252,.T.);
#4252 = SURFACE_CURVE('',#4253,(#4258,#4265),.PCURVE_S1.);
#4253 = CIRCLE('',#4254,1.89999874);
#4254 = AXIS2_PLACEMENT_3D('',#4255,#4256,#4257);
#4255 = CARTESIAN_POINT('',(78.25000098,42.49999882,0.E+000));
#4256 = DIRECTION('',(0.E+000,0.E+000,1.));
#4257 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4258 = PCURVE('',#4209,#4259);
#4259 = DEFINITIONAL_REPRESENTATION('',(#4260),#4264);
#4260 = LINE('',#4261,#4262);
#4261 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4262 = VECTOR('',#4263,1.);
#4263 = DIRECTION('',(-1.,0.E+000));
#4264 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4265 = PCURVE('',#137,#4266);
#4266 = DEFINITIONAL_REPRESENTATION('',(#4267),#4271);
#4267 = CIRCLE('',#4268,1.89999874);
#4268 = AXIS2_PLACEMENT_2D('',#4269,#4270);
#4269 = CARTESIAN_POINT('',(78.24999336,34.50001736));
#4270 = DIRECTION('',(1.,0.E+000));
#4271 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4272 = ADVANCED_FACE('',(#4273),#4287,.T.);
#4273 = FACE_BOUND('',#4274,.F.);
#4274 = EDGE_LOOP('',(#4275,#4305,#4327,#4328));
#4275 = ORIENTED_EDGE('',*,*,#4276,.T.);
#4276 = EDGE_CURVE('',#4277,#4279,#4281,.T.);
#4277 = VERTEX_POINT('',#4278);
#4278 = CARTESIAN_POINT('',(86.10000052,42.99999782,0.E+000));
#4279 = VERTEX_POINT('',#4280);
#4280 = CARTESIAN_POINT('',(86.10000052,42.99999782,1.64592));
#4281 = SEAM_CURVE('',#4282,(#4286,#4298),.PCURVE_S1.);
#4282 = LINE('',#4283,#4284);
#4283 = CARTESIAN_POINT('',(86.10000052,42.99999782,0.E+000));
#4284 = VECTOR('',#4285,1.);
#4285 = DIRECTION('',(0.E+000,0.E+000,1.));
#4286 = PCURVE('',#4287,#4292);
#4287 = CYLINDRICAL_SURFACE('',#4288,1.59999934);
#4288 = AXIS2_PLACEMENT_3D('',#4289,#4290,#4291);
#4289 = CARTESIAN_POINT('',(84.50000118,42.99999782,0.E+000));
#4290 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#4291 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4292 = DEFINITIONAL_REPRESENTATION('',(#4293),#4297);
#4293 = LINE('',#4294,#4295);
#4294 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4295 = VECTOR('',#4296,1.);
#4296 = DIRECTION('',(-0.E+000,-1.));
#4297 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4298 = PCURVE('',#4287,#4299);
#4299 = DEFINITIONAL_REPRESENTATION('',(#4300),#4304);
#4300 = LINE('',#4301,#4302);
#4301 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#4302 = VECTOR('',#4303,1.);
#4303 = DIRECTION('',(-0.E+000,-1.));
#4304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4305 = ORIENTED_EDGE('',*,*,#4306,.T.);
#4306 = EDGE_CURVE('',#4279,#4279,#4307,.T.);
#4307 = SURFACE_CURVE('',#4308,(#4313,#4320),.PCURVE_S1.);
#4308 = CIRCLE('',#4309,1.59999934);
#4309 = AXIS2_PLACEMENT_3D('',#4310,#4311,#4312);
#4310 = CARTESIAN_POINT('',(84.50000118,42.99999782,1.64592));
#4311 = DIRECTION('',(0.E+000,0.E+000,1.));
#4312 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4313 = PCURVE('',#4287,#4314);
#4314 = DEFINITIONAL_REPRESENTATION('',(#4315),#4319);
#4315 = LINE('',#4316,#4317);
#4316 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#4317 = VECTOR('',#4318,1.);
#4318 = DIRECTION('',(-1.,0.E+000));
#4319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4320 = PCURVE('',#83,#4321);
#4321 = DEFINITIONAL_REPRESENTATION('',(#4322),#4326);
#4322 = CIRCLE('',#4323,1.59999934);
#4323 = AXIS2_PLACEMENT_2D('',#4324,#4325);
#4324 = CARTESIAN_POINT('',(84.49999356,35.00001636));
#4325 = DIRECTION('',(1.,0.E+000));
#4326 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4327 = ORIENTED_EDGE('',*,*,#4276,.F.);
#4328 = ORIENTED_EDGE('',*,*,#4329,.F.);
#4329 = EDGE_CURVE('',#4277,#4277,#4330,.T.);
#4330 = SURFACE_CURVE('',#4331,(#4336,#4343),.PCURVE_S1.);
#4331 = CIRCLE('',#4332,1.59999934);
#4332 = AXIS2_PLACEMENT_3D('',#4333,#4334,#4335);
#4333 = CARTESIAN_POINT('',(84.50000118,42.99999782,0.E+000));
#4334 = DIRECTION('',(0.E+000,0.E+000,1.));
#4335 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4336 = PCURVE('',#4287,#4337);
#4337 = DEFINITIONAL_REPRESENTATION('',(#4338),#4342);
#4338 = LINE('',#4339,#4340);
#4339 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4340 = VECTOR('',#4341,1.);
#4341 = DIRECTION('',(-1.,0.E+000));
#4342 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4343 = PCURVE('',#137,#4344);
#4344 = DEFINITIONAL_REPRESENTATION('',(#4345),#4349);
#4345 = CIRCLE('',#4346,1.59999934);
#4346 = AXIS2_PLACEMENT_2D('',#4347,#4348);
#4347 = CARTESIAN_POINT('',(84.49999356,35.00001636));
#4348 = DIRECTION('',(1.,0.E+000));
#4349 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4350 = ADVANCED_FACE('',(#4351),#4365,.T.);
#4351 = FACE_BOUND('',#4352,.F.);
#4352 = EDGE_LOOP('',(#4353,#4383,#4405,#4406));
#4353 = ORIENTED_EDGE('',*,*,#4354,.T.);
#4354 = EDGE_CURVE('',#4355,#4357,#4359,.T.);
#4355 = VERTEX_POINT('',#4356);
#4356 = CARTESIAN_POINT('',(86.10000052,115.00000114,0.E+000));
#4357 = VERTEX_POINT('',#4358);
#4358 = CARTESIAN_POINT('',(86.10000052,115.00000114,1.64592));
#4359 = SEAM_CURVE('',#4360,(#4364,#4376),.PCURVE_S1.);
#4360 = LINE('',#4361,#4362);
#4361 = CARTESIAN_POINT('',(86.10000052,115.00000114,0.E+000));
#4362 = VECTOR('',#4363,1.);
#4363 = DIRECTION('',(0.E+000,0.E+000,1.));
#4364 = PCURVE('',#4365,#4370);
#4365 = CYLINDRICAL_SURFACE('',#4366,1.59999934);
#4366 = AXIS2_PLACEMENT_3D('',#4367,#4368,#4369);
#4367 = CARTESIAN_POINT('',(84.50000118,115.00000114,0.E+000));
#4368 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#4369 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4370 = DEFINITIONAL_REPRESENTATION('',(#4371),#4375);
#4371 = LINE('',#4372,#4373);
#4372 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4373 = VECTOR('',#4374,1.);
#4374 = DIRECTION('',(-0.E+000,-1.));
#4375 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4376 = PCURVE('',#4365,#4377);
#4377 = DEFINITIONAL_REPRESENTATION('',(#4378),#4382);
#4378 = LINE('',#4379,#4380);
#4379 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#4380 = VECTOR('',#4381,1.);
#4381 = DIRECTION('',(-0.E+000,-1.));
#4382 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4383 = ORIENTED_EDGE('',*,*,#4384,.T.);
#4384 = EDGE_CURVE('',#4357,#4357,#4385,.T.);
#4385 = SURFACE_CURVE('',#4386,(#4391,#4398),.PCURVE_S1.);
#4386 = CIRCLE('',#4387,1.59999934);
#4387 = AXIS2_PLACEMENT_3D('',#4388,#4389,#4390);
#4388 = CARTESIAN_POINT('',(84.50000118,115.00000114,1.64592));
#4389 = DIRECTION('',(0.E+000,0.E+000,1.));
#4390 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4391 = PCURVE('',#4365,#4392);
#4392 = DEFINITIONAL_REPRESENTATION('',(#4393),#4397);
#4393 = LINE('',#4394,#4395);
#4394 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#4395 = VECTOR('',#4396,1.);
#4396 = DIRECTION('',(-1.,0.E+000));
#4397 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4398 = PCURVE('',#83,#4399);
#4399 = DEFINITIONAL_REPRESENTATION('',(#4400),#4404);
#4400 = CIRCLE('',#4401,1.59999934);
#4401 = AXIS2_PLACEMENT_2D('',#4402,#4403);
#4402 = CARTESIAN_POINT('',(84.49999356,107.00001968));
#4403 = DIRECTION('',(1.,0.E+000));
#4404 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4405 = ORIENTED_EDGE('',*,*,#4354,.F.);
#4406 = ORIENTED_EDGE('',*,*,#4407,.F.);
#4407 = EDGE_CURVE('',#4355,#4355,#4408,.T.);
#4408 = SURFACE_CURVE('',#4409,(#4414,#4421),.PCURVE_S1.);
#4409 = CIRCLE('',#4410,1.59999934);
#4410 = AXIS2_PLACEMENT_3D('',#4411,#4412,#4413);
#4411 = CARTESIAN_POINT('',(84.50000118,115.00000114,0.E+000));
#4412 = DIRECTION('',(0.E+000,0.E+000,1.));
#4413 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4414 = PCURVE('',#4365,#4415);
#4415 = DEFINITIONAL_REPRESENTATION('',(#4416),#4420);
#4416 = LINE('',#4417,#4418);
#4417 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4418 = VECTOR('',#4419,1.);
#4419 = DIRECTION('',(-1.,0.E+000));
#4420 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4421 = PCURVE('',#137,#4422);
#4422 = DEFINITIONAL_REPRESENTATION('',(#4423),#4427);
#4423 = CIRCLE('',#4424,1.59999934);
#4424 = AXIS2_PLACEMENT_2D('',#4425,#4426);
#4425 = CARTESIAN_POINT('',(84.49999356,107.00001968));
#4426 = DIRECTION('',(1.,0.E+000));
#4427 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4428 = ADVANCED_FACE('',(#4429),#4443,.T.);
#4429 = FACE_BOUND('',#4430,.F.);
#4430 = EDGE_LOOP('',(#4431,#4461,#4483,#4484));
#4431 = ORIENTED_EDGE('',*,*,#4432,.T.);
#4432 = EDGE_CURVE('',#4433,#4435,#4437,.T.);
#4433 = VERTEX_POINT('',#4434);
#4434 = CARTESIAN_POINT('',(86.10000052,79.99999748,0.E+000));
#4435 = VERTEX_POINT('',#4436);
#4436 = CARTESIAN_POINT('',(86.10000052,79.99999748,1.64592));
#4437 = SEAM_CURVE('',#4438,(#4442,#4454),.PCURVE_S1.);
#4438 = LINE('',#4439,#4440);
#4439 = CARTESIAN_POINT('',(86.10000052,79.99999748,0.E+000));
#4440 = VECTOR('',#4441,1.);
#4441 = DIRECTION('',(0.E+000,0.E+000,1.));
#4442 = PCURVE('',#4443,#4448);
#4443 = CYLINDRICAL_SURFACE('',#4444,1.59999934);
#4444 = AXIS2_PLACEMENT_3D('',#4445,#4446,#4447);
#4445 = CARTESIAN_POINT('',(84.50000118,79.99999748,0.E+000));
#4446 = DIRECTION('',(-0.E+000,-0.E+000,-1.));
#4447 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4448 = DEFINITIONAL_REPRESENTATION('',(#4449),#4453);
#4449 = LINE('',#4450,#4451);
#4450 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4451 = VECTOR('',#4452,1.);
#4452 = DIRECTION('',(-0.E+000,-1.));
#4453 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4454 = PCURVE('',#4443,#4455);
#4455 = DEFINITIONAL_REPRESENTATION('',(#4456),#4460);
#4456 = LINE('',#4457,#4458);
#4457 = CARTESIAN_POINT('',(-6.28318530718,0.E+000));
#4458 = VECTOR('',#4459,1.);
#4459 = DIRECTION('',(-0.E+000,-1.));
#4460 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4461 = ORIENTED_EDGE('',*,*,#4462,.T.);
#4462 = EDGE_CURVE('',#4435,#4435,#4463,.T.);
#4463 = SURFACE_CURVE('',#4464,(#4469,#4476),.PCURVE_S1.);
#4464 = CIRCLE('',#4465,1.59999934);
#4465 = AXIS2_PLACEMENT_3D('',#4466,#4467,#4468);
#4466 = CARTESIAN_POINT('',(84.50000118,79.99999748,1.64592));
#4467 = DIRECTION('',(0.E+000,0.E+000,1.));
#4468 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4469 = PCURVE('',#4443,#4470);
#4470 = DEFINITIONAL_REPRESENTATION('',(#4471),#4475);
#4471 = LINE('',#4472,#4473);
#4472 = CARTESIAN_POINT('',(-0.E+000,-1.64592));
#4473 = VECTOR('',#4474,1.);
#4474 = DIRECTION('',(-1.,0.E+000));
#4475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4476 = PCURVE('',#83,#4477);
#4477 = DEFINITIONAL_REPRESENTATION('',(#4478),#4482);
#4478 = CIRCLE('',#4479,1.59999934);
#4479 = AXIS2_PLACEMENT_2D('',#4480,#4481);
#4480 = CARTESIAN_POINT('',(84.49999356,72.00001602));
#4481 = DIRECTION('',(1.,0.E+000));
#4482 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4483 = ORIENTED_EDGE('',*,*,#4432,.F.);
#4484 = ORIENTED_EDGE('',*,*,#4485,.F.);
#4485 = EDGE_CURVE('',#4433,#4433,#4486,.T.);
#4486 = SURFACE_CURVE('',#4487,(#4492,#4499),.PCURVE_S1.);
#4487 = CIRCLE('',#4488,1.59999934);
#4488 = AXIS2_PLACEMENT_3D('',#4489,#4490,#4491);
#4489 = CARTESIAN_POINT('',(84.50000118,79.99999748,0.E+000));
#4490 = DIRECTION('',(0.E+000,0.E+000,1.));
#4491 = DIRECTION('',(1.,0.E+000,-0.E+000));
#4492 = PCURVE('',#4443,#4493);
#4493 = DEFINITIONAL_REPRESENTATION('',(#4494),#4498);
#4494 = LINE('',#4495,#4496);
#4495 = CARTESIAN_POINT('',(-0.E+000,0.E+000));
#4496 = VECTOR('',#4497,1.);
#4497 = DIRECTION('',(-1.,0.E+000));
#4498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4499 = PCURVE('',#137,#4500);
#4500 = DEFINITIONAL_REPRESENTATION('',(#4501),#4505);
#4501 = CIRCLE('',#4502,1.59999934);
#4502 = AXIS2_PLACEMENT_2D('',#4503,#4504);
#4503 = CARTESIAN_POINT('',(84.49999356,72.00001602));
#4504 = DIRECTION('',(1.,0.E+000));
#4505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4506 = ADVANCED_FACE('',(#4507,#4515,#4518,#4521,#4555,#4558,#4561,
    #4564,#4567,#4570,#4573,#4576,#4579,#4582,#4585,#4588,#4591,#4594,
    #4597,#4600,#4603,#4606),#137,.F.);
#4507 = FACE_BOUND('',#4508,.T.);
#4508 = EDGE_LOOP('',(#4509,#4510,#4511,#4512,#4513,#4514));
#4509 = ORIENTED_EDGE('',*,*,#123,.T.);
#4510 = ORIENTED_EDGE('',*,*,#204,.T.);
#4511 = ORIENTED_EDGE('',*,*,#280,.T.);
#4512 = ORIENTED_EDGE('',*,*,#361,.T.);
#4513 = ORIENTED_EDGE('',*,*,#437,.T.);
#4514 = ORIENTED_EDGE('',*,*,#489,.T.);
#4515 = FACE_BOUND('',#4516,.F.);
#4516 = EDGE_LOOP('',(#4517));
#4517 = ORIENTED_EDGE('',*,*,#571,.T.);
#4518 = FACE_BOUND('',#4519,.F.);
#4519 = EDGE_LOOP('',(#4520));
#4520 = ORIENTED_EDGE('',*,*,#649,.T.);
#4521 = FACE_BOUND('',#4522,.F.);
#4522 = EDGE_LOOP('',(#4523,#4524,#4525,#4526,#4527,#4528,#4529,#4530,
    #4531,#4532,#4533,#4534,#4535,#4536,#4537,#4538,#4539,#4540,#4541,
    #4542,#4543,#4544,#4545,#4546,#4547,#4548,#4549,#4550,#4551,#4552,
    #4553,#4554));
#4523 = ORIENTED_EDGE('',*,*,#760,.T.);
#4524 = ORIENTED_EDGE('',*,*,#836,.T.);
#4525 = ORIENTED_EDGE('',*,*,#912,.T.);
#4526 = ORIENTED_EDGE('',*,*,#988,.T.);
#4527 = ORIENTED_EDGE('',*,*,#1064,.T.);
#4528 = ORIENTED_EDGE('',*,*,#1140,.T.);
#4529 = ORIENTED_EDGE('',*,*,#1216,.T.);
#4530 = ORIENTED_EDGE('',*,*,#1292,.T.);
#4531 = ORIENTED_EDGE('',*,*,#1368,.T.);
#4532 = ORIENTED_EDGE('',*,*,#1444,.T.);
#4533 = ORIENTED_EDGE('',*,*,#1520,.T.);
#4534 = ORIENTED_EDGE('',*,*,#1596,.T.);
#4535 = ORIENTED_EDGE('',*,*,#1672,.T.);
#4536 = ORIENTED_EDGE('',*,*,#1748,.T.);
#4537 = ORIENTED_EDGE('',*,*,#1824,.T.);
#4538 = ORIENTED_EDGE('',*,*,#1900,.T.);
#4539 = ORIENTED_EDGE('',*,*,#1976,.T.);
#4540 = ORIENTED_EDGE('',*,*,#2052,.T.);
#4541 = ORIENTED_EDGE('',*,*,#2128,.T.);
#4542 = ORIENTED_EDGE('',*,*,#2204,.T.);
#4543 = ORIENTED_EDGE('',*,*,#2280,.T.);
#4544 = ORIENTED_EDGE('',*,*,#2356,.T.);
#4545 = ORIENTED_EDGE('',*,*,#2432,.T.);
#4546 = ORIENTED_EDGE('',*,*,#2508,.T.);
#4547 = ORIENTED_EDGE('',*,*,#2584,.T.);
#4548 = ORIENTED_EDGE('',*,*,#2660,.T.);
#4549 = ORIENTED_EDGE('',*,*,#2736,.T.);
#4550 = ORIENTED_EDGE('',*,*,#2812,.T.);
#4551 = ORIENTED_EDGE('',*,*,#2888,.T.);
#4552 = ORIENTED_EDGE('',*,*,#2964,.T.);
#4553 = ORIENTED_EDGE('',*,*,#3035,.T.);
#4554 = ORIENTED_EDGE('',*,*,#3082,.T.);
#4555 = FACE_BOUND('',#4556,.F.);
#4556 = EDGE_LOOP('',(#4557));
#4557 = ORIENTED_EDGE('',*,*,#3159,.T.);
#4558 = FACE_BOUND('',#4559,.F.);
#4559 = EDGE_LOOP('',(#4560));
#4560 = ORIENTED_EDGE('',*,*,#3237,.T.);
#4561 = FACE_BOUND('',#4562,.F.);
#4562 = EDGE_LOOP('',(#4563));
#4563 = ORIENTED_EDGE('',*,*,#3315,.T.);
#4564 = FACE_BOUND('',#4565,.F.);
#4565 = EDGE_LOOP('',(#4566));
#4566 = ORIENTED_EDGE('',*,*,#3393,.T.);
#4567 = FACE_BOUND('',#4568,.F.);
#4568 = EDGE_LOOP('',(#4569));
#4569 = ORIENTED_EDGE('',*,*,#3471,.T.);
#4570 = FACE_BOUND('',#4571,.F.);
#4571 = EDGE_LOOP('',(#4572));
#4572 = ORIENTED_EDGE('',*,*,#3549,.T.);
#4573 = FACE_BOUND('',#4574,.F.);
#4574 = EDGE_LOOP('',(#4575));
#4575 = ORIENTED_EDGE('',*,*,#3627,.T.);
#4576 = FACE_BOUND('',#4577,.F.);
#4577 = EDGE_LOOP('',(#4578));
#4578 = ORIENTED_EDGE('',*,*,#3705,.T.);
#4579 = FACE_BOUND('',#4580,.F.);
#4580 = EDGE_LOOP('',(#4581));
#4581 = ORIENTED_EDGE('',*,*,#3783,.T.);
#4582 = FACE_BOUND('',#4583,.F.);
#4583 = EDGE_LOOP('',(#4584));
#4584 = ORIENTED_EDGE('',*,*,#3861,.T.);
#4585 = FACE_BOUND('',#4586,.F.);
#4586 = EDGE_LOOP('',(#4587));
#4587 = ORIENTED_EDGE('',*,*,#3939,.T.);
#4588 = FACE_BOUND('',#4589,.F.);
#4589 = EDGE_LOOP('',(#4590));
#4590 = ORIENTED_EDGE('',*,*,#4017,.T.);
#4591 = FACE_BOUND('',#4592,.F.);
#4592 = EDGE_LOOP('',(#4593));
#4593 = ORIENTED_EDGE('',*,*,#4095,.T.);
#4594 = FACE_BOUND('',#4595,.F.);
#4595 = EDGE_LOOP('',(#4596));
#4596 = ORIENTED_EDGE('',*,*,#4173,.T.);
#4597 = FACE_BOUND('',#4598,.F.);
#4598 = EDGE_LOOP('',(#4599));
#4599 = ORIENTED_EDGE('',*,*,#4251,.T.);
#4600 = FACE_BOUND('',#4601,.F.);
#4601 = EDGE_LOOP('',(#4602));
#4602 = ORIENTED_EDGE('',*,*,#4329,.T.);
#4603 = FACE_BOUND('',#4604,.F.);
#4604 = EDGE_LOOP('',(#4605));
#4605 = ORIENTED_EDGE('',*,*,#4407,.T.);
#4606 = FACE_BOUND('',#4607,.F.);
#4607 = EDGE_LOOP('',(#4608));
#4608 = ORIENTED_EDGE('',*,*,#4485,.T.);
#4609 = ADVANCED_FACE('',(#4610,#4618,#4621,#4624,#4658,#4661,#4664,
    #4667,#4670,#4673,#4676,#4679,#4682,#4685,#4688,#4691,#4694,#4697,
    #4700,#4703,#4706,#4709),#83,.T.);
#4610 = FACE_BOUND('',#4611,.F.);
#4611 = EDGE_LOOP('',(#4612,#4613,#4614,#4615,#4616,#4617));
#4612 = ORIENTED_EDGE('',*,*,#67,.T.);
#4613 = ORIENTED_EDGE('',*,*,#153,.T.);
#4614 = ORIENTED_EDGE('',*,*,#229,.T.);
#4615 = ORIENTED_EDGE('',*,*,#305,.T.);
#4616 = ORIENTED_EDGE('',*,*,#391,.T.);
#4617 = ORIENTED_EDGE('',*,*,#462,.T.);
#4618 = FACE_BOUND('',#4619,.T.);
#4619 = EDGE_LOOP('',(#4620));
#4620 = ORIENTED_EDGE('',*,*,#548,.T.);
#4621 = FACE_BOUND('',#4622,.T.);
#4622 = EDGE_LOOP('',(#4623));
#4623 = ORIENTED_EDGE('',*,*,#626,.T.);
#4624 = FACE_BOUND('',#4625,.T.);
#4625 = EDGE_LOOP('',(#4626,#4627,#4628,#4629,#4630,#4631,#4632,#4633,
    #4634,#4635,#4636,#4637,#4638,#4639,#4640,#4641,#4642,#4643,#4644,
    #4645,#4646,#4647,#4648,#4649,#4650,#4651,#4652,#4653,#4654,#4655,
    #4656,#4657));
#4626 = ORIENTED_EDGE('',*,*,#709,.T.);
#4627 = ORIENTED_EDGE('',*,*,#785,.T.);
#4628 = ORIENTED_EDGE('',*,*,#861,.T.);
#4629 = ORIENTED_EDGE('',*,*,#937,.T.);
#4630 = ORIENTED_EDGE('',*,*,#1013,.T.);
#4631 = ORIENTED_EDGE('',*,*,#1089,.T.);
#4632 = ORIENTED_EDGE('',*,*,#1165,.T.);
#4633 = ORIENTED_EDGE('',*,*,#1241,.T.);
#4634 = ORIENTED_EDGE('',*,*,#1317,.T.);
#4635 = ORIENTED_EDGE('',*,*,#1393,.T.);
#4636 = ORIENTED_EDGE('',*,*,#1469,.T.);
#4637 = ORIENTED_EDGE('',*,*,#1545,.T.);
#4638 = ORIENTED_EDGE('',*,*,#1621,.T.);
#4639 = ORIENTED_EDGE('',*,*,#1697,.T.);
#4640 = ORIENTED_EDGE('',*,*,#1773,.T.);
#4641 = ORIENTED_EDGE('',*,*,#1849,.T.);
#4642 = ORIENTED_EDGE('',*,*,#1925,.T.);
#4643 = ORIENTED_EDGE('',*,*,#2001,.T.);
#4644 = ORIENTED_EDGE('',*,*,#2077,.T.);
#4645 = ORIENTED_EDGE('',*,*,#2153,.T.);
#4646 = ORIENTED_EDGE('',*,*,#2229,.T.);
#4647 = ORIENTED_EDGE('',*,*,#2305,.T.);
#4648 = ORIENTED_EDGE('',*,*,#2381,.T.);
#4649 = ORIENTED_EDGE('',*,*,#2457,.T.);
#4650 = ORIENTED_EDGE('',*,*,#2533,.T.);
#4651 = ORIENTED_EDGE('',*,*,#2609,.T.);
#4652 = ORIENTED_EDGE('',*,*,#2685,.T.);
#4653 = ORIENTED_EDGE('',*,*,#2761,.T.);
#4654 = ORIENTED_EDGE('',*,*,#2837,.T.);
#4655 = ORIENTED_EDGE('',*,*,#2913,.T.);
#4656 = ORIENTED_EDGE('',*,*,#2989,.T.);
#4657 = ORIENTED_EDGE('',*,*,#3060,.T.);
#4658 = FACE_BOUND('',#4659,.T.);
#4659 = EDGE_LOOP('',(#4660));
#4660 = ORIENTED_EDGE('',*,*,#3136,.T.);
#4661 = FACE_BOUND('',#4662,.T.);
#4662 = EDGE_LOOP('',(#4663));
#4663 = ORIENTED_EDGE('',*,*,#3214,.T.);
#4664 = FACE_BOUND('',#4665,.T.);
#4665 = EDGE_LOOP('',(#4666));
#4666 = ORIENTED_EDGE('',*,*,#3292,.T.);
#4667 = FACE_BOUND('',#4668,.T.);
#4668 = EDGE_LOOP('',(#4669));
#4669 = ORIENTED_EDGE('',*,*,#3370,.T.);
#4670 = FACE_BOUND('',#4671,.T.);
#4671 = EDGE_LOOP('',(#4672));
#4672 = ORIENTED_EDGE('',*,*,#3448,.T.);
#4673 = FACE_BOUND('',#4674,.T.);
#4674 = EDGE_LOOP('',(#4675));
#4675 = ORIENTED_EDGE('',*,*,#3526,.T.);
#4676 = FACE_BOUND('',#4677,.T.);
#4677 = EDGE_LOOP('',(#4678));
#4678 = ORIENTED_EDGE('',*,*,#3604,.T.);
#4679 = FACE_BOUND('',#4680,.T.);
#4680 = EDGE_LOOP('',(#4681));
#4681 = ORIENTED_EDGE('',*,*,#3682,.T.);
#4682 = FACE_BOUND('',#4683,.T.);
#4683 = EDGE_LOOP('',(#4684));
#4684 = ORIENTED_EDGE('',*,*,#3760,.T.);
#4685 = FACE_BOUND('',#4686,.T.);
#4686 = EDGE_LOOP('',(#4687));
#4687 = ORIENTED_EDGE('',*,*,#3838,.T.);
#4688 = FACE_BOUND('',#4689,.T.);
#4689 = EDGE_LOOP('',(#4690));
#4690 = ORIENTED_EDGE('',*,*,#3916,.T.);
#4691 = FACE_BOUND('',#4692,.T.);
#4692 = EDGE_LOOP('',(#4693));
#4693 = ORIENTED_EDGE('',*,*,#3994,.T.);
#4694 = FACE_BOUND('',#4695,.T.);
#4695 = EDGE_LOOP('',(#4696));
#4696 = ORIENTED_EDGE('',*,*,#4072,.T.);
#4697 = FACE_BOUND('',#4698,.T.);
#4698 = EDGE_LOOP('',(#4699));
#4699 = ORIENTED_EDGE('',*,*,#4150,.T.);
#4700 = FACE_BOUND('',#4701,.T.);
#4701 = EDGE_LOOP('',(#4702));
#4702 = ORIENTED_EDGE('',*,*,#4228,.T.);
#4703 = FACE_BOUND('',#4704,.T.);
#4704 = EDGE_LOOP('',(#4705));
#4705 = ORIENTED_EDGE('',*,*,#4306,.T.);
#4706 = FACE_BOUND('',#4707,.T.);
#4707 = EDGE_LOOP('',(#4708));
#4708 = ORIENTED_EDGE('',*,*,#4384,.T.);
#4709 = FACE_BOUND('',#4710,.T.);
#4710 = EDGE_LOOP('',(#4711));
#4711 = ORIENTED_EDGE('',*,*,#4462,.T.);
#4712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#4716)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#4713,#4714,#4715)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#4713 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#4714 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#4715 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#4716 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-007),#4713,
  'distance_accuracy_value','confusion accuracy');
#4717 = SHAPE_DEFINITION_REPRESENTATION(#4718,#25);
#4718 = PRODUCT_DEFINITION_SHAPE('','',#4719);
#4719 = PRODUCT_DEFINITION('design','',#4720,#4723);
#4720 = PRODUCT_DEFINITION_FORMATION('','',#4721);
#4721 = PRODUCT('Board','Board','',(#4722));
#4722 = MECHANICAL_CONTEXT('',#2,'mechanical');
#4723 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#4724 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#4725,#4727);
#4725 = ( REPRESENTATION_RELATIONSHIP('','',#25,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#4726) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#4726 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#4727 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #4728);
#4728 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('1','=>[0:1:1:2]','',#5,#4719,$);
#4729 = PRODUCT_TYPE('part',$,(#4721));
#4730 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #4731),#4712);
#4731 = STYLED_ITEM('color',(#4732),#26);
#4732 = PRESENTATION_STYLE_ASSIGNMENT((#4733,#4739));
#4733 = SURFACE_STYLE_USAGE(.BOTH.,#4734);
#4734 = SURFACE_SIDE_STYLE('',(#4735));
#4735 = SURFACE_STYLE_FILL_AREA(#4736);
#4736 = FILL_AREA_STYLE('',(#4737));
#4737 = FILL_AREA_STYLE_COLOUR('',#4738);
#4738 = COLOUR_RGB('',0.E+000,0.501960813999,0.E+000);
#4739 = CURVE_STYLE('',#4740,POSITIVE_LENGTH_MEASURE(0.1),#4738);
#4740 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
