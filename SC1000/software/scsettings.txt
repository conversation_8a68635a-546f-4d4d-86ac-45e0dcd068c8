#######################
# SC1000 config file #
######################
# Any lines beginning with # are ignored


###################
# General Options #
###################
#
# Fader thresholds for hysteresis
# You might need to adjust these for different faders
# faderopenpoint should always be 
# more than faderclosepoint

# Value required to open the fader when it's closed
faderopenpoint=5

# Value required to close the fader when it's opened
faderclosepoint=3


# 0 to disable platter, default 1 (enabled)
platterenabled=1

# Ratio of platter movement to sample movement
# 4096 = 1 second for every platter rotation
# 3072 = equivalent to 45rpm record (default)
# 2275 = equivalent to 33rpm
platterspeed=2275

# How long the stop button takes to stop the track
# Higher is longer, default = 3000
brakespeed=3000

# Pitch range % of pitch bend MIDI command, default 50%
pitchrange=50

# How long to delay before looking for MIDI devices
# Useful if your MIDI device takes a long time to initialize
# Or to give you time to switch your phone to MIDI mode
mididelay=5

# Set this to 1 or 2 to make the fader cut the beats
# 1 will cut on the left, 2 will cut on the right
cutbeats=0

####################
#   GPIO Mapping   #
####################
#
# This section is used to wire up buttons.
# These can be either directly wired to the Olimex module,
# or on an external MCP23017 GPIO expander
# Format is : 
# gpio=port,pin,pull,edge,action

# port is 0 for MCP23017, or 123456 for ports BCDEFG of A13 on Olimex
# pin number- for MCP23017, A0-A7 is 0-7 and B0-B7 is 8-15
# pull is 1 for pullup, 2 for pulldown
# edge is 1 for pressing edge, 2 for holding action, 3 for pressed while shifted, 4 for held while shifted, 0 for releasing edge
# action is one of :
# 
# (replace x with 0 for beats or 1 for samples) :
# CHx_CUE - Set and trigger cue points
# CHx_SHIFTON - Activate shift mode (ignores x)
# CHx_SHIFTOFF - Deactivate shift mode (also ignores x)
# CHx_STARTSTOP - Start or Stop the track
# CHx_NOTEy - Change the pitch musically, replace y with the note number (60 being 0%)
# CHx_GND - make the pin an output and set it low - used to provide additional ground connections (ignores x)
# gpio=port,pin,pull,edge,action

#### SC500 Buttons ####
# Detection pin
gpio=6,11,1,1,CH0_SC500

# Shift
gpio=2,9,1,1,CH0_SHIFTON
gpio=2,9,1,0,CH0_SHIFTOFF

# Beat Transport
gpio=2,14,1,1,CH0_PREVFILE
gpio=2,14,1,2,CH0_PREVFOLDER
gpio=2,14,1,3,CH0_RANDOMFILE

gpio=2,13,1,1,CH0_STARTSTOP

# even though this is on a beat button it still affects sample
gpio=2,13,1,3,CH1_JOGREVERSE

gpio=2,10,1,1,CH0_NEXTFILE
gpio=2,10,1,2,CH0_NEXTFOLDER
gpio=2,10,1,3,CH0_JOGPIT
gpio=2,10,1,0,CH0_JOGPSTOP

# Beat Volume Buttons
gpio=4,5,1,1,CH0_VOLUP
gpio=4,5,1,2,CH0_VOLUHOLD
gpio=4,4,1,1,CH0_VOLDOWN
gpio=4,4,1,2,CH0_VOLDHOLD

# Sample Transport
gpio=2,5,1,1,CH1_PREVFILE
gpio=2,5,1,2,CH1_PREVFOLDER
gpio=2,5,1,3,CH1_JOGPIT
gpio=2,5,1,0,CH1_JOGPSTOP

gpio=2,6,1,1,CH1_STARTSTOP
gpio=2,6,1,3,CH1_RECORD

gpio=2,8,1,1,CH1_NEXTFILE
gpio=2,8,1,2,CH1_NEXTFOLDER
gpio=2,8,1,3,CH1_RANDOMFILE

# Sample volume buttons
gpio=1,10,1,1,CH1_VOLUP
gpio=1,10,1,2,CH1_VOLUHOLD
gpio=1,4,1,1,CH1_VOLDOWN
gpio=1,4,1,2,CH1_VOLDHOLD

# Sample cue buttons
gpio=2,4,1,1,CH1_CUE
gpio=2,4,1,3,CH1_DELETECUE
gpio=2,7,1,1,CH1_CUE
gpio=2,7,1,3,CH1_DELETECUE
gpio=2,11,1,1,CH1_CUE
gpio=2,11,1,3,CH1_DELETECUE
gpio=2,12,1,1,CH1_CUE
gpio=2,12,1,3,CH1_DELETECUE


#### Expansion port J7 ####
gpio=6,10,1,1,CH0_SHIFTON
gpio=6,10,1,0,CH0_SHIFTOFF
gpio=1,15,1,1,CH0_STARTSTOP
gpio=1,16,1,1,CH1_STARTSTOP

gpio=2,3,1,1,CH1_CUE
gpio=2,0,1,1,CH1_CUE
gpio=2,2,1,1,CH1_CUE
gpio=2,1,1,1,CH1_CUE

gpio=2,3,1,3,CH1_DELETECUE
gpio=2,0,1,3,CH1_DELETECUE
gpio=2,2,1,3,CH1_DELETECUE
gpio=2,1,1,3,CH1_DELETECUE


#### MCP23017 Buttons ####
gpio=0,0,0,1,CH0_GND

gpio=0,1,1,1,CH1_CUE
gpio=0,2,1,1,CH1_CUE
gpio=0,3,1,1,CH1_CUE
gpio=0,4,1,1,CH1_CUE
gpio=0,5,1,1,CH1_CUE
gpio=0,6,1,1,CH1_CUE
gpio=0,7,1,1,CH1_CUE

gpio=0,1,1,3,CH1_DELETECUE
gpio=0,2,1,3,CH1_DELETECUE
gpio=0,3,1,3,CH1_DELETECUE
gpio=0,4,1,3,CH1_DELETECUE
gpio=0,5,1,3,CH1_DELETECUE
gpio=0,6,1,3,CH1_DELETECUE
gpio=0,7,1,3,CH1_DELETECUE

gpio=0,8,1,1,CH1_STARTSTOP
gpio=0,9,1,1,CH0_SHIFTON
gpio=0,9,1,0,CH0_SHIFTOFF 
gpio=0,10,1,1,CH1_CUE
gpio=0,10,1,3,CH1_DELETECUE
gpio=0,11,1,1,CH0_STARTSTOP
gpio=0,12,0,1,CH1_GND
gpio=0,13,1,1,CH1_CUE
gpio=0,14,1,1,CH1_CUE
gpio=0,15,1,1,CH1_CUE
gpio=0,13,1,3,CH1_DELETECUE
gpio=0,14,1,3,CH1_DELETECUE
gpio=0,15,1,3,CH1_DELETECUE



################
# MIDI Mapping #
################
#
# Format is :
# Status,channel,data,edge, action
# 
# Status can be : 
# 8 - Note OFF (will also match zero-velocity Note ON)
# 9 - Note ON
# 11 - Control Change
# 14 - Pitch Bend
#
# Channel is the MIDI channel of the incoming MIDI message
#
# Data is the note or CC number - set to "255" to bind to ALL notes of a particular channel
#
# Action can be (replace x with 0 for beats or 1 for samples) :
# CHx_CUE - Set and trigger cue points
# CHx_SHIFTON - Activate shift mode (ignores x)
# CHx_SHIFTOFF - Deactivate shift mode (also ignores x)
# CHx_STARTSTOP - Start or Stop the track
# CHx_PITCH - Change the speed of the track
# CHx_NOTEy - Change the pitch musically, replace y with the note number (60 being 0%)



#### LEMUR PRESET, normally hardcoded ####

# CC 0 of channels 0/1 is volume
midii=11,0,0,1,CH0_VOLUME
midii=11,1,0,1,CH1_VOLUME

# Pitch bend on channels 0 and 1 is pitch

midii=14,0,0,1,CH0_PITCH
midii=14,1,0,1,CH1_PITCH

# notes on channels 0 and 1 are cue points
midii=9,0,255,1,CH0_CUE
midii=9,1,255,1,CH1_CUE


# Also add the delete cue command for shift modifier
midii=9,0,255,3,CH0_DELETECUE
midii=9,1,255,3,CH1_DELETECUE


# Notes on channel 3 are C1-style notes
midii=9,3,255,1,CH1_NOTE


# Channel 4 has various other commands mapped to its notes
# Notes 0-1 = start stop
midii=9,4,0,1,CH0_STARTSTOP
midii=9,4,1,1,CH1_STARTSTOP

# Notes 2-3 = Next file
midii=9,4,2,1,CH0_NEXTFILE
midii=9,4,3,1,CH1_NEXTFILE

# Notes 4-5 = Next folder
midii=9,4,4,1,CH0_NEXTFOLDER
midii=9,4,5,1,CH1_NEXTFOLDER

# Notes 6-7 = Prev file
midii=9,4,6,1,CH0_PREVFILE
midii=9,4,7,1,CH1_PREVFILE

# Notes 8-9 = Prev folder
midii=9,4,8,1,CH0_PREVFOLDER
midii=9,4,9,1,CH1_PREVFOLDER

# Notes 10-11 = Random file
midii=9,4,10,1,CH0_RANDOMFILE
midii=9,4,11,1,CH1_RANDOMFILE

# Note 126 = record
midii=9,4,126,1,CH0_RECORD

# Note 127 = shift
midii=9,4,127,1,CH1_SHIFTON
midii=8,4,127,3,CH1_SHIFTOFF

# Status,channel,data,edge,action