v1.7 (2018-01-19)
-----------------

* Fix a bug that prevented dithering from being used
* Honour the system locale; allow the full character set in track names
* When searching, match characters in other locales

v1.6 (2016-08-13)
-----------------

* Added timecode creator (for the adventurous)
* Correct interpretation of ALSA's buffer size
* Command-line flag to remove all window decorations (--no-decor)
* A dummy deck for test cases
* Internal restructing in preparation for new features
* Build without warnings on modern compilers

Acknowledgements: <PERSON>, Al<PERSON>io Treglia

v1.5 (2014-02-09)
-----------------

* Scan the music library in the background on startup
* Function to re-scan a crate
* Performance improovement to audio resampler
* Additional font paths
* Minor bug fixes

v1.4 (2013-06-08)
-----------------

* Scalable user interface
* Simple control of timecode levels for 'software pre-amp'
* Bug fix: MIDI interface now works when only JACK audio is used
* Di<PERSON> flag renamed to '--dicer'
* Minor fixes

Acknowledgements: <PERSON>

v1.3 (2012-11-29)
-----------------

* <PERSON><PERSON> (BPM) field, and sorting and changing of sort modes
* Allow initial X window size and position to be set by the user
* Makefile modifications for packagers
* Track loading errors are reported to the main interface

Acknowledgements: Engine, Lukas Fleischer, Daniel Holbach, Matej Laitl

v1.2 (2012-03-23)
-----------------

* Scan directories only for known file extensions
* Support Novation 'Dicer' controller
* Cue points, including 'punch' feature
* Extra protection against skips: optionally lock memory into RAM

Acknowledgements: Novation, Olivier Gauthier, Christoph Krapp, Mitchell Smith

v1.1 (2012-01-30)
-----------------

* Bug fix: incorrect display of track time remaining
* Instant loading of duplicate tracks
* Optionally protect decks during playback
* Improvements to music selector
* Increase speed building look-up tables

v1.0 (2011-08-01)
-----------------

* Changing of timecode at runtime
* Improved parsing of vinyl track numbers
* Bug fix: affecting multiple decks with different sample rates
* Optimise timecode error checking during scratching
* Require realtime priority; don't start without it
* Internal restructuring

Acknowledgements: Robert Flechtner, Daniel Holbach, Lukas Fleischer

v0.9 (2011-04-19)
-----------------

* Internal cleanups
* Filtering of duplicate entries in the record library
* Scanning of ordered playlists
* Improved response when scratching
* New parsing rules for filenames
* A single button toggles timecode control on/off

Acknowledgements: Daniel Holbach, Robert Vettel

v0.8 (2010-11-08)
-----------------

* 45 RPM control
* Conversion of non-44100Hz MP3 files
* Code cleanups and minor fixes

Acknowledgements: Ewan Colsell, Robert Flechtner, Daniel Holbach, Matej Laitl

v0.7 (2010-02-26)
-----------------

* Multiple crates in music selector (Yves Adler)
* Fix a potential buffer overflow (Matej Laitl)
* Higher quality resampler
* Improved pitch stability over long mixes
* Installation script and man page for distributions
* Moved from Bitstream Vera to DejaVu font
* Minor fixes

Acknowledgements: Yves Adler, Matej Laitl

v0.6 (2009-09-03)
-----------------

* Modular scanning of music library
* Improved parsing of pathnames
* Decreased memory use of timecode decoder
* Minor fixes

Acknowledgements: Yves Adler

v0.5 (2009-07-03)
-----------------

* Configurable sample rates
* Timecode support for MixVibes vinyls
* Rewritten timecode decoder with 4x resolution
* Rewritten timecode tracking with improved accuracy
* JACK audio device support
* Clearer display of current position in the track overview
* Minor fixes

Acknowledgements: Daniel Fasnacht, Yves Adler

v0.4 (2008-05-07)
-----------------

* Timecode improvements

v0.3 (2007-12-04)
-----------------

* ALSA device support
* Timecode optimisations
* Minor fixes

v0.2 (2007-06-12)
-----------------

* First open-source release
