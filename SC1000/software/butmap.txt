
S1 - 35 - 19 - C7 = CUE2
S2 - 31 - 17 - C5= PREVSAMP, PITCHSAMP
S3 - 33 - 18 - C6= STARTSAMP, REC
S4 - 37 - 20 - C8= NEXTSAMP, RANDSAMP
S5 - 39 - 21 - C9= SHIFT
S7 - 32 - 26 - C14 = PREVBEAT, PITCHBEAT
S8 - 34 - 25 - C13 = STARTBEAT, ALT
S9 - 40 - 22 - C10= NEXTBEAT, RANDBEAT
S10 - 36 - 24 - C12= CUE4
S11 - 38 - 23 - C11= CUE3
S12 - 29 - 16 - C4= CUE1
S13 - 15 - 9 - B10= SAMPVOL
S14 - 13 - 8 - B4= SAMPVOL
S15 - 24 - 30 - E5= BEATVOL
S16 - 26 - 29 - E4= BEATVOL
G11 - SC500 detection port

J7
1 - GND
2 - VCC
3 - 38 - G10 - SHIFT
4 - 10 - B15 - BEAT START/STOP
5 - 37 (LED1?) - 
6 - 11 - B16 - SAMPLE START/STOP
7 - 15 - C3 - CUE
8 - 12 - C0 - <PERSON>UE
9 - 14 - C2 - CUE
10 - 13 - C1 - CUE

01C20800 base




pull register AC set to 00000500
data register A0 should be 0x00000020 or 0x00000010 if we're upgrading
portG config 0x01C208DC default 0x00011010, should be set to 0x00010010
portG pull F4 default 0x00000119, should be 0x00400119
portG data register E8. Will be A06 if SC500, 206 if SC500
