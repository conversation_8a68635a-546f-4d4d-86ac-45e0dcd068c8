<?xml version="1.0" encoding="UTF-8"?>
<configurationDescriptor version="65">
  <logicalFolder name="root" displayName="root" projectFiles="true">
    <logicalFolder name="HeaderFiles"
                   displayName="Header Files"
                   projectFiles="true">
      <logicalFolder name="MCC Generated Files"
                     displayName="MCC Generated Files"
                     projectFiles="true">
        <itemPath>mcc_generated_files/mcc.h</itemPath>
        <itemPath>mcc_generated_files/pin_manager.h</itemPath>
      </logicalFolder>
    </logicalFolder>
    <logicalFolder name="LinkerScript"
                   displayName="Linker Files"
                   projectFiles="true">
    </logicalFolder>
    <logicalFolder name="SourceFiles"
                   displayName="Source Files"
                   projectFiles="true">
      <logicalFolder name="MCC Generated Files"
                     displayName="MCC Generated Files"
                     projectFiles="true">
        <itemPath>mcc_generated_files/mcc.c</itemPath>
        <itemPath>mcc_generated_files/device_config.c</itemPath>
        <itemPath>mcc_generated_files/pin_manager.c</itemPath>
      </logicalFolder>
      <itemPath>main.c</itemPath>
    </logicalFolder>
    <logicalFolder name="ExternalFiles"
                   displayName="Important Files"
                   projectFiles="false">
      <itemPath>Makefile</itemPath>
      <itemPath>MyConfig.mc3</itemPath>
    </logicalFolder>
  </logicalFolder>
  <projectmakefile>Makefile</projectmakefile>
  <confs>
    <conf name="default" type="2">
      <toolsSet>
        <developmentServer>localhost</developmentServer>
        <targetDevice>PIC18LF14K22</targetDevice>
        <targetHeader></targetHeader>
        <targetPluginBoard></targetPluginBoard>
        <platformTool>PICkit3PlatformTool</platformTool>
        <languageToolchain>XC8</languageToolchain>
        <languageToolchainVersion>2.05</languageToolchainVersion>
        <platform>3</platform>
      </toolsSet>
      <packs>
        <pack name="PIC18F-K_DFP" vendor="Microchip" version="1.1.58"/>
      </packs>
      <compileType>
        <linkerTool>
          <linkerLibItems>
          </linkerLibItems>
        </linkerTool>
        <archiverTool>
        </archiverTool>
        <loading>
          <useAlternateLoadableFile>false</useAlternateLoadableFile>
          <parseOnProdLoad>false</parseOnProdLoad>
          <alternateLoadableFile></alternateLoadableFile>
        </loading>
        <subordinates>
        </subordinates>
      </compileType>
      <makeCustomizationType>
        <makeCustomizationPreStepEnabled>false</makeCustomizationPreStepEnabled>
        <makeCustomizationPreStep></makeCustomizationPreStep>
        <makeCustomizationPostStepEnabled>false</makeCustomizationPostStepEnabled>
        <makeCustomizationPostStep></makeCustomizationPostStep>
        <makeCustomizationPutChecksumInUserID>false</makeCustomizationPutChecksumInUserID>
        <makeCustomizationEnableLongLines>false</makeCustomizationEnableLongLines>
        <makeCustomizationNormalizeHexFile>false</makeCustomizationNormalizeHexFile>
      </makeCustomizationType>
      <HI-TECH-COMP>
        <property key="additional-warnings" value="true"/>
        <property key="asmlist" value="true"/>
        <property key="default-bitfield-type" value="true"/>
        <property key="default-char-type" value="true"/>
        <property key="define-macros" value=""/>
        <property key="disable-optimizations" value="true"/>
        <property key="extra-include-directories" value=""/>
        <property key="favor-optimization-for" value="-speed,+space"/>
        <property key="garbage-collect-data" value="true"/>
        <property key="garbage-collect-functions" value="true"/>
        <property key="identifier-length" value="255"/>
        <property key="local-generation" value="false"/>
        <property key="operation-mode" value="free"/>
        <property key="opt-xc8-compiler-strict_ansi" value="false"/>
        <property key="optimization-assembler" value="true"/>
        <property key="optimization-assembler-files" value="true"/>
        <property key="optimization-debug" value="false"/>
        <property key="optimization-invariant-enable" value="false"/>
        <property key="optimization-invariant-value" value="16"/>
        <property key="optimization-level" value="9"/>
        <property key="optimization-speed" value="false"/>
        <property key="optimization-stable-enable" value="false"/>
        <property key="pack-struct" value="true"/>
        <property key="preprocess-assembler" value="true"/>
        <property key="short-enums" value="true"/>
        <property key="undefine-macros" value=""/>
        <property key="use-cci" value="false"/>
        <property key="use-iar" value="false"/>
        <property key="verbose" value="false"/>
        <property key="warning-level" value="-3"/>
        <property key="what-to-do" value="ignore"/>
      </HI-TECH-COMP>
      <HI-TECH-LINK>
        <property key="additional-options-checksum" value=""/>
        <property key="additional-options-code-offset" value=""/>
        <property key="additional-options-command-line" value=""/>
        <property key="additional-options-errata" value=""/>
        <property key="additional-options-extend-address" value="false"/>
        <property key="additional-options-trace-type" value=""/>
        <property key="additional-options-use-response-files" value="false"/>
        <property key="backup-reset-condition-flags" value="false"/>
        <property key="calibrate-oscillator" value="false"/>
        <property key="calibrate-oscillator-value" value="0x3400"/>
        <property key="clear-bss" value="true"/>
        <property key="code-model-external" value="wordwrite"/>
        <property key="code-model-rom" value=""/>
        <property key="create-html-files" value="false"/>
        <property key="data-model-ram" value=""/>
        <property key="data-model-size-of-double" value="32"/>
        <property key="data-model-size-of-double-gcc" value="no-short-double"/>
        <property key="data-model-size-of-float" value="32"/>
        <property key="data-model-size-of-float-gcc" value="no-short-float"/>
        <property key="display-class-usage" value="false"/>
        <property key="display-hex-usage" value="false"/>
        <property key="display-overall-usage" value="true"/>
        <property key="display-psect-usage" value="false"/>
        <property key="extra-lib-directories" value=""/>
        <property key="fill-flash-options-addr" value=""/>
        <property key="fill-flash-options-const" value=""/>
        <property key="fill-flash-options-how" value="0"/>
        <property key="fill-flash-options-inc-const" value="1"/>
        <property key="fill-flash-options-increment" value=""/>
        <property key="fill-flash-options-seq" value=""/>
        <property key="fill-flash-options-what" value="0"/>
        <property key="format-hex-file-for-download" value="false"/>
        <property key="initialize-data" value="true"/>
        <property key="input-libraries" value="libm"/>
        <property key="keep-generated-startup.as" value="false"/>
        <property key="link-in-c-library" value="true"/>
        <property key="link-in-c-library-gcc" value=""/>
        <property key="link-in-peripheral-library" value="false"/>
        <property key="managed-stack" value="false"/>
        <property key="opt-xc8-linker-file" value="false"/>
        <property key="opt-xc8-linker-link_startup" value="false"/>
        <property key="opt-xc8-linker-serial" value=""/>
        <property key="program-the-device-with-default-config-words" value="true"/>
        <property key="remove-unused-sections" value="true"/>
      </HI-TECH-LINK>
      <PICkit3PlatformTool>
        <property key="AutoSelectMemRanges" value="auto"/>
        <property key="Freeze Peripherals" value="true"/>
        <property key="SecureSegment.SegmentProgramming" value="FullChipProgramming"/>
        <property key="ToolFirmwareFilePath"
                  value="Press to browse for a specific firmware version"/>
        <property key="ToolFirmwareOption.UseLatestFirmware" value="true"/>
        <property key="debugoptions.useswbreakpoints" value="false"/>
        <property key="firmware.download.all" value="false"/>
        <property key="hwtoolclock.frcindebug" value="false"/>
        <property key="memories.aux" value="false"/>
        <property key="memories.bootflash" value="true"/>
        <property key="memories.configurationmemory" value="true"/>
        <property key="memories.configurationmemory2" value="true"/>
        <property key="memories.dataflash" value="true"/>
        <property key="memories.eeprom" value="true"/>
        <property key="memories.flashdata" value="true"/>
        <property key="memories.id" value="true"/>
        <property key="memories.instruction.ram" value="true"/>
        <property key="memories.instruction.ram.ranges"
                  value="${memories.instruction.ram.ranges}"/>
        <property key="memories.programmemory" value="true"/>
        <property key="memories.programmemory.ranges" value="0-1fff"/>
        <property key="poweroptions.powerenable" value="false"/>
        <property key="programmertogo.imagename" value=""/>
        <property key="programoptions.donoteraseauxmem" value="false"/>
        <property key="programoptions.eraseb4program" value="true"/>
        <property key="programoptions.pgmspeed" value="2"/>
        <property key="programoptions.preservedataflash" value="false"/>
        <property key="programoptions.preservedataflash.ranges"
                  value="${programoptions.preservedataflash.ranges}"/>
        <property key="programoptions.preserveeeprom" value="false"/>
        <property key="programoptions.preserveeeprom.ranges" value=""/>
        <property key="programoptions.preserveprogram.ranges" value=""/>
        <property key="programoptions.preserveprogramrange" value="false"/>
        <property key="programoptions.preserveuserid" value="false"/>
        <property key="programoptions.programcalmem" value="false"/>
        <property key="programoptions.programuserotp" value="false"/>
        <property key="programoptions.testmodeentrymethod" value="VDDFirst"/>
        <property key="programoptions.usehighvoltageonmclr" value="false"/>
        <property key="programoptions.uselvpprogramming" value="false"/>
        <property key="voltagevalue" value="3.375"/>
      </PICkit3PlatformTool>
      <XC8-CO>
        <property key="coverage-enable" value=""/>
      </XC8-CO>
      <XC8-config-global>
        <property key="advanced-elf" value="true"/>
        <property key="gcc-opt-driver-new" value="true"/>
        <property key="gcc-opt-std" value="-std=c99"/>
        <property key="gcc-output-file-format" value="dwarf-3"/>
        <property key="omit-pack-options" value="false"/>
        <property key="output-file-format" value="-mcof,+elf"/>
        <property key="stack-size-high" value="auto"/>
        <property key="stack-size-low" value="auto"/>
        <property key="stack-size-main" value="auto"/>
        <property key="stack-type" value="compiled"/>
        <property key="user-pack-device-support" value=""/>
      </XC8-config-global>
    </conf>
  </confs>
</configurationDescriptor>
